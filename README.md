# 🔥 Gas Delivery System

A comprehensive full-stack application for managing gas cylinder delivery operations with role-based access control, real-time inventory management, and seamless order processing.

## 📋 Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Tech Stack](#tech-stack)
- [Architecture](#architecture)
- [Getting Started](#getting-started)
- [API Documentation](#api-documentation)
- [Project Structure](#project-structure)
- [Environment Variables](#environment-variables)
- [Deployment](#deployment)
- [Contributing](#contributing)
- [License](#license)

## 🎯 Overview

The Gas Delivery System is a modern, scalable solution designed to streamline gas cylinder delivery operations. It provides a complete ecosystem for customers to place orders, agents to manage deliveries, and administrators to oversee the entire operation.

### Key Capabilities

- **Multi-Role Access**: Customer, Agent, Admin, and Supervisor roles with specific permissions
- **Real-time Inventory**: Live stock tracking with automatic reservation and release
- **Order Management**: Complete order lifecycle from creation to delivery
- **Payment Integration**: Secure payment processing with failure handling
- **Image Management**: Professional image upload and serving system
- **Mobile-First Design**: Responsive Flutter frontend optimized for mobile devices

## ✨ Features

### 👥 Role-Based Access Control
- **Customers**: Browse products, place orders, track deliveries
- **Agents**: Manage assigned deliveries, scan QR codes, update order status
- **Supervisors**: View today's sales, assign orders, limited reporting access
- **Admins**: Full system access, inventory management, comprehensive analytics

### 📦 Inventory Management
- **Multi-Category Products**: Gas cylinders, spare parts, and complete packages
- **Smart Stock Control**: Automatic reservation, release, and sold tracking
- **Low Stock Alerts**: Minimum stock level monitoring
- **Image Upload**: Professional product image management

### 🛒 Order Processing
- **Seamless Ordering**: Intuitive product selection and quantity management
- **Payment Integration**: Secure payment gateway with error handling
- **Order Tracking**: Real-time status updates from creation to delivery
- **QR Code Validation**: Secure delivery confirmation system

### 📊 Analytics & Reporting
- **Dashboard Metrics**: Sales, revenue, and performance analytics
- **Role-Specific Reports**: Tailored data access based on user permissions
- **Real-time Updates**: Live data synchronization across all platforms

## 🛠 Tech Stack

### Backend
- **Runtime**: Node.js 20.x
- **Framework**: Express.js with TypeScript
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT with role-based permissions
- **File Upload**: Multer for image handling
- **Security**: Helmet, CORS, Rate limiting
- **Validation**: Custom validation middleware

### Frontend
- **Framework**: Flutter (Dart)
- **State Management**: BLoC pattern
- **HTTP Client**: Dio with interceptors
- **Image Handling**: Image picker and caching
- **UI Components**: Custom design system with ScreenUtil
- **Navigation**: Custom navigation extensions

### DevOps & Tools
- **Version Control**: Git
- **Package Management**: npm (Backend), pub (Frontend)
- **Build Tools**: TypeScript compiler, Flutter build
- **Code Quality**: Prettier, ESLint
- **Testing**: Jest (Backend), Flutter test (Frontend)

## 🏗 Architecture

The system follows a clean, modular architecture with clear separation of concerns:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter App   │    │   Express API   │    │   MongoDB       │
│                 │    │                 │    │                 │
│ • BLoC Pattern  │◄──►│ • RESTful API   │◄──►│ • Collections   │
│ • Clean Arch    │    │ • JWT Auth      │    │ • Indexes       │
│ • Repository    │    │ • Middleware    │    │ • Transactions  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Backend Architecture
- **Controllers**: Handle HTTP requests and responses
- **Services**: Business logic and data processing
- **Models**: MongoDB schemas with Mongoose
- **Middleware**: Authentication, validation, error handling
- **Utils**: Helper functions and utilities

### Frontend Architecture
- **Presentation Layer**: UI components and pages
- **Business Logic Layer**: BLoC state management
- **Data Layer**: Repositories and data sources
- **Domain Layer**: Entities and use cases

## 🚀 Getting Started

### Prerequisites
- Node.js 20.x or higher
- Flutter SDK 3.x or higher
- MongoDB instance (local or cloud)
- Git

### Backend Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd gas_system_project/backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

### Frontend Setup

1. **Navigate to frontend directory**
   ```bash
   cd ../frontend
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Run the application**
   ```bash
   flutter run
   ```

## 📚 API Documentation

### Base URL
```
Development: http://localhost:3010/api/v1
Production: https://your-domain.com/api/v1
```

### Authentication
All protected endpoints require a JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

### Key Endpoints

#### Authentication
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `POST /auth/send-otp` - Send OTP for verification
- `POST /auth/verify-otp` - Verify OTP

#### Inventory Management
- `GET /cylinders` - Get all cylinders
- `POST /cylinders` - Create new cylinder (Admin only)
- `PUT /cylinders/:id` - Update cylinder (Admin only)
- `DELETE /cylinders/:id` - Delete cylinder (Admin only)

#### Order Management
- `GET /orders` - Get user orders
- `POST /orders` - Create new order
- `PUT /orders/:id/status` - Update order status
- `GET /orders/:id/qr-code` - Get order QR code

#### Image Management
- `POST /images/upload` - Upload product images
- `GET /images/:path` - Serve static images

### Response Format
All API responses follow a consistent format:
```json
{
  "status": "success|fail|error",
  "message": "Response message",
  "data": {}, // Response data
  "meta": {   // Optional metadata
    "pagination": {},
    "filters": {}
  }
}
```

## 📁 Project Structure

```
gas_system_project/
├── backend/                    # Node.js/Express API
│   ├── src/
│   │   ├── controllers/       # Request handlers
│   │   ├── models/           # MongoDB schemas
│   │   ├── routes/           # API routes
│   │   ├── services/         # Business logic
│   │   ├── middleware/       # Custom middleware
│   │   ├── utils/            # Helper functions
│   │   ├── enums/            # Type definitions
│   │   └── server.ts         # Application entry point
│   ├── uploads/              # Uploaded images
│   ├── public/               # Static assets
│   ├── dist/                 # Compiled JavaScript
│   └── package.json
│
├── frontend/                   # Flutter application
│   ├── lib/
│   │   ├── core/             # Core utilities and constants
│   │   ├── features/         # Feature-based modules
│   │   │   ├── auth/         # Authentication
│   │   │   ├── inventory/    # Inventory management
│   │   │   ├── orders/       # Order management
│   │   │   ├── main/         # Main app features
│   │   │   └── shared/       # Shared components
│   │   └── main.dart         # Application entry point
│   ├── assets/               # Images, fonts, etc.
│   └── pubspec.yaml
│
├── docs/                      # Documentation
├── README.md                  # This file
└── .gitignore
```

## 🔧 Environment Variables

### Backend (.env)
```env
# Database
MONGODB_URI=mongodb://localhost:27017/gas_system_db
DB_NAME=gas_system_db

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=30d

# Server
PORT=3010
NODE_ENV=development

# OTP (Optional)
DISABLE_OTP_VERIFICATION=true

# File Upload
MAX_FILE_SIZE=5MB
ALLOWED_FILE_TYPES=image/jpeg,image/jpg,image/png,image/webp,image/gif
```

### Frontend
Update `lib/core/constants/api_end_points.dart`:
```dart
class ApiEndpoints {
  static const String host = 'http://localhost:3010';
  static const String baseUrl = '$host/api/v1';
}
```

## 🚀 Deployment

### Backend Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Set production environment variables**
   ```bash
   export NODE_ENV=production
   export MONGODB_URI=your-production-mongodb-uri
   export JWT_SECRET=your-production-jwt-secret
   ```

3. **Start the production server**
   ```bash
   npm start
   ```

### Frontend Deployment

1. **Build for production**
   ```bash
   flutter build apk --release  # For Android
   flutter build ios --release  # For iOS
   flutter build web --release  # For Web
   ```

2. **Deploy to your preferred platform**
   - **Mobile**: Upload to Google Play Store / Apple App Store
   - **Web**: Deploy to Firebase Hosting, Netlify, or Vercel

### Docker Deployment (Optional)

Create `Dockerfile` for containerized deployment:
```dockerfile
# Backend Dockerfile
FROM node:20-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
COPY uploads ./uploads
COPY public ./public
EXPOSE 3010
CMD ["npm", "start"]
```

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. **Commit your changes**
   ```bash
   git commit -m 'Add some amazing feature'
   ```
4. **Push to the branch**
   ```bash
   git push origin feature/amazing-feature
   ```
5. **Open a Pull Request**

### Development Guidelines
- Follow the existing code style and patterns
- Write meaningful commit messages
- Add tests for new features
- Update documentation as needed
- Ensure all tests pass before submitting

## 📄 License

This project is licensed under the ISC License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Axmed Najaad**
- GitHub: [@axmed](https://github.com/axmed)
- Email: <EMAIL>

## 🙏 Acknowledgments

- Flutter team for the amazing framework
- Express.js community for the robust backend framework
- MongoDB for the flexible database solution
- All contributors who helped make this project better

---

**Built with ❤️ for efficient gas delivery operations**
