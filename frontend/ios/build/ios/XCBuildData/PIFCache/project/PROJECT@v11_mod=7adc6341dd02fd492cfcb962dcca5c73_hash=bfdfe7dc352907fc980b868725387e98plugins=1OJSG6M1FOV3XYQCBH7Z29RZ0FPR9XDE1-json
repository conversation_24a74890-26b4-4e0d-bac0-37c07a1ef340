{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98814b7e2c3bac55ee99d78eaa8d1ec61e", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98c22f26ca3341c3062f2313dc737070d4", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e9828903703a9fe9e3707306e58aab67b51", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984fe658c89ee5b665bef3ca1cb891fe5c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources/connectivity_plus/ConnectivityPlusPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d09217c87533cb2c5f03e5852de28b87", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources/connectivity_plus/ConnectivityProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9813e1a0d5b952628caff40b9b178dd97a", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources/connectivity_plus/PathMonitorConnectivityProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98bfab5b79ff81c831e54a9c51bb084a98", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources/connectivity_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988a6baa4ecadacf4afc32d4644b264532", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ff157c136133ee1b859c6f7d12e9382f", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cfe825909dac3a396316d329c79d8a6f", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e7cf24fdf7bb6c308c5ef4e8a8d5db23", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ba283bf2ad81fcc0fa309f32f40efad", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985721a53be58c6d93d210f84c78cfe77f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98820c0cf6be272b5b3bb3bdd3cc726759", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989f2eb420640712515a9791ed7402cdba", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98afa15a242f447be4b4e3acf3bcd2f944", "name": "frontend", "path": "frontend", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982a9472728d02a59af1959b53141f7882", "name": "gas_system_project", "path": "gas_system_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861b39e700fc03911107fce4437c378e5", "name": "Personal", "path": "Personal", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9866918d4f4d846ac180337009fb795eb5", "name": "Projects", "path": "Projects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985949f6ad9eff6cf129cee68e3c981d7d", "name": "Development", "path": "Development", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983689db500206f80244443b8445f594f5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f0eef4341549e6feda812a36de4017d7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c5905137716c1d8c0997ec555268cf2e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989d3ef12d860a815b0137b6b363de2d20", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a3b1215542d37c83257d5a62515ea51", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986c8843d97c297966fe10dc7be530d3de", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a0bdb956fadc3156148a9ab0406d497", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c1a3fec1bcae2a370e8c6eb7ad93a3c4", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98b4688993f56c4382c00ab951856b3975", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9805c136aa3c6100279447a48005d642a3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f78a37ca53a6a9da565fc9a65ccee846", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98fbcc2a3e87379e0f194611e883910466", "path": "connectivity_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f0d8df23ca231b762fe4878fb0108383", "path": "connectivity_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981f2e61251a2656652a914956e32ad86b", "path": "connectivity_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d315e93815145058acf37a72b2fda438", "path": "connectivity_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dcf627e2913a5e09290ed85ec74fbd3e", "path": "connectivity_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ed93758937cdd0f233d11ca49d094088", "path": "connectivity_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b4fa3b6d4ee12deac44333bda351976b", "path": "connectivity_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a2d253bccae993892703aaac22e834eb", "path": "ResourceBundle-connectivity_plus_privacy-connectivity_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989c67d208809fb7b448c5eedcedd1ffb7", "name": "Support Files", "path": "../../../../Pods/Target Support Files/connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9810704d9ba529aee3c652551e72bc2ce3", "name": "connectivity_plus", "path": "../.symlinks/plugins/connectivity_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98bfd6992d3d3e109d77a25833b9385cea", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984d3daf5efe3e9a5819cd8c69457529a7", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98af23713d6671847d8da0e0c00f9be7af", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9848a7d6a498993ff0ab3fa064963c09e7", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980310b50e9d680c5230fe40bfaf58e40e", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98efd62bba61ccb7994c77e88d5d6fd1cb", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988269ca0acdc27aa4a6ac9099a1149783", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Classes/FlutterSecureStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98177a3345c6642e0ca8a591b783b0dd4a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Classes/FlutterSecureStoragePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981d9c8cb87001259a6ead3ab50fbddfb1", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Classes/FlutterSecureStoragePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988fb1fdb0a4038da29fec91656996ffee", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Classes/SwiftFlutterSecureStoragePlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9880ef3e32689483b340c624c6be96e020", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e989474087f4cbe3162afa430662dcd4621", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98504b59bc52d6069d6ebc4ffd805c1a59", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983d1a1fa8f30d92372f3a85e50a446e57", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e05d292772cd61c0754a42eb5a572a38", "name": "flutter_secure_storage", "path": "flutter_secure_storage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9855a22fd845e011a22d30080d2784a1e6", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9816388064bc4e549a7c5f2b4da79c960d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988cb3b961ac0e6e566d9ca2efa3870203", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ece0050c9fe2c3322aa56cd2d8daeb6d", "name": "frontend", "path": "frontend", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9878e7926705ced3805b0d676bf98f0576", "name": "gas_system_project", "path": "gas_system_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f895c9e8bda4031b1fd948bd4740fc6", "name": "Personal", "path": "Personal", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ae1482202a683901332cc36992cc86a6", "name": "Projects", "path": "Projects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ba35343b6a21682ed068afa7a443dd5", "name": "Development", "path": "Development", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98235b9955d6c34befe3b8aee5f4900caf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986ec21b4db3bc08880c9cdaa6d2c873aa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bc5de25cfec726e18fbdc97f630ac6a4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854a477ef189694a7ac0ba65c62b994fd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989321ebbe60ffc4047f136122b339c4a4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9870f84e96ad7bcb707d2a810b6c671725", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98a85d4197c9fda0dc0b08907e76fe29d6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/flutter_secure_storage.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e981a4e3dc7d72149926cbaf32778c4f0e8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987e2c6a882e50c1644641ffcca9621603", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9811b8b98aa4cdc714f89a8b32895458f7", "path": "flutter_secure_storage.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981b4fb2f270639f389039eaa0b849d33a", "path": "flutter_secure_storage-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9835da24ea3a4d1aab4f35fadd9d2054a9", "path": "flutter_secure_storage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98285346a260892df1856d06c917520508", "path": "flutter_secure_storage-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986dc178ddb22ca1ab74972ebe8aef80f8", "path": "flutter_secure_storage-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9815d1ade88e911774107f13ee01438be4", "path": "flutter_secure_storage.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d10399b629dc8b5fc73c68608834b083", "path": "flutter_secure_storage.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9847d2a9156ee1cbb78dddb53bdfc54293", "path": "ResourceBundle-flutter_secure_storage-flutter_secure_storage-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985251e2df9914288586d4399345f6ebae", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_secure_storage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ae288ada1291cb885f7503084b8a6b4", "name": "flutter_secure_storage", "path": "../.symlinks/plugins/flutter_secure_storage/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98894ecb9e6a6d1d0861afa3a619bef6db", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989327767f279ccf6767de1c4006ac52cf", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b5b3c8920a29d8e3b9ec442097a92ecb", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ef562c69095557685448dc64e99176b", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982091f0f194e42d83a570d9f79bd73fbd", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9858cc6a3f2b9d4f69393fdc61b513895e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985712d0ba4dc8b2e7190907d970591056", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989c55ce097ffe609ad790694fc0cccd3e", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ee14cd56e6a0c4a261997fd75480671c", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981898d1522a2a7c31f85d705a6773c600", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d93a403b5c12a04c5c58e76ef9cc57b0", "name": "frontend", "path": "frontend", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987464d3aa1117618390513b96ca0ad4e1", "name": "gas_system_project", "path": "gas_system_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98433ed884c01d8a0b26735dec560fcc2e", "name": "Personal", "path": "Personal", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d730a11108cabd145bc9f84797e33f5b", "name": "Projects", "path": "Projects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9874e9bf88b48701385fec1d5360b62ecd", "name": "Development", "path": "Development", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e238b1ee5e97a20e021a1eb14ee8ed7c", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98116680bbcc998eb92c15b2b4dab2c4eb", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerImageUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98be8f06f049013f8f99af92449d0f8ab8", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerMetaDataUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989ba20f374cac25f30102593b95264254", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPhotoAssetUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9835bff56c8c201d466b8f5d64d875757b", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a2cb0ae53ba13aaf35b54ac25d0f0bd7", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTPHPickerSaveImageToPathOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98900a89cd739e4b989773d56f9c0cbcca", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98301edd52573e5d5d57505277901d521e", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9812e7ea1f73e56ede7636c8e47b3fd5d6", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerImageUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98899fd74c7b36a4272fce30f16c7ae7f0", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerMetaDataUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aeec801c19791fc06566ff9d0dbbb27d", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPhotoAssetUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a4e8f83225cb9fcec6ecb1a425386fa9", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982ae44bedfdcacace0b9a48ae677a5f2b", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b79f82901df48b3cd29269cfd78ae4e", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTPHPickerSaveImageToPathOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9854eae37d22915c5351bdb7a9b6a4ab16", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98274e1fc84b247264f6d2e657dc8bce0e", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a126f2e2b9b634b5aeff25ed77c55472", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9830ed46a7f5551dc4f1fd4ccf2489f14a", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aaaabbc269451bb2ab2c5637b017b7d4", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b4e9afd12059536f71b4ac5c42be3151", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857bcf57ee33d6591440598b4d4acb4b7", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dc348327f7c84a588d5cdcf276173c5d", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98001dfeb92954ebc3b42a0c11652c9988", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981184998d576eec5a04387b2f954a8bc1", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ebafabdba44d08df5dd37f25a49b5fd1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98581f09d756aa28de307df293b0a6eaea", "name": "frontend", "path": "frontend", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ef5b5c5bd28e2364709817cab852007", "name": "gas_system_project", "path": "gas_system_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824eeeb769b1d583cb74e5196bb64e273", "name": "Personal", "path": "Personal", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839313ac9d8b1e7153c7e2715fc9dc700", "name": "Projects", "path": "Projects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9871031ff3d6ec0629e2bcbd2e7a91e8b3", "name": "Development", "path": "Development", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98318536e40438b871d91bb00972a75239", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9833cb57199e254f30df260ab95acc612f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982e998582fb438bf95e7749de0c7e3434", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809f48730f4fa69b720f3a664add568cf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98888671593070b4a936bd60f2c0d82aa4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989e12d7e7a35e3cb1295e7de3f61105b6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a3484a9c8e1e10ff87cd2358a18fe31a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836984c473c093e0c83bb45427a496175", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9837d96e90448f30cd1d9d51bd40c619ab", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98503b9f5b056ea654dd3854557a95697f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/ImagePickerPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98eea7c498f3f67e8d84f7d5629e3b2251", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bc739f3a6fc3dc2f35da6a5e7eed7213", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e988b4e3a73e5b5b3c6c9f416506b3a9e37", "path": "image_picker_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986ef811a7828bdb78d3444c4263f12921", "path": "image_picker_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9805577b49ffd39ccff217b82b61abf3c9", "path": "image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98249844de24caf8a51ddd22a4302c4c59", "path": "image_picker_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98cebd5796728542beafd05f898ad2188b", "path": "image_picker_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98046dd0de7ef27b916ed7c435c12be5d6", "path": "image_picker_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f1b5c40edb0e587f3015393b62aecc96", "path": "ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ae52e171e6b523851c45bf08198263c4", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d76fd83b3f7cdc189750b457cec4494", "name": "image_picker_ios", "path": "../.symlinks/plugins/image_picker_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98acb532f760a90c8539447ae2c304ac94", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98316ebe9bf7cd14102ae9e2255b3f2755", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983d401db3c53ded1885a4818e6d860d63", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fb06affefe15d4cebc3882f244224174", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b1bedaa8f527bbb925aa577f2657e266", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cb842c2c4e3842f5833f9f2567a92a7a", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981bf433ef876375e2c4b83291c95a30d8", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988893b2b2da4e9aa97aaaabf0d1bb957f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e82788694e736d26bfe2eadee1c8cd93", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898d72fd32addb66d47a2d117905a91ae", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989dd7574fccdd8b9106e6cd18a99e5eca", "name": "frontend", "path": "frontend", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983362fac09abad64921a3e7d78485a7b5", "name": "gas_system_project", "path": "gas_system_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f44cb8afa15b2c59733dd6d63d8b6720", "name": "Personal", "path": "Personal", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854846906d6872be274f1a8140f87bfa8", "name": "Projects", "path": "Projects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c5fbdc7279fe48879e5812aee9dcc0dc", "name": "Development", "path": "Development", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb733777913482d5cdcc3391f762fa1e", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9871eec9e6242a46dd5632f17de87fc006", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989f78648329645e1afa5ba3bbbd5afa98", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980cd612842603f0811251d9de530ce5f1", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98296b38e8353792cbec5a9b6483408998", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a46eccf14f582d02d35bddff18c8ce68", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9855d977b8b55df75c776d6df3b3cd8271", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a047962d138513a096620b23190f214f", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988cf8eebfd339bcd5de8b64a046b7b957", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98853c599f0f0bf4bd0da17a2caa39e3cb", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eaac354abbf99f7cd1f9bd49538e6b87", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d71449183f61853d87d8052928b06973", "name": "frontend", "path": "frontend", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d6122115d2de733f5a2200417714d0d", "name": "gas_system_project", "path": "gas_system_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dc5dbe3e156e2f1ae55dbc6076dba92d", "name": "Personal", "path": "Personal", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848f16557363eacfe74cf8cf30ad00b4c", "name": "Projects", "path": "Projects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981d729468db2dc90cfcfdf3ab2a72cd39", "name": "Development", "path": "Development", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98558023e8971767ae38ee751ff34e214e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e8d4ca67ba535b64c4c14490953ff809", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983ae1a40558ada3ab2a76b35e3901371f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98df6190d358b9a3f7464d85548c745b82", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811600b42b8331926b968689afdc9cc4c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b5e06a91de379a14b1f611692a91177", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983350fa9f0fd14bd17e1e62cbaa214188", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98395849733f02df60e024816bbc994c97", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9822569ae40cdbd4faf4d85b29e4e0a2d8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9860ad1aabc4464b9c46329d1c566c4d07", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980711ecbc7b52d1f9762ee6ce7f2293e4", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ef76805a56980aee861fc53d5c79c14b", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9877233456e21db1dd72e446646cf4aa81", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980d63d68ec0f3d56700a3f45aa14b1287", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98831f868a7ec09f0d9967edbb9e7ccf33", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9813b1f87c38f28b3fc5fc5568bd4a6300", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9819885ed6ae49bfe95412df9bfc055d71", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9891ccbe65832048072a5b2fdc945746a5", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983a06d4d7b387168668a67a0aa941ec58", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986f15448211642377f860866c1a36dcf8", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af079bb1cc9d953bdfed0dbe3ad49db2", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98179cdb4ea4bf50f49e36c8d6571e5d78", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d866149d93211f8c8f5ecb1b3cb86dbf", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857e9d71e7a612ca04e13e48955fdf187", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98085b10d2f4adfb6e7430a37e9823af20", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9872b90b7ed5874d21f201f7dfc9013fac", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98491adbb62a92bc7f58fb1b29a0c4e78d", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c133ab0ce49000af29bb4fb4ce217e58", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98997effbbc42ba317c9a210b65eebeaf0", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a982ae1b5301f0c0c87ad694c5381aff", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8d6243936ca12f1c984b582363e2bf5", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac666ebaefb7fa167c6d22781c379b53", "name": "frontend", "path": "frontend", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db3dd84f5b320a1120bcca8bb8ed0f96", "name": "gas_system_project", "path": "gas_system_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c792c370f831fd678594b0918ced45d4", "name": "Personal", "path": "Personal", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ccb42c41076cc6025ae12f2c44db44a7", "name": "Projects", "path": "Projects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981346a4a06b22fe017c57c969cbbe1227", "name": "Development", "path": "Development", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e33de494430ea6a9226de0d5053187c0", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9884ca8e3139416c2abaab2abe939bbc98", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98aed65a3bcaac3c446bf6eda136529ced", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982e4593424bc44104bfba0cd8e1164de9", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9832eee71419a49f36c05f000c777550c8", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9873190972283ed449e59b5d929f4ab7c8", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a4ec65a00457db3e01f11d4a34fad39", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98acea86a0c41cc5d55a462905774916e0", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cb56a644f4ed2913d1cc59f94423e6c4", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9807e00ce7b53d446e6bc1f003ca6c46a5", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c56888210ed220f6b4370da0131e4bc", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9804420862e3506e8e2ee98243e823324b", "name": "frontend", "path": "frontend", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6196e6aad68905473d4344d2ea3d12f", "name": "gas_system_project", "path": "gas_system_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fdf3b1d53cace6639d5f60e8eb000ece", "name": "Personal", "path": "Personal", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9852d8c59e643cf90b186bbf31fc4a1cd5", "name": "Projects", "path": "Projects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9802813e51b158f6ab01810ef6930e86ee", "name": "Development", "path": "Development", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a87916e7962081bf5e6f7d78001959cc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984882bed152bfcc9d8ed85c464201820b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a783cfc08c2a0887cb88cfc9c2c57b52", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d6f92cf0c683f52f6306c99f8b59ce6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d0371c73487924bf2882044f78d2b3eb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985898dcc34ae735da407be8ced7d7b8b5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a6fcac1800ee9d77e98b7d857285b4f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982a98085668aad726be22ca48806eeb3a", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f55b856e9f02a94d98b226dc253af117", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9849e3a223a1dfef6f6bbc1364f5127f9a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986770053326df3bc1d50f349216c37129", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a1bb12643f1497c0cc931bb5948152ee", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984265f545672a71fd9331dc1cf78eae9b", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9883ad15de486e195eece5f6af6a120c82", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c18ec55266b1c969b9e73c685635d755", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aa6b354b5c0881a50ef1eeeefd95a2ed", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980c42a83042072845ab174bcb82c6f26e", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98feb02331c14e13d3cea89b8ffdf81e78", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ac9420dfa2ecf6d1d8e581296bc0fd6f", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9812c89a0c6fc89313522f212905996b3e", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9801ecd218c43b9b314836196a2d5b0554", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98d8d6340be6e373cf75c2cb142d0c4741", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985c91e264a1e7b18b926585bf0d477c53", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837320913b97e0ae912a963871cf3ce03", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c4a7057781a71b014fdd90501ccf353", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9889d1249c01c551de40c9581e57bb0a8f", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983f94322bb468ed63afb9cdaf0c5488d4", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989d5d2627510b04289d7f4bb8fb6379c0", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983ce3966be08221d40bb1a9fc7904d1b2", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98150b6b34347a8cdb126cd28a479ce0be", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f63d681e383ee6540a318ef4fa4b53e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98182a02cc368709588030649804db772d", "name": "frontend", "path": "frontend", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9820e049985e83bbcf06189c17d6f6d87a", "name": "gas_system_project", "path": "gas_system_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9877e81a37dab583b2a81bcdb2c920a3a2", "name": "Personal", "path": "Personal", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e59e5a40d3b676a7f3baa8250cc6d4d", "name": "Projects", "path": "Projects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9816f5207f592b99aa2e014ff783c0004e", "name": "Development", "path": "Development", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98408d32646c2cbe59da64b0bf734e5f53", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e06f251482b2ab5c62c3c455022df13f", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987c1b6d94340c144406f54fab14fa6142", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980f89200e4b3ad302cb671f85bd9a616b", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9846431b3f3a509aff285f85ad663f4bed", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec2418f1e5c8f2ddb783ee7e1609d430", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a849e72068940e639e74681ef2244522", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983302e9ba3777723fdc8e156b0d00503a", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986dcd170d3320398af57ffa9f22e16751", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9825de1bf882e78e45714e59b2b712b85e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDB.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f16583d2dc502392cb8f62bc670ae02f", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f737253fad8b91095309a8daca0c8b8", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9867d8638ce9e7719fd82a894b6d683a44", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986dcb29f3227dbb5ad75df8e8304c36c6", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986bca37c77fc52b912109d604ef6f8652", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a045bacdb1e207c98add2982b09f4658", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98936d4e2f53914f0a4dbd3ac0aef1e783", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9818ae8cabe7a4bd3ff8cfce043ed4b8b8", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9845de37e2831fd20a2448e01e646f40f6", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b8ab0e0e0e662caca306bc674af9ec36", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dd3bff66b053ffd85cdcf69fe7f0a9e4", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqfliteImportPublic.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983e0aa369b40d75b7352edc12f7e220bf", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqflitePluginPublic.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ad9b7b032b5740f45e0757c680b7beff", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98752935d8b619303aa5750f47b0a8d7b6", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98680b168908f09b47e0756dc19eb477f7", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986f29522235ef4b6af8096bba0a2ef87f", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989c57c7e5aae1e37159b8cb1d16fa2e38", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984e43f7d0a10b15a6b886fa37b9d81a21", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989b2d0e6a9c496a0dd93806fd81df9e63", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b556e753dc6426387d02ef5bba009df", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98586adb58aa7b9162739ab80260b53a8d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a9d3a7fc890d52bca9bd7937f42c9d03", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c5aa029901b6ac0a93388bbde3ea8933", "name": "frontend", "path": "frontend", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9800a7fa8f8bd34b8023c4d8805f6b53cc", "name": "gas_system_project", "path": "gas_system_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9872b0ab457bc8a8756d8b3e88fccb27d6", "name": "Personal", "path": "Personal", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fea43c1290c89013951cba5e74db5a44", "name": "Projects", "path": "Projects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980466db9270520439b65f586e094e14c3", "name": "Development", "path": "Development", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9889511a860d549d0d1048156df2606bd3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836f33488b3109abb646cbe3ca25e3c42", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9865419b6f8b5292df8b4251c4b5d385a1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cf6ad4495a305c8a7e07f02e39f2ad03", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b11fa02de7932f1aabff3712313e5bd8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f650419f75997858f9003134ccb360f1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98abf4c37a77454bf30054b4fb9d351ded", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e10697590609e2de1d0685114123d924", "name": "..", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98b32a08011fbfc7326052ea42af371fbe", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "bfdfe7dc352907fc980b868725387e9806b1fc52a269fb155d9be163cf60f8a5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98281afdadb56d02dc34e12e191f911fcc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981ce9f5a28a8dbf1f4a50f76a722217fa", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9842221dcae6772c29f6b2f13d9189b430", "path": "ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e989e0bd14182c474d41bc42eb2b1cac194", "path": "sqflite_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985f98f3ea9dde97158072d7cbd31bf126", "path": "sqflite_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b98597081b37f58da88926dac745c05a", "path": "sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985a431174f2892d7f288f6df1e761ce10", "path": "sqflite_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988d3de75e89406af74907af1b963fcb1a", "path": "sqflite_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f75fdabc210f65f3ad2abe2fccdc74d2", "path": "sqflite_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b08dc0b4dcade8b7953dea96fc54bd38", "path": "sqflite_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98853b3cfdca9609b93a07b25e67b8456c", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98045e81b0e1fb4afe63f202ca2c570e5a", "name": "sqflite_darwin", "path": "../.symlinks/plugins/sqflite_darwin/darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb803e8ec5bd10a2457b3776c8cc4ec1", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98942867ef9a9b82a20a4a7fc87d922201", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862d9c32904d63fdaba0f2aec66ff1562", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e981f309c828f40a21fcd2090dacd4a8f01", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d85490c13bb594476aa9be285597497d", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98c50a2eb9fb28cebb3540daef5d4a8334", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987b0bdfb96c434b1bdaf98ff08db5d964", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989c24b47fd1a04f7c3870243d256eb710", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e986b56855213c29113cc17d2b495b4605b", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98850ee204ad70211ee248c6855349a5f9", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f67184b23266d4586865ab49f1bd9d8e", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986175ec003efd0925b0e80b27c1a333bb", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981c04a92782605bedf8b5bd015c8dc01a", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ab5e5b11fcf9a2f9f4c2bf61b3a5e465", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dc2407b6e3245e78631c8d5833a16aaa", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983aadeb6c0efc55aa61d4a193c33d1a65", "path": "Pods-RunnerTests.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98a634232c699d5ed3646d3f024c937ffa", "path": "Pods-RunnerTests-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989938b906e3cb2707a2afa9a39150a604", "path": "Pods-RunnerTests-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988d01c1d667722a31ce8e51428338963f", "path": "Pods-RunnerTests-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a85576fdb8cb73c7cd4dd5902a45a27b", "path": "Pods-RunnerTests-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f448039d0a832e98acdd3ffd87da1731", "path": "Pods-RunnerTests-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98074a9b441078beef16a37aae33ee2900", "path": "Pods-RunnerTests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987bd2a5c12d5a72dad789e04e26dc5a25", "path": "Pods-RunnerTests.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fc46d149385d28a69f8f8bc860e81763", "path": "Pods-RunnerTests.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d5836cf4d97a0c9c99eca09bf2351047", "name": "Pods-RunnerTests", "path": "Target Support Files/Pods-RunnerTests", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f3d357a58233f32f97cf5aa060ebc8be", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/Development/Projects/Personal/gas_system_project/frontend/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/Development/Projects/Personal/gas_system_project/frontend/ios/Pods", "targets": ["TARGET@v11_hash=19f61d3608f451d8d421e519efb39d1d", "TARGET@v11_hash=ba325d260ec077bedce1392ae299d85e", "TARGET@v11_hash=f4c51137d470a8874f996ce184f5f492", "TARGET@v11_hash=a2be3ee88477683c94769f49de2bc580", "TARGET@v11_hash=a348d1dfb324df6e0d23d349b8ff2c23", "TARGET@v11_hash=8d96f10a6f530706c0db6a68df150325", "TARGET@v11_hash=111e0bef5f1ad8b7d85f503043defc69", "TARGET@v11_hash=2a55707e0bc232b64e685fffccf6e489", "TARGET@v11_hash=d051245bd1abb526e52790650b19a2c0", "TARGET@v11_hash=b8ec0c2645be516ba7d032fd78bc75d3", "TARGET@v11_hash=d605ab49eb9616f43ac48f6f811a9954", "TARGET@v11_hash=003cd193fa98d6331ab3ca6a1515e4a4", "TARGET@v11_hash=970c7e80e156e27d3a8dd38b193b29a6", "TARGET@v11_hash=4a2dc4d0056ba45af79df8906f79b2cb", "TARGET@v11_hash=0036673705a143cea7a660f0f79129ae"]}