// ignore_for_file: unreachable_switch_default


import 'package:fpdart/fpdart.dart';
import 'package:frontend/core/constants/app_error_messages.dart';
import 'package:frontend/core/enums/cache_failure_type.dart';
import 'package:frontend/core/enums/database_failure_type.dart';
import 'package:frontend/core/enums/http_failure_type.dart';
import 'package:frontend/core/enums/parse_failure_type.dart';

// Typedefs for convenience
typedef FutureEitherFailOr<T> = Future<Either<AppFailure, T>>;
typedef StreamEitherFailOr<T> = Stream<Either<AppFailure, T>>;
typedef EitherFailOr<T> = Either<AppFailure, T>;

// Abstract Base Class for all failures
abstract class AppFailure implements Exception {
  final String message;
  final StackTrace? stackTrace;

  AppFailure({required this.message, this.stackTrace});

  /// Abstract method to be overridden by each failure type
  String getErrorMessage();

  @override
  String toString() {
    return 'AppFailure(message: $message, stackTrace: $stackTrace)';
  }
}

/// HTTP Errors
class HttpFailure extends AppFailure {
  final String? apiMessage;
  final int? statusCode;
  final HttpFailureType failureType;

  HttpFailure({
    required super.message,
    super.stackTrace,
    required this.failureType,
    this.apiMessage,
    this.statusCode,
  });

  @override
  String getErrorMessage() {
    switch (failureType) {
      case HttpFailureType.badRequest:
        return apiMessage ?? AppErrorMessages.httpBadRequest;
      case HttpFailureType.unauthorized:
        return apiMessage ?? AppErrorMessages.httpUnauthorized;
      case HttpFailureType.forbidden:
        return apiMessage ?? AppErrorMessages.httpForbidden;
      case HttpFailureType.notFound:
        return apiMessage ?? AppErrorMessages.httpNotFound;
      case HttpFailureType.timeout:
        // return apiMessage ?? AppErrorMessages.httpTimeout;
        return apiMessage ??
            '${AppErrorMessages.httpTimeout} (${AppErrorCodes.connectionTimeout})';
      case HttpFailureType.network:
        return apiMessage ?? AppErrorMessages.httpNetworkError;
      case HttpFailureType.cancelled:
        return apiMessage ?? AppErrorMessages.httpCancelled;
      case HttpFailureType.clientError:
        return apiMessage ?? AppErrorMessages.httpClientError(apiMessage);
      case HttpFailureType.serverError:
        // return apiMessage ?? AppErrorMessages.httpServerError;
        return apiMessage ??
            '${AppErrorMessages.httpServerError} (${AppErrorCodes.internalServerError})';

      case HttpFailureType.connectionRefused:
        // return apiMessage ?? AppErrorMessages.httpConnectionRefused;
        return apiMessage ??
            '${AppErrorMessages.httpConnectionRefused} (${AppErrorCodes.connectionRefused})';
      case HttpFailureType.unknown:
        // return apiMessage ?? '${AppErrorMessages.httpUnknownError}$message';
        return apiMessage ??
            '${AppErrorMessages.httpUnknownError(message)} (${AppErrorCodes.unknown})';
      default:
        // return apiMessage ?? '${AppErrorMessages.httpUnExpectedError}$message';
        return apiMessage ??
            '${AppErrorMessages.httpUnExpectedError(message)} (${AppErrorCodes.unexpected})';
    }
  }

  @override
  String toString() {
    return 'HttpFailure(message: $message, stackTrace: $stackTrace, failureType: $failureType, apiMessage: $apiMessage, statusCode: $statusCode)';
  }
}

/// Database Errors
class DatabaseFailure extends AppFailure {
  final DatabaseFailureType failureType;

  DatabaseFailure({
    required super.message,
    super.stackTrace,
    required this.failureType,
  });

  @override
  String getErrorMessage() {
    switch (failureType) {
      case DatabaseFailureType.connectionError:
        return '${AppErrorMessages.dbConnectionError}$message';
      case DatabaseFailureType.readError:
        return '${AppErrorMessages.dbReadError}$message';
      case DatabaseFailureType.writeError:
        return '${AppErrorMessages.dbWriteError}$message';
      case DatabaseFailureType.queryError:
        return '${AppErrorMessages.dbQueryError}$message';
      case DatabaseFailureType.noDataFound:
        return '${AppErrorMessages.dbNoDataFound}$message';
      case DatabaseFailureType.transactionError:
        return '${AppErrorMessages.dbTransactionError}$message';
      case DatabaseFailureType.unknown:
        return '${AppErrorMessages.dbUnknownError}$message';
      default:
        return '${AppErrorMessages.dbUnExpectedError}$message';
    }
  }

  @override
  String toString() {
    return 'DatabaseFailure(message: $message, stackTrace: $stackTrace, failureType: $failureType)';
  }
}

/// Cache Errors
class CacheFailure extends AppFailure {
  final CacheFailureType failureType;

  CacheFailure({
    required super.message,
    super.stackTrace,
    required this.failureType,
  });

  @override
  String getErrorMessage() {
    switch (failureType) {
      case CacheFailureType.notFound:
        return '${AppErrorMessages.cacheNotFound}$message';
      case CacheFailureType.writeError:
        return '${AppErrorMessages.cacheWriteError}$message';
      case CacheFailureType.readError:
        return '${AppErrorMessages.cacheReadError}$message';
      case CacheFailureType.deleteError:
        return '${AppErrorMessages.cacheDeleteError}$message';
      case CacheFailureType.unknown:
        return '${AppErrorMessages.cacheUnknownError}$message';
      default:
        return '${AppErrorMessages.cacheUnExpectedError}$message';
    }
  }

  @override
  String toString() {
    return 'CacheFailure(message: $message, stackTrace: $stackTrace, failureType: $failureType)';
  }
}

/// Validation Errors
class ValidationFailure extends AppFailure {
  final String fieldName;

  ValidationFailure({
    required super.message,
    super.stackTrace,
    required this.fieldName,
  });

  @override
  String toString() {
    return 'ValidationFailure(message: $message, stackTrace: $stackTrace, fieldName: $fieldName)';
  }

  @override
  String getErrorMessage() {
    return AppErrorMessages.validationError(message, fieldName);
  }
}

/// Parsing Errors
class ParsingFailure<T> extends AppFailure {
  final ParsingFailureType failureType;
  final T expectedType;

  ParsingFailure({
    required super.message,
    super.stackTrace,
    required this.failureType,
    required this.expectedType,
  });

  @override
  String getErrorMessage() {
    switch (failureType) {
      case ParsingFailureType.jsonParsingError:
        return AppErrorMessages.jsonParsingError(message, expectedType);
      case ParsingFailureType.xmlParsingError:
        return AppErrorMessages.xmlParsingError(message, expectedType);
      case ParsingFailureType.typeConversionError:
        return AppErrorMessages.typeConversionError(message, expectedType);
      case ParsingFailureType.invalidFormat:
        return AppErrorMessages.formatParsingError(message, expectedType);
      case ParsingFailureType.typeMismatch:
        return AppErrorMessages.schemaMismatchError(message, expectedType);
      case ParsingFailureType.unknown:
        return AppErrorMessages.unknownParsingError(message, expectedType);
      default:
        return AppErrorMessages.unexpectedParsingError(message, expectedType);
    }
  }

  @override
  String toString() {
    return 'ParsingFailure(message: $message, stackTrace: $stackTrace, failureType: $failureType, expectedType: $expectedType)';
  }
}

/// Generic Failure
class UnexpectedFailure extends AppFailure {
  UnexpectedFailure({required super.message, super.stackTrace});

  @override
  String getErrorMessage() {
    return AppErrorMessages.unexpectedError(message);
  }

  @override
  String toString() {
    return 'UnexpectedFailure(message: $message, stackTrace: $stackTrace)';
  }
}
