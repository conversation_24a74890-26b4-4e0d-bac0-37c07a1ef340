import 'dart:async';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:fpdart/fpdart.dart';

import '../../features/shared/data/model/api_response_model.dart';
import '../constants/app_error_messages.dart';
import '../enums/http_failure_type.dart';
import '../network/connection_checker.dart';
import 'app_failure.dart';

class HttpErrorHandler {
  final ConnectionChecker connectionChecker;

  HttpErrorHandler({required this.connectionChecker});

  /// Handles Dio exceptions and maps them to `AppFailure`.
  AppFailure _handleDioError(DioException e) {
    // log e.type
    // print('DioExceptionType: ${e.type.name} and exception : $e');
    HttpFailureType failureType;
    String errorMessage;

    // Extract API Message safely
    String? apiMessage;
    if (e.response?.data is Map<String, dynamic>) {
      final responseData = e.response!.data as Map<String, dynamic>;
      if (responseData.containsKey('message')) {
        final messageData = responseData['message'];
        if (messageData is String) {
          apiMessage = messageData;
        } else if (messageData is Map<String, dynamic> &&
            messageData.containsKey('msg')) {
          apiMessage = messageData['msg'];
        }
      }
    }

    // Map DioException to `HttpFailureType`
    switch (e.type) {
      // log e.type
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        failureType = HttpFailureType.timeout;
        errorMessage = AppErrorMessages.httpTimeout;
        break;
      case DioExceptionType.connectionError:
        failureType = HttpFailureType.connectionRefused;
        errorMessage = AppErrorMessages.httpConnectionRefused;
        break;
      case DioExceptionType.badResponse:
        failureType =
            e.response?.statusCode != null && e.response!.statusCode! >= 500
            ? HttpFailureType.serverError
            : HttpFailureType.clientError;
        errorMessage = AppErrorMessages.httpClientError(apiMessage);
        break;
      case DioExceptionType.cancel:
        failureType = HttpFailureType.cancelled;
        errorMessage = AppErrorMessages.httpCancelled;
        break;
      case DioExceptionType.unknown:
        if (e.error is SocketException || e.error is HttpException) {
          failureType = HttpFailureType.network;
          errorMessage = AppErrorMessages.httpNetworkError;
        } else {
          failureType = HttpFailureType.unknown;
          errorMessage = AppErrorMessages.httpUnknownError();
        }
        break;
      default:
        failureType = HttpFailureType.unknown;
        errorMessage = AppErrorMessages.httpUnknownError();
        break;
    }

    return HttpFailure(
      message: errorMessage,
      failureType: failureType,
      stackTrace: e.stackTrace,
      apiMessage: apiMessage,
      statusCode: e.response?.statusCode,
    );
  }

  /// Handles client-side errors (e.g., 4xx status codes) and returns an `AppFailure`.
  AppFailure _handleClientError<T>({
    required ApiResponseModel<T>? apiResponse,
    required int statusCode,
    required Map<String, dynamic> response,
  }) {
    String errorMessage = apiResponse?.apiMessage ?? '';
    HttpFailureType failureType;

    switch (statusCode) {
      case 400:
        failureType = HttpFailureType.badRequest;
        errorMessage = apiResponse?.apiMessage ?? '';
        break;
      case 401:
        failureType = HttpFailureType.unauthorized;
        errorMessage = apiResponse?.apiMessage ?? '';
        break;
      case 403:
        failureType = HttpFailureType.forbidden;
        // errorMessage = AppErrorMessages.httpForbidden;
        errorMessage = apiResponse?.apiMessage ?? '';
        break;
      case 404:
        failureType = HttpFailureType.notFound;
        // errorMessage = AppErrorMessages.httpNoDataFound;
        errorMessage = apiResponse?.apiMessage ?? '';
        break;

      // case 417:
      // failureType = HttpFailureType.expectationFailed;
      // errorMessage = AppErrorMessages.httpExpectationFailed;
      default:
        failureType = HttpFailureType.clientError;
        errorMessage = apiResponse?.apiMessage ?? '';
        break;
    }

    return HttpFailure(
      message: AppErrorMessages.httpClientError(errorMessage),
      apiMessage: errorMessage,
      failureType: failureType,
      statusCode: statusCode,
    );
  }

  /// Main method to handle network requests and errors.
  FutureEitherFailOr<ApiResponseModel<T>> handleRequest<T>({
    required Future<Response?> Function() requestFunction,
    T Function(Object?)? fromJsonT,
    bool skipConnectionCheck = false,
  }) async {
    try {
      // ✅ Only check the connection if `skipConnectionCheck` is false
      if (!skipConnectionCheck) {
        final isConnected = await connectionChecker.isConnected;
        if (!isConnected) {
          return left(
            HttpFailure(
              message: 'Check your internet connection and try again.',
              failureType: HttpFailureType.network,
            ),
          );
        }
      }

      // ✅ Make the network request
      final response = await requestFunction();

      // Validate the response format
      if (response == null || response.data is! Map<String, dynamic>) {
        return left(
          HttpFailure(
            message: 'Invalid server response',
            failureType: HttpFailureType.serverError,
            statusCode: response?.statusCode,
          ),
        );
      }

      final responseBody = response.data as Map<String, dynamic>;

      // Hanlde success
      if (response.statusCode != null &&
          response.statusCode! >= 200 &&
          response.statusCode! < 400) {
        // Parse the API response
        final apiResponse = ApiResponseModel<T>.fromJson(
          json: responseBody,
          fromJsonT: fromJsonT,
          skipFrappeException: true,
        );
        return right(apiResponse);
      }

      final isClientError =
          response.statusCode != null &&
          response.statusCode! >= 400 &&
          response.statusCode! < 500;

      // Handle client-side errors (4xx)
      if (isClientError) {
        final apiResponse = ApiResponseModel<T>.fromJson(
          json: responseBody,
          fromJsonT: fromJsonT,
        );
        // return _handleClientError(apiResponse, response.statusCode!);
        return left(
          _handleClientError(
            apiResponse: apiResponse,
            statusCode: response.statusCode!,
            response: response.data,
          ),
        );
      }

      final apiResponse = ApiResponseModel<T>.fromJson(
        json: responseBody,
        fromJsonT: fromJsonT,
        skipFrappeException: true,
      );

      return left(
        HttpFailure(
          message: 'Server error occurred',
          failureType: HttpFailureType.serverError,
          statusCode: response.statusCode,
          apiMessage: apiResponse.apiMessage,
        ),
      );

      // Handle server-side errors (5xx)
      // return left(
      //   HttpFailure(
      //     message: 'Server error occurred',
      //     failureType: HttpFailureType.serverError,
      //     statusCode: response.statusCode,
      //   ),
      // );
    } on DioException catch (error) {
      return left(_handleDioError(error));
    } catch (error, stackTrace) {
      return left(
        HttpFailure(
          message: 'Unexpected error occurred',
          failureType: HttpFailureType.unknown,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  FutureEitherFailOr<Uint8List> handleRequestBytes({
    required Future<Response?> Function() requestFunction,
  }) async {
    try {
      final response = await requestFunction();
      if (response == null || response.data is! List<int>) {
        return left(
          HttpFailure(
            message: AppErrorMessages.httpInvalidResponse,
            failureType: HttpFailureType.serverError,
            statusCode: response?.statusCode,
            apiMessage: 'Invalid server response',
          ),
        );
      }
      final Uint8List bytes = Uint8List.fromList(response.data);
      return right(bytes);
    } on DioException catch (error) {
      return left(_handleDioError(error));
    } catch (error, stackTrace) {
      return left(
        HttpFailure(
          message: 'Unexpected error occurred',
          failureType: HttpFailureType.unknown,
          stackTrace: stackTrace,
        ),
      );
    }
  }
}
