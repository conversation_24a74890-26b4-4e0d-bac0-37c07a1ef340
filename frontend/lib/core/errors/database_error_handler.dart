// // ignore_for_file: unreachable_switch_default

// import 'package:frontend/core/config/logger/app_logger.dart';
// import 'package:frontend/core/enums/database_failure_type.dart';
// import 'package:frontend/core/enums/database_operation_type.dart';
// import 'package:frontend/core/errors/app_failure.dart';
// import 'package:frontend/core/database/database_manager.dart';
// import 'package:fpdart/fpdart.dart';

// class DatabaseErrorHandler {
//   final DatabaseManager databaseManager;

//   DatabaseErrorHandler({required this.databaseManager});

//   /// **Ensures database connection before any operation**
//   Future<bool> _ensureDatabaseConnection() async {
//     if (!databaseManager.isInitialized) {
//       AppLogger().warning(
//         '⚠️ Database not initialized. Attempting to initialize...',
//       );
//       try {
//         await databaseManager.init();
//         return true;
//       } catch (e, stackTrace) {
//         AppLogger().error(
//           '❌ Database initialization failed.',
//           error: e,
//           stackTrace: stackTrace,
//         );
//         return false;
//       }
//     }
//     return true;
//   }

//   /// **Handles database failures and logs errors**
//   DatabaseFailure _handleDatabaseError({
//     required Exception e,
//     required DatabaseOperationType operation,
//   }) {
//     final failureType = _mapOperationToFailureType(operation);
//     AppLogger().error(
//       '❌ Database Error | Operation: ${operation.name} | Error: ${failureType.getErrorMessage()} and e : ${e.toString()}',
//       error: e,
//       stackTrace: StackTrace.current,
//     );
//     return DatabaseFailure(
//       message: '${failureType.getErrorMessage()} | ${e.toString()}',
//       failureType: failureType,
//       stackTrace: StackTrace.current,
//     );
//   }

//   /// **Maps `DatabaseOperationType` to `DatabaseFailureType`**
//   DatabaseFailureType _mapOperationToFailureType(
//     DatabaseOperationType operation,
//   ) {
//     switch (operation) {
//       case DatabaseOperationType.read:
//         return DatabaseFailureType.readError;
//       case DatabaseOperationType.write:
//         return DatabaseFailureType.writeError;
//       case DatabaseOperationType.query:
//         return DatabaseFailureType.queryError;
//       case DatabaseOperationType.delete:
//         return DatabaseFailureType.writeError;
//       case DatabaseOperationType.transaction:
//         return DatabaseFailureType.transactionError;
//       default:
//         return DatabaseFailureType.unknown;
//     }
//   }

//   /// **Unified method to handle all database operations safely**
//   FutureEitherFailOr<T> handleDatabaseOperation<T>({
//     required DatabaseOperationType operationType,
//     required Future<T?> Function() operationFunction,
//   }) async {
//     try {
//       // Ensure database is connected before performing any operation
//       final isConnected = await _ensureDatabaseConnection();
//       if (!isConnected) {
//         return left(
//           _handleDatabaseError(
//             e: Exception('Database connection could not be established.'),
//             operation: operationType,
//           ),
//         );
//       }

//       // Execute the operation
//       final result = await operationFunction();

//       // Handle potential null results for certain operations
//       if (result == null && operationType == DatabaseOperationType.read) {
//         return left(
//           _handleDatabaseError(
//             e: Exception('No data found.'),
//             operation: DatabaseOperationType.read,
//           ),
//         );
//       }

//       return right(result as T);
//     } catch (e) {
//       return left(
//         _handleDatabaseError(
//           e: e is Exception ? e : Exception(e.toString()),
//           operation: operationType,
//         ),
//       );
//     }
//   }
// }
