class AppErrorMessages {
  // HTTP Error Messages
  static const String httpBadRequest =
      'The request was invalid. Please check your input and try again.';
  static const String httpUnauthorized =
      'You are not authorized to access this resource. Please log in.';
  static const String httpForbidden = 'Access to this resource is forbidden.';
  static const String httpNotFound = 'The requested resource was not found.';
  static const String httpTimeout =
      'The request timed out. Please check your internet connection and try again.';
  static const String httpNetworkError =
      'Please check your internet connection and try again.';
  static const String httpCancelled = 'The request was cancelled.';
  static String httpClientError(String? apiMessage) =>
      apiMessage ?? 'An error occurred on the client side. Please try again.';
  static const String httpServerError =
      'A server error occurred. Please try again later.';
  static String httpUnknownError([String? message]) =>
      'Unknown HTTP Error: $message';
  static String httpUnExpectedError([String? message]) =>
      'Unexpected HTTP Error: $message';
  static const String httpInvalidResponse = 'Invalid server response.';
  static const String httpConnectionRefused =
      'Internal Server Error: '; // 'Connection refused by server.';

  // Database Error Messages
  static const String dbConnectionError = 'Database Connection Error: ';
  static const String dbReadError = 'Database Read Error: ';
  static const String dbWriteError = 'Database Write Error: ';
  static const String dbQueryError = 'Database Query Error: ';
  static const String dbNoDataFound = 'No Data Found: ';
  static const String dbTransactionError = 'Database Transaction Error: ';
  static const String dbUnknownError = 'Unknown Database Error: ';
  static const String dbUnExpectedError =
      'An unexpected database error occurred: ';
  static const String dbConstraintError = 'Database Constraint Violation: ';
  static const String dbMigrationError = 'Database Migration Error: ';
  static const String dbVersionMismatch = 'Database Version Mismatch: ';

  // Cache Error Messages
  static const String cacheNotFound = 'Cache Not Found: ';
  static const String cacheWriteError = 'Cache Write Error: ';
  static const String cacheReadError = 'Cache Read Error: ';
  static const String cacheDeleteError = 'Cache Delete Error: ';
  static const String cacheUnknownError = 'Unknown Cache Error: ';
  static const String cacheUnExpectedError = 'Unexpected Cache Error: ';
  static const String cacheExpired = 'Cache Expired: ';
  static const String cacheSizeExceeded = 'Cache Size Limit Exceeded: ';

  // Validation Error Messages
  static String validationError(String message, String fieldName) =>
      'Validation Error: $message. Field: $fieldName';
  static const String validationEmptyField = 'This field cannot be empty';
  static const String validationInvalidEmail = 'Please enter a valid email';
  static const String validationInvalidPhone =
      'Please enter a valid phone number';
  static const String validationPasswordMismatch = 'Passwords do not match';
  static const String validationInvalidDate = 'Please enter a valid date';
  static const String validationInvalidNumber = 'Please enter a valid number';

  // Parsing Error Messages
  static String jsonParsingError(String message, dynamic expectedType) =>
      'JSON Parsing Error: $message. Expected type: $expectedType';
  static String xmlParsingError(String message, dynamic expectedType) =>
      'XML Parsing Error: $message. Expected type: $expectedType';
  static String typeConversionError(String message, dynamic expectedType) =>
      'Type Conversion Error: $message. Expected type: $expectedType';
  static String unknownParsingError(String message, dynamic expectedType) =>
      'Unknown Parsing Error: $message. Expected type: $expectedType';
  static String unexpectedParsingError(String message, dynamic expectedType) =>
      'Unexpected Parsing Error: $message. Expected type: $expectedType';
  static String formatParsingError(String message, dynamic expectedType) =>
      'Format Parsing Error: $message. Expected type: $expectedType';
  static String schemaMismatchError(String message, dynamic expectedType) =>
      'Schema Mismatch Error: $message. Expected type: $expectedType';

  // Unexpected Error Messages
  static String unexpectedError(String message) =>
      'An unexpected error occurred: $message';
  static const String unknownError = 'An unknown error occurred';
  static const String notImplementedError = 'Feature not implemented yet';
  static const String platformNotSupported = 'Platform not supported';

  // Frappe Error Messages
  static String frappeValidationError({
    String? serverMessage,
    required String message,
  }) => 'Error: ${serverMessage ?? message}';
  static String frappeMissingArgument({
    String? serverMessage,
    required String message,
  }) => 'Missing Argument Error: ${serverMessage ?? message}';
  static String frappeModuleNotFound({
    String? serverMessage,
    required String message,
  }) => 'Module Not Found: ${serverMessage ?? message}';
  static String frappePermissionError({
    String? serverMessage,
    required String message,
  }) => 'Permission Error: ${serverMessage ?? message}';
  static String frappeTypeError({
    String? serverMessage,
    required String message,
  }) => 'Type Error: ${serverMessage ?? message}';
  static String frappeUnknownError({
    String? serverMessage,
    required String message,
  }) => 'Unknown Error: ${serverMessage ?? message}';
  static String frappeUnexpectedError({
    String? serverMessage,
    required String message,
  }) => 'Unexpected Error: ${serverMessage ?? message}';
  static String frappeDuplicateEntry({
    String? serverMessage,
    required String message,
  }) => 'Duplicate Entry: ${serverMessage ?? message}';
  static String frappeSessionExpired({
    String? serverMessage,
    required String message,
  }) => 'Session Expired: ${serverMessage ?? message}';
  static String frappeAuthenticationError({
    String? serverMessage,
    required String message,
  }) => 'Authentication Failed: ${serverMessage ?? message}';

  // Payment Error Messages
  static const String paymentInsufficientBalance =
      'Your account balance is insufficient.';
  static const String paymentCustomerRejected =
      'Payment was rejected by the customer.';
  static const String paymentInvalidPinCode =
      'The provided PIN code is invalid.';
  static const String paymentInvalidMethod = 'Invalid payment method provided.';
  static const String paymentTransactionError =
      'There was an error processing your transaction.';
  static const String paymentCancelled = 'The payment was cancelled.';
  static const String paymentTimeout = 'The payment request timed out.';
  static const String paymentNetworkError =
      'Network error. Please check your internet connection.';
  static String paymentUnknownError(String message) =>
      'An unknown payment error occurred: $message.';
  static String paymentUnexpectedError(String message) =>
      'Unexpected payment error: $message.';
  static const String paymentCardDeclined = 'Your card was declined';
  static const String paymentInvalidCardDetails = 'Invalid card details';
  static const String paymentProcessorError = 'Payment processor error';
  static const String paymentFraudSuspected =
      'Transaction flagged as suspicious';

  // File/IO Error Messages
  static const String fileNotFound = 'File not found';
  static const String filePermissionDenied =
      'Permission denied for file operation';
  static const String fileReadError = 'Error reading file';
  static const String fileWriteError = 'Error writing to file';
  static const String fileDeleteError = 'Error deleting file';
  static const String fileSizeExceeded = 'File size exceeds limit';
  static const String fileUnsupportedFormat = 'Unsupported file format';

  // Network Error Messages
  static const String networkNoInternet = 'No internet connection available';
  static const String networkDnsFailed = 'Failed to resolve hostname';
  static const String networkSslError = 'SSL certificate error';
  static const String networkBadResponse = 'Server returned invalid response';
  static const String networkRequestFailed = 'Network request failed';

  // Authentication Error Messages
  static const String authInvalidCredentials = 'Invalid username or password';
  static const String authAccountLocked = 'Account temporarily locked';
  static const String authSessionExpired = 'Your session has expired';
  static const String authTokenInvalid = 'Invalid authentication token';
  static const String authUserDisabled = 'User account is disabled';
  static const String authEmailNotVerified = 'Email address not verified';
}

class AppErrorCodes {
  // 🌐 Network Errors (NET-1xx)
  static const String connectionRefused = 'NET-101';
  static const String connectionTimeout = 'NET-102';
  static const String noInternet = 'NET-103';
  static const String dnsFailed = 'NET-104';
  static const String sslError = 'NET-105';
  static const String networkError = 'NET-199';

  // 🖥️ Server Errors (SRV-2xx)
  static const String internalServerError = 'SRV-201';
  static const String badRequest = 'SRV-202';
  static const String unauthorized = 'SRV-203';
  static const String forbidden = 'SRV-204';
  static const String notFound = 'SRV-205';
  static const String timeout = 'SRV-206';
  static const String serverUnavailable = 'SRV-207';
  static const String serverError = 'SRV-299';

  // 👤 Client/Input/Validation Errors (CLI-3xx)
  static const String invalidInput = 'CLI-301';
  static const String validationFailed = 'CLI-302';
  static const String cancelled = 'CLI-303';
  static const String invalidRequest = 'CLI-304';
  static const String clientError = 'CLI-399';

  // 🔐 Authentication & Authorization Errors (AUTH-4xx)
  static const String invalidToken = 'AUTH-401';
  static const String invalidCredentials = 'AUTH-402';
  static const String sessionExpired = 'AUTH-403';
  static const String accountLocked = 'AUTH-404';
  static const String userDisabled = 'AUTH-405';

  // 💳 Payment Errors (PAY-5xx)
  static const String paymentFailed = 'PAY-501';
  static const String cardDeclined = 'PAY-502';
  static const String insufficientBalance = 'PAY-503';

  // ⚠️ Unknown & Unexpected Errors (SYS-9xx)
  static const String unknown = 'SYS-901';
  static const String unexpected = 'SYS-902';
  static const String invalidResponse = 'SYS-903';
}
