import 'package:dio/dio.dart';

import '../../features/shared/data/source/local/flutter_secure_storage_services.dart';
import '../constants/api_end_points.dart';
import 'interceptors/auth_interceptor.dart';
import 'interceptors/logger_interceptor.dart';
// import 'package:dio_cache_interceptor/dio_cache_interceptor.dart';

Dio createDioClient({
  required FlutterSecureStorageServices flutterSecureStorageServices,
}) {
  final options = BaseOptions(
    baseUrl: ApiEndpoints.baseUrl,
    connectTimeout: const Duration(seconds: 30),
    receiveTimeout: const Duration(seconds: 30),
    sendTimeout: const Duration(seconds: 30),
    followRedirects: true,
    validateStatus: (status) => status != null && status <= 500,
    receiveDataWhenStatusError: true,
    headers: {
      // 'Accept-Encoding': 'br, gzip',
      'Accept-Encoding': 'gzip',
    },
  );

  final dio = Dio(options);

  dio.interceptors.addAll([
    AuthInterceptor(flutterSecureStorageServices: flutterSecureStorageServices),
    LoggerInterceptor(),
    // DioCacheInterceptor(
    //   options: CacheOptions(
    //     store: MemCacheStore(),
    //     policy: CachePolicy.request,
    //     maxStale: const Duration(days: 7),
    //     allowPostMethod: false,
    //   ),
    // ),
  ]);

  return dio;
}
