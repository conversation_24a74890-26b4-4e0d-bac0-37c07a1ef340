import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

/// Interface to check internet connectivity and listen to connection changes.
abstract class Connection<PERSON>hecker {
  /// Checks if the device is connected to the internet.
  Future<bool> get isConnected;

  /// Stream that emits `true` if the device is connected to the internet,
  /// and `false` otherwise.
  Stream<bool> get onConnectionChange;
}

class ConnectionCheckerImpl implements ConnectionChecker {
  final InternetConnection internetConnection;

  ConnectionCheckerImpl({required this.internetConnection});

  // create instance of internet connection
  // final InternetConnection internetConnection = InternetConnection();

  /// Checks if the device currently has internet access.
  @override
  Future<bool> get isConnected async {
    try {
      return await internetConnection.hasInternetAccess;
    } catch (e) {
      // Log or handle the error appropriately
      return false;
    }
  }

  /// Stream that emits connectivity status changes.
  /// Emits `true` for connected and `false` for disconnected.
  @override
  Stream<bool> get onConnectionChange => internetConnection.onStatusChange
          .asBroadcastStream()
          // .listen(
          //   (status) => status == InternetStatus.connected,
          // )
          .map(
            (status) => status == InternetStatus.connected,
          )
          .handleError((error) {
        // Log or handle stream errors
        return false; // Emit `false` on error
      });

  /*

  final listener = InternetConnection().onStatusChange.listen(
  (InternetStatus status) {
    switch (status) {
      case InternetStatus.connected:
        // The internet is now connected
        break;
      case InternetStatus.disconnected:
        // The internet is now disconnected
        break;
    }
  },
);

   */
}
