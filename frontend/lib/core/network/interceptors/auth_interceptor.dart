import 'package:dio/dio.dart';
import 'package:frontend/core/constants/local_storage_key_constants.dart';
import 'package:frontend/features/shared/data/source/local/flutter_secure_storage_services.dart';

class AuthInterceptor extends Interceptor {
  final FlutterSecureStorageServices flutterSecureStorageServices;

  const AuthInterceptor({required this.flutterSecureStorageServices});

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    final token = await flutterSecureStorageServices.readData(
      key: LocalStorageKeyConstants.tokenKey,
    );
    if (token != null && token.trim().isNotEmpty) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    return handler.next(options);
  }
}
