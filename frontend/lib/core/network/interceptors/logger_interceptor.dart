import 'package:dio/dio.dart';

import '../../config/logger/app_logger.dart';

class LoggerInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    AppLogger().info('🔗 Request: ${options.uri}');
    AppLogger().info('🔗 Request Headers: ${options.headers}');
    AppLogger().info('🔗 Request Query Parameters: ${options.queryParameters}');

    // Manually inspect FormData
    final data = options.data;
    if (data is FormData) {
      final fields = <String, dynamic>{};
      for (var field in data.fields) {
        fields[field.key] = field.value;
      }

      final files = data.files.map((file) {
        return {
          'field': file.key,
          'filename': file.value.filename,
          'contentType': file.value.contentType.toString(),
          'length': file.value.length,
        };
      }).toList();

      AppLogger().info('📝 FormData Fields: $fields');
      AppLogger().info('🖼️ FormData Files: $files');
    } else {
      AppLogger().info('🔗 Request Data: $data');
    }

    return handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    AppLogger().info('✅ Response Status Code: ${response.statusCode}');
    AppLogger().info('✅ Response Data: ${response.data}');
    // AppLogger().info("✅ Response Headers: ${response.headers}");
    // AppLogger().info(
    //   '✅ Response Request Path: ${response.requestOptions.path}',
    // );
    return handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    AppLogger().error(
      '❌ Error: ${err.message}',
      error: {
        'url': err.requestOptions.uri.toString(),
        'data': err.response?.data,
        'headers': err.requestOptions.headers,
        'realError': err,
      },
      stackTrace: err.stackTrace,
    );
    return handler.next(err);
  }
}
