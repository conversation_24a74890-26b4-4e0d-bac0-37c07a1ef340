import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../features/authentication/presentation/bloc/authentication_bloc.dart';
import '../../../features/dashboard/presentation/bloc/dashboard_bloc.dart';
import '../../../features/inventory/presentation/bloc/inventory_bloc.dart';
import '../../../features/order/presentation/bloc/order_bloc.dart';
import '../../../features/shared/presentation/bloc/bottom-nav-cubit/bottom_nav_cubit.dart';
import '../../../features/shared/presentation/bloc/theme-cubit/theme_cubit.dart';
import '../../../features/user-management/presentation/bloc/user_bloc.dart';

extension BuildContextExtensions on BuildContext {
  AuthenticationBloc get authenticationBloc {
    return BlocProvider.of<AuthenticationBloc>(this);
  }

  UserBloc get userBloc {
    return BlocProvider.of<UserBloc>(this);
  }

  DashboardBloc get dashboardBloc {
    return BlocProvider.of<DashboardBloc>(this);
  }

  OrderBloc get orderBloc {
    return BlocProvider.of<OrderBloc>(this);
  }

  InventoryBloc get inventoryBloc {
    return BlocProvider.of<InventoryBloc>(this);
  }

  // BottomNavCubit
  BottomNavCubit get bottomNavCubit {
    return BlocProvider.of<BottomNavCubit>(this);
  }

  // ThemeCubit
  ThemeCubit get themeCubit {
    return BlocProvider.of<ThemeCubit>(this);
  }

  // SharedBloc
  // get sharedBloc {
  //   return BlocProvider.of<SharedBloc>(this);
  // }
}
