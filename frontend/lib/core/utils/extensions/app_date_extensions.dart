// ignore_for_file: unreachable_switch_default

import 'package:intl/intl.dart';

import '../../enums/date_format_type_enum.dart';
import '../../enums/language_enum.dart';

extension DateFormatting on DateTime {
  // Method to format DateTime based on DateFormatType
  String toFormattedString({
    DateFormatType formatType = DateFormatType.yearMonthDay,
    Language language = Language.english, // Default to English
  }) {
    // Map the Language enum to locale strings
    String locale;
    switch (language) {
      case Language.arabic:
        locale = 'ar'; // Arabic locale
        break;
      case Language.somali:
      case Language.english:
      default:
        locale = 'en'; // English locale
        break;
    }
    String formattedDate;

    // Select the date format based on the enum value
    switch (formatType) {
      case DateFormatType.yearMonthDay:
        formattedDate = DateFormat('yyyy-MM-dd').format(this); // 2020-10-23
        break;
      case DateFormatType.dayMonthYear:
        formattedDate = DateFormat('d-M-yyyy').format(this); // 10-3-2023
        break;
      case DateFormatType.dayTextMonthYear:
        formattedDate = DateFormat('d-MMM-yyyy').format(this); // 23-Jan-2022
        break;
      case DateFormatType.dayShortMonthYear:
        formattedDate = DateFormat('dd-MMM-yyyy').format(this); // 23-Jan-2022
        break;
      case DateFormatType.fullTextDate:
        formattedDate = DateFormat(
          'EEEE, d MMMM yyyy',
        ).format(this); // Monday, 1 January 2024
        break;
      case DateFormatType.shortTextDate:
        formattedDate = DateFormat(
          'd MMMM yyyy',
        ).format(this); // 1 January 2024
        break;
      case DateFormatType.shortDate: // New format: 15 Nov
        formattedDate = DateFormat('d MMM').format(this); // 15 Nov
        break;
      case DateFormatType.dayNameOnly: // New format: Friday
        formattedDate = DateFormat('EEEE').format(this); // Friday
        break;
      default:
        formattedDate = DateFormat(
          'yyyy-MM-dd',
          locale,
        ).format(this); // Default to yearMonthDay
    }

    return formattedDate;
  }
}

// to convert string to dateTime
extension StringToDateTime on String {
  DateTime? toDateTime() {
    return DateTime.tryParse(this);
  }
}
