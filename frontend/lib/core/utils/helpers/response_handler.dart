// import 'package:fpdart/fpdart.dart';
// import 'package:hodan_hospital/core/enums/parse_failure_type.dart';
// import 'package:hodan_hospital/core/errors/app_failure.dart';
// import 'package:hodan_hospital/features/shared/data/models/api_response_model.dart';

// class ResponseHandler<T> {
//   final Either<AppFailure, ApiResponseModel<T>> response;

//   ResponseHandler(this.response);

//   FutureEitherFailOr<T> handleResponse({
//     required EitherFailOr<T> Function(AppFailure failure) onFailure,
//     required EitherFailOr<T> Function(ApiResponseModel<T> data) onSuccess,
//   }) async {
//     return response.fold(
//       (failure) => onFailure(failure),
//       (apiResponse) => onSuccess(apiResponse),
//     );
//   }

//   FutureEitherFailOr<T> handleResponseAndExtractData({
//     required EitherFailOr<T> Function(AppFailure failure) onFailure,
//     required EitherFailOr<T> Function(T data) onSuccess,
//   }) async {
//     return response.fold(
//       (failure) => onFailure(failure),
//       (apiResponse) {
//         try {
//           final data = apiResponse.getNonNullableData(); // Extract data
//           return onSuccess(data);
//         } on ParsingFailure catch (e) {
//           return left(e);
//         } catch (error) {
//           return left(
//             ParsingFailure(
//               message:
//                   "Failed to extract response data for type ${T.toString()}",
//               failureType: ParsingFailureType.jsonParsingError,
//               stackTrace: StackTrace.current,
//               expectedType: T,
//             ),
//           );
//         }
//       },
//     );
//   }
// }
