import 'package:dio/dio.dart';
import 'package:image_picker/image_picker.dart';

import '../../enums/cylinder_material.dart';
import '../../enums/cylinder_status.dart';
import '../../enums/cylinder_type.dart';
import '../../enums/spare_part_category.dart';
import '../../../features/inventory/domain/entities/package_item_entity.dart';

/// Utility class for creating FormData for multipart requests
/// Handles image file uploads and form data creation for inventory items
class MultipartHelper {
  /// Helper method to get the correct MIME type for image files
  static String _getImageMimeType(String filePath) {
    final extension = filePath.toLowerCase().split('.').last;
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      default:
        return 'image/jpeg'; // Default fallback
    }
  }

  /// Helper method to build MultipartFile with proper MIME type
  static Future<MultipartFile> _buildImageFile(XFile file) async {
    return MultipartFile.fromFile(
      file.path,
      filename: file.name,
      contentType: DioMediaType.parse(_getImageMimeType(file.path)),
    );
  }

  //?-----------------   CYLINDERS      -----------------//

  /// Create form data for cylinder creation with optional image upload
  static Future<FormData> createCylinderFormData({
    required CylinderType type,
    required CylinderMaterial material,
    required double price,
    required double cost,
    String? imageUrl,
    required String description,
    required int quantity,
    required int minimumStockLevel,
    required CylinderStatus status,
    XFile? imageFile,
  }) async {
    final Map<String, dynamic> formFields = {
      'type': type.name,
      'material': material.label,
      'price': price,
      'cost': cost,
      'description': description,
      'quantity': quantity,
      'minimumStockLevel': minimumStockLevel,
      'status': status.label,
    };

    // Add imageUrl only if provided and no image file
    if (imageUrl != null && imageFile == null) {
      formFields['imageUrl'] = imageUrl;
    }

    // Add image file if provided
    if (imageFile != null) {
      formFields['image'] = await _buildImageFile(imageFile);
    }

    return FormData.fromMap(formFields);
  }

  /// Create form data for cylinder update with optional image upload
  static Future<FormData> updateCylinderFormData({
    required String cylinderId,
    CylinderType? type,
    CylinderMaterial? material,
    double? price,
    double? cost,
    String? imageUrl,
    String? description,
    int? quantity,
    int? minimumStockLevel,
    CylinderStatus? status,
    XFile? imageFile,
  }) async {
    final Map<String, dynamic> formFields = {};

    // Add only non-null values
    if (type != null) formFields['type'] = type.name;
    if (material != null) formFields['material'] = material.label;
    if (price != null) formFields['price'] = price;
    if (cost != null) formFields['cost'] = cost;
    if (description != null) formFields['description'] = description;
    if (quantity != null) formFields['quantity'] = quantity;
    if (minimumStockLevel != null) {
      formFields['minimumStockLevel'] = minimumStockLevel;
    }
    if (status != null) formFields['status'] = status.label;

    // Add imageUrl only if provided and no image file
    if (imageUrl != null && imageFile == null) {
      formFields['imageUrl'] = imageUrl;
    }

    // Add image file if provided
    if (imageFile != null) {
      formFields['image'] = await _buildImageFile(imageFile);
    }

    return FormData.fromMap(formFields);
  }

  //?-----------------   SPARE PARTS      -----------------//

  /// Create form data for spare part creation with optional image upload
  static Future<FormData> createSparePartFormData({
    required String description,
    required double price,
    required double cost,
    required SparePartCategory category,
    required List<CylinderType> compatibleCylinderTypes,
    required String barcode,
    required int minimumStockLevel,
    required int initialStock,
    required int initialReserved,
    required int initialSold,
    XFile? imageFile,
  }) async {
    final Map<String, dynamic> formFields = {
      'description': description,
      'price': price,
      'cost': cost,
      'category': category.label,
      'compatibleCylinderTypes': compatibleCylinderTypes
          .map((type) => type.name)
          .toList(),
      'barcode': barcode,
      'minimumStockLevel': minimumStockLevel,
      'initialStock': initialStock,
      'initialReserved': initialReserved,
      'initialSold': initialSold,
    };

    // Add image file if provided
    if (imageFile != null) {
      formFields['image'] = await _buildImageFile(imageFile);
    }

    return FormData.fromMap(formFields);
  }

  /// Create form data for spare part update with optional image upload
  static Future<FormData> updateSparePartFormData({
    required String sparePartId,
    String? description,
    double? price,
    double? cost,
    SparePartCategory? category,
    List<CylinderType>? compatibleCylinderTypes,
    String? barcode,
    int? minimumStockLevel,
    XFile? imageFile,
  }) async {
    final Map<String, dynamic> formFields = {};

    // Add only non-null values
    if (description != null) formFields['description'] = description;
    if (price != null) formFields['price'] = price;
    if (cost != null) formFields['cost'] = cost;
    if (category != null) formFields['category'] = category.label;
    if (compatibleCylinderTypes != null) {
      formFields['compatibleCylinderTypes'] = compatibleCylinderTypes
          .map((type) => type.name)
          .toList();
    }
    if (barcode != null) formFields['barcode'] = barcode;
    if (minimumStockLevel != null) {
      formFields['minimumStockLevel'] = minimumStockLevel;
    }

    // Add image file if provided
    if (imageFile != null) {
      formFields['image'] = await _buildImageFile(imageFile);
    }

    return FormData.fromMap(formFields);
  }

  //?-----------------   PACKAGES      -----------------//

  /// Create form data for package creation with optional image upload
  static Future<FormData> createPackageFormData({
    required String name,
    required String description,
    required String cylinderId,
    required List<PackageItemEntity> includedSpareParts,
    required double totalPrice,
    required double costPrice,
    required double discount,
    String? imageUrl,
    required int quantity,
    required int minimumStockLevel,
    XFile? imageFile,
  }) async {
    final Map<String, dynamic> formFields = {
      'name': name,
      'description': description,
      'cylinder': cylinderId,
      'includedSpareParts': includedSpareParts
          .map(
            (item) => {
              'part': item.sparePartId,
              'quantity': item.quantity,
              // 'name': item.name, // Optional
              // 'unitPrice': item.unitPrice, // Optional
              // 'totalPrice': item.totalPrice, // Optional
            },
          )
          .toList(),
      'totalPrice': totalPrice,
      'costPrice': costPrice,
      'discount': discount,
      'quantity': quantity,
      'minimumStockLevel': minimumStockLevel,
    };

    // Add imageUrl only if provided and no image file
    if (imageUrl != null && imageFile == null) {
      formFields['imageUrl'] = imageUrl;
    }

    // Add image file if provided
    if (imageFile != null) {
      formFields['image'] = await _buildImageFile(imageFile);
    }

    return FormData.fromMap(formFields);
  }

  /// Create form data for package update with optional image upload
  static Future<FormData> updatePackageFormData({
    required String packageId,
    String? name,
    String? description,
    String? cylinderId,
    List<PackageItemEntity>? includedSpareParts,
    double? totalPrice,
    double? costPrice,
    double? discount,
    String? imageUrl,
    int? quantity,
    int? minimumStockLevel,
    XFile? imageFile,
  }) async {
    final Map<String, dynamic> formFields = {};

    // Add only non-null values
    if (name != null) formFields['name'] = name;
    if (description != null) formFields['description'] = description;
    if (cylinderId != null) formFields['cylinder'] = cylinderId;
    if (includedSpareParts != null) {
      formFields['includedSpareParts'] = includedSpareParts
          .map(
            (item) => {
              'part': item.sparePartId,
              'name': item.name,
              'quantity': item.quantity,
              'unitPrice': item.unitPrice,
              'totalPrice': item.totalPrice,
            },
          )
          .toList();
    }
    if (totalPrice != null) formFields['totalPrice'] = totalPrice;
    if (costPrice != null) formFields['costPrice'] = costPrice;
    if (discount != null) formFields['discount'] = discount;
    if (quantity != null) formFields['quantity'] = quantity;
    if (minimumStockLevel != null) {
      formFields['minimumStockLevel'] = minimumStockLevel;
    }

    // Add imageUrl only if provided and no image file
    if (imageUrl != null && imageFile == null) {
      formFields['imageUrl'] = imageUrl;
    }

    // Add image file if provided
    if (imageFile != null) {
      formFields['image'] = await _buildImageFile(imageFile);
    }

    return FormData.fromMap(formFields);
  }
}
