import 'dart:io' show Platform;

import 'package:flutter/material.dart';
import 'package:frontend/core/config/logger/app_logger.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../config/enviroment/enviroment_config.dart';
import 'snack_bar_helper.dart';

class UrlLauncherHelper {
  /// Launch a phone call
  Future<void> launchTel({
    required BuildContext context,
    required String phoneNumber,
  }) async {
    final formatted = _formatPhoneNumber(phoneNumber);
    final url = 'tel:$formatted';
    await _launchUrl(context: context, url: url);
  }

  /// Launch an email
  Future<void> launchEmail({
    required BuildContext context,
    required String toEmail,
    String? subject,
    String? body,
  }) async {
    final url = Uri(
      scheme: 'mailto',
      path: toEmail,
      query: _encodeQueryParams({
        if (subject != null) 'subject': subject,
        if (body != null) 'body': body,
      }),
    ).toString();
    await _launchUrl(context: context, url: url);
  }

  /// Launch WhatsApp chat
  Future<void> launchWhatsApp({
    required BuildContext context,
    required String phoneNumber,
    String message = '',
  }) async {
    final formatted = _formatPhoneNumber(phoneNumber);
    final url = 'https://wa.me/$formatted?text=${Uri.encodeComponent(message)}';
    await _launchUrl(context: context, url: url);
  }

  /// Format Somali phone numbers to E.164 style: +252XXXXXXXXX
  String _formatPhoneNumber(String input) {
    // Remove all non-digit characters, but keep +
    final String cleaned = input.replaceAll(RegExp(r'[^\d+]'), '');

    if (cleaned.startsWith('+252')) {
      return cleaned; // Already in correct format
    } else if (cleaned.startsWith('252')) {
      return '+$cleaned';
    } else if (cleaned.startsWith('0')) {
      // Replace leading 0 with +252
      return '+252${cleaned.substring(1)}';
    } else if (cleaned.startsWith('+')) {
      // Assume user added another country code, which is not Somalia
      // You could throw or sanitize here depending on your app logic
      return cleaned;
    } else {
      // Assume it's local number without prefix
      return '+252$cleaned';
    }
  }

  /// Encode query parameters for mailto links
  String _encodeQueryParams(Map<String, String> params) {
    return params.entries
        .map(
          (e) =>
              '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}',
        )
        .join('&');
  }

  /// Launch in-app review if available, else fallback to external store review
  Future<void> launchSmartReview(BuildContext context) async {
    final inAppReview = InAppReview.instance;

    if (await inAppReview.isAvailable()) {
      AppLogger().info('Showing in-app review prompt');
      await inAppReview.requestReview();
    } else {
      AppLogger().info('Falling back to external store review');
      if (!context.mounted) return;
      await launchAppStoreReview(context);
    }
  }

  /// Launch AppStore or PlayStore for app review
  Future<void> launchAppStoreReview(BuildContext context) async {
    //  const appStoreId = '6748144979';
    final appStoreId = EnvironmentConfig.appStoreId;
    // const packageName = 'com.rasiin.hodan_hospital';
    final packageName = EnvironmentConfig.packageName;

    final appStoreUrl =
        'https://apps.apple.com/app/id$appStoreId?action=write-review';
    final playStoreUrl =
        'https://play.google.com/store/apps/details?id=$packageName&showAllReviews=true';

    final isIos = Platform.isIOS;

    final url = isIos ? appStoreUrl : playStoreUrl;

    final fallbackUrl = Platform.isIOS
        ? 'https://apps.apple.com/app/id$appStoreId'
        : 'https://play.google.com/store/apps/details?id=$packageName';

    await _launchUrl(
      context: context,
      url: url,
      mode: LaunchMode.externalApplication,
      onLaunch: (url) async {
        AppLogger().info('Launched app store review: $url');
      },
      onFallback: (url) async {
        AppLogger().warning(
          'Failed to launch app store review, opening app store',
        );
        await _launchUrl(
          context: context,
          url: fallbackUrl,
          mode: LaunchMode.externalApplication,
        );
      },
      onError: (e, stackTrace) async {
        AppLogger().error(
          'Error launching app store review: $e',
          stackTrace: stackTrace,
        );
      },
    );
  }

  /// Launch any URL and handle errors
  Future<void> _launchUrl({
    required BuildContext context,
    required String url,
    LaunchMode mode = LaunchMode.platformDefault,
    Future<void> Function(Object e, StackTrace stackTrace)? onError,
    Future<void> Function(String url)? onLaunch,
    Future<void> Function(String url)? onFallback,
  }) async {
    final uri = Uri.parse(url);
    try {
      if (await canLaunchUrl(uri)) {
        final launched = await launchUrl(uri, mode: mode);
        if (!launched) {
          if (onFallback != null) {
            onFallback(url);
            return;
          }
          if (!context.mounted) return;
          _showError(context, 'Could not launch URL');
        }
        if (onLaunch != null) {
          onLaunch(url);
        }
      } else {
        if (onFallback != null) {
          onFallback(url);
          return;
        }

        if (!context.mounted) return;

        _showError(context, 'This action is not supported on your device.');
      }
    } catch (e) {
      if (!context.mounted) return;

      _showError(context, 'Error launching URL: $e');
    }
  }

  void _showError(BuildContext context, String message) {
    SnackBarHelper.showErrorSnackBar(context, message: message);
  }
}
