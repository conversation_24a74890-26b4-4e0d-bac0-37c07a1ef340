// import 'package:fpdart/fpdart.dart';

// class DataRepositoryHelper {
//   FutureEitherFailOr<List<T>> fetchListWithCacheFallback<T>({
//     required FutureEitherFailOr<List<T>> Function() localFetch,
//     required FutureEitherFailOr<ApiResponseModel<List<T>>> Function()
//         remoteFetch,
//     required Future<void> Function(List<T>) saveToCache,
//     bool forceRefresh = false,
//   }) async {
//     try {
//       AppLogger()
//           .info('fetchListWithCacheFallback and forceRefresh: $forceRefresh');
//       // Step 1: Check if forceRefresh is true
//       if (!forceRefresh) {
//         // Step 2: Fetch from local cache
//         final localResult = await localFetch();
//         if (localResult.isRight()) {
//           remoteFetch();
//           return localResult;
//         }
//       }

//       // Step 3: Fetch from remote
//       final remoteResult = await remoteFetch();
//       return remoteResult.fold(
//         (failure) {
//           return left(failure);
//         },
//         (data) async {
//           final responseData = data.getNonNullableData();
//           await saveToCache(responseData);
//           return right(responseData);
//         },
//       );
//     } catch (e, s) {
//       AppLogger().error(
//         'fetchListWithCacheFallback error: $e',
//         error: e,
//         stackTrace: s,
//       );
//       return left(UnexpectedFailure(
//         message: 'Failed to fetch data : $e',
//       ));
//     }
//   }

//   FutureEitherFailOr<T> fetchDataWithCacheFallback<T>({
//     required FutureEitherFailOr<T> Function() localFetch,
//     required FutureEitherFailOr<ApiResponseModel<T>> Function() remoteFetch,
//     required Future<void> Function(T) saveToCache,
//     bool forceRefresh = false,
//   }) async {
//     AppLogger()
//         .info('fetchDataWithCacheFallback and forceRefresh: $forceRefresh');
//     // Step 1: Check if forceRefresh is true
//     if (!forceRefresh) {
//       // Step 2: Fetch from local cache
//       final localResult = await localFetch();
//       if (localResult.isRight()) {
//         return localResult;
//       }
//     }

//     // Step 3: Fetch from remote
//     final remoteResult = await remoteFetch();
//     return remoteResult.fold(
//       (failure) {
//         return left(failure);
//       },
//       (data) async {
//         final responseData = data.getNonNullableData();
//         await saveToCache(responseData);
//         return right(responseData);
//       },
//     );
//   }
// }
