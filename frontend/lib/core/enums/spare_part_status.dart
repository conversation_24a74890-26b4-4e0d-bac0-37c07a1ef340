import 'package:flutter/material.dart';

enum SparePartStatus {
  available('AVAILABLE'),
  lowStock('LOW_STOCK'),
  outOfStock('OUT_OF_STOCK'),
  discontinued('DISCONTINUED');

  const SparePartStatus(this.label);
  final String label;
}

extension SparePartStatusExtension on SparePartStatus {
  String get name {
    switch (this) {
      case SparePartStatus.available:
        return 'available';
      case SparePartStatus.lowStock:
        return 'lowStock';
      case SparePartStatus.outOfStock:
        return 'outOfStock';
      case SparePartStatus.discontinued:
        return 'discontinued';
    }
  }

  String get displayName {
    switch (this) {
      case SparePartStatus.available:
        return 'Available';
      case SparePartStatus.lowStock:
        return 'Low Stock';
      case SparePartStatus.outOfStock:
        return 'Out of Stock';
      case SparePartStatus.discontinued:
        return 'Discontinued';
    }
  }

  Color get color {
    switch (this) {
      case SparePartStatus.available:
        return Colors.green;
      case SparePartStatus.lowStock:
        return Colors.orange;
      case SparePartStatus.outOfStock:
        return Colors.red;
      case SparePartStatus.discontinued:
        return Colors.grey;
    }
  }

  IconData get icon {
    switch (this) {
      case SparePartStatus.available:
        return Icons.check_circle;
      case SparePartStatus.lowStock:
        return Icons.warning;
      case SparePartStatus.outOfStock:
        return Icons.inventory_2_outlined;
      case SparePartStatus.discontinued:
        return Icons.cancel;
    }
  }
}

// Helper function to convert from string
SparePartStatus sparePartStatusFromString(String status) {
  switch (status.toUpperCase()) {
    case 'AVAILABLE':
      return SparePartStatus.available;
    case 'LOW_STOCK':
      return SparePartStatus.lowStock;
    case 'OUT_OF_STOCK':
      return SparePartStatus.outOfStock;
    case 'DISCONTINUED':
      return SparePartStatus.discontinued;
    default:
      throw ArgumentError('Unknown SparePartStatus: $status');
  }
}
