import 'package:flutter/material.dart';

enum SparePartCategory {
  brassControlValveKit('BRASS_CONTROL_VALVE_KIT'),
  regulatorHighPressure('REGULATOR_HIGH_PRESSURE'),
  regulatorLowPressure('REGULATOR_LOW_PRESSURE'),
  singleBurnerGasStove('SINGLE_BURNER_GAS_STOVE'),
  threeBurnerLpgStove('THREE_BURNER_LPG_STOVE'),
  tubo2Meter('TUBO_2_METER'),
  valueBurner('VALUE_BURNER');

  const SparePartCategory(this.label);
  final String label;
}

extension SparePartCategoryExtension on SparePartCategory {
  String get name {
    switch (this) {
      case SparePartCategory.brassControlValveKit:
        return 'brassControlValveKit';
      case SparePartCategory.regulatorHighPressure:
        return 'regulatorHighPressure';
      case SparePartCategory.regulatorLowPressure:
        return 'regulatorLowPressure';
      case SparePartCategory.singleBurnerGasStove:
        return 'singleBurnerGasStove';
      case SparePartCategory.threeBurnerLpgStove:
        return 'threeBurnerLpgStove';
      case SparePartCategory.tubo2Meter:
        return 'tubo2Meter';
      case SparePartCategory.valueBurner:
        return 'valueBurner';
    }
  }

  String get displayName {
    switch (this) {
      case SparePartCategory.brassControlValveKit:
        return 'Brass Control Valve Kit';
      case SparePartCategory.regulatorHighPressure:
        return 'Regulator High Pressure';
      case SparePartCategory.regulatorLowPressure:
        return 'Regulator Low Pressure';
      case SparePartCategory.singleBurnerGasStove:
        return 'Single Burner Gas Stove';
      case SparePartCategory.threeBurnerLpgStove:
        return 'Three Burner LPG Stove';
      case SparePartCategory.tubo2Meter:
        return 'Tubo 2 Meter';
      case SparePartCategory.valueBurner:
        return 'Value Burner';
    }
  }

  String get description {
    switch (this) {
      case SparePartCategory.brassControlValveKit:
        return 'Brass control valve kit for gas cylinders';
      case SparePartCategory.regulatorHighPressure:
        return 'High pressure gas regulator';
      case SparePartCategory.regulatorLowPressure:
        return 'Low pressure gas regulator';
      case SparePartCategory.singleBurnerGasStove:
        return 'Single burner gas stove';
      case SparePartCategory.threeBurnerLpgStove:
        return 'Three burner LPG stove';
      case SparePartCategory.tubo2Meter:
        return '2 meter gas tube/hose';
      case SparePartCategory.valueBurner:
        return 'Value burner component';
    }
  }

  IconData get icon {
    switch (this) {
      case SparePartCategory.brassControlValveKit:
        return Icons.settings;
      case SparePartCategory.regulatorHighPressure:
        return Icons.tune;
      case SparePartCategory.regulatorLowPressure:
        return Icons.tune;
      case SparePartCategory.singleBurnerGasStove:
        return Icons.local_fire_department;
      case SparePartCategory.threeBurnerLpgStove:
        return Icons.kitchen;
      case SparePartCategory.tubo2Meter:
        return Icons.cable;
      case SparePartCategory.valueBurner:
        return Icons.whatshot;
    }
  }
}

// Helper function to convert from string
SparePartCategory sparePartCategoryFromString(String category) {
  switch (category.toUpperCase()) {
    case 'BRASS_CONTROL_VALVE_KIT':
      return SparePartCategory.brassControlValveKit;
    case 'REGULATOR_HIGH_PRESSURE':
      return SparePartCategory.regulatorHighPressure;
    case 'REGULATOR_LOW_PRESSURE':
      return SparePartCategory.regulatorLowPressure;
    case 'SINGLE_BURNER_GAS_STOVE':
      return SparePartCategory.singleBurnerGasStove;
    case 'THREE_BURNER_LPG_STOVE':
      return SparePartCategory.threeBurnerLpgStove;
    case 'TUBO_2_METER':
      return SparePartCategory.tubo2Meter;
    case 'VALUE_BURNER':
      return SparePartCategory.valueBurner;
    default:
      throw ArgumentError('Unknown SparePartCategory: $category');
  }
}
