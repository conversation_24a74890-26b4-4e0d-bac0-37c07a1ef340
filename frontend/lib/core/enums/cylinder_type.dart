enum CylinderType {
  sixKg(name: '6KG', displayName: '6 KG', weight: 6.0),
  thirteenKg(name: '13KG', displayName: '13 KG', weight: 13.0),
  seventeenKg(name: '17KG', displayName: '17 KG', weight: 17.0),
  twentyKg(name: '20KG', displayName: '20 KG', weight: 20.0),
  twentyFiveKg(name: '25KG', displayName: '25 KG', weight: 25.0);

  const CylinderType({
    required this.name,
    required this.displayName,
    required this.weight,
  });

  final String name;
  final String displayName;
  final double weight;
}


// Helper function to convert from string
CylinderType cylinderTypeFromString(String type) {
  final normalized = type.trim().toUpperCase();
  for (var value in CylinderType.values) {
    if (value.name.toUpperCase() == normalized) return value;
  }
  throw ArgumentError('Unknown CylinderType: $type');
}

