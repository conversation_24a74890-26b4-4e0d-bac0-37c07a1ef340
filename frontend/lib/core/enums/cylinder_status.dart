import 'package:flutter/material.dart';

enum CylinderStatus {
  active('ACTIVE'),
  discontinued('DISCONTINUED'),
  outOfStock('OUT_OF_STOCK');

  const CylinderStatus(this.label);
  final String label;
}

extension CylinderStatusExtension on CylinderStatus {
  String get name {
    switch (this) {
      case CylinderStatus.active:
        return 'active';
      case CylinderStatus.discontinued:
        return 'discontinued';
      case CylinderStatus.outOfStock:
        return 'outOfStock';
    }
  }

  String get displayName {
    switch (this) {
      case CylinderStatus.active:
        return 'Active';
      case CylinderStatus.discontinued:
        return 'Discontinued';
      case CylinderStatus.outOfStock:
        return 'Out of Stock';
    }
  }

  Color get color {
    switch (this) {
      case CylinderStatus.active:
        return Colors.green;
      case CylinderStatus.discontinued:
        return Colors.orange;
      case CylinderStatus.outOfStock:
        return Colors.red;
    }
  }

  IconData get icon {
    switch (this) {
      case CylinderStatus.active:
        return Icons.check_circle;
      case CylinderStatus.discontinued:
        return Icons.cancel;
      case CylinderStatus.outOfStock:
        return Icons.inventory_2_outlined;
    }
  }
}

// Helper function to convert from string
CylinderStatus cylinderStatusFromString(String status) {
  switch (status.toUpperCase()) {
    case 'ACTIVE':
      return CylinderStatus.active;
    case 'DISCONTINUED':
      return CylinderStatus.discontinued;
    case 'OUT_OF_STOCK':
      return CylinderStatus.outOfStock;
    default:
      throw ArgumentError('Unknown CylinderStatus: $status');
  }
}
