enum UserRole {
  customer(name: 'customer'),
  agent(name: 'agent'),
  admin(name: 'admin'),
  supervisor(name: 'supervisor');

  const UserRole({required this.name});
  final String name;

  String get label {
    switch (this) {
      case UserRole.customer:
        return 'Customer';
      case UserRole.agent:
        return 'Agent';
      case UserRole.admin:
        return 'Admin';
      case UserRole.supervisor:
        return 'Supervisor';
    }
  }

  static UserRole fromString(String name) {
    return UserRole.values.firstWhere(
      (role) => role.name == name,
      orElse: () => UserRole.customer,
    );
  }
}
