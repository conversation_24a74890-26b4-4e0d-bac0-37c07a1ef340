import 'package:flutter/material.dart';

enum PackageStatus {
  active('ACTIVE'),
  discontinued('DISCONTINUED'),
  outOfStock('OUT_OF_STOCK');

  const PackageStatus(this.label);
  final String label;
}

extension PackageStatusExtension on PackageStatus {
  String get name {
    switch (this) {
      case PackageStatus.active:
        return 'active';
      case PackageStatus.discontinued:
        return 'discontinued';
      case PackageStatus.outOfStock:
        return 'outOfStock';
    }
  }

  String get displayName {
    switch (this) {
      case PackageStatus.active:
        return 'Active';
      case PackageStatus.discontinued:
        return 'Discontinued';
      case PackageStatus.outOfStock:
        return 'Out of Stock';
    }
  }

  Color get color {
    switch (this) {
      case PackageStatus.active:
        return Colors.green;
      case PackageStatus.discontinued:
        return Colors.orange;
      case PackageStatus.outOfStock:
        return Colors.red;
    }
  }

  IconData get icon {
    switch (this) {
      case PackageStatus.active:
        return Icons.check_circle;
      case PackageStatus.discontinued:
        return Icons.cancel;
      case PackageStatus.outOfStock:
        return Icons.inventory_2_outlined;
    }
  }
}

// Helper function to convert from string
PackageStatus packageStatusFromString(String status) {
  switch (status.toUpperCase()) {
    case 'ACTIVE':
      return PackageStatus.active;
    case 'DISCONTINUED':
      return PackageStatus.discontinued;
    case 'OUT_OF_STOCK':
      return PackageStatus.outOfStock;
    default:
      throw ArgumentError('Unknown PackageStatus: $status');
  }
}
