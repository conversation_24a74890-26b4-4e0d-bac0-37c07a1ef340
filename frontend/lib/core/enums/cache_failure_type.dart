// ignore_for_file: unreachable_switch_default

enum CacheFailureType { notFound, writeError, readError, deleteError, unknown }

extension CacheFailureTypeEx on CacheFailureType {
  String getErrorMessage() {
    switch (this) {
      case CacheFailureType.notFound:
        return 'No cached data found.';
      case CacheFailureType.writeError:
        return 'Failed to save data to cache.';
      case CacheFailureType.readError:
        return 'Failed to read data from cache.';
      case CacheFailureType.deleteError:
        return 'Failed to delete cached data.';
      case CacheFailureType.unknown:
      default:
        return 'An unknown cache error occurred.';
    }
  }
}
