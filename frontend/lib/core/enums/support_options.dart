// import 'package:flutter/material.dart';
// import 'package:font_awesome_flutter/font_awesome_flutter.dart';


// enum SupportOption {
//   call,
//   whatsapp,
//   // email,
//   feedback,
// }

// extension SupportOptionExtension on SupportOption {
//   String get title {
//     switch (this) {
//       case SupportOption.call:
//         return 'Call';
//       case SupportOption.whatsapp:
//         return 'WhatsApp';
//       // case SupportOption.email:
//       //   return 'Email';
//       case SupportOption.feedback:
//         return 'Feedback';
//     }
//   }

//   IconData get icon {
//     switch (this) {
//       case SupportOption.call:
//         return FontAwesomeIcons.phone;
//       case SupportOption.whatsapp:
//         return FontAwesomeIcons.whatsapp;
//       // case SupportOption.email:
//       //   return FontAwesomeIcons.envelope;
//       case SupportOption.feedback:
//         return FontAwesomeIcons.comment;
//     }
//   }

//   String get url {
//     switch (this) {
//       case SupportOption.call:
//         return 'tel:${AppConstants.phoneNumber}';
//       case SupportOption.whatsapp:
//         return 'https://wa.me/${AppConstants.whatsappNumber}';
//       // case SupportOption.email:
//       //   return 'mailto:${AppConstants.email}';
//       case SupportOption.feedback:
//         // push to feedback form
//         return '';
//     }
//   }

//   String get description {
//     switch (this) {
//       case SupportOption.call:
//         return 'Call us directly';
//       case SupportOption.whatsapp:
//         return 'Chat with us on WhatsApp';
//       // case SupportOption.email:
//       //   return 'Email us';
//       case SupportOption.feedback:
//         return 'Send us your feedback';
//     }
//   }
// }
