/// Entity types for product categorization
/// Matches backend EntityType enum for consistency
enum EntityType {
  cylinder('CYLINDER'),
  sparePart('SPARE_PART'),
  package('PACKAGE');

  const EntityType(this.value);
  final String value;

  /// Get display name for UI
  String get displayName {
    switch (this) {
      case EntityType.cylinder:
        return 'Cylinder';
      case EntityType.sparePart:
        return 'Spare Part';
      case EntityType.package:
        return 'Package';
    }
  }

  /// Get image category for upload API
  String get imageCategory {
    switch (this) {
      case EntityType.cylinder:
        return 'cylinders';
      case EntityType.sparePart:
        return 'spare-parts';
      case EntityType.package:
        return 'packages';
    }
  }

  /// Get API entity type for image management
  String get apiEntityType {
    switch (this) {
      case EntityType.cylinder:
        return 'cylinder';
      case EntityType.sparePart:
        return 'sparepart';
      case EntityType.package:
        return 'package';
    }
  }

  /// Create from string value
  static EntityType fromString(String value) {
    return EntityType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => EntityType.cylinder,
    );
  }

  /// Create from API entity type
  static EntityType fromApiEntityType(String apiType) {
    switch (apiType.toLowerCase()) {
      case 'cylinder':
        return EntityType.cylinder;
      case 'sparepart':
      case 'spare_part':
        return EntityType.sparePart;
      case 'package':
        return EntityType.package;
      default:
        return EntityType.cylinder;
    }
  }
}
