enum CylinderMaterial {
  metal('METAL'),
  plastic('PLASTIC');

  const CylinderMaterial(this.label);
  final String label;
}

extension CylinderMaterialExtension on CylinderMaterial {
  String get name {
    switch (this) {
      case CylinderMaterial.metal:
        return 'metal';
      case CylinderMaterial.plastic:
        return 'plastic';
    }
  }

  String get displayName {
    switch (this) {
      case CylinderMaterial.metal:
        return 'Metal';
      case CylinderMaterial.plastic:
        return 'Plastic';
    }
  }

  String get description {
    switch (this) {
      case CylinderMaterial.metal:
        return 'Durable metal cylinder';
      case CylinderMaterial.plastic:
        return 'Lightweight plastic cylinder';
    }
  }
}

// Helper function to convert from string
CylinderMaterial cylinderMaterialFromString(String material) {
  switch (material.toUpperCase()) {
    case 'METAL':
    case 'IRON': // Keep backward compatibility
      return CylinderMaterial.metal;
    case 'PLASTIC':
      return CylinderMaterial.plastic;
    default:
      throw ArgumentError('Unknown CylinderMaterial: $material');
  }
}
