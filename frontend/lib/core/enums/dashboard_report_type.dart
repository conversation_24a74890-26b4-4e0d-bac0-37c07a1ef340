enum DashboardReportType { orders, revenue, inventory, agents }

extension DashboardReportTypeExtension on DashboardReportType {
  String get name {
    switch (this) {
      case DashboardReportType.orders:
        return 'orders';
      case DashboardReportType.revenue:
        return 'revenue';
      case DashboardReportType.inventory:
        return 'inventory';
      case DashboardReportType.agents:
        return 'agents';
    }
  }

  String get label {
    switch (this) {
      case DashboardReportType.orders:
        return 'Orders';
      case DashboardReportType.revenue:
        return 'Revenue';
      case DashboardReportType.inventory:
        return 'Inventory';
      case DashboardReportType.agents:
        return 'Agents';
    }
  }

  String get description {
    switch (this) {
      case DashboardReportType.orders:
        return 'Orders Report';
      case DashboardReportType.revenue:
        return 'Revenue Report';
      case DashboardReportType.inventory:
        return 'Inventory Report';
      case DashboardReportType.agents:
        return 'Agents Report';
    }
  }
}
