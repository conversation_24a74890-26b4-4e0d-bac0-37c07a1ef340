import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';
import 'package:frontend/core/config/logger/app_logger.dart';

/// Service to handle app permissions
class PermissionService {
  static final PermissionService _instance = PermissionService._internal();
  factory PermissionService() => _instance;
  PermissionService._internal();

  /// Check if camera permission is granted
  Future<bool> isCameraPermissionGranted() async {
    try {
      final status = await Permission.camera.status;
      return status.isGranted;
    } catch (e) {
      AppLogger().error('Error checking camera permission: $e');
      return false;
    }
  }

  /// Request camera permission
  Future<PermissionStatus> requestCameraPermission() async {
    try {
      final status = await Permission.camera.request();
      AppLogger().info('Camera permission status: $status');
      return status;
    } catch (e) {
      AppLogger().error('Error requesting camera permission: $e');
      return PermissionStatus.denied;
    }
  }

  /// Check and request camera permission with user-friendly handling
  Future<bool> ensureCameraPermission(BuildContext context) async {
    try {
      // Check current status
      final currentStatus = await Permission.camera.status;

      if (currentStatus.isGranted) {
        return true;
      }

      if (currentStatus.isDenied) {
        // Request permission
        final newStatus = await Permission.camera.request();

        if (newStatus.isGranted) {
          return true;
        }

        if (newStatus.isPermanentlyDenied && context.mounted) {
          _showPermissionDeniedDialog(context);
          return false;
        }

        if (context.mounted) {
          _showPermissionRequiredDialog(context);
        }
        return false;
      }

      if (currentStatus.isPermanentlyDenied && context.mounted) {
        _showPermissionDeniedDialog(context);
        return false;
      }

      return false;
    } catch (e) {
      AppLogger().error('Error ensuring camera permission: $e');
      if (context.mounted) {
        _showPermissionErrorDialog(context);
      }
      return false;
    }
  }

  /// Show dialog when permission is required
  void _showPermissionRequiredDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Camera Permission Required'),
          content: const Text(
            'This app needs camera access to scan QR codes for order verification. Please grant camera permission to continue.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                requestCameraPermission();
              },
              child: const Text('Grant Permission'),
            ),
          ],
        );
      },
    );
  }

  /// Show dialog when permission is permanently denied
  void _showPermissionDeniedDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Camera Permission Denied'),
          content: const Text(
            'Camera permission has been permanently denied. Please enable it in your device settings to use QR code scanning.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  /// Show dialog when there's an error with permissions
  void _showPermissionErrorDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Permission Error'),
          content: const Text(
            'There was an error checking camera permissions. Please try again or restart the app.',
          ),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Check if camera is available on the device
  Future<bool> isCameraAvailable() async {
    try {
      final status = await Permission.camera.status;
      return !status.isPermanentlyDenied;
    } catch (e) {
      AppLogger().error('Error checking camera availability: $e');
      return false;
    }
  }

  /// Get permission status as human-readable string
  String getPermissionStatusText(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 'Granted';
      case PermissionStatus.denied:
        return 'Denied';
      case PermissionStatus.restricted:
        return 'Restricted';
      case PermissionStatus.limited:
        return 'Limited';
      case PermissionStatus.permanentlyDenied:
        return 'Permanently Denied';
      case PermissionStatus.provisional:
        return 'Provisional';
    }
  }
}
