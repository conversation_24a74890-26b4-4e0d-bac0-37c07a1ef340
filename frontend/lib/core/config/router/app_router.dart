// import 'package:flutter/widgets.dart';
// import 'package:go_router/go_router.dart';
// import 'package:hodan_hospital/core/config/router/helper/go_router_refresh_stream.dart';
// import 'package:hodan_hospital/core/config/router/params/route_params.dart';
// import 'package:hodan_hospital/core/config/router/paths/route_paths.dart';
// import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
// import 'package:hodan_hospital/features/authentication/presentation/bloc/authentication_bloc.dart';
// import 'package:hodan_hospital/features/authentication/presentation/pages/login_page.dart';
// import 'package:hodan_hospital/features/authentication/presentation/pages/register_page.dart';
// import 'package:hodan_hospital/features/doctor/domain/entities/doctor_entity.dart';
// import 'package:hodan_hospital/features/doctor/presentation/pages/doctor_details_page.dart';
// import 'package:hodan_hospital/features/doctor/presentation/pages/doctors_page.dart';
// import 'package:hodan_hospital/features/doctor/presentation/pages/patient_appointment_confirm_page.dart';
// import 'package:hodan_hospital/features/doctor/presentation/pages/patient_appointnent_page.dart';
// import 'package:hodan_hospital/features/appointment/presentation/pages/appointment_page.dart';
// import 'package:hodan_hospital/features/home/<USER>/pages/home_page.dart';
// import 'package:hodan_hospital/features/main/presentation/pages/main_page.dart';
// import 'package:hodan_hospital/features/user/presentation/pages/profile_page.dart';
// // import 'package:hodan_hospital/shared/presentation/pages/on_boarding_page.dart';

// class AppRouter {
//   static GoRouter router(BuildContext context) {
//     final authBloc = context.authenticationBloc;
//     return GoRouter(
//       initialLocation: RoutePaths.authLogin.path,
//       debugLogDiagnostics: true,

//       /// ✅ `refreshListenable` listens to auth state changes
//       refreshListenable: GoRouterRefreshStream(authBloc.stream),

//       /// ✅ Redirect users based on Authentication State
//       redirect: (context, state) {
//         final authState = authBloc.state;

//         /// ✅ Check if the user is navigating to any /main/... route
//         final isGoingToMain = state.uri.path.startsWith(RoutePaths.main.path);
//         // ✅ Check if the user is navigating to any /auth/... route
//         final isGoingToAuth = state.uri.path.startsWith('/auth');

//         if (authState is Unauthenticated && !isGoingToAuth) {
//           // Redirect to login if not logged in
//           return RoutePaths.authLogin.path;
//         }

//         if (authState is OnboardingRequired) {
//           // Redirect to onboarding if needed
//           return RoutePaths.onboarding.path;
//         }

//         if (authState is Authenticated && !isGoingToMain) {
//           return RoutePaths.mainHome.path;
//         }

//         return null; // No redirect, continue as normal
//       },

//       //!!!!!!!!!!!!!!!!!!!!11
//       routes: [
//         /// Onboarding Page
//         // GoRoute(
//         //   path: RoutePaths.onboarding.path,
//         //   name: RoutePaths.onboarding.name,
//         //   builder: (context, state) => const OnBoardingPage(),
//         // ),

//         /// Authentication Routes
//         GoRoute(
//           path: RoutePaths.authLogin.path,
//           name: RoutePaths.authLogin.name,
//           builder: (context, state) => const LoginPage(),
//         ),
//         GoRoute(
//           path: RoutePaths.authRegister.path,
//           name: RoutePaths.authRegister.name,
//           builder: (context, state) => const RegisterPage(),
//         ),

//         /// 🔥 Stateful Shell Route for Bottom Navigation
//         StatefulShellRoute.indexedStack(
//           builder: (context, state, navigationShell) {
//             return MainPage(navigationShell: navigationShell);
//           },
//           branches: [
//             // Home Branch
//             StatefulShellBranch(
//               routes: [
//                 GoRoute(
//                   path: RoutePaths.mainHome.path,
//                   name: RoutePaths.mainHome.name,
//                   builder: (context, state) => const HomePage(),
//                   routes: [
//                     GoRoute(
//                       path: RoutePaths.homeDoctorDetails.path,
//                       name: RoutePaths.homeDoctorDetails.name,
//                       builder: (context, state) {
//                         final doctor = state.extra as DoctorEntity?;
//                         return DoctorDetailsPage(doctor: doctor!);
//                       },
//                       routes: [
//                         GoRoute(
//                           path: RoutePaths.homeDoctorPatientAppointment.path,
//                           name: RoutePaths.homeDoctorPatientAppointment.name,
//                           builder: (context, state) {
//                             final doctor = state.extra as DoctorEntity?;
//                             return PatientAppointnentPage(doctor: doctor!);
//                           },
//                           routes: [
//                             GoRoute(
//                               path: RoutePaths
//                                   .homeDoctorPatientAppointmentConfirm.path,
//                               name: RoutePaths
//                                   .homeDoctorPatientAppointmentConfirm.name,
//                               builder: (context, state) {
//                                 final doctor = state.extra as DoctorEntity?;
//                                 final pID =
//                                     state.pathParameters[RouteParams.pID]!;
//                                 return PatientAppointmentConfirmPage(
//                                   doctor: doctor!,
//                                   pID: pID,
//                                 );
//                               },
//                             ),
//                           ],
//                         ),
//                       ],
//                     ),
//                   ],
//                 ),
//               ],
//             ),

//             /// Appointments Branch
//             StatefulShellBranch(
//               routes: [
//                 GoRoute(
//                   path: RoutePaths.mainAppointments.path,
//                   name: RoutePaths.mainAppointments.name,
//                   builder: (context, state) => const AppointmentPage(),
//                 ),
//               ],
//               // preload: true,
//             ),

//             /// Doctors Branch
//             StatefulShellBranch(
//               routes: [
//                 GoRoute(
//                   path: RoutePaths.mainDoctors.path,
//                   name: RoutePaths.mainDoctors.name,
//                   builder: (context, state) => const DoctorsPage(),
//                   routes: [
//                     GoRoute(
//                       path: RoutePaths.doctorsDoctorDetails.path,
//                       name: RoutePaths.doctorsDoctorDetails.name,
//                       builder: (context, state) {
//                         final doctor = state.extra as DoctorEntity?;
//                         return DoctorDetailsPage(doctor: doctor!);
//                       },
//                       routes: [
//                         GoRoute(
//                           path: RoutePaths.doctorsDoctorPatientAppointment.path,
//                           name: RoutePaths.doctorsDoctorPatientAppointment.name,
//                           builder: (context, state) {
//                             final doctor = state.extra as DoctorEntity?;
//                             return PatientAppointnentPage(doctor: doctor!);
//                           },
//                           routes: [
//                             GoRoute(
//                               path: RoutePaths
//                                   .doctorsDoctorPatientAppointmentConfirm.path,
//                               name: RoutePaths
//                                   .doctorsDoctorPatientAppointmentConfirm.name,
//                               builder: (context, state) {
//                                 final doctor = state.extra as DoctorEntity?;
//                                 final pID =
//                                     state.pathParameters[RouteParams.pID]!;
//                                 return PatientAppointmentConfirmPage(
//                                   doctor: doctor!,
//                                   pID: pID,
//                                 );
//                               },
//                             ),
//                           ],
//                         ),
//                       ],
//                     ),
//                   ],
//                 ),
//               ],
//             ),

//             /// Profile Branch
//             StatefulShellBranch(
//               routes: [
//                 GoRoute(
//                   path: RoutePaths.mainProfile.path,
//                   name: RoutePaths.mainProfile.name,
//                   builder: (context, state) => const ProfilePage(),
//                 ),
//               ],
//             ),
//           ],
//         ),
//       ],
//     );
//   }
// }
