import 'dart:async';
import 'package:flutter/widgets.dart';

/// A `RefreshListenable` that listens to a stream and notifies GoRouter to refresh
class GoRouterRefreshStream extends ChangeNotifier {
  final Stream stream;
  late final StreamSubscription _subscription;

  GoRouterRefreshStream(this.stream) {
    _subscription = stream.listen((_) {
      notifyListeners(); // ✅ Notifies GoRouter to refresh when stream emits new value
    });
  }

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}
