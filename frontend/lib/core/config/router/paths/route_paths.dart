import '../params/route_params.dart';

enum RoutePaths {
  // Splash & Onboarding
  splash,
  onboarding,

  // Authentication
  authLogin,
  authRegister,

  // Main Navigation
  main,
  mainHome,
  mainDoctors,
  mainAppointments,
  mainProfile,

  // Doctor & Appointments Flow (Unique Names)
  homeDoctorDetails, // Inside Home Section
  doctorsDoctorDetails, // Inside Doctors Section
  homeDoctorPatientAppointment,
  doctorsDoctorPatientAppointment,
  homeDoctorPatientAppointmentConfirm,
  doctorsDoctorPatientAppointmentConfirm,
}

extension RoutePathsExtension on RoutePaths {
  String get path {
    switch (this) {
      // Splash & Onboarding
      case RoutePaths.splash:
        return '/';
      case RoutePaths.onboarding:
        return '/onboarding';

      // Authentication
      case RoutePaths.authLogin:
        return '/auth/login';
      case RoutePaths.authRegister:
        return '/auth/register';

      // Main Navigation
      case RoutePaths.main:
        return '/main';
      case RoutePaths.mainHome:
        return '/main/home';
      case RoutePaths.mainDoctors:
        return '/main/doctors';
      case RoutePaths.mainAppointments:
        return '/main/appointments';
      case RoutePaths.mainProfile:
        return '/main/profile';

      // Doctor & Appointments Flow (Unique Paths)
      case RoutePaths.homeDoctorDetails:
        return '/main/home/<USER>/details';
      case RoutePaths.doctorsDoctorDetails:
        return '/main/doctors/doctors/details';
      case RoutePaths.homeDoctorPatientAppointment:
        return '/main/home/<USER>/details/patient-appointment';
      case RoutePaths.doctorsDoctorPatientAppointment:
        return '/main/doctors/doctors/details/patient-appointment';
      case RoutePaths.homeDoctorPatientAppointmentConfirm:
        return '/main/home/<USER>/details/patient-appointment/confirm/:${RouteParams.pID}';
      case RoutePaths.doctorsDoctorPatientAppointmentConfirm:
        return '/main/doctors/doctors/details/patient-appointment/confirm/:${RouteParams.pID}';
    }
  }

  String get name => toString().split('.').last;

  /// ✅ **Helper Function to Replace Path Parameters**
  String withParams(Map<String, String> params) {
    String finalPath = path;
    params.forEach((key, value) {
      if (finalPath.contains(':$key')) {
        finalPath = finalPath.replaceFirst(':$key', value);
      }
    });
    return finalPath;
  }
}
