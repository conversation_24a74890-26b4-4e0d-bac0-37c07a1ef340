import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:frontend/core/network/dio_factory.dart';
import 'package:injectable/injectable.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

import '../../../../features/shared/data/source/local/flutter_secure_storage_services.dart';
import '../../../../features/shared/presentation/bloc/bottom-nav-cubit/bottom_nav_cubit.dart';
import '../../../../features/shared/presentation/bloc/theme-cubit/theme_cubit.dart';
import '../../../../features/shared/presentation/bloc/theme-cubit/theme_storage.dart';
import '../../../errors/http_error_handler.dart';
import '../../../network/api_client/dio_api_client.dart';
import '../../../network/connection_checker.dart';
import '../../../services/notification_services.dart';
import '../dependency_injection.dart';

@module
abstract class CoreModule {
  /// ✅ Database Manager (Pre-Resolved Singleton)
  // @preResolve
  // @lazySingleton
  // Future<DatabaseManager> provideDatabaseManager() async {
  //   final databaseManager = DatabaseManager();
  //   await databaseManager.init();
  //   return databaseManager;
  // }

  /// ✅ Secure Storage Service
  @lazySingleton
  FlutterSecureStorageServices provideSecureStorageServices(
    FlutterSecureStorage flutterSecureStorage,
  ) {
    return FlutterSecureStorageServices(
      // flutterSecureStorage: const FlutterSecureStorage(),
      flutterSecureStorage: flutterSecureStorage,
    );
  }

  /// ✅ Connection Checker (Singleton)
  @singleton
  ConnectionChecker provideConnectionChecker(
    InternetConnection internetConnection,
  ) {
    return ConnectionCheckerImpl(
      // internetConnection: InternetConnection(),
      internetConnection: internetConnection,
    );
  }

  /// ✅ Dio API Client (Singleton)
  @lazySingleton
  DioApiClient dioApiClient(
    FlutterSecureStorageServices flutterSecureStorageServices,
  ) {
    return DioApiClient(
      dio: createDioClient(
        flutterSecureStorageServices: flutterSecureStorageServices,
      ),
    );
  }

  @lazySingleton
  NotificationService get notificationService => NotificationService();

  /// ✅ Http Error Handler
  @lazySingleton
  HttpErrorHandler provideHttpErrorHandler(
    ConnectionChecker connectionChecker,
  ) {
    return HttpErrorHandler(connectionChecker: connectionChecker);
  }

  /// ✅ Database Error Handler
  // @lazySingleton
  // DatabaseErrorHandler provideDatabaseErrorHandler(
  //   DatabaseManager databaseManager,
  // ) {
  //   return DatabaseErrorHandler(databaseManager: databaseManager);
  // }

  /// ✅ ThemeStorage (Singleton)
  @lazySingleton
  ThemeStorage get themeStorage =>
      ThemeStorage(flutterSecureStorageServices: sl());

  /// ✅ Blocs and Cubit
  // @lazySingleton
  // DialogCubit get dialogCubit => DialogCubit();

  @lazySingleton
  BottomNavCubit get bottomNavCubit => BottomNavCubit();

  @lazySingleton
  ThemeCubit get themeCubit => ThemeCubit(themeStorage: sl());
}
