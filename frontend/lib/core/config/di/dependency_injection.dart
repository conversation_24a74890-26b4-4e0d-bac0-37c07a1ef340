// import './dependency_injection.config.dart';
import 'package:frontend/core/config/di/dependency_injection.config.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

import '../logger/app_logger.dart';

/// dart run build_runner build --delete-conflicting-outputs

final GetIt sl = GetIt.instance;

@InjectableInit()
Future<void> setupServiceLocator() async {
  try {
    AppLogger().info('Initializing dependencies...');

    sl.init();

    AppLogger().info('Dependencies initialized successfully!');
  } catch (e, stackTrace) {
    AppLogger().error(
      'Error initializing service locator',
      error: e,
      stackTrace: stackTrace,
    );
  }
}
