import 'package:injectable/injectable.dart';

import '../../../../features/order/data/repositories/order_repository_impl.dart';
import '../../../../features/order/data/source/order_remote_datasource.dart';
import '../../../../features/order/domain/repositories/order_repository.dart';
import '../../../../features/order/domain/usecases/assign_agent_to_order_usecase.dart';
import '../../../../features/order/domain/usecases/cancel_order_usecase.dart';
import '../../../../features/order/domain/usecases/complete_order_usecase.dart';
import '../../../../features/order/domain/usecases/create_order_usecase.dart';
import '../../../../features/order/domain/usecases/delete_order_usecase.dart';
import '../../../../features/order/domain/usecases/get_orders_usecase.dart';
import '../../../../features/order/domain/usecases/regenerate_qr_code_usecase.dart';
import '../../../../features/order/domain/usecases/update_order_usecase.dart';
import '../../../../features/order/domain/usecases/validate_order_qr_usecase.dart';
import '../../../../features/order/presentation/bloc/order_bloc.dart';
import '../../../errors/http_error_handler.dart';
import '../../../network/api_client/dio_api_client.dart';

@module
abstract class OrderModule {
  /// ✅ Remote Data Source
  @injectable
  OrderRemoteDataSource provideOrderRemoteDataSource(
    DioApiClient dioApiClient,
    HttpErrorHandler httpErrorHandler,
  ) {
    return OrderRemoteDataSourceImpl(
      dioApiClient: dioApiClient,
      httpErrorHandler: httpErrorHandler,
    );
  }

  /// ✅ Local Data Source
  // @injectable
  // DashboardLocalDataSource provideDashboardLocalDataSource(
  //   FlutterSecureStorageServices storageServices,
  // ) {
  //   return DashboardLocalDataSourceImpl(
  //     flutterSecureStorageServices: storageServices,
  //   );
  // }

  // /// ✅ Database Manager (Singleton)
  // @lazySingleton
  // DashboardDatabaseManager provideDashboardDatabaseManager(
  //   DatabaseErrorHandler databaseErrorHandler,
  // ) {
  //   return DashboardDatabaseManagerImpl(
  //     databaseErrorHandler: databaseErrorHandler,
  //   );
  // }

  /// ✅ Repository (Singleton)
  // @lazySingleton
  // DashboardRepository provideDashboardRepository(
  //   DashboardRemoteDataSource remoteDataSource,
  //   // DashboardLocalDataSource localDataSource,
  // ) {
  //   return DashboardRepositoryImpl(
  //     dashboardRemoteDataSource: remoteDataSource,
  //     // dashboardLocalDataSource: localDataSource,
  //   );
  // }
  @lazySingleton
  OrderRepository provideOrderRepository(
    OrderRemoteDataSource remoteDataSource,
    // OrderLocalDataSource localDataSource,
  ) {
    return OrderRepositoryImpl(
      orderRemoteDataSource: remoteDataSource,
      // orderLocalDataSource: localDataSource,
    );
  }

  /// ✅ Use Cases (Singleton)
  @lazySingleton
  CreateOrderUseCase provideCreateOrderUseCase(OrderRepository repository) {
    return CreateOrderUseCase(repository: repository);
  }

  @lazySingleton
  GetOrdersUseCase provideGetOrdersUseCase(OrderRepository repository) {
    return GetOrdersUseCase(repository: repository);
  }

  @lazySingleton
  AssignAgentToOrderUseCase provideAssignAgentToOrderUseCase(
    OrderRepository repository,
  ) {
    return AssignAgentToOrderUseCase(repository: repository);
  }

  @lazySingleton
  CancelOrderUseCase provideCancelOrderUseCase(OrderRepository repository) {
    return CancelOrderUseCase(repository: repository);
  }

  @lazySingleton
  CompleteOrderUseCase provideCompleteOrderUseCase(OrderRepository repository) {
    return CompleteOrderUseCase(repository: repository);
  }

  @lazySingleton
  RegenerateQRCodeUseCase provideRegenerateQRCodeUseCase(
    OrderRepository repository,
  ) {
    return RegenerateQRCodeUseCase(repository: repository);
  }

  @lazySingleton
  ValidateOrderQrCodeUseCase provideValidateOrderQrCodeUseCase(
    OrderRepository repository,
  ) {
    return ValidateOrderQrCodeUseCase(repository: repository);
  }

  @lazySingleton
  UpdateOrderUseCase provideUpdateOrderUseCase(OrderRepository repository) {
    return UpdateOrderUseCase(repository: repository);
  }

  @lazySingleton
  DeleteOrderUseCase provideDeleteOrderUseCase(OrderRepository repository) {
    return DeleteOrderUseCase(repository: repository);
  }

  /// ✅ Dashboard Bloc (Singleton)
  @lazySingleton
  OrderBloc provideOrderBloc(
    CreateOrderUseCase createOrderUseCase,
    GetOrdersUseCase getOrdersUseCase,
    AssignAgentToOrderUseCase assignAgentToOrderUseCase,
    CancelOrderUseCase cancelOrderUseCase,
    CompleteOrderUseCase completeOrderUseCase,
    RegenerateQRCodeUseCase regenerateQRCodeUseCase,
    ValidateOrderQrCodeUseCase validateOrderQrCodeUseCase,
    UpdateOrderUseCase updateOrderUseCase,
    DeleteOrderUseCase deleteOrderUseCase,
  ) {
    return OrderBloc(
      createOrderUseCase: createOrderUseCase,
      getOrdersUseCase: getOrdersUseCase,
      assignAgentToOrderUseCase: assignAgentToOrderUseCase,
      cancelOrderUseCase: cancelOrderUseCase,
      completeOrderUseCase: completeOrderUseCase,
      regenerateQRCodeUseCase: regenerateQRCodeUseCase,
      validateOrderQrCodeUseCase: validateOrderQrCodeUseCase,
      updateOrderUseCase: updateOrderUseCase,
      deleteOrderUseCase: deleteOrderUseCase,
    );
  }
}
