import 'package:frontend/features/authentication/data/repository/authentication_repository_impl.dart';
import 'package:frontend/features/authentication/data/source/local/authentication_local_data_source.dart';
import 'package:frontend/features/authentication/data/source/remote/authentication_remote_data_source.dart';
import 'package:frontend/features/authentication/domain/repository/authentication_repository.dart';
import 'package:frontend/features/authentication/domain/usecase/check_authentication_usecase.dart';
import 'package:frontend/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:injectable/injectable.dart';

import '../../../../features/authentication/domain/usecase/complete_onboarding_use_case.dart';
import '../../../../features/authentication/domain/usecase/login_usecase.dart';
import '../../../../features/authentication/domain/usecase/logout_usecase.dart';
import '../../../../features/authentication/domain/usecase/resend_otp_use_case.dart';
import '../../../../features/authentication/domain/usecase/verify_otp_use_case.dart';
import '../../../../features/shared/data/source/local/flutter_secure_storage_services.dart';
import '../../../errors/http_error_handler.dart';
import '../../../network/api_client/dio_api_client.dart';

@module
abstract class AuthenticationModule {
  /// ✅ Remote Data Source
  @injectable
  AuthenticationRemoteDataSource provideAuthRemoteDataSource(
    DioApiClient dioApiClient,
    HttpErrorHandler httpErrorHandler,
  ) {
    return AuthenticationRemoteDataSourceImpl(
      dioApiClient: dioApiClient,
      httpErrorHandler: httpErrorHandler,
    );
  }

  /// ✅ Local Data Source
  @injectable
  AuthenticationLocalDataSource provideAuthLocalDataSource(
    FlutterSecureStorageServices storageServices,
    // AuthDatabaseManager authDatabaseManager
  ) {
    return AuthenticationLocalDataSourceImpl(
      flutterSecureStorageServices: storageServices,
      // authDatabaseManager: authDatabaseManager,
    );
  }

  // /// ✅ Database Manager (Singleton)
  // @lazySingleton
  // AuthDatabaseManager provideAuthDatabaseManager(
  //     DatabaseErrorHandler databaseErrorHandler) {
  //   return AuthDatabaseManagerImpl(
  //     databaseErrorHandler: databaseErrorHandler,
  //   );
  // }

  /// ✅ Repository (Singleton)
  @lazySingleton
  AuthenticationRepository provideAuthRepository(
    AuthenticationRemoteDataSource remoteDataSource,
    AuthenticationLocalDataSource localDataSource,
  ) {
    return AuthenticationRepositoryImpl(
      authenticationRemoteDataSource: remoteDataSource,
      authenticationLocalDataSource: localDataSource,
    );
  }

  /// ✅ Use Cases (Singleton)
  @lazySingleton
  LoginUseCase provideLoginUseCase(AuthenticationRepository repository) {
    return LoginUseCase(repository: repository);
  }


  @lazySingleton
  LogoutUseCase provideLogoutUseCase(AuthenticationRepository repository) {
    return LogoutUseCase(repository: repository);
  }

  @lazySingleton
  CheckAuthenticationUseCase provideCheckUserAuthenticationUseCase(
    AuthenticationRepository repository,
  ) {
    return CheckAuthenticationUseCase(repository: repository);
  }

  @lazySingleton
  CompleteOnboardingUseCase provideCompleteOnboardingUseCase(
    AuthenticationRepository repository,
  ) {
    return CompleteOnboardingUseCase(repository: repository);
  }

  @lazySingleton
  ResendOtpUseCase provideSendOtpUseCase(AuthenticationRepository repository) {
    return ResendOtpUseCase(repository: repository);
  }

  @lazySingleton
  VerifyOtpUseCase provideVerifyOtpUseCase(
    AuthenticationRepository repository,
  ) {
    return VerifyOtpUseCase(repository: repository);
  }


  /// ✅ Authentication Bloc (Singleton)
  @lazySingleton
  AuthenticationBloc provideAuthenticationBloc(
    LoginUseCase loginUseCase,
    LogoutUseCase logoutUseCase,
    CheckAuthenticationUseCase checkUserAuthenticationUseCase,
    CompleteOnboardingUseCase completeOnboardingUseCase,
    ResendOtpUseCase resendOtpUseCase,
    VerifyOtpUseCase verifyOtpUseCase,
  ) {
    return AuthenticationBloc(
      loginUseCase: loginUseCase,
      logoutUseCase: logoutUseCase,
      checkAuthenticationUseCase: checkUserAuthenticationUseCase,
      completeOnboardingUseCase: completeOnboardingUseCase,
      resendOtpUseCase: resendOtpUseCase,
      verifyOtpUseCase: verifyOtpUseCase,
    );
  }
}
