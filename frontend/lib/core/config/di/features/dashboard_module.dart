import 'package:injectable/injectable.dart';

import '../../../../features/dashboard/data/repositories/dashboard_repository_impl.dart';
import '../../../../features/dashboard/data/source/remote/dashboard_remote_datasource.dart';
import '../../../../features/dashboard/domain/repositories/dashboard_reposiory.dart';
import '../../../../features/dashboard/domain/usecases/get_admin_dashboard_report.dart';
import '../../../../features/dashboard/domain/usecases/get_agent_dashboard_usecase.dart';
import '../../../../features/dashboard/domain/usecases/get_admin_dashboard_usecase.dart';
import '../../../../features/dashboard/domain/usecases/get_customer_dashboard_usecase.dart';
import '../../../../features/dashboard/domain/usecases/get_supervisor_dashboard_usecase.dart';
import '../../../../features/dashboard/presentation/bloc/dashboard_bloc.dart';
import '../../../errors/http_error_handler.dart';
import '../../../network/api_client/dio_api_client.dart';

@module
abstract class DashboardModule {
  /// ✅ Remote Data Source
  @injectable
  DashboardRemoteDataSource provideDashboardRemoteDataSource(
    DioApiClient dioApiClient,
    HttpErrorHandler httpErrorHandler,
  ) {
    return DashboardRemoteDataSourceImpl(
      dioApiClient: dioApiClient,
      httpErrorHandler: httpErrorHandler,
    );
  }

  /// ✅ Local Data Source
  // @injectable
  // DashboardLocalDataSource provideDashboardLocalDataSource(
  //   FlutterSecureStorageServices storageServices,
  // ) {
  //   return DashboardLocalDataSourceImpl(
  //     flutterSecureStorageServices: storageServices,
  //   );
  // }

  // /// ✅ Database Manager (Singleton)
  // @lazySingleton
  // DashboardDatabaseManager provideDashboardDatabaseManager(
  //   DatabaseErrorHandler databaseErrorHandler,
  // ) {
  //   return DashboardDatabaseManagerImpl(
  //     databaseErrorHandler: databaseErrorHandler,
  //   );
  // }

  /// ✅ Repository (Singleton)
  @lazySingleton
  DashboardRepository provideDashboardRepository(
    DashboardRemoteDataSource remoteDataSource,
    // DashboardLocalDataSource localDataSource,
  ) {
    return DashboardRepositoryImpl(
      dashboardRemoteDataSource: remoteDataSource,
      // dashboardLocalDataSource: localDataSource,
    );
  }

  /// ✅ Use Cases (Singleton)
  @lazySingleton
  GetAdminDashboardUseCase provideGetAdminDashboardUseCase(
    DashboardRepository repository,
  ) {
    return GetAdminDashboardUseCase(repository: repository);
  }

  @lazySingleton
  GetCustomerDashboardUseCase provideGetCustomerDashboardUseCase(
    DashboardRepository repository,
  ) {
    return GetCustomerDashboardUseCase(repository: repository);
  }

  @lazySingleton
  GetAgentDashboardUseCase provideGetAgentDashboardUseCase(
    DashboardRepository repository,
  ) {
    return GetAgentDashboardUseCase(repository: repository);
  }

  @lazySingleton
  GetAdminDashboardReportUseCase provideGetAdminDashboardReportUseCase(
    DashboardRepository repository,
  ) {
    return GetAdminDashboardReportUseCase(repository: repository);
  }

  @lazySingleton
  GetSupervisorDashboardUseCase provideGetSupervisorDashboardUseCase(
    DashboardRepository repository,
  ) {
    return GetSupervisorDashboardUseCase(repository: repository);
  }

  /// ✅ Dashboard Bloc (Singleton)
  @lazySingleton
  DashboardBloc provideDashboardBloc(
    GetAdminDashboardUseCase getAdminDashboardUseCase,
    GetCustomerDashboardUseCase getCustomerDashboardUseCase,
    GetAgentDashboardUseCase getAgentDashboardUseCase,
    GetAdminDashboardReportUseCase getAdminDashboardReportUseCase,
    GetSupervisorDashboardUseCase getSupervisorDashboardUseCase,
  ) {
    return DashboardBloc(
      getAdminDashboardUseCase: getAdminDashboardUseCase,
      getCustomerDashboardUseCase: getCustomerDashboardUseCase,
      getAgentDashboardUseCase: getAgentDashboardUseCase,
      getSupervisorDashboardUseCase: getSupervisorDashboardUseCase,
      getAdminDashboardReportUseCase: getAdminDashboardReportUseCase,
    );
  }
}
