import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../colors/app_colors.dart';
import 'custom_text_theme.dart';

class CustomInputDecorationTheme {
  CustomInputDecorationTheme._(); // to avoid creating instance

  static final double _borderRadius = 16.r;

  //-- light theme
  static InputDecorationTheme light = InputDecorationTheme(
    activeIndicatorBorder: BorderSide(
      color: CustomColors.light.primary,
    ),
    fillColor: CustomColors.light.surface,
    filled: true,
    hintStyle: CustomTextTheme.light.bodySmall,
    labelStyle: CustomTextTheme.light.labelMedium,
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius),
      borderSide: BorderSide(
        color: CustomColors.light.surface,
      ),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius),
      borderSide: BorderSide(
        color: CustomColors.light.surface,
      ),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius),
      borderSide: BorderSide(
        color: CustomColors.light.primary,
      ),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius),
      borderSide: BorderSide(
        color: CustomColors.light.error.withValues(alpha: 0.5),
      ),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius.r),
      borderSide: BorderSide(
        color: CustomColors.light.error,
      ),
    ),
  );

  //-- dark theme
  static InputDecorationTheme dark = InputDecorationTheme(
    activeIndicatorBorder: BorderSide(
      color: CustomColors.dark.primary,
    ),
    fillColor: CustomColors.dark.surface,
    filled: true,
    hintStyle: CustomTextTheme.dark.bodySmall,
    labelStyle: CustomTextTheme.dark.labelMedium,
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius),
      borderSide: BorderSide(
        color: CustomColors.dark.surface,
      ),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius),
      borderSide: BorderSide(
        color: CustomColors.dark.surface,
      ),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius),
      borderSide: BorderSide(
        color: CustomColors.dark.primary,
      ),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius),
      borderSide: BorderSide(
        color: CustomColors.dark.error.withValues(alpha: 0.5),
      ),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius.r),
      borderSide: BorderSide(
        color: CustomColors.dark.error,
      ),
    ),
  );
}
