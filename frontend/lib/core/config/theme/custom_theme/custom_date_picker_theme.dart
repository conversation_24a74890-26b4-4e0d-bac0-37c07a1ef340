import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../colors/app_colors.dart';

class CustomDatePickerTheme {
  // Light theme configuration for the date picker
  static DatePickerThemeData light = DatePickerThemeData(
    // backgroundColor: AppColors.surfaceColorLight,
    backgroundColor: CustomColors.light.surface,
    elevation: 4,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12.r),
    ),
    headerBackgroundColor: CustomColors.light.primary,
    headerForegroundColor: CustomColors.light.text,
    headerHeadlineStyle: TextStyle(
      color: CustomColors.light.text,
      fontSize: 18.sp,
      fontWeight: FontWeight.bold,
    ),
    headerHelpStyle: TextStyle(
      color: CustomColors.light.subtext,
      fontSize: 14.sp,
    ),
    weekdayStyle: TextStyle(
      color: CustomColors.light.text,
      fontSize: 14.sp,
      fontWeight: FontWeight.bold,
    ),
    dayStyle: TextStyle(
      color: CustomColors.light.text,
      fontSize: 14.sp,
    ),
    todayForegroundColor: WidgetStateProperty.all(CustomColors.light.primary),
    todayBackgroundColor: WidgetStateProperty.all(CustomColors.light.card),
    yearStyle: TextStyle(
      color: CustomColors.light.text,
      fontSize: 12.sp,
    ),
  );

  // Dark theme configuration for the date picker
  static DatePickerThemeData dark = DatePickerThemeData(
    backgroundColor: CustomColors.dark.surface,
    elevation: 4,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12.r),
    ),
    headerBackgroundColor: CustomColors.dark.primary,
    headerForegroundColor: CustomColors.dark.text,
    headerHeadlineStyle: TextStyle(
      color: CustomColors.dark.text,
      fontSize: 18.sp,
      fontWeight: FontWeight.bold,
    ),
    headerHelpStyle: TextStyle(
      color: CustomColors.dark.subtext,
      fontSize: 14.sp,
    ),
    weekdayStyle: TextStyle(
      color: CustomColors.dark.text,
      fontSize: 14.sp,
      fontWeight: FontWeight.bold,
    ),
    dayStyle: TextStyle(
      color: CustomColors.dark.text,
      fontSize: 14.sp,
    ),
    todayForegroundColor: WidgetStateProperty.all(CustomColors.dark.primary),
    todayBackgroundColor: WidgetStateProperty.all(CustomColors.dark.card),
    yearStyle: TextStyle(
      color: CustomColors.dark.text,
      fontSize: 12.sp,
    ),
  );
}
