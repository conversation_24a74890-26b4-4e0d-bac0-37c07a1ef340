import 'package:flutter/material.dart';

import '../colors/app_colors.dart';
import 'custom_text_theme.dart';

class CustomBottomNavigationBarTheme {
  CustomBottomNavigationBarTheme._(); // to avoid making instance

  //-- light theme
  static BottomNavigationBarThemeData light = BottomNavigationBarThemeData(
    backgroundColor: CustomColors.light.surface,
    type: BottomNavigationBarType.fixed,
    elevation: 8,
    selectedItemColor: CustomColors.light.primary,
    unselectedItemColor: CustomColors.light.subtext,
    showUnselectedLabels: true,
    showSelectedLabels: true,
    unselectedLabelStyle: CustomTextTheme.light.labelSmall,
  );

  //- dark theme
  static BottomNavigationBarThemeData dark = BottomNavigationBarThemeData(
    backgroundColor: CustomColors.dark.surface,
    type: BottomNavigationBarType.fixed,
    elevation: 8,
    selectedItemColor: CustomColors.dark.primary,
    unselectedItemColor: CustomColors.dark.subtext,
    showUnselectedLabels: true,
    showSelectedLabels: true,
    unselectedLabelStyle: CustomTextTheme.light.labelSmall,
  );
}
