import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import 'colors/app_colors.dart';
import 'custom_theme/custom_bottom_navigation_bar_theme.dart';
import 'custom_theme/custom_date_picker_theme.dart';
import 'custom_theme/custome_app_bar_theme.dart';

// Font size constants
const double kFontSizeDisplayLarge = 57.0;
const double kFontSizeDisplayMedium = 45.0;
const double kFontSizeDisplaySmall = 36.0;

const double kFontSizeHeadlineLarge = 32.0;
const double kFontSizeHeadlineMedium = 28.0;
const double kFontSizeHeadlineSmall = 24.0;

const double kFontSizeTitleLarge = 22.0;
const double kFontSizeTitleMedium = 16.0;
const double kFontSizeTitleSmall = 14.0;

const double kFontSizeBodyLarge = 16.0;
const double kFontSizeBodyMedium = 14.0;
const double kFontSizeBodySmall = 12.0;

const double kFontSizeLabelLarge = 14.0;
const double kFontSizeLabelMedium = 12.0;
const double kFontSizeLabelSmall = 10.0;

class AppTheme {
  static ThemeData light = ThemeData(
    useMaterial3: true,
    hoverColor: Colors.black,
    primaryColor: CustomColors.light.primary,
    navigationBarTheme: NavigationBarThemeData(
      backgroundColor: CustomColors.light.surface,
    ),
    colorScheme: ColorScheme.fromSeed(
      seedColor: CustomColors.light.primary,
      outlineVariant: Colors.grey,
    ),
    scaffoldBackgroundColor: CustomColors.light.background,
    fontFamily: GoogleFonts.poppins().fontFamily,
    iconTheme: IconThemeData(color: CustomColors.light.subtext),
    textTheme: _buildTextTheme(Brightness.light),
    // dialogBackgroundColor: CustomColors.light.surface,
    // dialogTheme: DialogTheme(
    //   backgroundColor: CustomColors.light.surface,
    //   shape: RoundedRectangleBorder(
    //     borderRadius: BorderRadius.circular(16),
    //   ),
    // ),
    dialogTheme: DialogThemeData(
      // Changed from DialogTheme to DialogThemeData
      backgroundColor: CustomColors.light.surface,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    ),
    unselectedWidgetColor: Colors.black,
    dividerColor: Colors.grey,
    cardColor: CustomColors.light.card,
    bottomSheetTheme: BottomSheetThemeData(
      backgroundColor: CustomColors.light.surface,
    ),
    // inputDecorationTheme: CustomInputDecorationTheme.light,
    bottomNavigationBarTheme: CustomBottomNavigationBarTheme.light,
    datePickerTheme: CustomDatePickerTheme.light,
    appBarTheme: CustomAppBarTheme.light,
    // appBarTheme: AppBarTheme(
    //   systemOverlayStyle: SystemUiOverlayStyle(
    //     statusBarIconBrightness: Brightness.light,
    //     statusBarColor: CustomColors.light.primary,
    //   ),
    // ),
    checkboxTheme: CheckboxThemeData(
      checkColor: WidgetStateProperty.all(Colors.white),
      fillColor: WidgetStateProperty.all(Colors.white),
    ),
    cardTheme: CardThemeData(
      color: CustomColors.light.card,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    ),
    pageTransitionsTheme: const PageTransitionsTheme(
      builders: <TargetPlatform, PageTransitionsBuilder>{
        TargetPlatform.android: OpenUpwardsPageTransitionsBuilder(),
        TargetPlatform.linux: OpenUpwardsPageTransitionsBuilder(),
        TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
      },
    ),
    extensions: <ThemeExtension<CustomColors>>[CustomColors.light],
  );

  static ThemeData dark = ThemeData(
    useMaterial3: true,
    hoverColor: Colors.white,
    primaryColor: CustomColors.light.primary,
    colorScheme: ColorScheme.fromSeed(
      seedColor: CustomColors.light.primary,
      outlineVariant: Colors.grey.withValues(alpha: 0.4),
      brightness: Brightness.dark,
    ),
    // scaffoldBackgroundColor: const Color(0xff181D2D),
    scaffoldBackgroundColor: CustomColors.dark.background,
    fontFamily: GoogleFonts.poppins().fontFamily,

    // inputDecorationTheme: CustomInputDecorationTheme.dark,
    iconTheme: const IconThemeData(color: Colors.white),
    textTheme: _buildTextTheme(Brightness.dark),
    // dialogBackgroundColor: const Color(0xff181D2D),
    // dialogBackgroundColor: CustomColors.light.surface,
    // dialogTheme: DialogTheme(
    //   backgroundColor: CustomColors.dark.surface,
    //   shape: RoundedRectangleBorder(
    //     borderRadius: BorderRadius.circular(16),
    //   ),
    // ),
    dialogTheme: DialogThemeData(
      // Changed from DialogTheme to DialogThemeData
      backgroundColor: CustomColors.dark.surface,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    ),
    unselectedWidgetColor: Colors.white60,
    cardColor: CustomColors.dark.card,
    bottomSheetTheme: const BottomSheetThemeData(
      backgroundColor: Color(0xff181D2D),
    ),
    // cardColor: const Color(0xff1D2335),
    bottomNavigationBarTheme: CustomBottomNavigationBarTheme.dark,
    datePickerTheme: CustomDatePickerTheme.dark,
    appBarTheme: CustomAppBarTheme.dark,
    checkboxTheme: CheckboxThemeData(
      checkColor: WidgetStateProperty.all(Colors.white),
      fillColor: WidgetStateProperty.all(Colors.white),
    ),
    cardTheme: CardThemeData(
      color: CustomColors.dark.card,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    ),
    pageTransitionsTheme: const PageTransitionsTheme(
      builders: <TargetPlatform, PageTransitionsBuilder>{
        TargetPlatform.android: OpenUpwardsPageTransitionsBuilder(),
        TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        TargetPlatform.linux: OpenUpwardsPageTransitionsBuilder(),
      },
    ),
    extensions: <ThemeExtension<CustomColors>>[CustomColors.dark],
  );

  static TextTheme _buildTextTheme(Brightness brightness) {
    final base = GoogleFonts.poppinsTextTheme();
    final color = brightness == Brightness.dark
        ? CustomColors.dark.text
        : CustomColors.light.text;

    return base.copyWith(
      displayLarge: GoogleFonts.poppins(
        fontSize: kFontSizeDisplayLarge,
        fontWeight: FontWeight.bold,
        color: color,
      ),
      displayMedium: GoogleFonts.poppins(
        fontSize: kFontSizeDisplayMedium,
        fontWeight: FontWeight.w600,
        color: color,
      ),
      displaySmall: GoogleFonts.poppins(
        fontSize: kFontSizeDisplaySmall,
        fontWeight: FontWeight.w500,
        color: color,
      ),
      headlineLarge: GoogleFonts.poppins(
        fontSize: kFontSizeHeadlineLarge,
        fontWeight: FontWeight.bold,
        color: color,
      ),
      headlineMedium: GoogleFonts.poppins(
        fontSize: kFontSizeHeadlineMedium,
        fontWeight: FontWeight.w600,
        color: color,
      ),
      headlineSmall: GoogleFonts.poppins(
        fontSize: kFontSizeHeadlineSmall,
        fontWeight: FontWeight.w500,
        color: color,
      ),
      titleLarge: GoogleFonts.poppins(
        fontSize: kFontSizeTitleLarge,
        fontWeight: FontWeight.w500,
        color: color,
      ),
      titleMedium: GoogleFonts.poppins(
        fontSize: kFontSizeTitleMedium,
        fontWeight: FontWeight.w400,
        color: color,
      ),
      titleSmall: GoogleFonts.poppins(
        fontSize: kFontSizeTitleSmall,
        fontWeight: FontWeight.w300,
        color: color,
      ),
      bodyLarge: GoogleFonts.poppins(
        fontSize: kFontSizeBodyLarge,
        fontWeight: FontWeight.normal,
        color: color,
      ),
      bodyMedium: GoogleFonts.poppins(
        fontSize: kFontSizeBodyMedium,
        fontWeight: FontWeight.normal,
        color: color,
      ),
      bodySmall: GoogleFonts.poppins(
        fontSize: kFontSizeBodySmall,
        fontWeight: FontWeight.w300,
        color: color,
      ),
      labelLarge: GoogleFonts.poppins(
        fontSize: kFontSizeLabelLarge,
        fontWeight: FontWeight.bold,
        color: color,
      ),
      labelMedium: GoogleFonts.poppins(
        fontSize: kFontSizeLabelMedium,
        fontWeight: FontWeight.w500,
        color: color,
      ),
      labelSmall: GoogleFonts.poppins(
        fontSize: kFontSizeLabelSmall,
        fontWeight: FontWeight.w400,
        color: color,
      ),
    );
  }
}
