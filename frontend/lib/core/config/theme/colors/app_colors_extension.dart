import 'package:flutter/material.dart';

import 'app_colors.dart';

extension AppColorsExtension on BuildContext {
  // ignore: library_private_types_in_public_api
  _AppColorsProxy get appColors => _AppColorsProxy(this);
}

class _AppColorsProxy {
  final BuildContext context;

  _AppColorsProxy(this.context);

  CustomColors get _customColors {
    return Theme.of(context).extension<CustomColors>()!;
  }

  // Primary and Secondary Colors
  Color get primaryColor => _customColors.primary;
  Color get secondaryColor => _customColors.secondary;

  // White and Black Colors
  Color get whiteColor => _customColors.white;
  Color get blackColor => _customColors.black;

  // Button Color
  Color get buttonColor => _customColors.button;

  // Background and Surface
  Color get backgroundColor => _customColors.background;
  Color get surfaceColor => _customColors.surface;

  // Text and Subtext
  Color get textColor => _customColors.text;
  Color get subtextColor => _customColors.subtext;

  // Side Menu and Card
  Color get sideMenuColor => _customColors.sideMenu;
  Color get cardColor => _customColors.card;

  // Divider and Borders
  Color get dividerColor => _customColors.divider;

  // Status Colors
  Color get errorColor => _customColors.error;
  Color get successColor => _customColors.success;
  Color get warningColor => _customColors.warning;
  Color get infoColor => _customColors.info;
  Color get loadingColor => _customColors.loading;
  Color get confirmColor => _customColors.confirmColor;

  // Loading and Link Colors
  Color get linkColor => _customColors.link;

  // Shimmer Colors
  Color get shimmerBaseColor => _customColors.shimmerBase;
  Color get shimmerHighlightColor => _customColors.shimmerHighlight;

  // Disabled Color
  Color get disabledColor => _customColors.disabled;

  // Transparent
  Color get transparentColor => _customColors.transparent;
}
