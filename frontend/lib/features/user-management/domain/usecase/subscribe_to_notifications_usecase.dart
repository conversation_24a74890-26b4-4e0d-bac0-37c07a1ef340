import 'package:frontend/core/errors/app_failure.dart';

import '../../../../core/usecase/usecase.dart';
import '../params/subscribe_notifications_params.dart';
import '../repository/user_repository.dart';

class SubscribeToNotificationsUseCase
    implements UseCase<void, SubscribeNotificationsParams> {
  final UserRepository repository;

  const SubscribeToNotificationsUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required SubscribeNotificationsParams params,
  }) async {
    return await repository.subscribeToNotifications(
      userId: params.userId,
      topic: params.topic,
    );
  }
}
