import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/change_user_role_params.dart';
import '../repository/user_repository.dart';

class ChangeUserRoleUseCase implements UseCase<String, ChangeUserRoleParams> {
  final UserRepository userRepository;

  const ChangeUserRoleUseCase({required this.userRepository});

  @override
  FutureEitherFailOr<String> call({
    required ChangeUserRoleParams params,
  }) async {
    return await userRepository.changeUserRole(
      userId: params.userId,
      newRole: params.newRole,
    );
  }
}
