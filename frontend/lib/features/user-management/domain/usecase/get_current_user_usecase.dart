// get_current_user_usecase.dart
import 'package:fpdart/fpdart.dart';

import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../../../authentication/domain/entities/user_entity.dart';
import '../repository/user_repository.dart';

class GetCurrentUserUseCase implements UseCase<UserEntity, NoParams> {
  final UserRepository repository;

  const GetCurrentUserUseCase({required this.repository});

  @override
  Future<Either<AppFailure, UserEntity>> call({NoParams? params}) async {
    return await repository.getCurrentUser();
  }
}
