import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/register_customer_params.dart';
import '../repository/user_repository.dart';

class RegisterCustomerUseCase implements UseCase<void, RegisterCustomerParams> {
  final UserRepository userRepository;

  RegisterCustomerUseCase({required this.userRepository});

  @override
  FutureEitherFailOr<void> call({
    required RegisterCustomerParams params,
  }) async {
    return await userRepository.registerCustomer(params);
  }
}
