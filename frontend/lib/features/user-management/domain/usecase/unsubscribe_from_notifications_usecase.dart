// unsubscribe_from_notifications_usecase.dart
import 'package:fpdart/fpdart.dart';

import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/unsubscribe_notifications_params.dart';
import '../repository/user_repository.dart';

class UnsubscribeFromNotificationsUseCase implements UseCase<void, UnsubscribeNotificationsParams> {
  final UserRepository repository;

  const UnsubscribeFromNotificationsUseCase({required this.repository});

  @override
  Future<Either<AppFailure, void>> call({
    required UnsubscribeNotificationsParams params,
  }) async {
    return await repository.unsubscribeFromNotifications(
      userId: params.userId,
      topic: params.topic,
    );
  }
}