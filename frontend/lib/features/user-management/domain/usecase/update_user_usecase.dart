import 'package:frontend/core/errors/app_failure.dart';

import '../../../../core/usecase/usecase.dart';
import '../params/update_user_params.dart';
import '../repository/user_repository.dart';

class UpdateUserUseCase implements UseCase<void, UpdateUserParams> {
  final UserRepository repository;

  const UpdateUserUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({required UpdateUserParams params}) async {
    return await repository.updateUser(
      userId: params.userId,
      email: params.email,
      username: params.username,
      password: params.password,
      addresses: params.addresses,
      isActive: params.isActive,
      agentMetadata: params.agentMetadata,
    );
  }
}
