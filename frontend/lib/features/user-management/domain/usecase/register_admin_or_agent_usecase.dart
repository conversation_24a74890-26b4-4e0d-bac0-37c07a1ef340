import 'package:frontend/core/errors/app_failure.dart';

import '../../../../core/usecase/usecase.dart';
import '../params/register_admin_or_agent_params.dart';
import '../repository/user_repository.dart';

class RegisterAdminOrAgentUseCase
    implements UseCase<void, RegisterAdminOrAgentParams> {
  final UserRepository repository;

  const RegisterAdminOrAgentUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required RegisterAdminOrAgentParams params,
  }) async {
    return await repository.registerAdminOrAgent(
      phone: params.phone,
      email: params.email,
      password: params.password,
      role: params.role,
      vehicleType: params.vehicleType,
      vehicleNumber: params.vehicleNumber,
    );
  }
}
