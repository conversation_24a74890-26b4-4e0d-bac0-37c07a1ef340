import 'package:frontend/core/errors/app_failure.dart';

import '../../../../core/usecase/usecase.dart';
import '../params/delete_user_params.dart';
import '../repository/user_repository.dart';

class DeleteUserUseCase implements UseCase<void, DeleteUserParams> {
  final UserRepository repository;

  const DeleteUserUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({required DeleteUserParams params}) async {
    return await repository.deleteUser(userId: params.userId);
  }
}
