import '../../../../core/enums/user_role.dart';
import '../../../../core/enums/vehicle_type.dart';
import '../../../../core/errors/app_failure.dart';
import '../../../authentication/domain/entities/user_entity.dart';
import '../params/update_user_params.dart';
import '../params/change_user_role_params.dart';
import '../params/register_customer_params.dart';

abstract class UserRepository {
  FutureEitherFailOr<void> registerAdminOrAgent({
    required String phone,
    String? email,
    required String password,
    required UserRole role,
    required VehicleType vehicleType,
    required String vehicleNumber,
  });

  FutureEitherFailOr<void> registerCustomer(RegisterCustomerParams params);

  FutureEitherFailOr<void> updateUser({
    required String userId,
    String? email,
    String? username,
    String? password,
    List<UpdateAddressParams>? addresses,
    bool? isActive,
    UpdateAgentMetadataParams? agentMetadata,
  });

  FutureEitherFailOr<void> deleteUser({required String userId});

  FutureEitherFailOr<String> changeUserRole({
    required String userId,
    required UserRole newRole,
  });

  FutureEitherFailOr<void> subscribeToNotifications({
    required String userId,
    required String topic,
  });

  FutureEitherFailOr<void> unsubscribeFromNotifications({
    required String userId,
    required String topic,
  });

  FutureEitherFailOr<List<UserEntity>> getAllUsers({
    UserRole? role,
    bool? isActive,
    int? page,
    int? limit,
    String? sortBy,
    String? sortOrder,
    String? search,
  });

  FutureEitherFailOr<UserEntity> getCurrentUser();
}
