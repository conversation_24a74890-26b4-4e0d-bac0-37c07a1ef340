import 'package:equatable/equatable.dart';
import '../../../../core/enums/vehicle_type.dart';

class UpdateUserParams extends Equatable {
  final String userId;
  final String? email;
  final String? username;
  final String? password;
  final List<UpdateAddressParams>? addresses;
  final bool? isActive;
  final UpdateAgentMetadataParams? agentMetadata;

  const UpdateUserParams({
    required this.userId,
    this.email,
    this.username,
    this.password,
    this.addresses,
    this.isActive,
    this.agentMetadata,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {};

    if (email != null) json['email'] = email;
    if (username != null) json['username'] = username;
    if (password != null) json['password'] = password;
    if (addresses != null) {
      json['addresses'] = addresses!.map((addr) => addr.toJson()).toList();
    }
    if (isActive != null) json['isActive'] = isActive;
    if (agentMetadata != null) json['agentMetadata'] = agentMetadata!.toJson();

    return json;
  }

  @override
  List<Object?> get props => [
    userId,
    email,
    username,
    password,
    addresses,
    isActive,
    agentMetadata,
  ];
}

class UpdateAddressParams extends Equatable {
  final String? tag;
  final List<double> coordinates;
  final String details;
  final String? contactPhone;

  const UpdateAddressParams({
    this.tag,
    required this.coordinates,
    required this.details,
    this.contactPhone,
  });

  Map<String, dynamic> toJson() {
    return {
      if (tag != null) 'tag': tag,
      'coordinates': coordinates,
      'details': details,
      if (contactPhone != null) 'contactPhone': contactPhone,
    };
  }

  @override
  List<Object?> get props => [tag, coordinates, details, contactPhone];
}

class UpdateAgentMetadataParams extends Equatable {
  final UpdateVehicleParams? vehicle;
  final bool? isOnDuty;
  final List<double>? lastKnownLocationCoordinates;

  const UpdateAgentMetadataParams({
    this.vehicle,
    this.isOnDuty,
    this.lastKnownLocationCoordinates,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {};

    if (vehicle != null) json['vehicle'] = vehicle!.toJson();
    if (isOnDuty != null) json['isOnDuty'] = isOnDuty;
    if (lastKnownLocationCoordinates != null) {
      json['lastKnownLocation'] = {'coordinates': lastKnownLocationCoordinates};
    }

    return json;
  }

  @override
  List<Object?> get props => [vehicle, isOnDuty, lastKnownLocationCoordinates];
}

class UpdateVehicleParams extends Equatable {
  final VehicleType? type;
  final String? number;

  const UpdateVehicleParams({this.type, this.number});

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {};

    if (type != null) json['type'] = type!.name.toUpperCase();
    if (number != null) json['number'] = number;

    return json;
  }

  @override
  List<Object?> get props => [type, number];
}
