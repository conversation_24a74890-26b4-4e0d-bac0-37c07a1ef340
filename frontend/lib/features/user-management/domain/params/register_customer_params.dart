import 'package:equatable/equatable.dart';

class RegisterCustomerParams extends Equatable {
  final String phone;
  final String? email;
  final String? username;
  final List<Map<String, dynamic>>? addresses;

  const RegisterCustomerParams({
    required this.phone,
    this.email,
    this.username,
    this.addresses,
  });

  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      'phone': phone,
      if (email != null) 'email': email,
      if (username != null) 'username': username,
      if (addresses != null) 'addresses': addresses,
    };
  }

  @override
  List<Object?> get props => [phone, email, username, addresses];
}
