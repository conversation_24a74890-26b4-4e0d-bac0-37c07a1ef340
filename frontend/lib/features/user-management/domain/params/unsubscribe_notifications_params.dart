// unsubscribe_notifications_params.dart
import 'package:equatable/equatable.dart';

class UnsubscribeNotificationsParams extends Equatable {
  final String userId;
  final String topic;

  const UnsubscribeNotificationsParams({
    required this.userId,
    required this.topic,
  });

  Map<String, dynamic> toJson() {
    return {'userId': userId, 'topic': topic};
  }

  @override
  List<Object?> get props => [userId, topic];
}
