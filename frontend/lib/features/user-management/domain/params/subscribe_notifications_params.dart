// subscribe_notifications_params.dart
import 'package:equatable/equatable.dart';

class SubscribeNotificationsParams extends Equatable {
  final String userId;
  final String topic;

  const SubscribeNotificationsParams({
    required this.userId,
    required this.topic,
  });

  Map<String, dynamic> to<PERSON>son() {
    return {'userId': userId, 'topic': topic};
  }

  @override
  List<Object?> get props => [userId, topic];
}
