import 'package:equatable/equatable.dart';
import '../../../../core/enums/user_role.dart';

class ChangeUserRoleParams extends Equatable {
  final String userId;
  final UserRole newRole;

  const ChangeUserRoleParams({
    required this.userId,
    required this.newRole,
  });

  Map<String, dynamic> toJson() {
    return {
      'newRole': newRole.name.toUpperCase(),
    };
  }

  @override
  List<Object?> get props => [userId, newRole];
}
