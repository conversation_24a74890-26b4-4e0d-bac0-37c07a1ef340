import 'package:equatable/equatable.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';

/// Response model for paginated user lists
class PaginatedUsersResponse extends Equatable {
  final List<UserEntity> users;
  final PaginationMeta pagination;

  const PaginatedUsersResponse({
    required this.users,
    required this.pagination,
  });

  @override
  List<Object?> get props => [users, pagination];
}

/// Pagination metadata
class PaginationMeta extends Equatable {
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final int itemsPerPage;
  final bool hasNextPage;
  final bool hasPreviousPage;

  const PaginationMeta({
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.itemsPerPage,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });

  @override
  List<Object?> get props => [
        currentPage,
        totalPages,
        totalItems,
        itemsPerPage,
        hasNextPage,
        hasPreviousPage,
      ];
}

/// User statistics model
class UserStatsModel extends Equatable {
  final int totalUsers;
  final int activeUsers;
  final int inactiveUsers;
  final int totalCustomers;
  final int totalAgents;
  final int totalAdmins;
  final int onDutyAgents;
  final int offDutyAgents;
  final double averageAgentRating;
  final Map<String, int> usersByDate;

  const UserStatsModel({
    required this.totalUsers,
    required this.activeUsers,
    required this.inactiveUsers,
    required this.totalCustomers,
    required this.totalAgents,
    required this.totalAdmins,
    required this.onDutyAgents,
    required this.offDutyAgents,
    required this.averageAgentRating,
    required this.usersByDate,
  });

  @override
  List<Object?> get props => [
        totalUsers,
        activeUsers,
        inactiveUsers,
        totalCustomers,
        totalAgents,
        totalAdmins,
        onDutyAgents,
        offDutyAgents,
        averageAgentRating,
        usersByDate,
      ];
}

/// Agent performance model
class AgentPerformanceModel extends Equatable {
  final String agentId;
  final String phone;
  final double rating;
  final int totalDeliveries;
  final int completedDeliveries;
  final int cancelledDeliveries;
  final double totalEarnings;
  final double averageDeliveryTime;
  final int totalWorkingHours;
  final DateTime lastActiveDate;

  const AgentPerformanceModel({
    required this.agentId,
    required this.phone,
    required this.rating,
    required this.totalDeliveries,
    required this.completedDeliveries,
    required this.cancelledDeliveries,
    required this.totalEarnings,
    required this.averageDeliveryTime,
    required this.totalWorkingHours,
    required this.lastActiveDate,
  });

  @override
  List<Object?> get props => [
        agentId,
        phone,
        rating,
        totalDeliveries,
        completedDeliveries,
        cancelledDeliveries,
        totalEarnings,
        averageDeliveryTime,
        totalWorkingHours,
        lastActiveDate,
      ];
}

/// Bulk operation result
class BulkOperationResult extends Equatable {
  final int totalProcessed;
  final int successful;
  final int failed;
  final List<String> failedUserIds;
  final List<String> errors;

  const BulkOperationResult({
    required this.totalProcessed,
    required this.successful,
    required this.failed,
    required this.failedUserIds,
    required this.errors,
  });

  @override
  List<Object?> get props => [
        totalProcessed,
        successful,
        failed,
        failedUserIds,
        errors,
      ];
}

/// Notification subscription result
class NotificationSubscriptionResult extends Equatable {
  final bool success;
  final String message;
  final List<String> subscribedTopics;

  const NotificationSubscriptionResult({
    required this.success,
    required this.message,
    required this.subscribedTopics,
  });

  @override
  List<Object?> get props => [success, message, subscribedTopics];
}

/// User activity model
class UserActivityModel extends Equatable {
  final String userId;
  final String activityType;
  final String description;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  const UserActivityModel({
    required this.userId,
    required this.activityType,
    required this.description,
    required this.timestamp,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        userId,
        activityType,
        description,
        timestamp,
        metadata,
      ];
}

/// User search result
class UserSearchResult extends Equatable {
  final List<UserEntity> users;
  final int totalResults;
  final String query;
  final List<String> suggestions;

  const UserSearchResult({
    required this.users,
    required this.totalResults,
    required this.query,
    required this.suggestions,
  });

  @override
  List<Object?> get props => [users, totalResults, query, suggestions];
}

/// Agent availability model
class AgentAvailabilityModel extends Equatable {
  final String agentId;
  final bool isOnDuty;
  final LocationEntity? currentLocation;
  final DateTime? lastLocationUpdate;
  final int activeOrders;
  final double distanceFromCustomer;

  const AgentAvailabilityModel({
    required this.agentId,
    required this.isOnDuty,
    this.currentLocation,
    this.lastLocationUpdate,
    required this.activeOrders,
    required this.distanceFromCustomer,
  });

  @override
  List<Object?> get props => [
        agentId,
        isOnDuty,
        currentLocation,
        lastLocationUpdate,
        activeOrders,
        distanceFromCustomer,
      ];
}

/// User validation result
class UserValidationResult extends Equatable {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const UserValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  @override
  List<Object?> get props => [isValid, errors, warnings];
}

/// User export model
class UserExportModel extends Equatable {
  final String exportId;
  final String format; // 'csv', 'excel', 'pdf'
  final String downloadUrl;
  final DateTime expiresAt;
  final int totalRecords;

  const UserExportModel({
    required this.exportId,
    required this.format,
    required this.downloadUrl,
    required this.expiresAt,
    required this.totalRecords,
  });

  @override
  List<Object?> get props => [
        exportId,
        format,
        downloadUrl,
        expiresAt,
        totalRecords,
      ];
}
