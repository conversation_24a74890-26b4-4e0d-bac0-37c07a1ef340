class OrderItemEntity {
  final String itemType;
  final String itemId;
  final int quantity;
  final String id;
  final Map<String, dynamic>? itemDetails; // Populated product details

  OrderItemEntity({
    required this.itemType,
    required this.itemId,
    required this.quantity,
    required this.id,
    this.itemDetails,
  });

  Map<String, dynamic> toJson() {
    final json = {'itemType': itemType, 'itemId': itemId, 'quantity': quantity};

    // Only include _id if it's not empty (for existing items)
    if (id.isNotEmpty) {
      json['_id'] = id;
    }

    return json;
  }

  @override
  String toString() {
    return 'OrderItemEntity(itemType: $itemType, itemId: $itemId, quantity: $quantity, id: $id, itemDetails: $itemDetails)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderItemEntity &&
        other.itemType == itemType &&
        other.itemId == itemId &&
        other.quantity == quantity &&
        other.id == id &&
        other.itemDetails == itemDetails;
  }

  @override
  int get hashCode {
    return itemType.hashCode ^
        itemId.hashCode ^
        quantity.hashCode ^
        id.hashCode ^
        itemDetails.hashCode;
  }
}
