import 'package:equatable/equatable.dart';

import '../../../../core/enums/order_status.dart';

class GetOrdersParams extends Equatable {
  final String? customerId;
  final OrderStatus? status;
  final String? paymentMethod;
  final DateTime? startDate;
  final DateTime? endDate;

  const GetOrdersParams({
    this.customerId,
    this.status,
    this.paymentMethod,
    this.startDate,
    this.endDate,
  });

  Map<String, dynamic> toJson() {
    return {
      if (customerId != null) 'customerId': customerId,
      if (status != null) 'status': status!.name,
      if (paymentMethod != null) 'paymentMethod': paymentMethod,
      if (startDate != null) 'startDate': startDate!.toIso8601String(),
      if (endDate != null) 'endDate': endDate!.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [
    customerId,
    status,
    paymentMethod,
    startDate,
    endDate,
  ];
}
