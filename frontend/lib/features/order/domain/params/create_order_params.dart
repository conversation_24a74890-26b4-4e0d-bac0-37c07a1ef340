import 'package:equatable/equatable.dart';

import '../entities/order_item_entity.dart';

class CreateOrderParams extends Equatable {
  final String? customerId;
  final List<OrderItemEntity> items;
  final String deliveryAddress;
  final String? paymentMethod;

  const CreateOrderParams({
    required this.customerId,
    required this.items,
    required this.deliveryAddress,
    this.paymentMethod,
  });

  Map<String, dynamic> toJson() {
    return {
      'customerId': customerId,
      'items': items.map((item) => item.toJson()).toList(),
      'deliveryAddress': deliveryAddress,
      'paymentMethod': paymentMethod,
    };
  }

  @override
  List<Object?> get props => [
    customerId,
    items,
    deliveryAddress,
    paymentMethod,
  ];
}
