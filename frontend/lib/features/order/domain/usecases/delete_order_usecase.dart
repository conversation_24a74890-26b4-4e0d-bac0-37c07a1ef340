import 'package:frontend/core/errors/app_failure.dart';

import '../../../../core/usecase/usecase.dart';
import '../params/delete_order_params.dart';
import '../repositories/order_repository.dart';

class DeleteOrderUseCase implements UseCase<void, DeleteOrderParams> {
  final OrderRepository repository;

  const DeleteOrderUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({required DeleteOrderParams params}) async {
    final response = await repository.deleteOrder(orderId: params.orderId);
    return response;
  }
}
