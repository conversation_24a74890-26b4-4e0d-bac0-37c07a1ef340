import 'package:frontend/core/errors/app_failure.dart';

import '../../../../core/usecase/usecase.dart';
import '../entities/order_entity.dart';
import '../params/validate_order_qrcode_params.dart';
import '../repositories/order_repository.dart';

class ValidateOrderQrCodeUseCase
    implements UseCase<OrderEntity, ValidateOrderQrCodeParams> {
  final OrderRepository repository;

  const ValidateOrderQrCodeUseCase({required this.repository});

  @override
  FutureEitherFailOr<OrderEntity> call({
    required ValidateOrderQrCodeParams params,
  }) async {
    final response = await repository.validateOrderInQRCode(
      qrCode: params.qrCode,
    );
    return response;
  }
}
