
import 'package:frontend/core/errors/app_failure.dart';

import '../../../../core/usecase/usecase.dart';
import '../params/cancel_order_params.dart';
import '../repositories/order_repository.dart';

class CancelOrderUseCase implements UseCase<void, CancelOrderParams> {
  final OrderRepository repository;

  const CancelOrderUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({required CancelOrderParams params}) async {
    final response = await repository.cancelOrder(orderId: params.orderId);
    return response;
  }
}
