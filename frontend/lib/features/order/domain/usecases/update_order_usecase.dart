
import 'package:frontend/core/errors/app_failure.dart';

import '../../../../core/usecase/usecase.dart';
import '../params/update_order_params.dart';
import '../repositories/order_repository.dart';

class UpdateOrderUseCase implements UseCase<void, UpdateOrderParams> {
  final OrderRepository repository;

  const UpdateOrderUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required UpdateOrderParams params,
  }) async {
    final response = await repository.updateOrder(
      orderId: params.orderId,
      customerId: params.customerId,
      items: params.items,
      deliveryAddress: params.deliveryAddress,
      paymentMethod: params.paymentMethod,
    );
    return response;
  }
}
