import 'package:frontend/core/errors/app_failure.dart';

import '../../../../core/usecase/usecase.dart';
import '../params/create_order_params.dart';
import '../repositories/order_repository.dart';

class CreateOrderUseCase implements UseCase<String, CreateOrderParams> {
  final OrderRepository repository;

  const CreateOrderUseCase({required this.repository});

  @override
  FutureEitherFailOr<String> call({
    required CreateOrderParams params,
  }) async {
    final response = await repository.createOrder(
      customerId: params.customerId,
      items: params.items,
      deliveryAddress: params.deliveryAddress,
      paymentMethod: params.paymentMethod,
    );
    return response;
  }
}
