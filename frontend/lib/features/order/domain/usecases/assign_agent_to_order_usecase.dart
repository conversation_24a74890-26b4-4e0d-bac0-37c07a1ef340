
import 'package:frontend/core/errors/app_failure.dart';

import '../../../../core/usecase/usecase.dart';
import '../params/assign_agent_to_order_params.dart';
import '../repositories/order_repository.dart';

class AssignAgentToOrderUseCase
    implements UseCase<void, AssignAgentToOrderParams> {
  final OrderRepository repository;

  const AssignAgentToOrderUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required AssignAgentToOrderParams params,
  }) async {
    final response = await repository.assignAgentToOrder(
      orderId: params.orderId,
      agentId: params.agentId,
    );
    return response;
  }
}
