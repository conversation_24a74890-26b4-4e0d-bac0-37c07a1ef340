
import 'package:frontend/core/errors/app_failure.dart';

import '../../../../core/usecase/usecase.dart';
import '../entities/order_entity.dart';
import '../params/get_orders_params.dart';
import '../repositories/order_repository.dart';

class GetOrdersUseCase implements UseCase<List<OrderEntity>, GetOrdersParams> {
  final OrderRepository repository;

  const GetOrdersUseCase({required this.repository});

  @override
  FutureEitherFailOr<List<OrderEntity>> call({
    required GetOrdersParams params,
  }) async {
    final response = await repository.getOrders(
      customerId: params.customerId,
      status: params.status,
      paymentMethod: params.paymentMethod,
      startDate: params.startDate,
      endDate: params.endDate,
    );
    return response;
  }
}
