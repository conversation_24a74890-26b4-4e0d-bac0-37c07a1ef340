import 'package:frontend/core/errors/app_failure.dart';

import '../../../../core/usecase/usecase.dart';
import '../entities/order_entity.dart';
import '../params/regenerate_qr_code_params.dart';
import '../repositories/order_repository.dart';

class RegenerateQRCodeUseCase
    implements UseCase<OrderEntity, RegenerateQRCodeParams> {
  final OrderRepository repository;

  const RegenerateQRCodeUseCase({required this.repository});

  @override
  FutureEitherFailOr<OrderEntity> call({
    required RegenerateQRCodeParams params,
  }) async {
    final response = await repository.regenerateQRCode(
      orderId: params.orderId,
    );
    return response;
  }
}
