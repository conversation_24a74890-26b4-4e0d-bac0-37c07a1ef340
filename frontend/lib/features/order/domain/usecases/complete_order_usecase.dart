
import 'package:frontend/core/errors/app_failure.dart';

import '../../../../core/usecase/usecase.dart';
import '../params/complete_order_params.dart';
import '../repositories/order_repository.dart';

class CompleteOrderUseCase implements UseCase<void, CompleteOrderParams> {
  final OrderRepository repository;

  const CompleteOrderUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({required CompleteOrderParams params}) async {
    final response = await repository.completeOrder(orderId: params.orderId);
    return response;
  }
}
