
import '../../domain/entities/order_entity.dart';
import '../models/order_model.dart';

class OrderMapper {
  static OrderEntity toEntity(OrderModel model) {
    return OrderEntity(
      id: model.id,
      customer: model.customer,
      payment: model.payment,
      items: model.items,
      totalAmount: model.totalAmount,
      status: model.status,
      deliveryAddress: model.deliveryAddress,
      paymentMethod: model.paymentMethod,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      deliveredAt: model.deliveredAt,
      cancelledAt: model.cancelledAt,
      failedAt: model.failedAt,
      failureReason: model.failureReason,
      qrCode: model.qrCode,
      qrCodeExpiresAt: model.qrCodeExpiresAt,
      deliveryAgent: model.deliveryAgent,
      verificationAttempts: model.verificationAttempts,
      assignedAt: model.assignedAt,
      verifiedAt: model.verifiedAt,
    );
  }

  static OrderModel toModel(OrderEntity entity) {
    return OrderModel(
      id: entity.id,
      customer: entity.customer,
      payment: entity.payment,
      items: entity.items,
      totalAmount: entity.totalAmount,
      status: entity.status,
      deliveryAddress: entity.deliveryAddress,
      paymentMethod: entity.paymentMethod,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deliveredAt: entity.deliveredAt,
      cancelledAt: entity.cancelledAt,
      failedAt: entity.failedAt,
      failureReason: entity.failureReason,
      qrCode: entity.qrCode,
      qrCodeExpiresAt: entity.qrCodeExpiresAt,
      deliveryAgent: entity.deliveryAgent,
      verificationAttempts: entity.verificationAttempts,
      assignedAt: entity.assignedAt,
      verifiedAt: entity.verifiedAt,
    );
  }

  static List<OrderEntity> toEntityList(List<OrderModel> models) {
    return models.map((model) => toEntity(model)).toList();
  }

  static List<OrderModel> toModelList(List<OrderEntity> entities) {
    return entities.map((entity) => toModel(entity)).toList();
  }
}
