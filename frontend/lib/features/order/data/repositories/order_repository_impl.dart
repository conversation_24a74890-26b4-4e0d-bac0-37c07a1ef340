// import 'package:fpdart/fpdart.dart';
// import 'package:frontend/features/order/domain/repositories/order_repository.dart';

import 'package:fpdart/fpdart.dart';

import '../../../../core/enums/order_status.dart';
import '../../../../core/errors/app_failure.dart';
import '../../domain/entities/order_entity.dart';
import '../../domain/entities/order_item_entity.dart';
import '../../domain/repositories/order_repository.dart';
import '../mappers/order_mapper.dart';
import '../source/order_remote_datasource.dart';

class OrderRepositoryImpl implements OrderRepository {
  final OrderRemoteDataSource orderRemoteDataSource;

  const OrderRepositoryImpl({required this.orderRemoteDataSource});

  @override
  FutureEitherFailOr<String> createOrder({
    required String? customerId,
    required List<OrderItemEntity> items,
    required String deliveryAddress,
    String? paymentMethod,
  }) async {
    final response = await orderRemoteDataSource.createOrder(
      customerId: customerId,
      items: items,
      deliveryAddress: deliveryAddress,
      paymentMethod: paymentMethod,
    );
    return response.fold((failure) => left(failure), (message) {
      return right(message);
    });
  }

  @override
  FutureEitherFailOr<List<OrderEntity>> getOrders({
    String? customerId,
    OrderStatus? status,
    String? paymentMethod,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final response = await orderRemoteDataSource.getOrders(
      customerId: customerId,
      status: status,
      paymentMethod: paymentMethod,
      startDate: startDate,
      endDate: endDate,
    );
    return response.fold((failure) => left(failure), (orderModels) {
      final orderEntities = OrderMapper.toEntityList(orderModels);
      return right(orderEntities);
    });
  }

  @override
  FutureEitherFailOr<void> assignAgentToOrder({
    required String orderId,
    required String agentId,
  }) async {
    final response = await orderRemoteDataSource.assignAgentToOrder(
      orderId: orderId,
      agentId: agentId,
    );
    return response;
  }

  @override
  FutureEitherFailOr<void> cancelOrder({required String orderId}) async {
    final response = await orderRemoteDataSource.cancelOrder(orderId: orderId);
    return response;
  }

  @override
  FutureEitherFailOr<void> completeOrder({required String orderId}) async {
    final response = await orderRemoteDataSource.completeOrder(
      orderId: orderId,
    );
    return response;
  }

  @override
  FutureEitherFailOr<OrderEntity> regenerateQRCode({
    required String orderId,
  }) async {
    final response = await orderRemoteDataSource.regenerateQRCode(
      orderId: orderId,
    );
    return response.fold((failure) => left(failure), (orderModel) {
      final orderEntity = OrderMapper.toEntity(orderModel);
      return right(orderEntity);
    });
  }

  @override
  FutureEitherFailOr<OrderEntity> validateOrderInQRCode({
    required String qrCode,
  }) async {
    final response = await orderRemoteDataSource.validateOrderInQRCode(
      qrCode: qrCode,
    );
    return response.fold((failure) => left(failure), (orderModel) {
      final orderEntity = OrderMapper.toEntity(orderModel);
      return right(orderEntity);
    });
  }

  @override
  FutureEitherFailOr<void> updateOrder({
    required String orderId,
    String? customerId,
    List<OrderItemEntity>? items,
    String? deliveryAddress,
    String? paymentMethod,
  }) async {
    final response = await orderRemoteDataSource.updateOrder(
      orderId: orderId,
      customerId: customerId,
      items: items,
      deliveryAddress: deliveryAddress,
      paymentMethod: paymentMethod,
    );
    return response;
  }

  @override
  FutureEitherFailOr<void> deleteOrder({required String orderId}) async {
    final response = await orderRemoteDataSource.deleteOrder(orderId: orderId);
    return response;
  }
}
