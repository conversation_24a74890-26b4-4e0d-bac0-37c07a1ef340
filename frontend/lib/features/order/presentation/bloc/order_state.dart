part of 'order_bloc.dart';

sealed class OrderState extends Equatable {
  const OrderState();

  @override
  List<Object?> get props => [];
}

final class OrderInitial extends OrderState {}

/// Create Order States
class CreateOrderLoading extends OrderState {}

class CreateOrderSuccess extends OrderState {
  final String message;

  const CreateOrderSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class CreateOrderFailure extends OrderState {
  final AppFailure appFailure;

  const CreateOrderFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Get Orders States
class GetOrdersLoading extends OrderState {}

class GetOrdersSuccess extends OrderState {
  final List<OrderEntity> orders;

  const GetOrdersSuccess({required this.orders});

  @override
  List<Object?> get props => [orders];
}

class GetOrdersFailure extends OrderState {
  final AppFailure appFailure;

  const GetOrdersFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Assign Agent to Order States
class AssignAgentToOrderLoading extends OrderState {}

class AssignAgentToOrderSuccess extends OrderState {
  final String message;

  const AssignAgentToOrderSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class AssignAgentToOrderFailure extends OrderState {
  final AppFailure appFailure;

  const AssignAgentToOrderFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Cancel Order States
class CancelOrderLoading extends OrderState {}

class CancelOrderSuccess extends OrderState {
  final String message;

  const CancelOrderSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class CancelOrderFailure extends OrderState {
  final AppFailure appFailure;

  const CancelOrderFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Complete Order States
class CompleteOrderLoading extends OrderState {}

class CompleteOrderSuccess extends OrderState {
  final String message;

  const CompleteOrderSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class CompleteOrderFailure extends OrderState {
  final AppFailure appFailure;

  const CompleteOrderFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Regenerate QR Code States
class RegenerateQRCodeLoading extends OrderState {}

class RegenerateQRCodeSuccess extends OrderState {
  final OrderEntity order;
  final String message;

  const RegenerateQRCodeSuccess({required this.order, required this.message});

  @override
  List<Object?> get props => [order, message];
}

class RegenerateQRCodeFailure extends OrderState {
  final AppFailure appFailure;

  const RegenerateQRCodeFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Validate Order QR Code States
class ValidateOrderQrCodeLoading extends OrderState {}

class ValidateOrderQrCodeSuccess extends OrderState {
  final OrderEntity order;

  const ValidateOrderQrCodeSuccess({required this.order});

  @override
  List<Object?> get props => [order];
}

class ValidateOrderQrCodeFailure extends OrderState {
  final AppFailure appFailure;

  const ValidateOrderQrCodeFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Update Order States
class UpdateOrderLoading extends OrderState {}

class UpdateOrderSuccess extends OrderState {
  final String message;

  const UpdateOrderSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class UpdateOrderFailure extends OrderState {
  final AppFailure appFailure;

  const UpdateOrderFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Delete Order States
class DeleteOrderLoading extends OrderState {}

class DeleteOrderSuccess extends OrderState {
  final String message;

  const DeleteOrderSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class DeleteOrderFailure extends OrderState {
  final AppFailure appFailure;

  const DeleteOrderFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}
