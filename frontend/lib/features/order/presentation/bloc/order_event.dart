part of 'order_bloc.dart';

sealed class OrderEvent extends Equatable {
  const OrderEvent();

  @override
  List<Object?> get props => [];
}

final class CreateOrderEvent extends OrderEvent {
  final String? customerId;
  final List<OrderItemEntity> items;
  final String deliveryAddress;
  final String? paymentMethod;

  const CreateOrderEvent({
    required this.customerId,
    required this.items,
    required this.deliveryAddress,
    this.paymentMethod,
  });

  @override
  List<Object?> get props => [
    customerId,
    items,
    deliveryAddress,
    paymentMethod,
  ];
}

final class GetOrdersEvent extends OrderEvent {
  final String? customerId;
  final OrderStatus? status;
  final String? paymentMethod;
  final DateTime? startDate;
  final DateTime? endDate;

  const GetOrdersEvent({
    this.customerId,
    this.status,
    this.paymentMethod,
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [
    customerId,
    status,
    paymentMethod,
    startDate,
    endDate,
  ];
}

final class AssignAgentToOrderEvent extends OrderEvent {
  final String orderId;
  final String agentId;

  const AssignAgentToOrderEvent({required this.orderId, required this.agentId});

  @override
  List<Object?> get props => [orderId, agentId];
}

final class CancelOrderEvent extends OrderEvent {
  final String orderId;

  const CancelOrderEvent({required this.orderId});

  @override
  List<Object?> get props => [orderId];
}

final class CompleteOrderEvent extends OrderEvent {
  final String orderId;

  const CompleteOrderEvent({required this.orderId});

  @override
  List<Object?> get props => [orderId];
}

final class RegenerateQRCodeEvent extends OrderEvent {
  final String orderId;

  const RegenerateQRCodeEvent({required this.orderId});

  @override
  List<Object?> get props => [orderId];
}

final class ValidateOrderQrCodeEvent extends OrderEvent {
  final String qrCode;

  const ValidateOrderQrCodeEvent({required this.qrCode});

  @override
  List<Object?> get props => [qrCode];
}

final class UpdateOrderEvent extends OrderEvent {
  final String orderId;
  final String? customerId;
  final List<OrderItemEntity>? items;
  final String? deliveryAddress;
  final String? paymentMethod;

  const UpdateOrderEvent({
    required this.orderId,
    this.customerId,
    this.items,
    this.deliveryAddress,
    this.paymentMethod,
  });

  @override
  List<Object?> get props => [
    orderId,
    customerId,
    items,
    deliveryAddress,
    paymentMethod,
  ];
}

final class DeleteOrderEvent extends OrderEvent {
  final String orderId;

  const DeleteOrderEvent({required this.orderId});

  @override
  List<Object?> get props => [orderId];
}
