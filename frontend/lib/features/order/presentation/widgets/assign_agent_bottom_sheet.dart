import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/button_state.dart';
import 'package:frontend/core/enums/selection_type.dart';
import 'package:frontend/core/enums/user_role.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/core/utils/helpers/snack_bar_helper.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';
import 'package:frontend/features/order/domain/entities/order_entity.dart';
import 'package:frontend/features/order/presentation/bloc/order_bloc.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_button.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_selected_filed_displayer.dart';
import 'package:frontend/features/user-management/presentation/bloc/user_bloc.dart';

class AssignAgentBottomSheet extends StatefulWidget {
  final OrderEntity order;
  final VoidCallback? onAssignmentComplete;

  const AssignAgentBottomSheet({
    super.key,
    required this.order,
    this.onAssignmentComplete,
  });

  @override
  State<AssignAgentBottomSheet> createState() => _AssignAgentBottomSheetState();
}

class _AssignAgentBottomSheetState extends State<AssignAgentBottomSheet> {
  List<UserEntity> _availableAgents = [];
  List<UserEntity> _selectedAgents = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _fetchAvailableAgents();
  }

  void _fetchAvailableAgents() {
    // Fetch agents using the user bloc
    context.userBloc.add(
      const GetAllUsersEvent(
        role: UserRole.agent,
        // isActive: true,// Get all active agents
        limit: 100, // Get all active agents
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        children: [
          _buildHeader(context),
          Expanded(
            child: MultiBlocListener(
              listeners: [
                BlocListener<UserBloc, UserState>(
                  listener: _handleUserBlocState,
                ),
                BlocListener<OrderBloc, OrderState>(
                  listener: _handleOrderBlocState,
                ),
              ],
              child: _buildContent(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: context.appColors.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.assignment_ind,
                color: context.appColors.primaryColor,
                size: 24.sp,
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Text(
                  'Assign Agent to Order',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: context.appColors.primaryColor,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: Icon(Icons.close, color: context.appColors.primaryColor),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          _buildOrderInfo(context),
        ],
      ),
    );
  }

  Widget _buildOrderInfo(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Details',
            style: Theme.of(
              context,
            ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              Icon(Icons.receipt, size: 16.sp, color: Colors.grey.shade600),
              SizedBox(width: 8.w),
              Text(
                'Order ID: ${widget.order.id.substring(0, 8)}...',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Row(
            children: [
              Icon(
                Icons.attach_money,
                size: 16.sp,
                color: Colors.grey.shade600,
              ),
              SizedBox(width: 8.w),
              Text(
                'Amount: \$${widget.order.totalAmount.toStringAsFixed(2)}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Row(
            children: [
              Icon(Icons.location_on, size: 16.sp, color: Colors.grey.shade600),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  'Address: ${widget.order.deliveryAddress}',
                  style: Theme.of(context).textTheme.bodySmall,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Padding(
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select Agent',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          if (_availableAgents.isNotEmpty) ...[
            CustomSelectFieldDisplayer<UserEntity>(
              labelText: 'Choose an agent for this order',
              selectedItems: _selectedAgents,
              selectionType: SelectionType.singleSelection,
              displayItem: _getAgentDisplayName,
              displaySubTitle: _getAgentSubtitle,
              displayImage: (user) =>
                  'https://cdn-icons-png.flaticon.com/128/16856/16856078.png',
              onSelectionChanged: _onAgentSelectionChanged,
              options: _availableAgents,
              bottomSheetTitle: 'Select Delivery Agent',
              validator: (value) {
                if (_selectedAgents.isEmpty) {
                  return 'Please select an agent';
                }
                return null;
              },
            ),
            SizedBox(height: 24.h),
            if (_selectedAgents.isNotEmpty) _buildSelectedAgentInfo(context),
            const Spacer(),
            _buildAssignButton(context),
          ] else
            _buildEmptyState(context),
        ],
      ),
    );
  }

  Widget _buildSelectedAgentInfo(BuildContext context) {
    final selectedAgent = _selectedAgents.first;
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: context.appColors.primaryColor.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Selected Agent',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: context.appColors.primaryColor,
            ),
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              CircleAvatar(
                radius: 20.r,
                backgroundColor: context.appColors.primaryColor,
                child: Icon(Icons.person, color: Colors.white, size: 20.sp),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getAgentDisplayName(selectedAgent),
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      _getAgentSubtitle(selectedAgent),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                    if (selectedAgent.agentMetadata?.rating != null)
                      Row(
                        children: [
                          Icon(Icons.star, size: 14.sp, color: Colors.amber),
                          SizedBox(width: 4.w),
                          Text(
                            selectedAgent.agentMetadata!.rating!
                                .toStringAsFixed(1),
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAssignButton(BuildContext context) {
    return BlocBuilder<OrderBloc, OrderState>(
      buildWhen: (previous, current) {
        return current is AssignAgentToOrderLoading ||
            current is AssignAgentToOrderSuccess ||
            current is AssignAgentToOrderFailure;
      },
      builder: (context, state) {
        final isAssigning = state is AssignAgentToOrderLoading;

        return CustomButton(
          buttonText: isAssigning ? 'Assigning...' : 'Assign Agent',
          onTap: _selectedAgents.isNotEmpty && !isAssigning
              ? _assignAgent
              : null,
          buttonState: isAssigning ? ButtonState.loading : ButtonState.normal,
          leadingIcon: Icon(Icons.assignment_turned_in, size: 20.sp),
          width: double.infinity,
          height: 50,
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.person_off, size: 64.sp, color: Colors.grey.shade400),
          SizedBox(height: 16.h),
          Text(
            'No Available Agents',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(color: Colors.grey.shade600),
          ),
          SizedBox(height: 8.h),
          Text(
            'There are no active agents available for assignment at the moment.',
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade500),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.h),
          CustomButton(
            buttonText: 'Refresh',
            onTap: _fetchAvailableAgents,
            leadingIcon: Icon(Icons.refresh, size: 20.sp),
            width: 150,
            height: 45,
          ),
        ],
      ),
    );
  }

  // Helper methods
  String _getAgentDisplayName(UserEntity agent) {
    if (agent.username != null && agent.username!.trim().isNotEmpty) {
      return agent.username!;
    }

    if (agent.email != null && agent.email!.trim().isNotEmpty) {
      return agent.email!;
    }
    return agent.phone;
  }

  String _getAgentSubtitle(UserEntity agent) {
    final parts = <String>[];

    // Add phone if email is the display name
    if (agent.email != null && agent.email!.isNotEmpty) {
      parts.add(agent.phone);
    }

    // Add vehicle info if available
    if (agent.agentMetadata?.vehicle != null) {
      final vehicle = agent.agentMetadata!.vehicle!;
      final vehicleInfo = vehicle.number != null
          ? '${vehicle.type.name.toUpperCase()} - ${vehicle.number}'
          : vehicle.type.name.toUpperCase();
      parts.add(vehicleInfo);
    }

    // Add on-duty status
    final isOnDuty = agent.agentMetadata?.isOnDuty ?? false;
    parts.add(isOnDuty ? 'On Duty' : 'Off Duty');

    return parts.join(' • ');
  }

  void _onAgentSelectionChanged(List<UserEntity> selectedAgents) {
    setState(() {
      _selectedAgents = selectedAgents;
    });
  }

  void _assignAgent() {
    if (_selectedAgents.isEmpty) return;

    final selectedAgent = _selectedAgents.first;
    context.orderBloc.add(
      AssignAgentToOrderEvent(
        orderId: widget.order.id,
        agentId: selectedAgent.id,
      ),
    );
  }

  void _handleUserBlocState(BuildContext context, UserState state) {
    if (state is GetAllUsersLoading) {
      setState(() {
        _isLoading = true;
      });
    } else if (state is GetAllUsersSuccess) {
      setState(() {
        _isLoading = false;
        _availableAgents = state.users;
        // .where(
        //   (user) =>
        //       user.role == UserRole.agent &&
        //       user.isActive &&
        //       (user.agentMetadata?.isOnDuty ?? false),
        // )
        // .toList();
      });
    } else if (state is GetAllUsersFailure) {
      setState(() {
        _isLoading = false;
      });

      SnackBarHelper.showErrorSnackBar(
        context,
        message: state.appFailure.getErrorMessage(),
      );
    }
  }

  void _handleOrderBlocState(BuildContext context, OrderState state) {
    if (state is AssignAgentToOrderSuccess) {
      SnackBarHelper.showSuccessSnackBar(context, message: state.message);

      // Call the completion callback
      widget.onAssignmentComplete?.call();

      // Close the bottom sheet
      Navigator.of(context).pop(true);
    } else if (state is AssignAgentToOrderFailure) {
      SnackBarHelper.showErrorSnackBar(
        context,
        message: state.appFailure.getErrorMessage(),
      );
    }
  }
}
