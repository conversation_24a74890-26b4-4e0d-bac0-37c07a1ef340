import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/selection_type.dart';
import 'package:frontend/core/enums/user_role.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/core/utils/helpers/snack_bar_helper.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_button.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_selected_filed_displayer.dart';
import 'package:frontend/features/user-management/presentation/bloc/user_bloc.dart';

class SelectCustomerBottomSheet extends StatefulWidget {
  final Function(UserEntity) onCustomerSelected;

  const SelectCustomerBottomSheet({
    super.key,
    required this.onCustomerSelected,
  });

  @override
  State<SelectCustomerBottomSheet> createState() =>
      _SelectCustomerBottomSheetState();
}

class _SelectCustomerBottomSheetState extends State<SelectCustomerBottomSheet> {
  List<UserEntity> _availableCustomers = [];
  List<UserEntity> _selectedCustomers = [];
  bool _isLoading = false;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fetchAvailableCustomers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _fetchAvailableCustomers() {
    // Fetch customers using the user bloc
    context.userBloc.add(
      const GetAllUsersEvent(
        role: UserRole.customer,
        isActive: true,
        limit: 100, // Get all active customers
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        children: [
          _buildHeader(context),
          Expanded(
            child: BlocListener<UserBloc, UserState>(
              listener: _handleUserBlocState,
              child: _buildContent(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: context.appColors.primaryColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40.w,
            height: 4.h,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Icon(Icons.people, color: Colors.white, size: 24.sp),
              SizedBox(width: 12.w),
              Expanded(
                child: Text(
                  'Select Customer',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: Icon(Icons.close, color: Colors.white, size: 24.sp),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Padding(
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search field
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search customers by phone, email, or username...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              filled: true,
              fillColor: context.appColors.surfaceColor,
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value.toLowerCase();
              });
            },
          ),
          SizedBox(height: 16.h),

          if (_getFilteredCustomers().isNotEmpty) ...[
            Text(
              'Available Customers (${_getFilteredCustomers().length})',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16.h),
            CustomSelectFieldDisplayer<UserEntity>(
              labelText: 'Choose a customer for this order',
              selectedItems: _selectedCustomers,
              selectionType: SelectionType.singleSelection,
              displayItem: _getCustomerDisplayName,
              displaySubTitle: _getCustomerSubtitle,
              displayImage: (user) =>
                  'https://cdn-icons-png.flaticon.com/128/3135/3135715.png',
              onSelectionChanged: _onCustomerSelectionChanged,
              options: _getFilteredCustomers(),
              bottomSheetTitle: 'Select Customer',
              validator: (value) {
                if (_selectedCustomers.isEmpty) {
                  return 'Please select a customer';
                }
                return null;
              },
            ),
            SizedBox(height: 24.h),
            if (_selectedCustomers.isNotEmpty)
              _buildSelectedCustomerInfo(context),
            const Spacer(),
            _buildSelectButton(context),
          ] else
            _buildEmptyState(context),
        ],
      ),
    );
  }

  List<UserEntity> _getFilteredCustomers() {
    if (_searchQuery.isEmpty) {
      return _availableCustomers;
    }

    return _availableCustomers.where((customer) {
      final phone = customer.phone.toLowerCase();
      final email = customer.email?.toLowerCase() ?? '';
      final username = customer.username?.toLowerCase() ?? '';

      return phone.contains(_searchQuery) ||
          email.contains(_searchQuery) ||
          username.contains(_searchQuery);
    }).toList();
  }

  Widget _buildSelectedCustomerInfo(BuildContext context) {
    final selectedCustomer = _selectedCustomers.first;
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: context.appColors.primaryColor.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Selected Customer',
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              color: context.appColors.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              CircleAvatar(
                radius: 20.r,
                backgroundColor: context.appColors.primaryColor.withValues(
                  alpha: 0.1,
                ),
                child: Icon(
                  Icons.person,
                  color: context.appColors.primaryColor,
                  size: 20.sp,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getCustomerDisplayName(selectedCustomer),
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      _getCustomerSubtitle(selectedCustomer),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: context.appColors.subtextColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSelectButton(BuildContext context) {
    return CustomButton(
      buttonText: 'Select Customer',
      onTap: _selectedCustomers.isNotEmpty ? _handleCustomerSelection : null,
      width: double.infinity,
      height: 48,
      leadingIcon: Icon(Icons.check, size: 20.sp),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.people_outline, size: 64.sp, color: Colors.grey.shade400),
          SizedBox(height: 16.h),
          Text(
            _searchQuery.isNotEmpty
                ? 'No Customers Found'
                : 'No Available Customers',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(color: Colors.grey.shade600),
          ),
          SizedBox(height: 8.h),
          Text(
            _searchQuery.isNotEmpty
                ? 'No customers match your search criteria.'
                : 'There are no active customers available at the moment.',
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade500),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.h),
          CustomButton(
            buttonText: _searchQuery.isNotEmpty ? 'Clear Search' : 'Refresh',
            onTap: _searchQuery.isNotEmpty
                ? _clearSearch
                : _fetchAvailableCustomers,
            leadingIcon: Icon(
              _searchQuery.isNotEmpty ? Icons.clear : Icons.refresh,
              size: 20.sp,
            ),
            width: 150,
            height: 45,
          ),
        ],
      ),
    );
  }

  void _clearSearch() {
    setState(() {
      _searchQuery = '';
      _searchController.clear();
    });
  }

  String _getCustomerDisplayName(UserEntity customer) {
    if (customer.username != null && customer.username!.isNotEmpty) {
      return customer.username!;
    }
    if (customer.email != null && customer.email!.isNotEmpty) {
      return customer.email!;
    }
    return customer.phone;
  }

  String _getCustomerSubtitle(UserEntity customer) {
    final parts = <String>[];

    // Add phone if email or username is the display name
    if ((customer.email != null && customer.email!.isNotEmpty) ||
        (customer.username != null && customer.username!.isNotEmpty)) {
      parts.add(customer.phone);
    }

    // Add email if username is the display name
    if (customer.username != null &&
        customer.username!.isNotEmpty &&
        customer.email != null &&
        customer.email!.isNotEmpty) {
      parts.add(customer.email!);
    }

    // Add address count if available
    if (customer.addresses.isNotEmpty) {
      parts.add(
        '${customer.addresses.length} address${customer.addresses.length > 1 ? 'es' : ''}',
      );
    }

    return parts.join(' • ');
  }

  void _onCustomerSelectionChanged(List<UserEntity> selectedCustomers) {
    setState(() {
      _selectedCustomers = selectedCustomers;
    });
  }

  void _handleCustomerSelection() {
    if (_selectedCustomers.isNotEmpty) {
      widget.onCustomerSelected(_selectedCustomers.first);
      Navigator.pop(context);
    }
  }

  void _handleUserBlocState(BuildContext context, UserState state) {
    if (state is GetAllUsersLoading) {
      setState(() {
        _isLoading = true;
      });
    } else if (state is GetAllUsersSuccess) {
      setState(() {
        _isLoading = false;
        _availableCustomers = state.users;
      });
    } else if (state is GetAllUsersFailure) {
      setState(() {
        _isLoading = false;
      });

      SnackBarHelper.showErrorSnackBar(
        context,
        message: state.appFailure.getErrorMessage(),
      );
    }
  }
}
