import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/order_status.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/features/order/domain/entities/order_entity.dart';
import 'package:frontend/features/order/presentation/bloc/order_bloc.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_list_grid_view.dart';
import 'package:frontend/core/enums/layout_type.dart';
import 'package:frontend/features/shared/presentation/widgets/qr_scanner_widget.dart';
import 'package:frontend/core/services/permission_service.dart';
import '../../../../../core/config/logger/app_logger.dart';
import '../../../../../core/utils/helpers/snack_bar_helper.dart';
import 'agent_order_completion_page.dart';

class AgentDeliveriesPage extends StatefulWidget {
  const AgentDeliveriesPage({super.key});

  @override
  State<AgentDeliveriesPage> createState() => _AgentDeliveriesPageState();
}

class _AgentDeliveriesPageState extends State<AgentDeliveriesPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadOrders();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadOrders({bool forceRefresh = false}) {
    final orderBloc = context.orderBloc;
    final canRefetch = forceRefresh || orderBloc.orders.isEmpty;
    if (!canRefetch) return;

    // Load all orders for the agent (will be filtered by backend based on agent role)
    context.read<OrderBloc>().add(const GetOrdersEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<OrderBloc, OrderState>(
      listener: (context, state) {
        if (state is ValidateOrderQrCodeSuccess) {
          // Close any open dialog first
          if (Navigator.canPop(context)) {
            Navigator.pop(context);
          }

          AppLogger().info('Order validated successfully: ${state.order.id}');

          // Show success feedback
          SnackBarHelper.showSuccessSnackBar(
            context,
            message: 'QR Code validated successfully!',
          );

          // Navigate to order completion page
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  AgentOrderCompletionPage(order: state.order),
            ),
          );
        } else if (state is ValidateOrderQrCodeFailure) {
          // Close any open dialog first
          if (Navigator.canPop(context)) {
            Navigator.pop(context);
          }

          AppLogger().error(
            'QR Code validation failed: ${state.appFailure.getErrorMessage()}',
          );

          // Show error feedback
          SnackBarHelper.showErrorSnackBar(
            context,
            message: state.appFailure.getErrorMessage(),
          );
        }
      },
      child: Scaffold(
        backgroundColor: context.appColors.backgroundColor,
        appBar: AppBar(
          backgroundColor: context.appColors.backgroundColor,
          elevation: 0,
          title: Text(
            'My Deliveries',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        body: SafeArea(
          child: RefreshIndicator(
            onRefresh: () async {
              _loadOrders(forceRefresh: true);
            },
            child: CustomScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              slivers: [
                SliverToBoxAdapter(child: _buildHeader(context)),
                SliverToBoxAdapter(child: _buildTabBar(context)),
                SliverFillRemaining(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildInProgressTab(context),
                      _buildCompletedTab(context),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        // QR Scanner
        floatingActionButton: FloatingActionButton(
          onPressed: () => _openQRScanner(context),
          backgroundColor: context.appColors.primaryColor,
          child: const Icon(Icons.qr_code_scanner, color: Colors.white),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            context.appColors.primaryColor,
            context.appColors.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Delivery Management',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'Manage your assigned deliveries',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(Icons.local_shipping, color: Colors.white, size: 32.sp),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: context.appColors.primaryColor,
        unselectedLabelColor: context.appColors.subtextColor,
        indicatorColor: context.appColors.primaryColor,
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        tabs: const [
          Tab(text: 'In Progress'),
          Tab(text: 'Completed'),
        ],
      ),
    );
  }

  Widget _buildInProgressTab(BuildContext context) {
    return BlocBuilder<OrderBloc, OrderState>(
      builder: (context, state) {
        if (state is GetOrdersLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is GetOrdersFailure) {
          return _buildErrorWidget(
            context,
            'Failed to load orders',
            () => _loadOrders(),
          );
        }

        final allOrders = context.read<OrderBloc>().orders;
        final inProgressOrders = allOrders
            .where((order) => order.status == OrderStatus.inTransit)
            .toList();

        return _buildOrdersList(context, inProgressOrders, 'in_progress');
      },
    );
  }

  Widget _buildCompletedTab(BuildContext context) {
    return BlocBuilder<OrderBloc, OrderState>(
      builder: (context, state) {
        if (state is GetOrdersLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is GetOrdersFailure) {
          return _buildErrorWidget(
            context,
            'Failed to load orders',
            () => _loadOrders(),
          );
        }

        final allOrders = context.read<OrderBloc>().orders;
        final completedOrders = allOrders
            .where((order) => order.status == OrderStatus.delivered)
            .toList();

        return _buildOrdersList(context, completedOrders, 'completed');
      },
    );
  }

  Widget _buildOrdersList(
    BuildContext context,
    List<OrderEntity> orders,
    String type,
  ) {
    return CustomListGridView<OrderEntity>(
      isEmpty: orders.isEmpty,
      isLoading: false,
      showFooter: false,
      items: orders,
      itemCount: orders.length,
      layoutType: LayoutType.listView,
      padding: EdgeInsets.all(16.w),
      itemBuilder: (context, order) => _buildOrderCard(context, order, type),
      emptyDataBuilder: () =>
          _buildEmptyWidget(context, 'No ${type.replaceAll('_', ' ')} orders'),
      onRefresh: () => _loadOrders(),
    );
  }

  Widget _buildOrderCard(BuildContext context, OrderEntity order, String type) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order Header
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: order.status.color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Order #${order.id.substring(0, 8).toUpperCase()}',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: context.appColors.textColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        _formatDate(order.createdAt),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 6.h,
                  ),
                  decoration: BoxDecoration(
                    color: order.status.color,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Text(
                    order.status.label,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Order Details
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Customer Info
                Row(
                  children: [
                    Icon(
                      Icons.person,
                      size: 16.sp,
                      color: context.appColors.primaryColor,
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Text(
                        order.customer.phone,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: context.appColors.textColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                // Delivery Address
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 16.sp,
                      color: context.appColors.primaryColor,
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Text(
                        order.deliveryAddress,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12.h),
                // Total Amount and Action Button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Total Amount',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: context.appColors.subtextColor),
                        ),
                        Text(
                          '\$${order.totalAmount.toStringAsFixed(2)}',
                          style: Theme.of(context).textTheme.bodyLarge
                              ?.copyWith(
                                color: context.appColors.primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ],
                    ),
                    _buildActionButton(context, order, type),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    OrderEntity order,
    String type,
  ) {
    switch (type) {
      case 'assigned':
        return ElevatedButton(
          onPressed: () => _startDelivery(context, order),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
          child: Text(
            'Start Delivery',
            style: TextStyle(color: Colors.white, fontSize: 12.sp),
          ),
        );
      case 'in_progress':
        return ElevatedButton(
          onPressed: () => _scanQRForCompletion(context, order),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
          child: Text(
            'Scan QR',
            style: TextStyle(color: Colors.white, fontSize: 12.sp),
          ),
        );
      case 'completed':
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Text(
            'Delivered',
            style: TextStyle(
              color: Colors.green,
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildErrorWidget(
    BuildContext context,
    String message,
    VoidCallback onRetry,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.sp, color: Colors.red),
          SizedBox(height: 16.h),
          Text(
            message,
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.red),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: onRetry,
            style: ElevatedButton.styleFrom(
              backgroundColor: context.appColors.primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: const Text('Retry', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_shipping_outlined,
            size: 64.sp,
            color: context.appColors.subtextColor,
          ),
          SizedBox(height: 16.h),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: context.appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          Text(
            'Orders will appear here when assigned to you',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: context.appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _startDelivery(BuildContext context, OrderEntity order) {
    // TODO: Implement start delivery logic
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Starting delivery for order #${order.id.substring(0, 8)}',
        ),
        backgroundColor: Colors.blue,
      ),
    );
  }

  Future<void> _openQRScanner(BuildContext context) async {
    try {
      // Check camera permission first
      final hasPermission = await PermissionService().ensureCameraPermission(
        context,
      );

      if (!hasPermission) {
        if (mounted && context.mounted) {
          SnackBarHelper.showErrorSnackBar(
            context,
            message: 'Camera permission is required to scan QR codes',
          );
        }
        return;
      }

      if (mounted && context.mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => QRScannerWidget(
              title: 'Scan Order QR Code',
              instruction:
                  'Position the QR code within the frame to scan and verify delivery',
              onQRScanned: (qrCode) => _handleQRScanned(context, qrCode),
            ),
          ),
        );
      }
    } catch (e, stackTrace) {
      AppLogger().error(
        'Error opening QR scanner: $e',
        error: e,
        stackTrace: stackTrace,
      );

      if (mounted && context.mounted) {
        SnackBarHelper.showErrorSnackBar(
          context,
          message: 'Failed to open QR scanner. Please try again.',
        );
      }
    }
  }

  Future<void> _scanQRForCompletion(
    BuildContext context,
    OrderEntity order,
  ) async {
    try {
      // Check camera permission first
      final hasPermission = await PermissionService().ensureCameraPermission(
        context,
      );

      if (!hasPermission) {
        if (mounted && context.mounted) {
          SnackBarHelper.showErrorSnackBar(
            context,
            message: 'Camera permission is required to scan QR codes',
          );
        }
        return;
      }

      if (mounted && context.mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => QRScannerWidget(
              title: 'Complete Delivery',
              instruction: 'Scan the order QR code to complete delivery',
              onQRScanned: (qrCode) =>
                  _handleQRScannedForOrder(context, qrCode, order),
            ),
          ),
        );
      }
    } catch (e, stackTrace) {
      AppLogger().error(
        'Error opening QR scanner for completion: $e',
        error: e,
        stackTrace: stackTrace,
      );

      if (mounted && context.mounted) {
        SnackBarHelper.showErrorSnackBar(
          context,
          message: 'Failed to open QR scanner. Please try again.',
        );
      }
    }
  }

  void _handleQRScanned(BuildContext context, String qrCode) {
    Navigator.pop(context); // Close scanner

    // Show loading dialog and validate QR code
    _showQRValidationDialog(context, qrCode);
  }

  void _handleQRScannedForOrder(
    BuildContext context,
    String qrCode,
    OrderEntity order,
  ) {
    Navigator.pop(context); // Close scanner

    // Show loading dialog and validate QR code for specific order
    _showQRValidationDialog(context, qrCode, specificOrder: order);
  }

  void _showQRValidationDialog(
    BuildContext context,
    String qrCode, {
    OrderEntity? specificOrder,
  }) {
    // Trigger the validation event
    context.read<OrderBloc>().add(ValidateOrderQrCodeEvent(qrCode: qrCode));

    // Show simple loading dialog - the main BlocListener will handle the response
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => AlertDialog(
        backgroundColor: context.appColors.surfaceColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(color: context.appColors.primaryColor),
            SizedBox(height: 20.h),
            Text(
              'Validating QR Code...',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Please wait while we verify the order',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: context.appColors.subtextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today ${_formatTime(date)}';
    } else if (difference.inDays == 1) {
      return 'Yesterday ${_formatTime(date)}';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  String _formatTime(DateTime date) {
    final hour = date.hour.toString().padLeft(2, '0');
    final minute = date.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
}
