import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/order_status.dart';
import 'package:frontend/features/order/domain/entities/order_entity.dart';
import 'package:frontend/features/order/presentation/bloc/order_bloc.dart';

import '../../../../../core/utils/helpers/snack_bar_helper.dart';

class AgentOrderCompletionPage extends StatefulWidget {
  final OrderEntity order;

  const AgentOrderCompletionPage({super.key, required this.order});

  @override
  State<AgentOrderCompletionPage> createState() =>
      _AgentOrderCompletionPageState();
}

class _AgentOrderCompletionPageState extends State<AgentOrderCompletionPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.backgroundColor,
        elevation: 0,
        title: Text(
          'Complete Delivery',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: Icon(Icons.arrow_back, color: context.appColors.textColor),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSuccessHeader(context),
            SizedBox(height: 24.h),
            _buildOrderDetails(context),
            SizedBox(height: 24.h),
            _buildCustomerInfo(context),
            SizedBox(height: 24.h),
            _buildDeliveryInfo(context),
            SizedBox(height: 32.h),
            _buildCompleteButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSuccessHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green, Colors.green.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(Icons.check_circle, color: Colors.white, size: 48.sp),
          ),
          SizedBox(height: 16.h),
          Text(
            'QR Code Verified!',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Customer identity confirmed. You can now complete the delivery.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetails(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Details',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Order ID',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: context.appColors.subtextColor,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      '#${widget.order.id.substring(0, 8).toUpperCase()}',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: context.appColors.textColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: widget.order.status.color,
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Text(
                  widget.order.status.label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Total Amount',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: context.appColors.subtextColor,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      '\$${widget.order.totalAmount.toStringAsFixed(2)}',
                      style: Theme.of(context).textTheme.headlineSmall
                          ?.copyWith(
                            color: context.appColors.primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'Payment Method',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: context.appColors.subtextColor,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      widget.order.paymentMethod.toUpperCase(),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: context.appColors.textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerInfo(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Customer Information',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Icon(
                Icons.person,
                color: context.appColors.primaryColor,
                size: 20.sp,
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Phone Number',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: context.appColors.subtextColor,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      widget.order.customer.phone,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: context.appColors.textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDeliveryInfo(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Delivery Information',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.location_on,
                color: context.appColors.primaryColor,
                size: 20.sp,
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Delivery Address',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: context.appColors.subtextColor,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      widget.order.deliveryAddress,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: context.appColors.textColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCompleteButton(BuildContext context) {
    return BlocConsumer<OrderBloc, OrderState>(
      listener: (context, state) {
        if (state is CompleteOrderSuccess) {
          context.read<OrderBloc>().add(const GetOrdersEvent());

          Navigator.pop(context); // Go back to main deliveries page
          SnackBarHelper.showSuccessSnackBar(
            context,
            message: 'Order completed successfully!',
          );
        } else if (state is CompleteOrderFailure) {
          SnackBarHelper.showErrorSnackBar(
            context,
            message: state.appFailure.getErrorMessage(),
          );
        }
      },
      builder: (context, state) {
        final isLoading = state is CompleteOrderLoading;

        return SizedBox(
          width: double.infinity,
          height: 56.h,
          child: ElevatedButton(
            onPressed: isLoading ? null : () => _completeOrder(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: isLoading
                ? SizedBox(
                    width: 24.w,
                    height: 24.h,
                    child: const CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: Colors.white,
                        size: 24.sp,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        'Complete Delivery',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
          ),
        );
      },
    );
  }

  void _completeOrder(BuildContext context) {
    // context.read<OrderBloc>().add(CompleteOrderEvent(orderId: widget.order.id));
    //// show confirmation dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Complete Delivery'),
        content: const Text('Are you sure you want to complete this delivery?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<OrderBloc>().add(
                CompleteOrderEvent(orderId: widget.order.id),
              );
            },
            child: const Text(
              'Complete',
              style: TextStyle(color: Colors.green),
            ),
          ),
        ],
      ),
    );
  }
}
