import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/router/extension/navigation_extension.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/vehicle_type.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_textfield.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_drop_down.dart';
import 'package:frontend/features/user-management/domain/params/update_user_params.dart';
import 'package:frontend/features/user-management/presentation/bloc/user_bloc.dart';

import 'package:frontend/features/authentication/presentation/bloc/authentication_bloc.dart';

import '../../../../../core/utils/helpers/snack_bar_helper.dart';
import '../../../../authentication/presentation/pages/login_page.dart';

class AgentProfilePage extends StatefulWidget {
  const AgentProfilePage({super.key});

  @override
  State<AgentProfilePage> createState() => _AgentProfilePageState();
}

class _AgentProfilePageState extends State<AgentProfilePage> {
  @override
  void initState() {
    super.initState();
    _refreshUserData();
  }

  void _refreshUserData({bool forceFetch = false}) {
    final userBloc = context.userBloc;
    final currentUser = userBloc.currentUser;
    final canRefetch = forceFetch || currentUser == null;
    if (!canRefetch) return;

    userBloc.add(const GetCurrentUserEvent());
    SnackBarHelper.showSuccessSnackBar(
      context,
      message: 'Refreshing profile data...',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.backgroundColor,
        elevation: 0,
        title: Text(
          'My Profile',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () => _showEditProfileDialog(context),
            icon: Icon(Icons.edit, color: context.appColors.textColor),
          ),
          IconButton(
            onPressed: () => _refreshUserData(forceFetch: true),
            icon: Icon(Icons.refresh, color: context.appColors.textColor),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProfileHeader(context),
            SizedBox(height: 24.h),
            _buildPersonalInfo(context),
            SizedBox(height: 24.h),
            _buildEmploymentInfo(context),
            SizedBox(height: 24.h),
            _buildVehicleInfo(context),
            SizedBox(height: 24.h),
            _buildWorkPreferences(context),
            SizedBox(height: 24.h),
            _buildEmergencyContacts(context),
            SizedBox(height: 24.h),
            _buildSettingsSection(context),
            SizedBox(height: 24.h),
            _buildLogoutButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader(BuildContext context) {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, userState) {
        final user = context.userBloc.currentUser;
        if (user != null) {
          final isOnDuty = user.agentMetadata?.isOnDuty ?? false;
          final rating = user.agentMetadata?.rating;

          return Container(
            padding: EdgeInsets.all(24.w),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  context.appColors.primaryColor,
                  context.appColors.primaryColor.withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Column(
              children: [
                Container(
                  width: 100.w,
                  height: 100.h,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 3.w,
                    ),
                  ),
                  child: Icon(
                    Icons.delivery_dining,
                    color: Colors.white,
                    size: 50.sp,
                  ),
                ),
                SizedBox(height: 16.h),
                Text(
                  user.username ?? user.phone,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (user.email != null) ...[
                  SizedBox(height: 4.h),
                  Text(
                    user.email!,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                ],
                SizedBox(height: 12.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 12.w,
                        vertical: 6.h,
                      ),
                      decoration: BoxDecoration(
                        color: isOnDuty
                            ? Colors.green.withValues(alpha: 0.2)
                            : Colors.red.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(16.r),
                        border: Border.all(
                          color: isOnDuty ? Colors.green : Colors.red,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 8.w,
                            height: 8.h,
                            decoration: BoxDecoration(
                              color: isOnDuty ? Colors.green : Colors.red,
                              shape: BoxShape.circle,
                            ),
                          ),
                          SizedBox(width: 6.w),
                          Text(
                            isOnDuty ? 'On Duty' : 'Off Duty',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                        ],
                      ),
                    ),
                    if (rating != null) ...[
                      SizedBox(width: 12.w),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 12.w,
                          vertical: 6.h,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.amber.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(16.r),
                          border: Border.all(color: Colors.amber),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.star, color: Colors.amber, size: 16.sp),
                            SizedBox(width: 4.w),
                            Text(
                              rating.toStringAsFixed(1),
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          );
        }

        return Container(
          padding: EdgeInsets.all(24.w),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                context.appColors.primaryColor,
                context.appColors.primaryColor.withValues(alpha: 0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Column(
            children: [
              Container(
                width: 100.w,
                height: 100.h,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 3.w,
                  ),
                ),
                child: Icon(
                  Icons.delivery_dining,
                  color: Colors.white,
                  size: 50.sp,
                ),
              ),
              SizedBox(height: 16.h),
              Text(
                'Loading...',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmploymentInfo(BuildContext context) {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, userState) {
        final user = context.userBloc.currentUser;
        if (user != null) {
          return Container(
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              color: context.appColors.surfaceColor,
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(color: context.appColors.dividerColor),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Employment Information',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 20.h),
                _buildInfoRow(
                  context,
                  'Employee ID',
                  user.id.length >= 8
                      ? user.id.substring(0, 8).toUpperCase()
                      : user.id.toUpperCase(),
                  Icons.badge,
                ),
                SizedBox(height: 16.h),
                _buildInfoRow(
                  context,
                  'Department',
                  'Delivery Services',
                  Icons.business,
                ),
                SizedBox(height: 16.h),
                if (user.createdAt != null) ...[
                  _buildInfoRow(
                    context,
                    'Start Date',
                    _formatDate(user.createdAt!),
                    Icons.calendar_today,
                  ),
                  SizedBox(height: 16.h),
                ],
                _buildInfoRow(
                  context,
                  'Employment Type',
                  'Full-time',
                  Icons.work,
                ),
                SizedBox(height: 16.h),
                _buildInfoRow(
                  context,
                  'Current Status',
                  user.agentMetadata?.isOnDuty == true ? 'On Duty' : 'Off Duty',
                  user.agentMetadata?.isOnDuty == true
                      ? Icons.work
                      : Icons.work_off,
                ),
                if (user.agentMetadata?.rating != null) ...[
                  SizedBox(height: 16.h),
                  _buildInfoRow(
                    context,
                    'Rating',
                    '${user.agentMetadata!.rating!.toStringAsFixed(1)} / 5.0',
                    Icons.star,
                  ),
                ],
              ],
            ),
          );
        }

        return Container(
          padding: EdgeInsets.all(20.w),
          decoration: BoxDecoration(
            color: context.appColors.surfaceColor,
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(color: context.appColors.dividerColor),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Employment Information',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 20.h),
              _buildInfoRow(
                context,
                'Loading...',
                'Please wait',
                Icons.hourglass_empty,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildVehicleInfo(BuildContext context) {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, userState) {
        final user = context.userBloc.currentUser;
        if (user != null) {
          final vehicle = user.agentMetadata?.vehicle;

          return Container(
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              color: context.appColors.surfaceColor,
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(color: context.appColors.dividerColor),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Vehicle Information',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 20.h),
                _buildInfoRow(
                  context,
                  'Vehicle Type',
                  vehicle?.type.name.toUpperCase() ?? 'Not assigned',
                  _getVehicleIcon(vehicle?.type),
                ),
                SizedBox(height: 16.h),
                _buildInfoRow(
                  context,
                  'Vehicle Number',
                  vehicle?.number ?? 'Not assigned',
                  Icons.confirmation_number,
                ),
                SizedBox(height: 16.h),
                if (user.agentMetadata?.lastKnownLocation != null) ...[
                  _buildInfoRow(
                    context,
                    'Last Known Location',
                    '${user.agentMetadata!.lastKnownLocation!.coordinates[1].toStringAsFixed(4)}, ${user.agentMetadata!.lastKnownLocation!.coordinates[0].toStringAsFixed(4)}',
                    Icons.location_on,
                  ),
                  SizedBox(height: 16.h),
                ],
                _buildInfoRow(
                  context,
                  'Duty Status',
                  user.agentMetadata?.isOnDuty == true
                      ? 'Currently On Duty'
                      : 'Currently Off Duty',
                  user.agentMetadata?.isOnDuty == true
                      ? Icons.work
                      : Icons.work_off,
                ),
              ],
            ),
          );
        }

        return Container(
          padding: EdgeInsets.all(20.w),
          decoration: BoxDecoration(
            color: context.appColors.surfaceColor,
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(color: context.appColors.dividerColor),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Vehicle Information',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 20.h),
              _buildInfoRow(
                context,
                'Loading...',
                'Please wait',
                Icons.hourglass_empty,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildWorkPreferences(BuildContext context) {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, userState) {
        final user = context.userBloc.currentUser;
        if (user != null) {
          return Container(
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              color: context.appColors.surfaceColor,
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(color: context.appColors.dividerColor),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Work Information',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 20.h),
                _buildInfoRow(
                  context,
                  'Work Status',
                  user.agentMetadata?.isOnDuty == true ? 'On Duty' : 'Off Duty',
                  user.agentMetadata?.isOnDuty == true
                      ? Icons.work
                      : Icons.work_off,
                ),
                SizedBox(height: 16.h),
                if (user.agentMetadata?.rating != null) ...[
                  _buildInfoRow(
                    context,
                    'Performance Rating',
                    '${user.agentMetadata!.rating!.toStringAsFixed(1)} / 5.0 ⭐',
                    Icons.star,
                  ),
                  SizedBox(height: 16.h),
                ],
                _buildInfoRow(
                  context,
                  'Account Status',
                  user.isActive ? 'Active Agent' : 'Inactive',
                  user.isActive ? Icons.verified : Icons.block,
                ),
                SizedBox(height: 16.h),
                _buildInfoRow(
                  context,
                  'Total Addresses',
                  '${user.addresses.length} saved',
                  Icons.location_city,
                ),
                if (user.addresses.isNotEmpty) ...[
                  SizedBox(height: 16.h),
                  _buildInfoRow(
                    context,
                    'Primary Address',
                    user.addresses.first.details,
                    Icons.home,
                  ),
                ],
              ],
            ),
          );
        }

        return Container(
          padding: EdgeInsets.all(20.w),
          decoration: BoxDecoration(
            color: context.appColors.surfaceColor,
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(color: context.appColors.dividerColor),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Work Information',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 20.h),
              _buildInfoRow(
                context,
                'Loading...',
                'Please wait',
                Icons.hourglass_empty,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmergencyContacts(BuildContext context) {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, userState) {
        final user = context.userBloc.currentUser;
        if (user != null) {
          return Container(
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              color: context.appColors.surfaceColor,
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(color: context.appColors.dividerColor),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Saved Addresses',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 20.h),
                if (user.addresses.isEmpty) ...[
                  _buildInfoRow(
                    context,
                    'No Addresses',
                    'No saved addresses found',
                    Icons.location_off,
                  ),
                ] else ...[
                  for (int i = 0; i < user.addresses.length; i++) ...[
                    if (i > 0) SizedBox(height: 16.h),
                    _buildAddressItem(context, user.addresses[i], i + 1),
                  ],
                ],
              ],
            ),
          );
        }

        return Container(
          padding: EdgeInsets.all(20.w),
          decoration: BoxDecoration(
            color: context.appColors.surfaceColor,
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(color: context.appColors.dividerColor),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Saved Addresses',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 20.h),
              _buildInfoRow(
                context,
                'Loading...',
                'Please wait',
                Icons.hourglass_empty,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPersonalInfo(BuildContext context) {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, userState) {
        final user = context.userBloc.currentUser;
        if (user != null) {
          return Container(
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              color: context.appColors.surfaceColor,
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(color: context.appColors.dividerColor),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Personal Information',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 20.h),
                _buildInfoRow(context, 'User ID', user.id, Icons.fingerprint),
                SizedBox(height: 16.h),
                _buildInfoRow(context, 'Phone Number', user.phone, Icons.phone),
                SizedBox(height: 16.h),
                _buildInfoRow(
                  context,
                  'Email',
                  user.email ?? 'Not provided',
                  Icons.email,
                ),
                SizedBox(height: 16.h),
                _buildInfoRow(
                  context,
                  'Username',
                  user.username ?? 'Not set',
                  Icons.person,
                ),
                SizedBox(height: 16.h),
                _buildInfoRow(
                  context,
                  'Role',
                  user.role.name.toUpperCase(),
                  Icons.work,
                ),
                SizedBox(height: 16.h),
                _buildInfoRow(
                  context,
                  'Account Status',
                  user.isActive ? 'Active' : 'Inactive',
                  user.isActive ? Icons.verified : Icons.block,
                ),
                if (user.createdAt != null) ...[
                  SizedBox(height: 16.h),
                  _buildInfoRow(
                    context,
                    'Member Since',
                    _formatDate(user.createdAt!),
                    Icons.calendar_today,
                  ),
                ],
              ],
            ),
          );
        }

        return Container(
          padding: EdgeInsets.all(20.w),
          decoration: BoxDecoration(
            color: context.appColors.surfaceColor,
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(color: context.appColors.dividerColor),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Personal Information',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 20.h),
              _buildInfoRow(
                context,
                'Loading...',
                'Please wait',
                Icons.hourglass_empty,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    String title,
    String value,
    IconData icon,
  ) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: context.appColors.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(icon, color: context.appColors.primaryColor, size: 20.sp),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: context.appColors.subtextColor,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Settings',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 20.h),
          _buildSettingItem(
            context,
            'Notifications',
            'Manage your notification preferences',
            Icons.notifications,
            () => _showNotificationSettings(context),
          ),
          SizedBox(height: 16.h),
          _buildSettingItem(
            context,
            'Privacy',
            'Privacy and security settings',
            Icons.privacy_tip,
            () => _showPrivacySettings(context),
          ),
          SizedBox(height: 16.h),
          _buildSettingItem(
            context,
            'Help & Support',
            'Get help and contact support',
            Icons.help,
            () => _showHelpSupport(context),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingItem(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: context.appColors.primaryColor.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: context.appColors.primaryColor.withValues(alpha: 0.1),
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: context.appColors.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                icon,
                color: context.appColors.primaryColor,
                size: 20.sp,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: context.appColors.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: context.appColors.subtextColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: context.appColors.subtextColor,
              size: 16.sp,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoutButton(BuildContext context) {
    return BlocConsumer<AuthenticationBloc, AuthenticationState>(
      listener: (context, state) {
        if (state is Unauthenticated) {
          context.pushAndRemoveUntilRoute(const LoginPage());
        }
      },
      builder: (context, state) {
        final isLoading = state is AuthenticationLoading;

        return SizedBox(
          width: double.infinity,
          height: 56.h,
          child: ElevatedButton(
            onPressed: isLoading ? null : () => _showLogoutDialog(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: isLoading
                ? SizedBox(
                    width: 24.w,
                    height: 24.h,
                    child: const CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.logout, color: Colors.white, size: 20.sp),
                      SizedBox(width: 8.w),
                      Text(
                        'Logout',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
          ),
        );
      },
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AuthenticationBloc>().add(LogoutEvent());
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }

  void _showNotificationSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notification Settings'),
        content: const Text('Notification settings will be available soon.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showPrivacySettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Privacy Settings'),
        content: const Text('Privacy settings will be available soon.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showHelpSupport(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Help & Support'),
        content: const Text(
          'Help and support features will be available soon.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  // Helper methods
  String _formatDate(DateTime date) {
    final months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  IconData _getVehicleIcon(VehicleType? vehicleType) {
    switch (vehicleType) {
      case VehicleType.motorcycle:
        return Icons.motorcycle;
      case VehicleType.bike:
        return Icons.pedal_bike;
      case VehicleType.car:
        return Icons.directions_car;
      default:
        return Icons.help_outline;
    }
  }

  Widget _buildAddressItem(
    BuildContext context,
    AddressEntity address,
    int index,
  ) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: context.appColors.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: context.appColors.primaryColor.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: context.appColors.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Icon(
                  Icons.location_on,
                  color: context.appColors.primaryColor,
                  size: 16.sp,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  address.tag ?? 'Address $index',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            address.details,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
          if (address.contactPhone != null) ...[
            SizedBox(height: 4.h),
            Row(
              children: [
                Icon(
                  Icons.phone,
                  color: context.appColors.subtextColor,
                  size: 14.sp,
                ),
                SizedBox(width: 4.w),
                Text(
                  address.contactPhone!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                ),
              ],
            ),
          ],
          SizedBox(height: 4.h),
          Row(
            children: [
              Icon(
                Icons.gps_fixed,
                color: context.appColors.subtextColor,
                size: 14.sp,
              ),
              SizedBox(width: 4.w),
              Text(
                '${address.location.coordinates[1].toStringAsFixed(4)}, ${address.location.coordinates[0].toStringAsFixed(4)}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: context.appColors.subtextColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Show edit profile dialog
  void _showEditProfileDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) => BlocProvider.value(
        value: context.userBloc,
        child: _EditProfileDialog(),
      ),
    );
  }
}

// Edit Profile Dialog Widget
class _EditProfileDialog extends StatefulWidget {
  @override
  _EditProfileDialogState createState() => _EditProfileDialogState();
}

class _EditProfileDialogState extends State<_EditProfileDialog> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _usernameController = TextEditingController();
  final _vehicleNumberController = TextEditingController();
  VehicleType? _selectedVehicleType;
  bool _isOnDuty = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    final user = context.userBloc.currentUser;
    if (user != null) {
      _emailController.text = user.email ?? '';
      _usernameController.text = user.username ?? '';
      _selectedVehicleType = user.agentMetadata?.vehicle?.type;
      _vehicleNumberController.text = user.agentMetadata?.vehicle?.number ?? '';
      _isOnDuty = user.agentMetadata?.isOnDuty ?? false;
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _usernameController.dispose();
    _vehicleNumberController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<UserBloc, UserState>(
      listener: (context, state) {
        if (state is UpdateUserSuccess) {
          Navigator.pop(context);
          // ScaffoldMessenger.of(
          //   context,
          // ).showSnackBar(SnackBar(content: Text(state.message)));
          SnackBarHelper.showSuccessSnackBar(context, message: state.message);
          // Refresh authentication state
          context.read<UserBloc>().add(const GetCurrentUserEvent());
        } else if (state is UpdateUserFailure) {
          SnackBarHelper.showErrorSnackBar(
            context,
            message: state.appFailure.getErrorMessage(),
          );
        }
      },
      child: AlertDialog(
        title: const Text('Edit Profile'),
        content: SizedBox(
          width: double.maxFinite,
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildEmailField(),
                  SizedBox(height: 16.h),
                  _buildUsernameField(),
                  SizedBox(height: 16.h),
                  _buildVehicleTypeField(),
                  SizedBox(height: 16.h),
                  _buildVehicleNumberField(),
                  SizedBox(height: 16.h),
                  _buildDutyStatusField(),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          BlocBuilder<UserBloc, UserState>(
            builder: (context, state) {
              final isLoading = state is UpdateUserLoading;
              return ElevatedButton(
                onPressed: isLoading ? null : _saveProfile,
                child: isLoading
                    ? SizedBox(
                        width: 20.w,
                        height: 20.h,
                        child: const CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('Save'),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEmailField() {
    return CustomTextField(
      controller: _emailController,
      labelText: 'Email',
      prefixIcon: const Icon(Icons.email),
      keyboardType: TextInputType.emailAddress,
      validator: (value) {
        if (value != null && value.isNotEmpty) {
          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
            return 'Please enter a valid email';
          }
        }
        return null;
      },
    );
  }

  Widget _buildUsernameField() {
    return CustomTextField(
      controller: _usernameController,
      labelText: 'Username',
      prefixIcon: const Icon(Icons.person),
      validator: (value) {
        if (value != null && value.isNotEmpty && value.length < 3) {
          return 'Username must be at least 3 characters';
        }
        return null;
      },
    );
  }

  Widget _buildVehicleTypeField() {
    return CustomDropDown<VehicleType>(
      items: VehicleType.values,
      value: _selectedVehicleType,
      labelText: 'Vehicle Type',
      displayItem: (type) => type?.name.toUpperCase() ?? '',
      onChanged: (value) {
        setState(() {
          _selectedVehicleType = value;
        });
      },
    );
  }

  Widget _buildVehicleNumberField() {
    return CustomTextField(
      controller: _vehicleNumberController,
      labelText: 'Vehicle Number',
      prefixIcon: const Icon(Icons.confirmation_number),
      validator: (value) {
        if (value != null && value.isNotEmpty && value.length < 3) {
          return 'Vehicle number must be at least 3 characters';
        }
        return null;
      },
    );
  }

  Widget _buildDutyStatusField() {
    return SwitchListTile(
      title: const Text('On Duty'),
      subtitle: Text(
        _isOnDuty
            ? 'Currently available for deliveries'
            : 'Currently unavailable',
      ),
      value: _isOnDuty,
      onChanged: (value) {
        setState(() {
          _isOnDuty = value;
        });
      },
      secondary: Icon(
        _isOnDuty ? Icons.work : Icons.work_off,
        color: _isOnDuty ? Colors.green : Colors.red,
      ),
    );
  }

  void _saveProfile() {
    print('Save profile triggered');

    if (!_formKey.currentState!.validate()) {
      print('Form validation failed');
      return;
    }
    print('Form validated successfully');

    final user = context.userBloc.currentUser;
    if (user == null) {
      print('User is null, cannot save profile');
      return;
    }

    print('User ID: ${user.id}');
    print('User role: ${user.role.name}');

    // Prepare agent metadata if user is an agent
    UpdateAgentMetadataParams? agentMetadata;
    if (user.role.name.toLowerCase() == 'agent') {
      print('User is an agent');

      final isVehicleInfoProvided =
          _selectedVehicleType != null ||
          _vehicleNumberController.text.isNotEmpty;
      print('Is vehicle info provided: $isVehicleInfoProvided');

      agentMetadata = UpdateAgentMetadataParams(
        vehicle: isVehicleInfoProvided
            ? UpdateVehicleParams(
                type: _selectedVehicleType,
                number: _vehicleNumberController.text.isNotEmpty
                    ? _vehicleNumberController.text
                    : null,
              )
            : null,
        isOnDuty: _isOnDuty,
      );

      print('Agent metadata prepared: $agentMetadata');
    } else {
      print('User is not an agent');
    }

    final email = _emailController.text.isNotEmpty
        ? _emailController.text
        : null;
    final username = _usernameController.text.isNotEmpty
        ? _usernameController.text
        : null;

    print('Prepared update fields:');
    print('Email: $email');
    print('Username: $username');

    context.userBloc.add(
      UpdateUserEvent(
        userId: user.id,
        email: email,
        username: username,
        agentMetadata: agentMetadata,
      ),
    );

    print('UpdateUserEvent dispatched');
  }

  IconData _getVehicleIcon(VehicleType? vehicleType) {
    switch (vehicleType) {
      case VehicleType.motorcycle:
        return Icons.motorcycle;
      case VehicleType.bike:
        return Icons.pedal_bike;
      case VehicleType.car:
        return Icons.directions_car;
      default:
        return Icons.help_outline;
    }
  }
}
