import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/router/extension/navigation_extension.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/button_state.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/core/utils/extensions/build_context_extensions.dart';
import 'package:frontend/core/utils/helpers/somali_phone_number_formatter.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';
import 'package:frontend/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_button.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_textfield.dart';
import 'package:frontend/features/user-management/domain/params/update_user_params.dart';
import 'package:frontend/features/user-management/presentation/bloc/user_bloc.dart';

import '../../../../authentication/presentation/pages/login_page.dart';
import 'contact_support_page.dart';
import 'help_center_page.dart';
import 'rate_app_page.dart';
import 'terms_privacy_page.dart';

class CustomerProfilePage extends StatefulWidget {
  const CustomerProfilePage({super.key});

  @override
  State<CustomerProfilePage> createState() => _CustomerProfilePageState();
}

class _CustomerProfilePageState extends State<CustomerProfilePage> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, userState) {
        final user = context.userBloc.currentUser;

        return Scaffold(
          backgroundColor: context.appColors.backgroundColor,
          appBar: AppBar(
            backgroundColor: context.appColors.backgroundColor,
            elevation: 0,
            title: Text(
              'My Profile',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            actions: [
              IconButton(
                onPressed: () => _editProfile(context, user),
                icon: Icon(Icons.edit, color: context.appColors.textColor),
              ),
              IconButton(
                onPressed: () => _refreshUserData(context),
                icon: Icon(Icons.refresh, color: context.appColors.textColor),
              ),
            ],
          ),
          body: SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildProfileHeader(context, user),
                  SizedBox(height: 24.h),
                  _buildAccountSection(context, user),
                  SizedBox(height: 24.h),
                  _buildPreferencesSection(context),
                  SizedBox(height: 24.h),
                  _buildSupportSection(context),
                  SizedBox(height: 24.h),
                  _buildLogoutSection(context),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildProfileHeader(BuildContext context, UserEntity? user) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            appColors.primaryColor,
            appColors.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 32.r,
            backgroundColor: appColors.whiteColor.withValues(alpha: 0.2),
            child: Icon(Icons.person, color: appColors.whiteColor, size: 32.sp),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user?.phone ?? 'Customer',
                  style: textTheme.headlineSmall?.copyWith(
                    color: appColors.whiteColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4.h),
                if (user?.email != null)
                  Text(
                    user!.email!,
                    style: textTheme.bodyMedium?.copyWith(
                      color: appColors.whiteColor.withValues(alpha: 0.9),
                    ),
                  ),
                SizedBox(height: 8.h),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: appColors.whiteColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Text(
                    'Customer',
                    style: TextStyle(
                      color: appColors.whiteColor,
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _editProfile(context, user),
            icon: Icon(Icons.edit, color: appColors.whiteColor, size: 24.sp),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountSection(BuildContext context, UserEntity? user) {
    return _buildSection(context, 'Account Information', Icons.account_circle, [
      _buildInfoTile(
        context,
        'Phone Number',
        user?.phone ?? 'Not provided',
        Icons.phone,
        () => _editPhone(context),
      ),
      _buildInfoTile(
        context,
        'Email Address',
        user?.email ?? 'Not provided',
        Icons.email,
        () => _editEmail(context),
      ),
    ]);
  }

  Widget _buildPreferencesSection(BuildContext context) {
    return _buildSection(context, 'Preferences', Icons.settings, [
      _buildActionTile(
        context,
        'Notifications',
        'Manage notification preferences',
        Icons.notifications,
        Colors.orange,
        () => _manageNotifications(context),
      ),
      _buildActionTile(
        context,
        'Language',
        'Change app language',
        Icons.language,
        Colors.purple,
        () => _changeLanguage(context),
      ),
      _buildActionTile(
        context,
        'Theme',
        'Switch between light and dark mode',
        Icons.palette,
        Colors.indigo,
        () => _changeTheme(context),
      ),
    ]);
  }

  Widget _buildSupportSection(BuildContext context) {
    return _buildSection(context, 'Support & Help', Icons.help, [
      _buildActionTile(
        context,
        'Help Center',
        'Find answers to common questions',
        Icons.help_center,
        Colors.blue,
        () => _openHelpCenter(context),
      ),
      _buildActionTile(
        context,
        'Contact Support',
        'Get help from our support team',
        Icons.support_agent,
        Colors.green,
        () => _contactSupport(context),
      ),
      _buildActionTile(
        context,
        'Rate App',
        'Rate and review our app',
        Icons.star,
        Colors.orange,
        () => _rateApp(context),
      ),
      _buildActionTile(
        context,
        'Terms & Privacy',
        'View terms of service and privacy policy',
        Icons.policy,
        Colors.purple,
        () => _viewTermsAndPrivacy(context),
      ),
    ]);
  }

  Widget _buildLogoutSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.logout, color: Colors.red, size: 20.sp),
              SizedBox(width: 8.w),
              Text(
                'Account Actions',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          BlocListener<AuthenticationBloc, AuthenticationState>(
            listener: (context, state) {
              if (state is Unauthenticated) {
                context.pushAndRemoveUntilRoute(const LoginPage());
              }
            },
            child: SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _logout(context),
                icon: const Icon(Icons.logout, color: Colors.red),
                label: const Text(
                  'Logout',
                  style: TextStyle(color: Colors.red),
                ),
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(color: Colors.red),
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(
    BuildContext context,
    String title,
    IconData icon,
    List<Widget> children,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: context.appColors.primaryColor, size: 20.sp),
            SizedBox(width: 8.w),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Container(
          decoration: BoxDecoration(
            color: context.appColors.surfaceColor,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: context.appColors.dividerColor),
          ),
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildInfoTile(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: context.appColors.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                icon,
                color: context.appColors.primaryColor,
                size: 20.sp,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: context.appColors.subtextColor,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    value,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: context.appColors.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: context.appColors.subtextColor,
              size: 20.sp,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionTile(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(icon, color: color, size: 20.sp),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: context.appColors.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: context.appColors.subtextColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: context.appColors.subtextColor,
              size: 20.sp,
            ),
          ],
        ),
      ),
    );
  }

  // Refresh user data
  void _refreshUserData(BuildContext context) {
    context.userBloc.add(GetCurrentUserEvent());
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Refreshing profile data...')));
  }

  // Action methods
  void _editProfile(BuildContext context, UserEntity? user) {
    showDialog(
      context: context,
      builder: (dialogContext) => BlocProvider.value(
        value: context.userBloc,
        child: const _CustomerEditProfileDialog(),
      ),
    );
  }

  void _editPhone(BuildContext context) {
    final TextEditingController phoneController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        title: Row(
          children: [
            Icon(
              Icons.phone,
              color: context.appColors.primaryColor,
              size: 20.sp,
            ),
            SizedBox(width: 8.w),
            const Text('Update Phone Number'),
          ],
        ),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Enter your new phone number:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.subtextColor,
                ),
              ),
              SizedBox(height: 16.h),
              CustomTextField(
                controller: phoneController,
                labelText: '61XXXXXXXX',
                hintText: '61XXXXXXXX',
                keyboardType: TextInputType.phone,
                textInputAction: TextInputAction.done,
                prefixIcon: Container(
                  padding: EdgeInsets.all(12.w),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        '+252',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: context.appColors.textColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Container(
                        width: 1.w,
                        height: 20.h,
                        color: context.appColors.disabledColor,
                      ),
                    ],
                  ),
                ),
                inputFormatters: [
                  SomaliPhoneNumberFormatter(),
                  LengthLimitingTextInputFormatter(9),
                ],
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Phone number is required';
                  }
                  if (value.length < 9) {
                    return 'Please enter a valid phone number';
                  }
                  // Regex that checks Somali number format 61xxxxxxx must start with 61
                  const pattern = r'^61[1-9]\d{6}$';
                  final regex = RegExp(pattern);
                  if (!regex.hasMatch(value.trim())) {
                    return 'Enter a valid phone number.';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                final phone = '+252${phoneController.text.trim()}';
                Navigator.pop(context);

                // TODO: Implement actual phone update functionality
                // context.read<UserBloc>().add(UpdateUserEvent(phone: phone));

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Phone number will be updated to: $phone'),
                    backgroundColor: Colors.orange,
                  ),
                );
              }
            },
            icon: const Icon(Icons.save),
            label: const Text('Update'),
          ),
        ],
      ),
    );
  }

  void _editEmail(BuildContext context) {
    final TextEditingController emailController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Email Address'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Enter your new email address:'),
            const SizedBox(height: 16),
            TextField(
              controller: emailController,
              keyboardType: TextInputType.emailAddress,
              decoration: const InputDecoration(
                labelText: 'Email Address',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Email update functionality will be available soon',
                  ),
                ),
              );
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  void _manageAddresses(BuildContext context) {
    // TODO: Navigate to addresses management page
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Manage addresses')));
  }

  void _setDefaultAddress(BuildContext context) {
    // TODO: Show default address selection
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Set default address')));
  }

  void _manageNotifications(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notification Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: const Text('Order Updates'),
              subtitle: const Text('Get notified about order status changes'),
              value: true,
              onChanged: (value) {},
            ),
            SwitchListTile(
              title: const Text('Promotions'),
              subtitle: const Text('Receive promotional offers and discounts'),
              value: false,
              onChanged: (value) {},
            ),
            SwitchListTile(
              title: const Text('Delivery Notifications'),
              subtitle: const Text(
                'Get notified when your order is out for delivery',
              ),
              value: true,
              onChanged: (value) {},
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Notification preferences saved')),
              );
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _changeLanguage(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Language'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Text('🇺🇸'),
              title: const Text('English'),
              trailing: const Icon(Icons.check, color: Colors.green),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Language changed to English')),
                );
              },
            ),
            ListTile(
              leading: const Text('🇸🇴'),
              title: const Text('Somali'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Language support coming soon')),
                );
              },
            ),
            ListTile(
              leading: const Text('🇸🇦'),
              title: const Text('Arabic'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Language support coming soon')),
                );
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _changeTheme(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Theme'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.light_mode),
              title: const Text('Light Mode'),
              trailing: const Icon(Icons.check, color: Colors.green),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Light theme selected')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.dark_mode),
              title: const Text('Dark Mode'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Dark theme support coming soon'),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.auto_mode),
              title: const Text('System Default'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('System theme support coming soon'),
                  ),
                );
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _openHelpCenter(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const HelpCenterPage()),
    );
  }

  void _contactSupport(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ContactSupportPage()),
    );
  }

  void _rateApp(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const RateAppPage()),
    );
  }

  void _viewTermsAndPrivacy(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const TermsPrivacyPage()),
    );
  }

  void _logout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AuthenticationBloc>().add(LogoutEvent());
            },
            child: const Text('Logout', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}

// Customer Edit Profile Dialog Widget
class _CustomerEditProfileDialog extends StatefulWidget {
  const _CustomerEditProfileDialog();

  @override
  _CustomerEditProfileDialogState createState() =>
      _CustomerEditProfileDialogState();
}

class _CustomerEditProfileDialogState
    extends State<_CustomerEditProfileDialog> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _usernameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    final user = context.userBloc.currentUser;
    if (user != null) {
      _emailController.text = user.email ?? '';
      _usernameController.text = user.username ?? '';
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _usernameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<UserBloc, UserState>(
      listener: (context, state) {
        if (state is UpdateUserSuccess) {
          Navigator.pop(context);
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
          // Refresh user data
          context.userBloc.add(const GetCurrentUserEvent());
        } else if (state is UpdateUserFailure) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.appFailure.message),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      child: AlertDialog(
        title: const Text('Edit Profile'),
        content: SizedBox(
          width: double.maxFinite,
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildEmailField(),
                  SizedBox(height: 16.h),
                  _buildUsernameField(),
                ],
              ),
            ),
          ),
        ),
        actions: [
          CustomButton.inline(
            onTap: () => Navigator.pop(context),
            buttonText: 'Cancel',
            width: 80.0,
            height: 36.0,
          ),
          BlocBuilder<UserBloc, UserState>(
            builder: (context, state) {
              final isLoading = state is UpdateUserLoading;
              return CustomButton.filled(
                onTap: isLoading ? null : _saveProfile,
                buttonText: 'Save',
                buttonState: isLoading
                    ? ButtonState.loading
                    : ButtonState.normal,
                width: 80.0,
                height: 36.0,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEmailField() {
    return CustomTextField(
      controller: _emailController,
      labelText: 'Email',
      prefixIcon: const Icon(Icons.email),
      keyboardType: TextInputType.emailAddress,
      validator: (value) {
        if (value != null && value.isNotEmpty) {
          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
            return 'Please enter a valid email';
          }
        }
        return null;
      },
    );
  }

  Widget _buildUsernameField() {
    return CustomTextField(
      controller: _usernameController,
      labelText: 'Username',
      prefixIcon: const Icon(Icons.person),
      validator: (value) {
        if (value != null && value.isNotEmpty && value.length < 3) {
          return 'Username must be at least 3 characters';
        }
        return null;
      },
    );
  }

  void _saveProfile() {
    if (!_formKey.currentState!.validate()) return;

    final user = context.userBloc.currentUser;
    if (user == null) return;

    context.userBloc.add(
      UpdateUserEvent(
        userId: user.id,
        email: _emailController.text.isNotEmpty ? _emailController.text : null,
        username: _usernameController.text.isNotEmpty
            ? _usernameController.text
            : null,
      ),
    );
  }
}
