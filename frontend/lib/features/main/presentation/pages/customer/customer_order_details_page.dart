import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/router/extension/navigation_extension.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/order_status.dart';
import 'package:frontend/core/utils/extensions/build_context_extensions.dart';
import 'package:frontend/core/utils/helpers/snack_bar_helper.dart';
import 'package:frontend/features/main/presentation/pages/customer/customer_order_qrcode_page.dart';
import 'package:frontend/features/order/domain/entities/order_entity.dart';
import 'package:frontend/features/order/domain/entities/order_item_entity.dart';
import 'package:frontend/features/order/presentation/bloc/order_bloc.dart';
import 'package:frontend/features/shared/presentation/widgets/order_timeline_widget.dart';
import 'package:intl/intl.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';

class CustomerOrderDetailsPage extends StatefulWidget {
  final OrderEntity order;

  const CustomerOrderDetailsPage({super.key, required this.order});

  @override
  State<CustomerOrderDetailsPage> createState() =>
      _CustomerOrderDetailsPageState();
}

class _CustomerOrderDetailsPageState extends State<CustomerOrderDetailsPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.backgroundColor,
        elevation: 0,
        title: Text(
          'Order Details',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: Icon(Icons.arrow_back, color: context.appColors.textColor),
        ),
        actions: [
          IconButton(
            onPressed: () => _shareOrder(context),
            icon: Icon(Icons.share, color: context.appColors.textColor),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildOrderHeader(context),
            SizedBox(height: 24.h),
            _buildOrderStatus(context),
            SizedBox(height: 24.h),
            _buildOrderItems(context),
            SizedBox(height: 24.h),
            _buildDeliveryInfo(context),
            SizedBox(height: 24.h),
            OrderTimelineWidget(
              order: widget.order,
              style: TimelineStyle.detailed,
            ),
            if (_canCancelOrder()) ...[
              SizedBox(height: 32.h),
              _buildCancelButton(context),
            ],
          ],
        ),
      ),
      // floatingActionButton: widget.order.qrCode != null
      //     ? FloatingActionButton.extended(
      //         onPressed: () {
      //           context.pushRoute(
      //             OrderQrCodePage(
      //               qrCode: widget.order.qrCode!,
      //               orderId: widget.order.id,
      //               expiresAt: widget.order.qrCodeExpiresAt,
      //               orderStatus: OrderStatus.confirmed, // widget.order.status,
      //               // verificationAttempts: widget.order.verificationAttempts,
      //               verificationAttempts: 3,
      //             ),
      //           );
      //         },
      //         backgroundColor: context.appColors.primaryColor,
      //         icon: const Icon(Icons.qr_code, color: Colors.white),
      //         label: const Text(
      //           'Show QR Code',
      //           style: TextStyle(color: Colors.white),
      //         ),
      //       )
      //     : null,
      floatingActionButton: _shouldShowQrCodeButton()
          ? FloatingActionButton.extended(
              onPressed: () {
                if (_isQrCodeExpired()) {
                  _showQrCodeExpiredDialog(context);
                } else if (widget.order.status == OrderStatus.cancelled ||
                    widget.order.status == OrderStatus.failed) {
                  _showOrderNotAvailableDialog(context);
                } else {
                  context.pushRoute(
                    CustomerOrderQrCodePage(
                      qrCode: widget.order.qrCode!,
                      orderId: widget.order.id,
                      expiresAt: widget.order.qrCodeExpiresAt,
                      orderStatus: widget.order.status,
                      // widget.order.verificationAttempts ?? 0,
                    ),
                  );
                }
              },
              backgroundColor: _getQrButtonColor(),
              icon: Icon(_getQrButtonIcon(), color: Colors.white),
              label: Text(
                _getQrButtonText(),
                style: const TextStyle(color: Colors.white),
              ),
            )
          : null,
    );
  }

  /// ------ QR CODE HELPERS
  // Helper methods in your order details page
  bool _shouldShowQrCodeButton() {
    return widget.order.qrCode != null &&
        (widget.order.status == OrderStatus.confirmed ||
            widget.order.status == OrderStatus.inTransit);
  }

  bool _isQrCodeExpired() {
    return widget.order.qrCodeExpiresAt?.isBefore(DateTime.now()) ?? false;
  }

  Color _getQrButtonColor() {
    if (!_shouldShowQrCodeButton()) return Colors.grey;
    if (_isQrCodeExpired()) return Colors.orange;
    return context.appColors.primaryColor;
  }

  IconData _getQrButtonIcon() {
    if (!_shouldShowQrCodeButton()) return Icons.block;
    if (_isQrCodeExpired()) return Icons.error_outline;
    return Icons.qr_code;
  }

  String _getQrButtonText() {
    if (!_shouldShowQrCodeButton()) return 'Unavailable';
    if (_isQrCodeExpired()) return 'Expired Code';
    return 'Show QR Code';
  }

  void _showQrCodeExpiredDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('QR Code Expired'),
        content: const Text(
          'This QR code has expired. Please contact support if you need assistance.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showOrderNotAvailableDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Order ${widget.order.status.label}'),
        content: Text(
          'This order has been ${widget.order.status.label.toLowerCase()}. QR code is no longer available.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            widget.order.status.color,
            widget.order.status.color.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Order #${widget.order.id.substring(0, 8).toUpperCase()}',
                      style: Theme.of(context).textTheme.headlineSmall
                          ?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      _formatDate(widget.order.createdAt),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
              GestureDetector(
                onTap: () => _copyOrderId(context),
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(Icons.copy, color: Colors.white, size: 20.sp),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Icon(Icons.attach_money, color: Colors.white, size: 24.sp),
              SizedBox(width: 8.w),
              Text(
                '\$${widget.order.totalAmount.toStringAsFixed(2)}',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOrderStatus(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Status',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Container(
                width: 12.w,
                height: 12.h,
                decoration: BoxDecoration(
                  color: widget.order.status.color,
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Text(
                  widget.order.status.label,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              if (widget.order.deliveryAgent != null)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'Delivery Agent',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: context.appColors.subtextColor,
                      ),
                    ),
                    Text(
                      widget.order.deliveryAgent!.phone,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: context.appColors.textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
            ],
          ),
          if (_getStatusDescription().isNotEmpty) ...[
            SizedBox(height: 8.h),
            Text(
              _getStatusDescription(),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOrderItems(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Items (${widget.order.items.length})',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          ...widget.order.items.map(
            (item) => Container(
              margin: EdgeInsets.only(bottom: 12.h),
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: context.appColors.primaryColor.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: context.appColors.primaryColor.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 40.w,
                    height: 40.h,
                    decoration: BoxDecoration(
                      color: context.appColors.primaryColor.withValues(
                        alpha: 0.1,
                      ),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Icon(
                      _getItemIcon(item.itemType),
                      color: context.appColors.primaryColor,
                      size: 20.sp,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _getItemDisplayName(item),
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(
                                color: context.appColors.textColor,
                                fontWeight: FontWeight.w600,
                              ),
                        ),
                        Text(
                          _getItemDescription(item),
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: context.appColors.subtextColor),
                        ),
                        if (_getItemPrice(item) != null) ...[
                          SizedBox(height: 4.h),
                          Text(
                            'Price: \$${_getItemPrice(item)!.toStringAsFixed(2)}',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: context.appColors.primaryColor,
                                  fontWeight: FontWeight.w500,
                                ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: context.appColors.primaryColor,
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      'x${item.quantity}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeliveryInfo(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Delivery Information',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.location_on,
                color: context.appColors.primaryColor,
                size: 20.sp,
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Delivery Address',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: context.appColors.subtextColor,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      widget.order.deliveryAddress,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: context.appColors.textColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (widget.order.deliveredAt != null) ...[
            SizedBox(height: 16.h),
            Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green, size: 20.sp),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Delivered At',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        _formatDate(widget.order.deliveredAt!),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: context.appColors.textColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCancelButton(BuildContext context) {
    return BlocConsumer<OrderBloc, OrderState>(
      listener: (context, state) {
        if (state is CancelOrderSuccess) {
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Order cancelled successfully'),
              backgroundColor: Colors.green,
            ),
          );
        } else if (state is CancelOrderFailure) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.appFailure.getErrorMessage()),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      builder: (context, state) {
        final isLoading = state is CancelOrderLoading;

        return SizedBox(
          width: double.infinity,
          height: 50.h,
          child: ElevatedButton(
            onPressed: isLoading ? null : () => _showCancelDialog(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: isLoading
                ? SizedBox(
                    width: 20.w,
                    height: 20.h,
                    child: const CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    'Cancel Order',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        );
      },
    );
  }

  // Utility Methods
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today ${_formatTime(date)}';
    } else if (difference.inDays == 1) {
      return 'Yesterday ${_formatTime(date)}';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year} ${_formatTime(date)}';
    }
  }

  String _formatTime(DateTime date) {
    final hour = date.hour.toString().padLeft(2, '0');
    final minute = date.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  String _getStatusDescription() {
    switch (widget.order.status) {
      case OrderStatus.pending:
        return 'Your order is being processed';
      case OrderStatus.confirmed:
        return 'Your order has been confirmed and will be prepared for delivery';
      case OrderStatus.inTransit:
        return 'Your order is on the way to your delivery address';
      case OrderStatus.delivered:
        return 'Your order has been successfully delivered';
      case OrderStatus.cancelled:
        return 'This order has been cancelled';
      case OrderStatus.failed:
        return widget.order.failureReason ?? 'This order has failed';
    }
  }

  IconData _getItemIcon(String itemType) {
    switch (itemType.toLowerCase()) {
      case 'cylinder':
        return Icons.local_gas_station;
      case 'package':
        return Icons.inventory_2;
      case 'spare_part':
        return Icons.build;
      default:
        return Icons.shopping_cart;
    }
  }

  String _getItemTypeName(String itemType) {
    switch (itemType.toLowerCase()) {
      case 'cylinder':
        return 'Gas Cylinder';
      case 'package':
        return 'Full Package';
      case 'spare_part':
        return 'Spare Part';
      default:
        return itemType;
    }
  }

  bool _canCancelOrder() {
    return widget.order.status == OrderStatus.pending ||
        widget.order.status == OrderStatus.confirmed;
  }

  void _showCancelDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Order'),
        content: const Text(
          'Are you sure you want to cancel this order? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Keep Order'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<OrderBloc>().add(
                CancelOrderEvent(orderId: widget.order.id),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Cancel Order'),
          ),
        ],
      ),
    );
  }

  void _copyOrderId(BuildContext context) {
    Clipboard.setData(ClipboardData(text: widget.order.id));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Order ID copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _shareOrder(BuildContext context) async {
    final orderInfo =
        '''
Order #${widget.order.id.substring(0, 8).toUpperCase()}
Status: ${widget.order.status.label}
Total: \$${widget.order.totalAmount.toStringAsFixed(2)}
Date: ${_formatDate(widget.order.createdAt)}
Delivery: ${widget.order.deliveryAddress}
''';

    // Note: You would typically use share_plus package here
    // Share.share(orderInfo, subject: 'Order Details');
    // SharePlus.instance.share(
    //   text : orderInfo,
    //   subject: 'Order Details',
    // );

    // For now, just copy to clipboard
    // Clipboard.setData(ClipboardData(text: orderInfo));
    try {
      // Implement actual sharing functionality
      await Share.share(orderInfo, subject: 'Order Verification Code');
    } catch (e) {
      SnackBarHelper.showErrorSnackBar(
        context,
        message: 'Failed to share QR code: ${e.toString()}',
      );
    }
    // SnackBarHelper.showSuccessSnackBar(
    //   context,
    //   message: 'Order details copied to clipboard',
    // );
  }

  // Helper methods for displaying item details
  String _getItemDisplayName(OrderItemEntity item) {
    if (item.itemDetails != null) {
      final details = item.itemDetails!;

      switch (item.itemType.toUpperCase()) {
        case 'CYLINDER':
          final type = details['type'] ?? '';
          final material = details['material'] ?? '';
          return '$type $material Gas Cylinder';
        case 'SPARE_PART':
          final category = details['category'] ?? '';
          return _formatSparePartCategory(category);
        case 'PACKAGE':
          return details['name'] ?? 'Package';
        default:
          return _getItemTypeName(item.itemType);
      }
    }
    return _getItemTypeName(item.itemType);
  }

  String _getItemDescription(OrderItemEntity item) {
    if (item.itemDetails != null) {
      final details = item.itemDetails!;
      final description = details['description'];

      if (description != null && description.toString().isNotEmpty) {
        return description.toString();
      }

      // Fallback to item ID if no description
      return 'Item ID: ${item.itemId.substring(0, 8)}...';
    }
    return 'Item ID: ${item.itemId.substring(0, 8)}...';
  }

  double? _getItemPrice(OrderItemEntity item) {
    if (item.itemDetails != null) {
      final details = item.itemDetails!;
      final price = details['price'];

      if (price != null) {
        return (price as num).toDouble();
      }
    }
    return null;
  }

  String _formatSparePartCategory(String category) {
    // Convert category like "TUBO_2_METER" to "2 Meter Tubo"
    switch (category.toUpperCase()) {
      case 'TUBO_2_METER':
        return '2 Meter Tubo';
      case 'TUBO_5_METER':
        return '5 Meter Tubo';
      case 'REGULATOR':
        return 'Gas Regulator';
      case 'VALVE':
        return 'Gas Valve';
      case 'HOSE':
        return 'Gas Hose';
      default:
        // Convert SNAKE_CASE to Title Case
        return category
            .toLowerCase()
            .split('_')
            .map(
              (word) => word.isNotEmpty
                  ? word[0].toUpperCase() + word.substring(1)
                  : word,
            )
            .join(' ');
    }
  }
}
