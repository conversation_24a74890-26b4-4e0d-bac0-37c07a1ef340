import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/order_status.dart';
import 'package:frontend/core/utils/extensions/build_context_extensions.dart';
import 'package:intl/intl.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';

import '../../../../../core/utils/helpers/snack_bar_helper.dart';

class CustomerOrderQrCodePage extends StatelessWidget {
  final String qrCode;
  final String orderId;
  final DateTime? expiresAt;
  final OrderStatus orderStatus;
  final int verificationAttempts;

  const CustomerOrderQrCodePage({
    super.key,
    required this.qrCode,
    required this.orderId,
    required this.orderStatus,
    this.expiresAt,
    this.verificationAttempts = 0,
  });

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;

    final isExpired = expiresAt?.isBefore(DateTime.now()) ?? false;
    final isActive =
        orderStatus == OrderStatus.confirmed ||
        orderStatus == OrderStatus.inTransit;

    return Scaffold(
      backgroundColor: appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: appColors.backgroundColor,
        elevation: 0,
        title: Text(
          'Delivery Verification',
          style: textTheme.headlineSmall?.copyWith(
            color: appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: Icon(Icons.arrow_back, color: appColors.textColor),
        ),
        actions: [
          if (isActive && !isExpired)
            IconButton(
              icon: Icon(Icons.share, color: appColors.primaryColor),
              onPressed: () => _shareQrCode(context),
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Order status chip
            // Container(
            //   padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
            //   decoration: BoxDecoration(
            //     color: orderStatus.color.withValues(alpha: 0.2),
            //     borderRadius: BorderRadius.circular(20.r),
            //     border: Border.all(color: orderStatus.color),
            //   ),
            //   child: Text(
            //     orderStatus.label,
            //     style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            //       color: orderStatus.color,
            //       fontWeight: FontWeight.bold,
            //     ),
            //   ),
            // ),
            SizedBox(height: 16.h),

            // Order reference info
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              decoration: BoxDecoration(
                color: appColors.surfaceColor,
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(color: appColors.dividerColor),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Order ID:', style: textTheme.bodyMedium),
                  Text(
                    orderId,
                    style: textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: appColors.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 24.h),

            // Verification attempts badge
            if (verificationAttempts > 0)
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20.r),
                  border: Border.all(color: Colors.blue),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.verified_outlined,
                      color: Colors.blue,
                      size: 16.sp,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      'Verified ${verificationAttempts}x',
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: Colors.blue),
                    ),
                  ],
                ),
              ),
            SizedBox(height: verificationAttempts > 0 ? 16.h : 0),

            // QR Code container
            Container(
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                color: appColors.whiteColor,
                borderRadius: BorderRadius.circular(20.r),
                boxShadow: [
                  BoxShadow(
                    color: appColors.blackColor.withValues(alpha: 0.1),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Status banners
                  if (!isActive) ...[
                    _buildStatusBanner(
                      context,
                      icon: Icons.block,
                      message: 'ORDER ${orderStatus.name}',
                      color: appColors.errorColor,
                    ),
                    SizedBox(height: 16.h),
                  ] else if (isExpired) ...[
                    _buildStatusBanner(
                      context,
                      icon: Icons.error_outline,
                      message: 'QR CODE EXPIRED',
                      color: appColors.errorColor,
                    ),
                    SizedBox(height: 16.h),
                  ],

                  // QR Code display
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      QrImageView(
                        data: qrCode,
                        size: 250.w,
                        backgroundColor: appColors.whiteColor,
                        eyeStyle: QrEyeStyle(
                          eyeShape: QrEyeShape.square,
                          color: appColors.primaryColor,
                        ),
                        errorCorrectionLevel: QrErrorCorrectLevel.H,
                      ),
                      if (!isActive || isExpired)
                        Container(
                          width: 250.w,
                          height: 250.w,
                          color: appColors.blackColor.withValues(alpha: 0.5),
                          alignment: Alignment.center,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                !isActive ? Icons.block : Icons.error_outline,
                                color: appColors.whiteColor,
                                size: 48.sp,
                              ),
                              SizedBox(height: 8.h),
                              Text(
                                !isActive
                                    ? orderStatus.label.toUpperCase()
                                    : 'EXPIRED',
                                style: textTheme.headlineSmall?.copyWith(
                                  color: appColors.whiteColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                  SizedBox(height: 16.h),

                  // Status messages
                  if (!isActive) ...[
                    Text(
                      'This order cannot be verified',
                      style: textTheme.bodyLarge?.copyWith(
                        color: appColors.errorColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ] else ...[
                    Text(
                      'Scan this code to verify delivery',
                      style: textTheme.bodyLarge?.copyWith(
                        color: appColors.textColor,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      isExpired
                          ? 'This QR code has expired and is no longer valid'
                          : expiresAt != null
                          ? 'Expires: ${_formatExpiration(expiresAt!)}'
                          : 'Valid until delivery is completed',
                      style: textTheme.bodySmall?.copyWith(
                        color: isExpired
                            ? appColors.errorColor
                            : appColors.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            SizedBox(height: 24.h),

            // Only show security tips for active, unexpired codes
            if (isActive && !isExpired) _buildSecurityTips(context),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBanner(
    BuildContext context, {
    required IconData icon,
    required String message,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 18.sp),
          SizedBox(width: 8.w),
          Text(
            message,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityTips(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.security, color: Colors.orange, size: 20.sp),
              SizedBox(width: 8.w),
              Text(
                'Security Tips',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.orange,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            '• Only share this code with authorized delivery personnel\n'
            '• Verify the agent\'s identity before scanning\n'
            '• Do not share this code through unsecured channels\n'
            '• Report any suspicious verification attempts',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
        ],
      ),
    );
  }

  String _formatExpiration(DateTime expiresAt) {
    return DateFormat('MMM dd, yyyy - hh:mm a').format(expiresAt);
  }

  Future<void> _shareQrCode(BuildContext context) async {
    try {
      // Implement actual sharing functionality
      await Share.share(
        'Order Verification Code for Order #$orderId\n'
        'Valid until: ${expiresAt != null ? _formatExpiration(expiresAt!) : 'delivery completion'}',
        subject: 'Order Verification Code',
      );
    } catch (e) {
      // ScaffoldMessenger.of(context).showSnackBar(
      //   SnackBar(
      //     content: Text('Failed to share QR code: ${e.toString()}'),
      //     backgroundColor: Colors.red,
      //   ),
      // );
      SnackBarHelper.showErrorSnackBar(
        context,
        message: 'Failed to share QR code: ${e.toString()}',
      );
    }
  }
}
