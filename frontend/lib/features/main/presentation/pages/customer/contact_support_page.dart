import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/button_state.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_button.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_textfield.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_drop_down.dart';
import 'package:frontend/core/constants/app_constants.dart';
import 'package:frontend/core/utils/helpers/url_laucher_helper.dart';

class ContactSupportPage extends StatefulWidget {
  const ContactSupportPage({super.key});

  @override
  State<ContactSupportPage> createState() => _ContactSupportPageState();
}

class _ContactSupportPageState extends State<ContactSupportPage> {
  final _formKey = GlobalKey<FormState>();
  final _subjectController = TextEditingController();
  final _messageController = TextEditingController();

  String _selectedCategory = 'General Inquiry';
  bool _isSubmitting = false;

  final List<String> _categories = [
    'General Inquiry',
    'Order Issue',
    'Delivery Problem',
    'Payment Issue',
    'Technical Support',
    'Account Problem',
    'Feedback',
    'Complaint',
  ];

  final List<ContactMethod> _contactMethods = [
    ContactMethod(
      title: 'Phone Support',
      subtitle: 'Call us directly',
      icon: Icons.phone,
      color: Colors.green,
      value: AppConstants.phoneNumber,
      action: ContactAction.call,
    ),
    ContactMethod(
      title: 'WhatsApp',
      subtitle: 'Chat with us on WhatsApp',
      icon: Icons.chat,
      color: Colors.green.shade600,
      value: AppConstants.whatsappNumber,
      action: ContactAction.whatsapp,
    ),
    ContactMethod(
      title: 'Email Support',
      subtitle: 'Send us an email',
      icon: Icons.email,
      color: Colors.blue,
      value: AppConstants.email,
      action: ContactAction.email,
    ),
    // ContactMethod(
    //   title: 'Live Chat',
    //   subtitle: 'Chat with our support team',
    //   icon: Icons.support_agent,
    //   color: Colors.purple,
    //   value: 'Available 24/7',
    //   action: ContactAction.chat,
    // ),
  ];

  @override
  void dispose() {
    _subjectController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.backgroundColor,
        elevation: 0,
        title: Text(
          'Contact Support',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: Icon(Icons.arrow_back, color: context.appColors.textColor),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildQuickContactSection(),
            SizedBox(height: 24.h),
            _buildContactFormSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickContactSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Contact',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          'Choose your preferred way to reach us',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: context.appColors.textColor.withValues(alpha: 0.7),
          ),
        ),
        SizedBox(height: 16.h),
        ...(_contactMethods.map((method) => _buildContactMethodCard(method))),
      ],
    );
  }

  Widget _buildContactMethodCard(ContactMethod method) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        leading: Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: method.color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Icon(method.icon, color: method.color, size: 24.sp),
        ),
        title: Text(
          method.title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              method.subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: context.appColors.textColor.withValues(alpha: 0.6),
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              method.value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: method.color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          color: context.appColors.textColor.withValues(alpha: 0.4),
          size: 16.sp,
        ),
        onTap: () => _handleContactMethod(method),
      ),
    );
  }

  Widget _buildContactFormSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Send us a Message',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          'Fill out the form below and we\'ll get back to you soon',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: context.appColors.textColor.withValues(alpha: 0.7),
          ),
        ),
        SizedBox(height: 16.h),
        _buildContactForm(),
      ],
    );
  }

  Widget _buildContactForm() {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            // SizedBox(height: 16.h),
            _buildCategoryDropdown(),
            SizedBox(height: 16.h),
            _buildTextField(
              controller: _subjectController,
              label: 'Subject',
              icon: Icons.subject,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a subject';
                }
                return null;
              },
            ),
            SizedBox(height: 16.h),
            _buildTextField(
              controller: _messageController,
              label: 'Message',
              icon: Icons.message,
              maxLines: 5,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your message';
                }
                if (value.length < 10) {
                  return 'Message must be at least 10 characters';
                }
                return null;
              },
            ),
            SizedBox(height: 24.h),
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return CustomTextField(
      controller: controller,
      labelText: label,
      prefixIcon: Icon(icon),
      keyboardType: keyboardType,
      maxLine: maxLines,
      validator: validator,
      borderRadius: 8.0,
    );
  }

  Widget _buildCategoryDropdown() {
    return CustomDropDown<String>(
      items: _categories,
      value: _selectedCategory,
      labelText: 'Category',
      displayItem: (category) => category,
      onChanged: (value) {
        setState(() {
          _selectedCategory = value!;
        });
      },
    );
  }

  Widget _buildSubmitButton() {
    return CustomButton.filled(
      onTap: _isSubmitting ? null : _submitForm,
      buttonText: 'Send Message',
      buttonState: _isSubmitting ? ButtonState.loading : ButtonState.normal,
      width: double.infinity,
      height: 48.0,
      borderRadius: 8.0,
      backgroundColor: context.appColors.primaryColor,
    );
  }

  void _handleContactMethod(ContactMethod method) async {
    switch (method.action) {
      case ContactAction.call:
        UrlLauncherHelper().launchTel(
          context: context,
          phoneNumber: method.value,
        );
        break;
      case ContactAction.whatsapp:
        UrlLauncherHelper().launchWhatsApp(
          context: context,
          phoneNumber: method.value,
        );
        break;
      case ContactAction.email:
        UrlLauncherHelper().launchEmail(
          context: context,
          toEmail: method.value,
        );
        break;
      case ContactAction.chat:
        _showChatDialog();
        break;
    }
  }

  void _showChatDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Live Chat'),
        content: const Text(
          'Live chat feature is coming soon! For immediate assistance, please call or WhatsApp us.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSubmitting = true;
    });

    // Simulate form submission
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isSubmitting = false;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Message sent successfully! We\'ll get back to you soon.',
          ),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.pop(context);
    }
  }
}

class ContactMethod {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final String value;
  final ContactAction action;

  ContactMethod({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.value,
    required this.action,
  });
}

enum ContactAction { call, whatsapp, email, chat }
