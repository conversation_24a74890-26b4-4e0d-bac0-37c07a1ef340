import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_textfield.dart';

class HelpCenterPage extends StatefulWidget {
  const HelpCenterPage({super.key});

  @override
  State<HelpCenterPage> createState() => _HelpCenterPageState();
}

class _HelpCenterPageState extends State<HelpCenterPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  final List<HelpCategory> _helpCategories = [
    HelpCategory(
      title: 'Getting Started',
      icon: Icons.rocket_launch,
      color: Colors.blue,
      faqs: [
        FAQ(
          question: 'How do I create an account?',
          answer:
              'To create an account, tap on "Sign Up" on the login screen, enter your phone number, and follow the verification process.',
        ),
        FAQ(
          question: 'How do I place my first order?',
          answer:
              'Browse our products, select the items you need, add them to cart, choose your delivery address, and confirm your order.',
        ),
        FAQ(
          question: 'What payment methods do you accept?',
          answer:
              'We accept cash on delivery, mobile money, and bank transfers. More payment options are coming soon.',
        ),
      ],
    ),
    HelpCategory(
      title: 'Orders & Delivery',
      icon: Icons.local_shipping,
      color: Colors.green,
      faqs: [
        FAQ(
          question: 'How long does delivery take?',
          answer:
              'Standard delivery takes 2-4 hours within the city. Express delivery is available for urgent orders.',
        ),
        FAQ(
          question: 'Can I track my order?',
          answer:
              'Yes! You can track your order in real-time from the Orders section in your profile.',
        ),
        FAQ(
          question: 'What if I need to cancel my order?',
          answer:
              'You can cancel your order within 30 minutes of placing it. Go to Orders and tap "Cancel Order".',
        ),
        FAQ(
          question: 'Do you deliver to my area?',
          answer:
              'We deliver within the city limits. Enter your address during checkout to confirm delivery availability.',
        ),
      ],
    ),
    HelpCategory(
      title: 'Gas Cylinders',
      icon: Icons.propane_tank,
      color: Colors.orange,
      faqs: [
        FAQ(
          question: 'What sizes of gas cylinders do you offer?',
          answer:
              'We offer 6kg, 13kg, and 25kg gas cylinders. All cylinders are certified and safe for home use.',
        ),
        FAQ(
          question: 'How do I exchange my empty cylinder?',
          answer:
              'Select "Exchange Cylinder" when ordering, and our delivery agent will collect your empty cylinder and provide a full one.',
        ),
        FAQ(
          question: 'Are your cylinders safe?',
          answer:
              'Yes, all our cylinders undergo regular safety inspections and meet international safety standards.',
        ),
      ],
    ),
    HelpCategory(
      title: 'Account & Profile',
      icon: Icons.account_circle,
      color: Colors.purple,
      faqs: [
        FAQ(
          question: 'How do I update my profile information?',
          answer:
              'Go to Profile, tap the edit icon, update your information, and save changes.',
        ),
        FAQ(
          question: 'How do I add or change my delivery address?',
          answer:
              'In your profile, go to "Manage Addresses" to add, edit, or set your default delivery address.',
        ),
        FAQ(
          question: 'I forgot my password, what should I do?',
          answer:
              'We use phone number verification. Simply enter your phone number on the login screen to receive a new OTP.',
        ),
      ],
    ),
    HelpCategory(
      title: 'Billing & Payments',
      icon: Icons.payment,
      color: Colors.teal,
      faqs: [
        FAQ(
          question: 'How do I pay for my order?',
          answer:
              'You can pay cash on delivery or use mobile money. Select your preferred payment method during checkout.',
        ),
        FAQ(
          question: 'Can I get a receipt for my order?',
          answer:
              'Yes, you will receive a digital receipt via SMS and in your order history.',
        ),
        FAQ(
          question: 'What if I was charged incorrectly?',
          answer:
              'Contact our support team immediately with your order details, and we will resolve the billing issue.',
        ),
      ],
    ),
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<HelpCategory> get _filteredCategories {
    if (_searchQuery.isEmpty) return _helpCategories;

    return _helpCategories
        .map((category) {
          final filteredFaqs = category.faqs
              .where(
                (faq) =>
                    faq.question.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ) ||
                    faq.answer.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ),
              )
              .toList();

          return HelpCategory(
            title: category.title,
            icon: category.icon,
            color: category.color,
            faqs: filteredFaqs,
          );
        })
        .where((category) => category.faqs.isNotEmpty)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.backgroundColor,
        elevation: 0,
        title: Text(
          'Help Center',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: Icon(Icons.arrow_back, color: context.appColors.textColor),
        ),
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          Expanded(
            child: _filteredCategories.isEmpty
                ? _buildNoResultsFound()
                : _buildHelpContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: CustomTextField(
        controller: _searchController,
        hintText: 'Search for help...',
        prefixIcon: Icon(
          Icons.search,
          color: context.appColors.textColor.withValues(alpha: 0.6),
        ),
        borderRadius: 12.0,
        fillColor: context.appColors.surfaceColor,
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildNoResultsFound() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64.sp,
            color: context.appColors.textColor.withValues(alpha: 0.4),
          ),
          SizedBox(height: 16.h),
          Text(
            'No results found',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: context.appColors.textColor.withValues(alpha: 0.6),
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Try searching with different keywords',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.textColor.withValues(alpha: 0.4),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpContent() {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      itemCount: _filteredCategories.length,
      itemBuilder: (context, index) {
        final category = _filteredCategories[index];
        return _buildCategoryCard(category);
      },
    );
  }

  Widget _buildCategoryCard(HelpCategory category) {
    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      color: context.appColors.surfaceColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: ExpansionTile(
        leading: Container(
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: category.color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(category.icon, color: category.color, size: 24.sp),
        ),
        title: Text(
          category.title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          '${category.faqs.length} articles',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: context.appColors.textColor.withValues(alpha: 0.6),
          ),
        ),
        children: category.faqs.map((faq) => _buildFAQItem(faq)).toList(),
      ),
    );
  }

  Widget _buildFAQItem(FAQ faq) {
    return ExpansionTile(
      title: Text(
        faq.question,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          color: context.appColors.textColor,
          fontWeight: FontWeight.w500,
        ),
      ),
      children: [
        Padding(
          padding: EdgeInsets.fromLTRB(16.w, 0, 16.w, 16.h),
          child: Text(
            faq.answer,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.textColor.withValues(alpha: 0.8),
              height: 1.5,
            ),
          ),
        ),
      ],
    );
  }
}

class HelpCategory {
  final String title;
  final IconData icon;
  final Color color;
  final List<FAQ> faqs;

  HelpCategory({
    required this.title,
    required this.icon,
    required this.color,
    required this.faqs,
  });
}

class FAQ {
  final String question;
  final String answer;

  FAQ({required this.question, required this.answer});
}
