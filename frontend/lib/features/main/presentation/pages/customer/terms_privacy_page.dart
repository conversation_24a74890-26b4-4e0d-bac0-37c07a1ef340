import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';

class TermsPrivacyPage extends StatefulWidget {
  const TermsPrivacyPage({super.key});

  @override
  State<TermsPrivacyPage> createState() => _TermsPrivacyPageState();
}

class _TermsPrivacyPageState extends State<TermsPrivacyPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.backgroundColor,
        elevation: 0,
        title: Text(
          'Terms & Privacy',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: Icon(Icons.arrow_back, color: context.appColors.textColor),
        ),
        bottom: TabBar(
          controller: _tabController,
          labelColor: context.appColors.primaryColor,
          unselectedLabelColor: context.appColors.textColor.withValues(alpha: 0.6),
          indicatorColor: context.appColors.primaryColor,
          tabs: const [
            Tab(text: 'Terms of Service'),
            Tab(text: 'Privacy Policy'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildTermsOfService(),
          _buildPrivacyPolicy(),
        ],
      ),
    );
  }

  Widget _buildTermsOfService() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Terms of Service'),
          SizedBox(height: 8.h),
          _buildLastUpdated('Last updated: January 15, 2024'),
          SizedBox(height: 24.h),

          _buildSection(
            'Acceptance of Terms',
            'By accessing and using our gas delivery service, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.',
          ),

          _buildSection(
            'Service Description',
            'Our service provides on-demand delivery of gas cylinders and related products. We connect customers with verified delivery agents to ensure safe and timely delivery of gas products to your specified location.',
          ),

          _buildSection(
            'User Responsibilities',
            '''• You must be at least 18 years old to use our service
• Provide accurate and complete information when placing orders
• Ensure someone is available to receive the delivery at the specified time
• Handle gas cylinders safely and according to manufacturer guidelines
• Report any issues or damages immediately upon delivery''',
          ),

          _buildSection(
            'Order and Payment',
            '''• All orders are subject to availability and confirmation
• Prices are subject to change without notice
• Payment must be made as agreed upon order placement
• Cancellations must be made within 30 minutes of order placement
• Refunds will be processed according to our refund policy''',
          ),

          _buildSection(
            'Delivery Terms',
            '''• Delivery times are estimates and may vary due to traffic, weather, or other factors
• Our delivery agents will attempt delivery at the specified address
• If no one is available to receive the delivery, additional charges may apply for re-delivery
• You are responsible for providing accurate delivery instructions and contact information''',
          ),

          _buildSection(
            'Safety and Liability',
            '''• Gas cylinders must be handled with care and stored properly
• We are not liable for damages resulting from improper use or storage
• Report any leaks, damages, or safety concerns immediately
• Follow all local regulations regarding gas cylinder storage and use
• Our liability is limited to the value of the delivered products''',
          ),

          _buildSection(
            'Prohibited Uses',
            '''• Using the service for illegal activities
• Providing false or misleading information
• Interfering with the service or other users
• Attempting to gain unauthorized access to our systems
• Reselling our products without authorization''',
          ),

          _buildSection(
            'Termination',
            'We reserve the right to terminate or suspend your account and access to our service at our sole discretion, without notice, for conduct that we believe violates these Terms of Service or is harmful to other users, us, or third parties.',
          ),

          _buildSection(
            'Changes to Terms',
            'We reserve the right to modify these terms at any time. Changes will be effective immediately upon posting. Your continued use of the service after changes are posted constitutes acceptance of the modified terms.',
          ),

          _buildSection(
            'Contact Information',
            'If you have any questions about these Terms of Service, please contact <NAME_EMAIL> or call +252 61 912 4489.',
          ),
        ],
      ),
    );
  }

  Widget _buildPrivacyPolicy() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Privacy Policy'),
          SizedBox(height: 8.h),
          _buildLastUpdated('Last updated: January 15, 2024'),
          SizedBox(height: 24.h),

          _buildSection(
            'Information We Collect',
            '''We collect information you provide directly to us, such as:
• Personal information (name, phone number, email address)
• Delivery addresses and location data
• Payment information
• Order history and preferences
• Communications with our support team''',
          ),

          _buildSection(
            'How We Use Your Information',
            '''We use the information we collect to:
• Process and fulfill your orders
• Communicate with you about your orders and our services
• Provide customer support
• Improve our services and user experience
• Send promotional offers and updates (with your consent)
• Ensure the safety and security of our platform''',
          ),

          _buildSection(
            'Information Sharing',
            '''We may share your information with:
• Delivery agents (only information necessary for delivery)
• Payment processors (for transaction processing)
• Service providers who assist in our operations
• Law enforcement when required by law
• Other parties with your explicit consent''',
          ),

          _buildSection(
            'Location Information',
            '''We collect location information to:
• Provide accurate delivery services
• Calculate delivery fees and estimated times
• Improve our service coverage
• Ensure delivery agent safety
You can control location sharing through your device settings.''',
          ),

          _buildSection(
            'Data Security',
            '''We implement appropriate security measures to protect your information:
• Encryption of sensitive data
• Secure payment processing
• Regular security audits
• Limited access to personal information
• Secure data storage and transmission''',
          ),

          _buildSection(
            'Data Retention',
            'We retain your information for as long as necessary to provide our services, comply with legal obligations, resolve disputes, and enforce our agreements. You may request deletion of your account and associated data at any time.',
          ),

          _buildSection(
            'Your Rights',
            '''You have the right to:
• Access your personal information
• Correct inaccurate information
• Delete your account and data
• Opt-out of marketing communications
• Request data portability
• File complaints with relevant authorities''',
          ),

          _buildSection(
            'Cookies and Tracking',
            'We use cookies and similar technologies to improve your experience, analyze usage patterns, and provide personalized content. You can control cookie settings through your browser preferences.',
          ),

          _buildSection(
            'Third-Party Services',
            'Our app may contain links to third-party services. We are not responsible for the privacy practices of these external services. We encourage you to review their privacy policies.',
          ),

          _buildSection(
            'Children\'s Privacy',
            'Our service is not intended for children under 18. We do not knowingly collect personal information from children. If we become aware of such collection, we will take steps to delete the information.',
          ),

          _buildSection(
            'Changes to Privacy Policy',
            'We may update this Privacy Policy from time to time. We will notify you of any material changes by posting the new policy on our app and updating the "Last updated" date.',
          ),

          _buildSection(
            'Contact Us',
            'If you have questions about this Privacy Policy or our data practices, please contact <NAME_EMAIL> or call +252 61 912 4489.',
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
        color: context.appColors.textColor,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildLastUpdated(String date) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: context.appColors.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Text(
        date,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: context.appColors.primaryColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Container(
      margin: EdgeInsets.only(bottom: 24.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.textColor.withValues(alpha: 0.8),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
