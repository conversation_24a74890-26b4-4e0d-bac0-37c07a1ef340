import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/order_status.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/core/utils/helpers/snack_bar_helper.dart';
import 'package:frontend/features/order/domain/entities/order_entity.dart';
import 'package:frontend/features/order/domain/entities/order_item_entity.dart';
import 'package:frontend/features/order/presentation/bloc/order_bloc.dart';
import 'package:intl/intl.dart';
import 'package:frontend/features/shared/presentation/widgets/order_timeline_widget.dart';

import '../../../../../core/utils/helpers/bottom_sheet_helper.dart';

class SupervisorOrderDetailsPage extends StatefulWidget {
  final OrderEntity order;

  const SupervisorOrderDetailsPage({super.key, required this.order});

  @override
  State<SupervisorOrderDetailsPage> createState() =>
      _SupervisorOrderDetailsPageState();
}

class _SupervisorOrderDetailsPageState
    extends State<SupervisorOrderDetailsPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.surfaceColor,
        elevation: 0,
        title: Text(
          'Order Details',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          if (_canAssignAgent(widget.order))
            IconButton(
              onPressed: () => _showAssignAgentDialog(context, widget.order),
              icon: Icon(
                Icons.assignment,
                color: context.appColors.primaryColor,
              ),
              tooltip: 'Assign Agent',
            ),
          PopupMenuButton<String>(
            icon: Icon(Icons.more_vert, color: context.appColors.textColor),
            onSelected: (value) => _handleAction(context, value),
            itemBuilder: (context) => [
              // QR Code regeneration for active orders
              if (widget.order.status == OrderStatus.confirmed ||
                  widget.order.status == OrderStatus.inTransit)
                const PopupMenuItem(
                  value: 'regenerate_qr',
                  child: Row(
                    children: [
                      Icon(Icons.qr_code, color: Colors.blue),
                      SizedBox(width: 8),
                      Text(
                        'Regenerate QR Code',
                        style: TextStyle(color: Colors.blue),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ],
      ),
      body: BlocListener<OrderBloc, OrderState>(
        listener: (context, state) {
          if (state is AssignAgentToOrderSuccess) {
            SnackBarHelper.showSuccessSnackBar(context, message: state.message);
            Navigator.pop(context); // Go back to refresh the list
          } else if (state is AssignAgentToOrderFailure) {
            SnackBarHelper.showErrorSnackBar(
              context,
              message: state.appFailure.getErrorMessage(),
            );
          } else if (state is RegenerateQRCodeSuccess) {
            SnackBarHelper.showSuccessSnackBar(context, message: state.message);
            // No need to navigate back, just show success message
          } else if (state is RegenerateQRCodeFailure) {
            SnackBarHelper.showErrorSnackBar(
              context,
              message: state.appFailure.getErrorMessage(),
            );
          }
        },
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildOrderHeader(context),
              SizedBox(height: 20.h),
              _buildCustomerInfo(context),
              SizedBox(height: 20.h),
              _buildOrderItems(context),
              SizedBox(height: 20.h),
              _buildDeliveryInfo(context),
              SizedBox(height: 20.h),
              _buildPaymentInfo(context),
              SizedBox(height: 20.h),
              OrderTimelineWidget(
                order: widget.order,
                style: TimelineStyle.detailed,
              ),
              if (_canAssignAgent(widget.order)) ...[
                SizedBox(height: 20.h),
                _buildAssignAgentSection(context),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOrderHeader(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Order #${widget.order.id.substring(widget.order.id.length - 6)}',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 6.h,
                  ),
                  decoration: BoxDecoration(
                    color: widget.order.status.color,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Text(
                    widget.order.status.label,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 16.sp,
                  color: context.appColors.subtextColor,
                ),
                SizedBox(width: 8.w),
                Text(
                  'Created: ${DateFormat('MMM dd, yyyy - hh:mm a').format(widget.order.createdAt)}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Row(
              children: [
                Icon(
                  Icons.attach_money,
                  size: 16.sp,
                  color: context.appColors.subtextColor,
                ),
                SizedBox(width: 8.w),
                Text(
                  'Total Amount: \$${widget.order.totalAmount.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerInfo(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Customer Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            _buildInfoRow(
              context,
              Icons.person,
              'Phone',
              widget.order.customer.phone,
            ),
            if (widget.order.customer.email != null) ...[
              SizedBox(height: 8.h),
              _buildInfoRow(
                context,
                Icons.email,
                'Email',
                widget.order.customer.email!,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItems(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Order Items',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            ...widget.order.items.map((item) => _buildOrderItem(context, item)),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItem(BuildContext context, OrderItemEntity item) {
    // Calculate unit price from total amount and total quantity
    final totalQuantity = widget.order.items.fold<int>(
      0,
      (sum, item) => sum + item.quantity,
    );
    final unitPrice = totalQuantity > 0
        ? widget.order.totalAmount / totalQuantity
        : 0.0;
    final itemTotal = item.quantity * unitPrice;

    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: context.appColors.backgroundColor,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: context.appColors.subtextColor.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.itemId,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'Qty: ${item.quantity} × \$${unitPrice.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '\$${itemTotal.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeliveryInfo(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Delivery Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            _buildInfoRow(
              context,
              Icons.location_on,
              'Address',
              widget.order.deliveryAddress,
            ),
            if (widget.order.deliveryAgent != null) ...[
              SizedBox(height: 8.h),
              _buildInfoRow(
                context,
                Icons.delivery_dining,
                'Assigned Agent',
                widget.order.deliveryAgent!.phone,
              ),
            ] else ...[
              SizedBox(height: 8.h),
              Row(
                children: [
                  Icon(
                    Icons.delivery_dining,
                    size: 16.sp,
                    color: context.appColors.subtextColor,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'No agent assigned yet',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: context.appColors.subtextColor,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentInfo(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            _buildInfoRow(
              context,
              Icons.payment,
              'Payment Method',
              widget.order.paymentMethod,
            ),
            // SizedBox(height: 8.h),
            // _buildInfoRow(
            //   context,
            //   Icons.receipt,
            //   'Payment Status',
            //   widget.order.paymentMethod,
            // ),
          ],
        ),
      ),
    );
  }

  Widget _buildAssignAgentSection(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Agent Assignment',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            Text(
              'This order is ready for agent assignment. Click the button below to assign an available agent.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
            SizedBox(height: 16.h),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showAssignAgentDialog(context, widget.order),
                icon: Icon(Icons.assignment, size: 20.sp),
                label: const Text('Assign Agent'),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                  backgroundColor: context.appColors.primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    IconData icon,
    String label,
    String value,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 16.sp, color: context.appColors.subtextColor),
        SizedBox(width: 8.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: context.appColors.subtextColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.textColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Helper Methods
  bool _canAssignAgent(OrderEntity order) {
    return order.status == OrderStatus.pending ||
        order.status == OrderStatus.confirmed;
  }

  void _showAssignAgentDialog(BuildContext context, OrderEntity order) async {
    await BottomSheetHelper.showAssignAgentBottomSheet(
      context: context,
      order: order,
      onAssignmentComplete: () => _loadOrdersData(forceRefresh: true),
    );
  }

  // void _assignAgent(String agentId) {
  //   context.read<OrderBloc>().add(
  //     AssignAgentToOrderEvent(orderId: widget.order.id, agentId: agentId),
  //   );
  // }

  void _loadOrdersData({bool forceRefresh = false}) {
    final orderBloc = context.orderBloc;
    final canRefetch = forceRefresh || orderBloc.orders.isEmpty;
    if (!canRefetch) return;

    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = DateTime(
      today.year,
      today.month,
      today.day,
      23,
      59,
      59,
      999,
    );

    orderBloc.add(GetOrdersEvent(startDate: startOfDay, endDate: endOfDay));
  }

  void _handleAction(BuildContext context, String action) {
    switch (action) {
      case 'regenerate_qr':
        _regenerateQRCode(context);
        break;
    }
  }

  void _regenerateQRCode(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Regenerate QR Code'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Generate a new QR code for order #${widget.order.id.substring(widget.order.id.length - 6)}?',
            ),
            SizedBox(height: 8.h),
            Text(
              'This will invalidate the current QR code and create a new one.',
              style: TextStyle(color: Colors.orange, fontSize: 12.sp),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<OrderBloc>().add(
                RegenerateQRCodeEvent(orderId: widget.order.id),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('Regenerate'),
          ),
        ],
      ),
    );
  }
}
