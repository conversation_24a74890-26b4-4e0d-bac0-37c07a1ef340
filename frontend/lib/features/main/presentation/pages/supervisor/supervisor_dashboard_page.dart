import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:intl/intl.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/features/dashboard/presentation/bloc/dashboard_bloc.dart';
import 'package:frontend/features/dashboard/domain/entities/supervisor_dashboard_entity.dart';

import 'package:frontend/features/main/presentation/widgets/supervisor_drawer.dart';

class SupervisorDashboardPage extends StatefulWidget {
  const SupervisorDashboardPage({super.key});

  @override
  State<SupervisorDashboardPage> createState() =>
      _SupervisorDashboardPageState();
}

class _SupervisorDashboardPageState extends State<SupervisorDashboardPage> {
  @override
  void initState() {
    super.initState();
    _loadDashboard();
  }

  Future<void> _loadDashboard({bool forceRefresh = false}) async {
    final dashboardBloc = context.dashboardBloc;
    final canRefetch =
        forceRefresh || dashboardBloc.supervisorDashboard == null;
    if (!canRefetch) return;

    context.read<DashboardBloc>().add(GetSupervisorDashboardEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      drawer: const SupervisorDrawer(),
      appBar: AppBar(
        backgroundColor: context.appColors.surfaceColor,
        elevation: 0,
        leading: Builder(
          builder: (context) => IconButton(
            icon: Icon(Icons.menu, color: context.appColors.textColor),
            onPressed: () => Scaffold.of(context).openDrawer(),
          ),
        ),
        title: Text(
          'Supervisor Dashboard',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () => _loadDashboard(forceRefresh: true),
            icon: Icon(Icons.refresh, color: context.appColors.primaryColor),
          ),
        ],
      ),
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async {
            await _loadDashboard(forceRefresh: true);
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: EdgeInsets.all(16.w),
            child: _buildDashboardContent(context),
          ),
        ),
      ),
    );
  }

  Widget _buildDashboardContent(BuildContext context) {
    return BlocBuilder<DashboardBloc, DashboardState>(
      builder: (context, state) {
        if (state is GetSupervisorDashboardLoading) {
          return _buildLoadingWidget(context);
        }

        if (state is GetSupervisorDashboardFailure) {
          return _buildErrorWidget(context, state.appFailure.getErrorMessage());
        }

        if (state is GetSupervisorDashboardSuccess) {
          return _buildDashboardData(context, state.dashboard);
        }

        return _buildEmptyWidget(context);
      },
    );
  }

  Widget _buildLoadingWidget(BuildContext context) {
    return SizedBox(
      height: 400.h,
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildErrorWidget(BuildContext context, String message) {
    return SizedBox(
      height: 400.h,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64.sp, color: Colors.red),
            SizedBox(height: 16.h),
            Text(
              message,
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: () => _loadDashboard(forceRefresh: true),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyWidget(BuildContext context) {
    return SizedBox(
      height: 400.h,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.dashboard,
              size: 64.sp,
              color: context.appColors.subtextColor,
            ),
            SizedBox(height: 16.h),
            Text(
              'No dashboard data available',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: () => _loadDashboard(forceRefresh: true),
              child: const Text('Load Dashboard'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDashboardData(
    BuildContext context,
    SupervisorDashboardEntity dashboard,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildWelcomeHeader(context),
        SizedBox(height: 24.h),
        _buildTodayStatsOverview(context, dashboard),
        SizedBox(height: 24.h),
        _buildTodayChartsSection(context, dashboard),
        SizedBox(height: 24.h),
        _buildAvailableAgents(context, dashboard.availableAgents),
        SizedBox(height: 24.h),
        _buildTodayOrders(context, dashboard.todayOrders),
      ],
    );
  }

  Widget _buildWelcomeHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            context.appColors.primaryColor,
            context.appColors.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: context.appColors.primaryColor.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back, Supervisor!',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'Here\'s today\'s operational overview.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                SizedBox(height: 12.h),
                Text(
                  DateFormat('EEEE, MMMM d, y').format(DateTime.now()),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.supervisor_account,
            size: 60.sp,
            color: Colors.white.withValues(alpha: 0.3),
          ),
        ],
      ),
    );
  }

  Widget _buildTodayStatsOverview(
    BuildContext context,
    SupervisorDashboardEntity dashboard,
  ) {
    final pendingOrders =
        dashboard.todayStats.orders.pending +
        dashboard.todayStats.orders.confirmed;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Today\'s Key Metrics',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: _buildKPICard(
                context: context,
                title: 'Total Orders',
                value: dashboard.todayStats.orders.total.toString(),
                icon: Icons.shopping_cart,
                color: Colors.blue,
                subtitle: '${dashboard.todayStats.orders.delivered} delivered',
                isPositive: true,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildKPICard(
                context: context,
                title: 'Revenue',
                value:
                    '\$${NumberFormat('#,##0.00').format(dashboard.todayStats.orders.totalRevenue)}',
                icon: Icons.attach_money,
                color: Colors.green,
                // subtitle: '\$${NumberFormat('#,##0.00').format(dashboard.todayStats.orders.totalRevenue)} today\'s earnings',
                subtitle: 'Today\'s earnings',
                isPositive: true,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildKPICard(
                context: context,
                title: 'Items Sold',
                value: dashboard.todayStats.sales.itemsSold.toString(),
                icon: Icons.inventory_2,
                color: Colors.orange,
                subtitle: 'Units moved',
                isPositive: true,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildKPICard(
                context: context,
                title: 'Pending Orders',
                value: '$pendingOrders',
                icon: Icons.pending_actions,
                color: Colors.amber,
                subtitle: 'Awaiting processing',
                isPositive: pendingOrders == 0,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildKPICard({
    required BuildContext context,
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required String subtitle,
    required bool isPositive,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(icon, color: color, size: 20.sp),
              ),
              const Spacer(),
              Icon(
                isPositive ? Icons.trending_up : Icons.trending_down,
                color: isPositive ? Colors.green : Colors.amber,
                size: 16.sp,
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: context.appColors.subtextColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTodayChartsSection(
    BuildContext context,
    SupervisorDashboardEntity dashboard,
  ) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: context.appColors.dividerColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.analytics, color: Colors.purple, size: 24.sp),
              SizedBox(width: 8.w),
              Text(
                'Today\'s Analytics',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 20.h),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Order Status Distribution',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: context.appColors.textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 12.h),
                    _buildTodayOrderStatusChart(
                      context,
                      dashboard.todayStats.orders,
                    ),
                  ],
                ),
              ),
              SizedBox(width: 24.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Top Selling Products',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: context.appColors.textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 12.h),
                    _buildTodayTopProductsChart(
                      context,
                      dashboard.todayTopProducts,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTodayOrderStatusChart(
    BuildContext context,
    SupervisorOrderStatsEntity orderStats,
  ) {
    final data = [
      ChartData('Pending', orderStats.pending, Colors.orange),
      ChartData('Confirmed', orderStats.confirmed, Colors.blue),
      ChartData('Out for Delivery', orderStats.outForDelivery, Colors.purple),
      ChartData('Delivered', orderStats.delivered, Colors.green),
      ChartData('Cancelled', orderStats.cancelled, Colors.red),
      ChartData('Failed', orderStats.failed, Colors.grey),
    ].where((data) => data.value > 0).toList();

    if (data.isEmpty) {
      return SizedBox(
        height: 200.h,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.pie_chart_outline,
                size: 48.sp,
                color: context.appColors.subtextColor,
              ),
              SizedBox(height: 8.h),
              Text(
                'No orders today',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.subtextColor,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SizedBox(
      height: 200.h,
      child: SfCircularChart(
        legend: Legend(
          isVisible: true,
          position: LegendPosition.bottom,
          textStyle: TextStyle(
            color: context.appColors.textColor,
            fontSize: 10.sp,
          ),
        ),
        series: <CircularSeries>[
          DoughnutSeries<ChartData, String>(
            dataSource: data,
            xValueMapper: (ChartData data, _) => data.category,
            yValueMapper: (ChartData data, _) => data.value,
            pointColorMapper: (ChartData data, _) => data.color,
            dataLabelSettings: const DataLabelSettings(
              labelPosition: ChartDataLabelPosition.outside,
            ),
            innerRadius: '60%',
          ),
        ],
      ),
    );
  }

  Widget _buildTodayTopProductsChart(
    BuildContext context,
    List<TodayTopProductEntity> topProducts,
  ) {
    if (topProducts.isEmpty) {
      return SizedBox(
        height: 200.h,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.inventory_2_outlined,
                size: 48.sp,
                color: context.appColors.subtextColor,
              ),
              SizedBox(height: 8.h),
              Text(
                'No products sold today',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.subtextColor,
                ),
              ),
            ],
          ),
        ),
      );
    }

    final data = topProducts.take(5).map((product) {
      final name =
          product.productDetails.name ??
          product.productDetails.type ??
          'Product ${product.id.itemId}';
      return ChartData(
        name,
        product.totalQuantity,
        context.appColors.primaryColor,
      );
    }).toList();

    return SizedBox(
      height: 200.h,
      child: SfCartesianChart(
        primaryXAxis: CategoryAxis(
          labelStyle: TextStyle(
            color: context.appColors.subtextColor,
            fontSize: 10.sp,
          ),
          majorGridLines: const MajorGridLines(width: 0),
        ),
        primaryYAxis: NumericAxis(
          labelStyle: TextStyle(
            color: context.appColors.subtextColor,
            fontSize: 10.sp,
          ),
          majorGridLines: MajorGridLines(
            color: context.appColors.dividerColor,
            width: 0.5,
          ),
        ),
        plotAreaBorderWidth: 0,
        series: <CartesianSeries>[
          ColumnSeries<ChartData, String>(
            dataSource: data,
            xValueMapper: (ChartData data, _) => data.category,
            yValueMapper: (ChartData data, _) => data.value,
            color: context.appColors.primaryColor,
            dataLabelSettings: DataLabelSettings(
              isVisible: true,
              textStyle: TextStyle(
                color: context.appColors.textColor,
                fontSize: 10.sp,
              ),
            ),
            borderRadius: BorderRadius.circular(4.r),
          ),
        ],
      ),
    );
  }

  Widget _buildAvailableAgents(
    BuildContext context,
    List<AvailableAgentEntity> agents,
  ) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: context.appColors.dividerColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(Icons.people, color: Colors.blue, size: 24.sp),
                  SizedBox(width: 8.w),
                  Text(
                    'Available Agents',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: context.appColors.textColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: agents.isNotEmpty
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Text(
                  '${agents.length} Online',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: agents.isNotEmpty ? Colors.green : Colors.red,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          if (agents.isEmpty)
            Container(
              padding: EdgeInsets.all(24.w),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.person_off,
                      size: 48.sp,
                      color: context.appColors.subtextColor,
                    ),
                    SizedBox(height: 12.h),
                    Text(
                      'No agents available',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: context.appColors.subtextColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'All agents are currently busy or offline',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: context.appColors.subtextColor,
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            ...agents.take(5).map((agent) => _buildAgentCard(context, agent)),
          if (agents.length > 5) ...[
            SizedBox(height: 12.h),
            Center(
              child: TextButton(
                onPressed: () {
                  // TODO: Navigate to full agents list
                },
                child: Text(
                  'View All ${agents.length} Agents',
                  style: TextStyle(
                    color: context.appColors.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAgentCard(BuildContext context, AvailableAgentEntity agent) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.backgroundColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Row(
        children: [
          Container(
            width: 48.w,
            height: 48.h,
            decoration: BoxDecoration(
              color: context.appColors.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(24.r),
            ),
            child: Icon(
              Icons.person,
              color: context.appColors.primaryColor,
              size: 24.sp,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  agent.phone,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (agent.email != null) ...[
                  SizedBox(height: 2.h),
                  Text(
                    agent.email!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: context.appColors.subtextColor,
                    ),
                  ),
                ],
                SizedBox(height: 4.h),
                Row(
                  children: [
                    Icon(Icons.star, size: 14.sp, color: Colors.amber),
                    SizedBox(width: 4.w),
                    Text(
                      agent.agentMetadata.rating.toStringAsFixed(1),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: context.appColors.subtextColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (agent.agentMetadata.vehicle != null) ...[
                      SizedBox(width: 12.w),
                      Icon(
                        Icons.local_shipping,
                        size: 14.sp,
                        color: context.appColors.subtextColor,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        agent.agentMetadata.vehicle!.type,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                      ),
                    ],
                    const Spacer(),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 6.w,
                        vertical: 2.h,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: Text(
                        'Online',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.green,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(width: 12.w),
          ElevatedButton(
            onPressed: () {
              // TODO: Implement assign order functionality
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Assign order to ${agent.phone}'),
                  backgroundColor: context.appColors.primaryColor,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: context.appColors.primaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: const Text('Assign'),
          ),
        ],
      ),
    );
  }

  Widget _buildTodayOrders(
    BuildContext context,
    List<TodayOrderEntity> orders,
  ) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: context.appColors.dividerColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(Icons.receipt_long, color: Colors.orange, size: 24.sp),
                  SizedBox(width: 8.w),
                  Text(
                    'Today\'s Orders',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: context.appColors.textColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              if (orders.isNotEmpty)
                TextButton(
                  onPressed: () {
                    // TODO: Navigate to full orders list
                  },
                  child: Text(
                    'View All',
                    style: TextStyle(
                      color: context.appColors.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 16.h),
          if (orders.isEmpty)
            Container(
              padding: EdgeInsets.all(24.w),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.receipt_outlined,
                      size: 48.sp,
                      color: context.appColors.subtextColor,
                    ),
                    SizedBox(height: 12.h),
                    Text(
                      'No orders today',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: context.appColors.subtextColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'Orders will appear here as they come in',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: context.appColors.subtextColor,
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            ...orders.take(8).map((order) => _buildOrderCard(context, order)),
        ],
      ),
    );
  }

  Widget _buildOrderCard(BuildContext context, TodayOrderEntity order) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.backgroundColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Row(
        children: [
          Container(
            width: 48.w,
            height: 48.h,
            decoration: BoxDecoration(
              color: _getStatusColor(order.status).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              _getStatusIcon(order.status),
              color: _getStatusColor(order.status),
              size: 24.sp,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      'Order #${order.id.substring(order.id.length - 6)}',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: context.appColors.textColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '\$${NumberFormat('#,##0.00').format(order.totalAmount)}',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: context.appColors.textColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 4.h),
                Text(
                  order.customer.phone,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: context.appColors.subtextColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  order.deliveryAddress,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 8.h),
                Row(
                  children: [
                    Text(
                      DateFormat('HH:mm').format(order.createdAt),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: context.appColors.subtextColor,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 4.h,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(
                          order.status,
                        ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Text(
                        _getStatusDisplayName(order.status),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: _getStatusColor(order.status),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'out_for_delivery':
      case 'outfordelivery':
        return Colors.purple;
      case 'delivered':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      case 'failed':
        return Colors.red.shade700;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Icons.pending;
      case 'confirmed':
        return Icons.check_circle_outline;
      case 'out_for_delivery':
      case 'outfordelivery':
        return Icons.local_shipping;
      case 'delivered':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      case 'failed':
        return Icons.error;
      default:
        return Icons.help_outline;
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'Pending';
      case 'confirmed':
        return 'Confirmed';
      case 'out_for_delivery':
      case 'outfordelivery':
        return 'Out for Delivery';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
      case 'failed':
        return 'Failed';
      default:
        return status.toUpperCase();
    }
  }
}

// Helper class for chart data
class ChartData {
  final String category;
  final int value;
  final Color color;

  ChartData(this.category, this.value, this.color);
}
