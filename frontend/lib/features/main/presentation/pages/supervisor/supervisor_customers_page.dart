import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/config/router/extension/navigation_extension.dart';
import 'package:frontend/core/enums/user_role.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/core/utils/helpers/snack_bar_helper.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';
import 'package:frontend/features/user-management/presentation/bloc/user_bloc.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_button.dart';

import 'supervisor_register_customer_page.dart';

class SupervisorCustomersPage extends StatefulWidget {
  const SupervisorCustomersPage({super.key});

  @override
  State<SupervisorCustomersPage> createState() =>
      _SupervisorCustomersPageState();
}

class _SupervisorCustomersPageState extends State<SupervisorCustomersPage> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // Search and pagination state
  List<UserEntity> _customers = [];
  String _currentSearchQuery = '';
  bool _isLoading = false;
  bool _isSearching = false;
  bool _hasMoreData = true;
  bool _isLoadingMore = false;
  int _currentPage = 1;
  final int _pageSize = 20;

  // Debounce timer for search
  Timer? _searchDebounceTimer;
  static const Duration _searchDebounceDelay = Duration(milliseconds: 500);

  // Error handling
  String? _errorMessage;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
    _setupSearchListener();
    _setupScrollListener();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  void _initializeData() {
    _fetchCustomers(isInitialLoad: true);
  }

  void _setupSearchListener() {
    _searchController.addListener(_onSearchChanged);
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        _loadMoreCustomers();
      }
    });
  }

  // 🔍 Debounced search implementation
  void _onSearchChanged() {
    _searchDebounceTimer?.cancel();
    _searchDebounceTimer = Timer(_searchDebounceDelay, () {
      final query = _searchController.text.trim();
      if (query != _currentSearchQuery) {
        _performSearch(query);
      }
    });
  }

  // 🚀 Perform search with server-side filtering
  void _performSearch(String query) {
    setState(() {
      _currentSearchQuery = query;
      _currentPage = 1;
      _hasMoreData = true;
      _customers.clear();
      _hasError = false;
      _errorMessage = null;
    });
    _fetchCustomers(searchQuery: query);
  }

  // 📊 Fetch customers with pagination and search
  void _fetchCustomers({bool isInitialLoad = false, String? searchQuery}) {
    if (_isLoading || _isLoadingMore) return;

    setState(() {
      if (isInitialLoad) {
        _isLoading = true;
        _customers.clear();
        _currentPage = 1;
        _hasMoreData = true;
      } else {
        _isSearching = searchQuery != null && searchQuery.isNotEmpty;
      }
      _hasError = false;
      _errorMessage = null;
    });

    // Use search query if provided, otherwise use current search query
    final query = searchQuery ?? _currentSearchQuery;

    // 🚀 Now using powerful server-side search functionality!
    context.userBloc.add(
      GetAllUsersEvent(
        role: UserRole.customer,
        page: _currentPage,
        limit: _pageSize,
        search: query.isNotEmpty ? query : null,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      ),
    );

    // Log search activity for debugging
    if (query.isNotEmpty) {
      debugPrint(
        '🔍 Server-side search for: "$query" (page: $_currentPage, limit: $_pageSize)',
      );
    } else {
      debugPrint(
        '📋 Loading customers (page: $_currentPage, limit: $_pageSize)',
      );
    }
  }

  // 📄 Load more customers for pagination
  void _loadMoreCustomers() {
    if (_isLoadingMore || !_hasMoreData || _isLoading) return;

    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });

    context.userBloc.add(
      GetAllUsersEvent(
        role: UserRole.customer,
        page: _currentPage,
        limit: _pageSize,
        search: _currentSearchQuery.isNotEmpty ? _currentSearchQuery : null,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      ),
    );
  }

  // 🔄 Refresh data
  void _refreshData() {
    setState(() {
      _currentPage = 1;
      _hasMoreData = true;
      _customers.clear();
      _currentSearchQuery = '';
      _searchController.clear();
    });
    _fetchCustomers(isInitialLoad: true);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.surfaceColor,
      appBar: AppBar(
        title: const Text('Customers'),
        backgroundColor: context.appColors.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _refreshData),
        ],
      ),
      body: BlocListener<UserBloc, UserState>(
        listener: _handleUserBlocState,
        child: Column(
          children: [
            _buildSearchBar(),
            Expanded(child: _buildCustomersList()),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          context.pushRoute(const SupervisorRegisterCustomerPage());
        },
        backgroundColor: context.appColors.primaryColor,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.person_add),
        label: const Text('Add Customer'),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        border: Border(
          bottom: BorderSide(color: context.appColors.dividerColor),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search customers by phone, email, or username...',
                prefixIcon: _isSearching
                    ? SizedBox(
                        width: 20.w,
                        height: 20.h,
                        child: Padding(
                          padding: EdgeInsets.all(12.w),
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              context.appColors.primaryColor,
                            ),
                          ),
                        ),
                      )
                    : const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _performSearch('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  borderSide: BorderSide(color: context.appColors.dividerColor),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  borderSide: BorderSide(color: context.appColors.dividerColor),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  borderSide: BorderSide(color: context.appColors.primaryColor),
                ),
                filled: true,
                fillColor: context.appColors.backgroundColor,
              ),
            ),
          ),
          if (_currentSearchQuery.isNotEmpty) ...[
            SizedBox(width: 8.w),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
              decoration: BoxDecoration(
                color: context.appColors.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                '${_customers.length} results',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: context.appColors.primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCustomersList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64.sp, color: Colors.red),
            SizedBox(height: 16.h),
            Text(
              'Error loading customers',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: Colors.red),
            ),
            SizedBox(height: 8.h),
            Text(
              _errorMessage ?? 'Something went wrong',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.subtextColor,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            CustomButton(
              buttonText: 'Retry',
              onTap: _refreshData,
              leadingIcon: const Icon(Icons.refresh),
            ),
          ],
        ),
      );
    }

    if (_customers.isEmpty && _currentSearchQuery.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64.sp,
              color: context.appColors.subtextColor,
            ),
            SizedBox(height: 16.h),
            Text(
              'No customers found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Try adjusting your search terms',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
          ],
        ),
      );
    }

    if (_customers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64.sp,
              color: context.appColors.subtextColor,
            ),
            SizedBox(height: 16.h),
            Text(
              'No customers yet',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Add your first customer to get started',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
            SizedBox(height: 24.h),
            CustomButton(
              buttonText: 'Add Customer',
              onTap: () {
                context.pushRoute(const SupervisorRegisterCustomerPage());
              },
              leadingIcon: const Icon(Icons.person_add),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _refreshData();
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.all(16.w),
        itemCount: _customers.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _customers.length) {
            return _buildLoadingIndicator();
          }
          final customer = _customers[index];
          return _buildCustomerCard(customer);
        },
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: EdgeInsets.all(16.w),
      alignment: Alignment.center,
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(
          context.appColors.primaryColor,
        ),
      ),
    );
  }

  Widget _buildCustomerCard(UserEntity customer) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.backgroundColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 24.r,
            backgroundColor: context.appColors.primaryColor,
            child: Icon(Icons.person, color: Colors.white, size: 24.sp),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  customer.phone,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: context.appColors.textColor,
                  ),
                ),
                if (customer.email != null) ...[
                  SizedBox(height: 4.h),
                  Text(
                    customer.email!,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: context.appColors.subtextColor,
                    ),
                  ),
                ],
                if (customer.username != null) ...[
                  SizedBox(height: 4.h),
                  Text(
                    '@${customer.username}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: context.appColors.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
                SizedBox(height: 4.h),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                  decoration: BoxDecoration(
                    color: customer.isActive
                        ? Colors.green.withValues(alpha: 0.1)
                        : Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Text(
                    customer.isActive ? 'Active' : 'Inactive',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: customer.isActive ? Colors.green : Colors.red,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            size: 16.sp,
            color: context.appColors.subtextColor,
          ),
        ],
      ),
    );
  }

  void _handleUserBlocState(BuildContext context, UserState state) {
    if (state is GetAllUsersLoading) {
      // Handle loading state based on current operation
      if (_currentPage == 1) {
        setState(() {
          _isLoading = true;
        });
      } else {
        setState(() {
          _isLoadingMore = true;
        });
      }
    } else if (state is GetAllUsersSuccess) {
      setState(() {
        _isLoading = false;
        _isLoadingMore = false;
        _isSearching = false;
        _hasError = false;
        _errorMessage = null;

        if (_currentPage == 1) {
          // First page or new search - replace data
          _customers = List.from(state.users);
        } else {
          // Subsequent pages - append data
          _customers.addAll(state.users);
        }

        // Check if we have more data
        _hasMoreData = state.users.length == _pageSize;
      });
    } else if (state is GetAllUsersFailure) {
      setState(() {
        _isLoading = false;
        _isLoadingMore = false;
        _isSearching = false;
        _hasError = true;
        _errorMessage = state.appFailure.getErrorMessage();
      });

      SnackBarHelper.showErrorSnackBar(
        context,
        message:
            'Failed to load customers: ${state.appFailure.getErrorMessage()}',
      );
    }
  }
}
