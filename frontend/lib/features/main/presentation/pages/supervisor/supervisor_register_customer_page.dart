import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/button_state.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_button.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_textfield.dart';
import 'package:frontend/features/user-management/presentation/bloc/user_bloc.dart';

import '../../../../../core/utils/helpers/snack_bar_helper.dart';

class SupervisorRegisterCustomerPage extends StatefulWidget {
  const SupervisorRegisterCustomerPage({super.key});

  @override
  State<SupervisorRegisterCustomerPage> createState() =>
      _SupervisorRegisterCustomerPageState();
}

class _SupervisorRegisterCustomerPageState
    extends State<SupervisorRegisterCustomerPage> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _usernameController = TextEditingController();
  final _streetController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _zipCodeController = TextEditingController();
  final _countryController = TextEditingController();

  bool _addAddress = false;

  @override
  void dispose() {
    _phoneController.dispose();
    _emailController.dispose();
    _usernameController.dispose();
    _streetController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _zipCodeController.dispose();
    _countryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.surfaceColor,
      appBar: AppBar(
        title: const Text('Register New Customer'),
        backgroundColor: context.appColors.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: BlocListener<UserBloc, UserState>(
        listener: (context, state) {
          if (state is RegisterCustomerSuccess) {
            // ScaffoldMessenger.of(context).showSnackBar(
            //   const SnackBar(
            //     content: Text('Customer registered successfully!'),
            //     backgroundColor: Colors.green,
            //   ),
            // );
            SnackBarHelper.showSuccessSnackBar(
              context,
              message: 'Customer registered successfully!',
            );
            Navigator.pop(context);
          } else if (state is RegisterCustomerFailure) {
            SnackBarHelper.showErrorSnackBar(
              context,
              message: state.appFailure.getErrorMessage(),
            );
          }
        },
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionTitle('Customer Information'),
                SizedBox(height: 16.h),
                _buildPhoneField(),
                SizedBox(height: 16.h),
                _buildEmailField(),
                SizedBox(height: 16.h),
                _buildUsernameField(),
                SizedBox(height: 24.h),
                // _buildAddressToggle(),
                // if (_addAddress) ...[
                //   SizedBox(height: 16.h),
                //   _buildSectionTitle('Address Information'),
                //   SizedBox(height: 16.h),
                //   _buildAddressFields(),
                // ],
                SizedBox(height: 32.h),
                _buildRegisterButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
        fontWeight: FontWeight.bold,
        color: context.appColors.textColor,
      ),
    );
  }

  Widget _buildPhoneField() {
    return CustomTextField(
      controller: _phoneController,
      labelText: 'Phone Number',
      hintText: '61XXXXXXXX',
      keyboardType: TextInputType.phone,
      prefixIcon: Container(
        padding: EdgeInsets.all(12.w),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '+252',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(width: 8.w),
            Container(
              width: 1.w,
              height: 20.h,
              color: context.appColors.disabledColor,
            ),
          ],
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Phone number is required';
        }
        if (value.length != 9) {
          return 'Phone number must be 9 digits';
        }
        if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
          return 'Phone number must contain only digits';
        }
        return null;
      },
    );
  }

  Widget _buildEmailField() {
    return CustomTextField(
      controller: _emailController,
      labelText: 'Email (Optional)',
      keyboardType: TextInputType.emailAddress,
      prefixIcon: const Icon(Icons.email),
      validator: (value) {
        if (value != null && value.isNotEmpty) {
          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
            return 'Please enter a valid email';
          }
        }
        return null;
      },
    );
  }

  Widget _buildUsernameField() {
    return CustomTextField(
      controller: _usernameController,
      labelText: 'Username (Optional)',
      prefixIcon: const Icon(Icons.person),
      validator: (value) {
        if (value != null && value.isNotEmpty && value.length < 3) {
          return 'Username must be at least 3 characters';
        }
        return null;
      },
    );
  }

  Widget _buildAddressToggle() {
    return Row(
      children: [
        Checkbox(
          value: _addAddress,
          onChanged: (value) {
            setState(() {
              _addAddress = value ?? false;
            });
          },
        ),
        Text(
          'Add delivery address',
          style: Theme.of(context).textTheme.bodyLarge,
        ),
      ],
    );
  }

  Widget _buildAddressFields() {
    return Column(
      children: [
        CustomTextField(
          controller: _streetController,
          labelText: 'Street Address',
          prefixIcon: const Icon(Icons.location_on),
          validator: _addAddress
              ? (value) {
                  if (value == null || value.isEmpty) {
                    return 'Street address is required';
                  }
                  return null;
                }
              : null,
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _cityController,
                labelText: 'City',
                prefixIcon: const Icon(Icons.location_city),
                validator: _addAddress
                    ? (value) {
                        if (value == null || value.isEmpty) {
                          return 'City is required';
                        }
                        return null;
                      }
                    : null,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: CustomTextField(
                controller: _stateController,
                labelText: 'State',
                prefixIcon: const Icon(Icons.map),
                validator: _addAddress
                    ? (value) {
                        if (value == null || value.isEmpty) {
                          return 'State is required';
                        }
                        return null;
                      }
                    : null,
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _zipCodeController,
                labelText: 'Zip Code',
                prefixIcon: const Icon(Icons.local_post_office),
                validator: _addAddress
                    ? (value) {
                        if (value == null || value.isEmpty) {
                          return 'Zip code is required';
                        }
                        return null;
                      }
                    : null,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: CustomTextField(
                controller: _countryController,
                labelText: 'Country',
                prefixIcon: const Icon(Icons.flag),
                validator: _addAddress
                    ? (value) {
                        if (value == null || value.isEmpty) {
                          return 'Country is required';
                        }
                        return null;
                      }
                    : null,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRegisterButton() {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, state) {
        return CustomButton(
          buttonText: 'Register Customer',
          buttonState: state is RegisterCustomerLoading
              ? ButtonState.loading
              : ButtonState.normal,
          onTap: _registerCustomer,
          leadingIcon: const Icon(Icons.person_add),
        );
      },
    );
  }

  void _registerCustomer() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final phone = '+252${_phoneController.text.trim()}';
    final email = _emailController.text.trim().isEmpty
        ? null
        : _emailController.text.trim();
    final username = _usernameController.text.trim().isEmpty
        ? null
        : _usernameController.text.trim();

    List<Map<String, dynamic>>? addresses;
    if (_addAddress) {
      addresses = [
        {
          'street': _streetController.text.trim(),
          'city': _cityController.text.trim(),
          'state': _stateController.text.trim(),
          'zipCode': _zipCodeController.text.trim(),
          'country': _countryController.text.trim(),
          'isDefault': true,
        },
      ];
    }

    context.read<UserBloc>().add(
      RegisterCustomerEvent(
        phone: phone,
        email: email,
        username: username,
        addresses: addresses,
      ),
    );
  }
}
