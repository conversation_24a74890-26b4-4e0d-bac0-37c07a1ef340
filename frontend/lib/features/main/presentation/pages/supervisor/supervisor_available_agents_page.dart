import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/features/dashboard/presentation/bloc/dashboard_bloc.dart';
import 'package:frontend/features/dashboard/domain/entities/supervisor_dashboard_entity.dart';

class SupervisorAvailableAgentsPage extends StatefulWidget {
  const SupervisorAvailableAgentsPage({super.key});

  @override
  State<SupervisorAvailableAgentsPage> createState() =>
      _SupervisorAvailableAgentsPageState();
}

class _SupervisorAvailableAgentsPageState
    extends State<SupervisorAvailableAgentsPage> {
  final TextEditingController _searchController = TextEditingController();
  List<AvailableAgentEntity> _filteredAgents = [];
  String _sortBy = 'rating'; // rating, phone, vehicle

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadDashboardData() {
    context.read<DashboardBloc>().add(GetSupervisorDashboardEvent());
  }

  void _filterAndSortAgents(List<AvailableAgentEntity> agents) {
    setState(() {
      _filteredAgents = agents.where((agent) {
        final matchesSearch =
            _searchController.text.isEmpty ||
            agent.phone.toLowerCase().contains(
              _searchController.text.toLowerCase(),
            ) ||
            (agent.email?.toLowerCase().contains(
                  _searchController.text.toLowerCase(),
                ) ??
                false);
        return matchesSearch;
      }).toList();

      // Sort agents
      _filteredAgents.sort((a, b) {
        switch (_sortBy) {
          case 'rating':
            return b.agentMetadata.rating.compareTo(a.agentMetadata.rating);
          case 'phone':
            return a.phone.compareTo(b.phone);
          case 'vehicle':
            final aVehicle = a.agentMetadata.vehicle?.type ?? '';
            final bVehicle = b.agentMetadata.vehicle?.type ?? '';
            return aVehicle.compareTo(bVehicle);
          default:
            return 0;
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.surfaceColor,
        elevation: 0,
        title: Text(
          'Available Agents',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _loadDashboardData,
            icon: Icon(Icons.refresh, color: context.appColors.primaryColor),
          ),
        ],
      ),
      body: SafeArea(
        child: BlocBuilder<DashboardBloc, DashboardState>(
          builder: (context, state) {
            if (state is GetSupervisorDashboardLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (state is GetSupervisorDashboardFailure) {
              return _buildErrorWidget(
                context,
                state.appFailure.getErrorMessage(),
              );
            }

            if (state is GetSupervisorDashboardSuccess) {
              final dashboard = state.dashboard;

              // Filter and sort agents on data load
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _filterAndSortAgents(dashboard.availableAgents);
              });

              return _buildContent(context, dashboard);
            }

            return _buildEmptyWidget(context);
          },
        ),
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    SupervisorDashboardEntity dashboard,
  ) {
    return Column(
      children: [
        _buildStatsHeader(context, dashboard.availableAgents),
        _buildFiltersSection(context),
        Expanded(child: _buildAgentsList(context)),
      ],
    );
  }

  Widget _buildStatsHeader(
    BuildContext context,
    List<AvailableAgentEntity> agents,
  ) {
    final totalAgents = agents.length;
    final averageRating = agents.isEmpty
        ? 0.0
        : agents.map((a) => a.agentMetadata.rating).reduce((a, b) => a + b) /
              agents.length;
    final agentsWithVehicles = agents
        .where((a) => a.agentMetadata.vehicle != null)
        .length;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        border: Border(
          bottom: BorderSide(color: context.appColors.dividerColor),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              context,
              'Total Agents',
              totalAgents.toString(),
              Icons.people,
              context.appColors.primaryColor,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildStatCard(
              context,
              'Avg Rating',
              averageRating.toStringAsFixed(1),
              Icons.star,
              Colors.amber,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildStatCard(
              context,
              'With Vehicles',
              agentsWithVehicles.toString(),
              Icons.local_shipping,
              Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20.sp),
          SizedBox(height: 4.h),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: context.appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        border: Border(
          bottom: BorderSide(color: context.appColors.dividerColor),
        ),
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search agents by phone or email...',
              prefixIcon: Icon(
                Icons.search,
                color: context.appColors.subtextColor,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: BorderSide(color: context.appColors.dividerColor),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: BorderSide(color: context.appColors.dividerColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: BorderSide(color: context.appColors.primaryColor),
              ),
            ),
            onChanged: (value) =>
                _filterAndSortAgents(_getAgentsFromCurrentState()),
          ),
          SizedBox(height: 12.h),
          // Sort options
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _sortBy,
                  decoration: InputDecoration(
                    labelText: 'Sort by',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  items: const [
                    DropdownMenuItem(
                      value: 'rating',
                      child: Text('Rating (High to Low)'),
                    ),
                    DropdownMenuItem(
                      value: 'phone',
                      child: Text('Phone Number'),
                    ),
                    DropdownMenuItem(
                      value: 'vehicle',
                      child: Text('Vehicle Type'),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _sortBy = value!;
                    });
                    _filterAndSortAgents(_getAgentsFromCurrentState());
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAgentsList(BuildContext context) {
    if (_filteredAgents.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people,
              size: 64.sp,
              color: context.appColors.subtextColor,
            ),
            SizedBox(height: 16.h),
            Text(
              'No agents found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Try adjusting your search criteria',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _loadDashboardData();
      },
      child: ListView.separated(
        padding: EdgeInsets.all(16.w),
        itemCount: _filteredAgents.length,
        separatorBuilder: (context, index) => SizedBox(height: 12.h),
        itemBuilder: (context, index) {
          final agent = _filteredAgents[index];
          return _buildAgentCard(context, agent);
        },
      ),
    );
  }

  Widget _buildAgentCard(BuildContext context, AvailableAgentEntity agent) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30.r,
                  backgroundColor: context.appColors.primaryColor,
                  child: Text(
                    agent.phone.substring(0, 2).toUpperCase(),
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        agent.phone,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: context.appColors.textColor,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      if (agent.email != null) ...[
                        SizedBox(height: 4.h),
                        Text(
                          agent.email!,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: context.appColors.subtextColor),
                        ),
                      ],
                      SizedBox(height: 8.h),
                      Row(
                        children: [
                          Icon(Icons.star, color: Colors.amber, size: 16.sp),
                          SizedBox(width: 4.w),
                          Text(
                            agent.agentMetadata.rating.toStringAsFixed(1),
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(
                                  color: context.appColors.textColor,
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                          SizedBox(width: 8.w),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 8.w,
                              vertical: 2.h,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.green.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                            child: Text(
                              'Available',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: Colors.green,
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (agent.agentMetadata.vehicle != null) ...[
              SizedBox(height: 16.h),
              _buildVehicleInfo(context, agent.agentMetadata.vehicle!),
            ],
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showAgentDetails(context, agent),
                    icon: const Icon(Icons.info),
                    label: const Text('View Details'),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showAssignOrderDialog(context, agent),
                    icon: const Icon(Icons.assignment),
                    label: const Text('Assign Order'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVehicleInfo(BuildContext context, VehicleEntity vehicle) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: context.appColors.backgroundColor,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Row(
        children: [
          Icon(
            Icons.local_shipping,
            color: context.appColors.primaryColor,
            size: 20.sp,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  vehicle.type,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (vehicle.plateNumber != null) ...[
                  SizedBox(height: 2.h),
                  Text(
                    'Plate: ${vehicle.plateNumber}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: context.appColors.subtextColor,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showAgentDetails(BuildContext context, AvailableAgentEntity agent) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Agent Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Phone', agent.phone),
              if (agent.email != null) _buildDetailRow('Email', agent.email!),
              _buildDetailRow(
                'Rating',
                '${agent.agentMetadata.rating.toStringAsFixed(1)} ⭐',
              ),
              if (agent.agentMetadata.vehicle != null) ...[
                _buildDetailRow(
                  'Vehicle Type',
                  agent.agentMetadata.vehicle!.type,
                ),
                if (agent.agentMetadata.vehicle!.plateNumber != null)
                  _buildDetailRow(
                    'Plate Number',
                    agent.agentMetadata.vehicle!.plateNumber!,
                  ),
              ],
              _buildDetailRow('Status', 'Available'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showAssignOrderDialog(
    BuildContext context,
    AvailableAgentEntity agent,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Assign Order'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Assign an order to ${agent.phone}'),
            SizedBox(height: 16.h),
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: context.appColors.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.construction,
                    color: context.appColors.primaryColor,
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'Order Assignment',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: context.appColors.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    'This feature will be available soon. You can assign orders from the Order Assignment page.',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: context.appColors.primaryColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  List<AvailableAgentEntity> _getAgentsFromCurrentState() {
    final state = context.read<DashboardBloc>().state;
    if (state is GetSupervisorDashboardSuccess) {
      return state.dashboard.availableAgents;
    }
    return [];
  }

  Widget _buildErrorWidget(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.sp, color: Colors.red),
          SizedBox(height: 16.h),
          Text(
            'Error Loading Agents',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.textColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _loadDashboardData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people,
            size: 64.sp,
            color: context.appColors.subtextColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'No Data Available',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _loadDashboardData,
            child: const Text('Load Data'),
          ),
        ],
      ),
    );
  }
}
