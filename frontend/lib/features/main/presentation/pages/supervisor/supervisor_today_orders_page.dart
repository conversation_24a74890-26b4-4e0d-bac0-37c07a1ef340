import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';

import 'package:frontend/core/utils/helpers/snack_bar_helper.dart';
import 'package:frontend/features/dashboard/presentation/bloc/dashboard_bloc.dart';
import 'package:frontend/features/dashboard/domain/entities/supervisor_dashboard_entity.dart';
import 'package:frontend/features/order/presentation/bloc/order_bloc.dart';
import 'package:intl/intl.dart';

class SupervisorTodayOrdersPage extends StatefulWidget {
  const SupervisorTodayOrdersPage({super.key});

  @override
  State<SupervisorTodayOrdersPage> createState() =>
      _SupervisorTodayOrdersPageState();
}

class _SupervisorTodayOrdersPageState extends State<SupervisorTodayOrdersPage> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedStatus = 'All';
  List<TodayOrderEntity> _filteredOrders = [];

  final List<String> _statusOptions = [
    'All',
    'PENDING',
    'CONFIRMED',
    'IN_TRANSIT',
    'DELIVERED',
    'CANCELLED',
  ];

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadDashboardData() {
    context.read<DashboardBloc>().add(GetSupervisorDashboardEvent());
  }

  void _filterOrders(List<TodayOrderEntity> orders) {
    setState(() {
      _filteredOrders = orders.where((order) {
        final matchesSearch =
            _searchController.text.isEmpty ||
            order.customer.phone.toLowerCase().contains(
              _searchController.text.toLowerCase(),
            ) ||
            order.deliveryAddress.toLowerCase().contains(
              _searchController.text.toLowerCase(),
            ) ||
            order.id.toLowerCase().contains(
              _searchController.text.toLowerCase(),
            );

        final matchesStatus =
            _selectedStatus == 'All' ||
            order.status.toUpperCase() == _selectedStatus;

        return matchesSearch && matchesStatus;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.surfaceColor,
        elevation: 0,
        title: Text(
          'Today\'s Orders',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _loadDashboardData,
            icon: Icon(Icons.refresh, color: context.appColors.primaryColor),
          ),
        ],
      ),
      body: SafeArea(
        child: BlocListener<OrderBloc, OrderState>(
          listener: (context, state) {
            if (state is RegenerateQRCodeSuccess) {
              SnackBarHelper.showSuccessSnackBar(
                context,
                message: state.message,
              );
              // Refresh dashboard to show updated data
              _loadDashboardData();
            } else if (state is RegenerateQRCodeFailure) {
              SnackBarHelper.showErrorSnackBar(
                context,
                message: state.appFailure.getErrorMessage(),
              );
            }
          },
          child: BlocBuilder<DashboardBloc, DashboardState>(
            builder: (context, state) {
              if (state is GetSupervisorDashboardLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              if (state is GetSupervisorDashboardFailure) {
                return _buildErrorWidget(
                  context,
                  state.appFailure.getErrorMessage(),
                );
              }

              if (state is GetSupervisorDashboardSuccess) {
                final dashboard = state.dashboard;

                // Filter orders on data load
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _filterOrders(dashboard.todayOrders);
                });

                return _buildContent(context, dashboard);
              }

              return _buildEmptyWidget(context);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    SupervisorDashboardEntity dashboard,
  ) {
    return Column(
      children: [
        _buildStatsHeader(context, dashboard),
        _buildFiltersSection(context),
        Expanded(child: _buildOrdersList(context)),
      ],
    );
  }

  Widget _buildStatsHeader(
    BuildContext context,
    SupervisorDashboardEntity dashboard,
  ) {
    final stats = dashboard.todayStats;
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        border: Border(
          bottom: BorderSide(color: context.appColors.dividerColor),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              context,
              'Total Orders',
              stats.orders.total.toString(),
              Icons.receipt_long,
              context.appColors.primaryColor,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildStatCard(
              context,
              'Delivered',
              stats.orders.delivered.toString(),
              Icons.check_circle,
              Colors.green,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildStatCard(
              context,
              'Revenue',
              '\$${stats.sales.totalValue.toStringAsFixed(0)}',
              Icons.attach_money,
              Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20.sp),
          SizedBox(height: 4.h),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: context.appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        border: Border(
          bottom: BorderSide(color: context.appColors.dividerColor),
        ),
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search orders by customer, address, or ID...',
              prefixIcon: Icon(
                Icons.search,
                color: context.appColors.subtextColor,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: BorderSide(color: context.appColors.dividerColor),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: BorderSide(color: context.appColors.dividerColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: BorderSide(color: context.appColors.primaryColor),
              ),
            ),
            onChanged: (value) => _filterOrders(_getOrdersFromCurrentState()),
          ),
          SizedBox(height: 12.h),
          // Status filter
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedStatus,
                  decoration: InputDecoration(
                    labelText: 'Filter by Status',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  items: _statusOptions.map((status) {
                    return DropdownMenuItem(
                      value: status,
                      child: Text(
                        status == 'All'
                            ? 'All Statuses'
                            : status.replaceAll('_', ' '),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedStatus = value!;
                    });
                    _filterOrders(_getOrdersFromCurrentState());
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersList(BuildContext context) {
    if (_filteredOrders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long,
              size: 64.sp,
              color: context.appColors.subtextColor,
            ),
            SizedBox(height: 16.h),
            Text(
              'No orders found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Try adjusting your search or filter criteria',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _loadDashboardData();
      },
      child: ListView.separated(
        padding: EdgeInsets.all(16.w),
        itemCount: _filteredOrders.length,
        separatorBuilder: (context, index) => SizedBox(height: 12.h),
        itemBuilder: (context, index) {
          final order = _filteredOrders[index];
          return _buildOrderCard(context, order);
        },
      ),
    );
  }

  Widget _buildOrderCard(BuildContext context, TodayOrderEntity order) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Order #${order.id.substring(order.id.length - 6)}',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: context.appColors.textColor,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        DateFormat(
                          'MMM dd, yyyy HH:mm',
                        ).format(order.createdAt),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 12.w,
                        vertical: 6.h,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(
                          order.status,
                        ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                      child: Text(
                        order.status.toUpperCase().replaceAll('_', ' '),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: _getStatusColor(order.status),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    SizedBox(width: 8.w),
                    PopupMenuButton<String>(
                      icon: Icon(
                        Icons.more_vert,
                        color: context.appColors.subtextColor,
                        size: 20.sp,
                      ),
                      onSelected: (value) =>
                          _handleOrderAction(context, order, value),
                      itemBuilder: (context) => [
                        // QR Code regeneration for active orders
                        if (order.status.toUpperCase() == 'CONFIRMED' ||
                            order.status.toUpperCase() == 'IN_TRANSIT')
                          const PopupMenuItem(
                            value: 'regenerate_qr',
                            child: Row(
                              children: [
                                Icon(Icons.qr_code, color: Colors.blue),
                                SizedBox(width: 8),
                                Text(
                                  'Regenerate QR Code',
                                  style: TextStyle(color: Colors.blue),
                                ),
                              ],
                            ),
                          ),
                        const PopupMenuItem(
                          value: 'view_details',
                          child: Row(
                            children: [
                              Icon(Icons.visibility),
                              SizedBox(width: 8),
                              Text('View Details'),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 16.h),
            _buildOrderInfo(context, order),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showOrderDetails(context, order),
                    icon: const Icon(Icons.visibility),
                    label: const Text('View Details'),
                  ),
                ),
                SizedBox(width: 12.w),
                if (order.status.toUpperCase() == 'PENDING' ||
                    order.status.toUpperCase() == 'CONFIRMED')
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _showTrackingInfo(context, order),
                      icon: const Icon(Icons.track_changes),
                      label: const Text('Track Order'),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderInfo(BuildContext context, TodayOrderEntity order) {
    return Column(
      children: [
        _buildInfoRow(context, Icons.person, 'Customer', order.customer.phone),
        SizedBox(height: 8.h),
        _buildInfoRow(
          context,
          Icons.location_on,
          'Address',
          order.deliveryAddress,
        ),
        SizedBox(height: 8.h),
        _buildInfoRow(
          context,
          Icons.attach_money,
          'Amount',
          '\$${order.totalAmount.toStringAsFixed(2)}',
        ),
      ],
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    IconData icon,
    String label,
    String value,
  ) {
    return Row(
      children: [
        Icon(icon, size: 16.sp, color: context.appColors.subtextColor),
        SizedBox(width: 8.w),
        Text(
          '$label: ',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: context.appColors.subtextColor,
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: context.appColors.textColor),
          ),
        ),
      ],
    );
  }

  void _showOrderDetails(BuildContext context, TodayOrderEntity order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Order #${order.id.substring(order.id.length - 6)}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Order ID', order.id),
              _buildDetailRow('Customer', order.customer.phone),
              _buildDetailRow('Address', order.deliveryAddress),
              _buildDetailRow(
                'Status',
                order.status.toUpperCase().replaceAll('_', ' '),
              ),
              _buildDetailRow(
                'Amount',
                '\$${order.totalAmount.toStringAsFixed(2)}',
              ),
              _buildDetailRow(
                'Created',
                DateFormat('MMM dd, yyyy HH:mm').format(order.createdAt),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showTrackingInfo(BuildContext context, TodayOrderEntity order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Order Tracking'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Order #${order.id.substring(order.id.length - 6)}'),
            SizedBox(height: 16.h),
            _buildTrackingStep('Order Placed', true, order.createdAt),
            _buildTrackingStep('Confirmed', order.status != 'PENDING', null),
            _buildTrackingStep(
              'Out for Delivery',
              order.status == 'OUT_FOR_DELIVERY' || order.status == 'DELIVERED',
              null,
            ),
            _buildTrackingStep('Delivered', order.status == 'DELIVERED', null),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildTrackingStep(
    String title,
    bool isCompleted,
    DateTime? timestamp,
  ) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        children: [
          Icon(
            isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
            color: isCompleted ? Colors.green : Colors.grey,
            size: 20.sp,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: isCompleted
                        ? FontWeight.bold
                        : FontWeight.normal,
                    color: isCompleted ? Colors.green : Colors.grey,
                  ),
                ),
                if (timestamp != null)
                  Text(
                    DateFormat('MMM dd, HH:mm').format(timestamp),
                    style: TextStyle(fontSize: 12.sp, color: Colors.grey),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return Colors.orange;
      case 'CONFIRMED':
        return Colors.blue;
      case 'IN_TRANSIT':
        return Colors.purple;
      case 'DELIVERED':
        return Colors.green;
      case 'CANCELLED':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  List<TodayOrderEntity> _getOrdersFromCurrentState() {
    final state = context.read<DashboardBloc>().state;
    if (state is GetSupervisorDashboardSuccess) {
      return state.dashboard.todayOrders;
    }
    return [];
  }

  Widget _buildErrorWidget(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.sp, color: Colors.red),
          SizedBox(height: 16.h),
          Text(
            'Error Loading Orders',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.textColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _loadDashboardData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long,
            size: 64.sp,
            color: context.appColors.subtextColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'No Data Available',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _loadDashboardData,
            child: const Text('Load Data'),
          ),
        ],
      ),
    );
  }

  void _handleOrderAction(
    BuildContext context,
    TodayOrderEntity order,
    String action,
  ) {
    switch (action) {
      case 'regenerate_qr':
        _regenerateQRCode(context, order);
        break;
      case 'view_details':
        _showOrderDetails(context, order);
        break;
    }
  }

  void _regenerateQRCode(BuildContext context, TodayOrderEntity order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Regenerate QR Code'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Generate a new QR code for order #${order.id.substring(order.id.length - 6)}?',
            ),
            SizedBox(height: 8.h),
            Text(
              'This will invalidate the current QR code and create a new one.',
              style: TextStyle(color: Colors.orange, fontSize: 12.sp),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<OrderBloc>().add(
                RegenerateQRCodeEvent(orderId: order.id),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('Regenerate'),
          ),
        ],
      ),
    );
  }
}
