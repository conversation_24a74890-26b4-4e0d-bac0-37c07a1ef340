import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/core/utils/helpers/snack_bar_helper.dart';
import 'package:frontend/core/enums/layout_type.dart';

import 'package:frontend/features/inventory/presentation/bloc/inventory_bloc.dart';
import 'package:frontend/features/inventory/domain/entities/cylinder_entity.dart';
import 'package:frontend/features/inventory/domain/entities/package_entity.dart';
import 'package:frontend/features/inventory/domain/entities/spare_part_entity.dart';
import 'package:frontend/features/shared/presentation/widgets/image_place_holder.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_list_grid_view.dart';

class AdminDeletedInventoryPage extends StatefulWidget {
  const AdminDeletedInventoryPage({super.key});

  @override
  State<AdminDeletedInventoryPage> createState() =>
      _AdminDeletedInventoryPageState();
}

class _AdminDeletedInventoryPageState extends State<AdminDeletedInventoryPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadDeletedItems(forceRefresh: false);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadDeletedItems({bool forceRefresh = true}) {
    final inventoryBloc = context.inventoryBloc;
    final deletedCylinders = inventoryBloc.deletedCylinders;
    final deletedPackages = inventoryBloc.deletedPackages;
    final deletedSpareParts = inventoryBloc.deletedSpareParts;
    final canRefetchCylinders = forceRefresh || deletedCylinders.isEmpty;
    final canRefetchPackages = forceRefresh || deletedPackages.isEmpty;
    final canRefetchSpareParts = forceRefresh || deletedSpareParts.isEmpty;

    if (canRefetchCylinders) {
      inventoryBloc.add(const GetDeletedCylindersEvent(page: 1, limit: 100));
    }
    if (canRefetchPackages) {
      inventoryBloc.add(const GetDeletedPackagesEvent(page: 1, limit: 100));
    }
    if (canRefetchSpareParts) {
      inventoryBloc.add(const GetDeletedSparePartsEvent(page: 1, limit: 100));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.primaryColor,
        foregroundColor: Colors.white,
        title: Text(
          'Deleted Inventory Management',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () => _loadDeletedItems(),
            icon: const Icon(Icons.refresh, color: Colors.white),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
          tabs: const [
            Tab(text: 'Cylinders', icon: Icon(Icons.propane_tank)),
            Tab(text: 'Packages', icon: Icon(Icons.inventory_2)),
            Tab(text: 'Spare Parts', icon: Icon(Icons.build)),
          ],
        ),
      ),
      body: BlocListener<InventoryBloc, InventoryState>(
        listener: (context, state) {
          if (state is RestoreCylinderSuccess ||
              state is RestorePackageSuccess ||
              state is RestoreSparePartSuccess) {
            SnackBarHelper.showSuccessSnackBar(
              context,
              message: 'Item restored successfully',
            );
            _loadDeletedItems();
          } else if (state is PermanentlyDeleteCylinderSuccess ||
              state is PermanentlyDeletePackageSuccess ||
              state is PermanentlyDeleteSparePartSuccess) {
            SnackBarHelper.showWarningSnackBar(
              context,
              message: 'Item permanently deleted',
              title: 'Deleted',
            );
            _loadDeletedItems();
          } else if (state is RestoreCylinderFailure) {
            SnackBarHelper.showErrorSnackBar(
              context,
              message:
                  'Failed to restore Cylinder: ${state.appFailure.getErrorMessage()}',
            );
          } else if (state is RestorePackageFailure) {
            SnackBarHelper.showErrorSnackBar(
              context,
              message:
                  'Failed to restore Package: ${state.appFailure.getErrorMessage()}',
            );
          } else if (state is RestoreSparePartFailure) {
            SnackBarHelper.showErrorSnackBar(
              context,
              message:
                  'Failed to restore Spare Part: ${state.appFailure.getErrorMessage()}',
            );
          }
        },
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildCylindersTab(),
            _buildPackagesTab(),
            _buildSparePartsTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildCylindersTab() {
    return BlocBuilder<InventoryBloc, InventoryState>(
      buildWhen: (previous, current) {
        return current is GetDeletedCylindersLoading ||
            current is GetDeletedCylindersSuccess ||
            current is GetDeletedCylindersFailure;
      },
      builder: (context, state) {
        if (state is GetDeletedCylindersLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is GetDeletedCylindersFailure) {
          return _buildErrorState('Failed to load deleted cylinders');
        }
        final deletedCylinders = context.inventoryBloc.deletedCylinders;
        return _buildCylindersList(deletedCylinders);
      },
    );
  }

  Widget _buildPackagesTab() {
    return BlocBuilder<InventoryBloc, InventoryState>(
      buildWhen: (previous, current) {
        return current is GetDeletedPackagesLoading ||
            current is GetDeletedPackagesSuccess ||
            current is GetDeletedPackagesFailure;
      },
      builder: (context, state) {
        if (state is GetDeletedPackagesLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is GetDeletedPackagesFailure) {
          return _buildErrorState('Failed to load deleted packages');
        }

        final deletedPackages = context.inventoryBloc.deletedPackages;
        return _buildPackagesList(deletedPackages);
      },
    );
  }

  Widget _buildSparePartsTab() {
    return BlocBuilder<InventoryBloc, InventoryState>(
      buildWhen: (previous, current) {
        return current is GetDeletedSparePartsLoading ||
            current is GetDeletedSparePartsSuccess ||
            current is GetDeletedSparePartsFailure;
      },
      builder: (context, state) {
        if (state is GetDeletedSparePartsLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is GetDeletedSparePartsFailure) {
          return _buildErrorState('Failed to load deleted spare parts');
        }
        final deletedSpareParts = context.inventoryBloc.deletedSpareParts;
        return _buildSparePartsList(deletedSpareParts);
      },
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.delete_sweep,
            size: 64.sp,
            color: context.appColors.subtextColor,
          ),
          SizedBox(height: 16.h),
          Text(
            message,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.sp,
            color: context.appColors.errorColor,
          ),
          SizedBox(height: 16.h),
          Text(
            message,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.errorColor,
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _loadDeletedItems,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildCylindersList(List<CylinderEntity> cylinders) {
    return RefreshIndicator(
      onRefresh: () async => _loadDeletedItems(),
      child: CustomListGridView<CylinderEntity>(
        items: cylinders,
        isLoading: false,
        isEmpty: cylinders.isEmpty,
        layoutType: LayoutType.listView,
        onRefresh: _loadDeletedItems,
        emptyDataBuilder: () => _buildEmptyState('No deleted cylinders found'),
        padding: EdgeInsets.all(16.w),
        itemBuilder: (context, cylinder) => _buildCylinderCard(cylinder),
      ),
    );
  }

  Widget _buildPackagesList(List<PackageEntity> packages) {
    return RefreshIndicator(
      onRefresh: () async => _loadDeletedItems(),
      child: CustomListGridView<PackageEntity>(
        items: packages,
        isLoading: false,
        isEmpty: packages.isEmpty,
        layoutType: LayoutType.listView,
        onRefresh: _loadDeletedItems,
        emptyDataBuilder: () => _buildEmptyState('No deleted packages found'),
        padding: EdgeInsets.all(16.w),
        itemBuilder: (context, package) => _buildPackageCard(package),
      ),
    );
  }

  Widget _buildSparePartsList(List<SparePartEntity> spareParts) {
    return RefreshIndicator(
      onRefresh: () async => _loadDeletedItems(),
      child: CustomListGridView<SparePartEntity>(
        items: spareParts,
        isLoading: false,
        isEmpty: spareParts.isEmpty,
        layoutType: LayoutType.listView,
        onRefresh: _loadDeletedItems,
        emptyDataBuilder: () =>
            _buildEmptyState('No deleted spare parts found'),
        padding: EdgeInsets.all(16.w),
        itemBuilder: (context, sparePart) => _buildSparePartCard(sparePart),
      ),
    );
  }

  Widget _buildCylinderCard(CylinderEntity cylinder) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      color: context.appColors.surfaceColor,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Image section
                Container(
                  width: 60.w,
                  height: 60.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      color: context.appColors.dividerColor.withValues(
                        alpha: 0.3,
                      ),
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8.r),
                    child: cylinder.imageUrl.isNotEmpty
                        ? CachedNetworkImage(
                            imageUrl: cylinder.formattedImageUrl,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => ImagePlaceholder(
                              width: 60.w,
                              height: 60.h,
                              child: Icon(
                                Icons.propane_tank,
                                color: context.appColors.primaryColor,
                                size: 24.sp,
                              ),
                            ),
                            errorWidget: (context, url, error) =>
                                ImagePlaceholder(
                                  width: 60.w,
                                  height: 60.h,
                                  child: Icon(
                                    Icons.propane_tank,
                                    color: context.appColors.primaryColor,
                                    size: 24.sp,
                                  ),
                                ),
                          )
                        : ImagePlaceholder(
                            width: 60.w,
                            height: 60.h,
                            child: Icon(
                              Icons.propane_tank,
                              color: context.appColors.primaryColor,
                              size: 24.sp,
                            ),
                          ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${cylinder.type.name} - ${cylinder.material.name}',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        'Description: ${cylinder.description}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        'Price: \$${cylinder.price.toStringAsFixed(2)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: context.appColors.subtextColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _showRestoreDialog(
                    context,
                    'cylinder',
                    '${cylinder.type.name} - ${cylinder.material.name}',
                    () => context.read<InventoryBloc>().add(
                      RestoreCylinderEvent(cylinderId: cylinder.id),
                    ),
                  ),
                  icon: const Icon(Icons.restore),
                  label: const Text('Restore'),
                  style: TextButton.styleFrom(
                    foregroundColor: context.appColors.successColor,
                  ),
                ),
                SizedBox(width: 8.w),
                TextButton.icon(
                  onPressed: () => _showPermanentDeleteDialog(
                    context,
                    'cylinder',
                    '${cylinder.type.name} - ${cylinder.material.name}',
                    () => context.read<InventoryBloc>().add(
                      PermanentlyDeleteCylinderEvent(cylinderId: cylinder.id),
                    ),
                  ),
                  icon: const Icon(Icons.delete_forever),
                  label: const Text('Delete Forever'),
                  style: TextButton.styleFrom(
                    foregroundColor: context.appColors.errorColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPackageCard(PackageEntity package) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      color: context.appColors.surfaceColor,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Image section
                Container(
                  width: 60.w,
                  height: 60.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      color: context.appColors.dividerColor.withValues(
                        alpha: 0.3,
                      ),
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8.r),
                    child: package.imageUrl.isNotEmpty
                        ? CachedNetworkImage(
                            imageUrl: package.formattedImageUrl,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => ImagePlaceholder(
                              width: 60.w,
                              height: 60.h,
                              child: Icon(
                                Icons.inventory_2,
                                color: context.appColors.primaryColor,
                                size: 24.sp,
                              ),
                            ),
                            errorWidget: (context, url, error) =>
                                ImagePlaceholder(
                                  width: 60.w,
                                  height: 60.h,
                                  child: Icon(
                                    Icons.inventory_2,
                                    color: context.appColors.primaryColor,
                                    size: 24.sp,
                                  ),
                                ),
                          )
                        : ImagePlaceholder(
                            width: 60.w,
                            height: 60.h,
                            child: Icon(
                              Icons.inventory_2,
                              color: context.appColors.primaryColor,
                              size: 24.sp,
                            ),
                          ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        package.name,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        'Description: ${package.description}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        'Price: \$${package.finalPrice.toStringAsFixed(2)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: context.appColors.subtextColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _showRestoreDialog(
                    context,
                    'package',
                    package.name,
                    () => context.read<InventoryBloc>().add(
                      RestorePackageEvent(packageId: package.id),
                    ),
                  ),
                  icon: const Icon(Icons.restore),
                  label: const Text('Restore'),
                  style: TextButton.styleFrom(
                    foregroundColor: context.appColors.successColor,
                  ),
                ),
                SizedBox(width: 8.w),
                TextButton.icon(
                  onPressed: () => _showPermanentDeleteDialog(
                    context,
                    'package',
                    package.name,
                    () => context.read<InventoryBloc>().add(
                      PermanentlyDeletePackageEvent(packageId: package.id),
                    ),
                  ),
                  icon: const Icon(Icons.delete_forever),
                  label: const Text('Delete Forever'),
                  style: TextButton.styleFrom(
                    foregroundColor: context.appColors.errorColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSparePartCard(SparePartEntity sparePart) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      color: context.appColors.surfaceColor,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Image section
                Container(
                  width: 60.w,
                  height: 60.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      color: context.appColors.dividerColor.withValues(
                        alpha: 0.3,
                      ),
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8.r),
                    child: sparePart.imageUrl.isNotEmpty
                        ? CachedNetworkImage(
                            imageUrl: sparePart.formattedImageUrl,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => ImagePlaceholder(
                              width: 60.w,
                              height: 60.h,
                              child: Icon(
                                Icons.build,
                                color: context.appColors.primaryColor,
                                size: 24.sp,
                              ),
                            ),
                            errorWidget: (context, url, error) =>
                                ImagePlaceholder(
                                  width: 60.w,
                                  height: 60.h,
                                  child: Icon(
                                    Icons.build,
                                    color: context.appColors.primaryColor,
                                    size: 24.sp,
                                  ),
                                ),
                          )
                        : ImagePlaceholder(
                            width: 60.w,
                            height: 60.h,
                            child: Icon(
                              Icons.build,
                              color: context.appColors.primaryColor,
                              size: 24.sp,
                            ),
                          ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        sparePart.category.name,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        'Description: ${sparePart.description}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        'Price: \$${sparePart.price.toStringAsFixed(2)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: context.appColors.subtextColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _showRestoreDialog(
                    context,
                    'spare part',
                    sparePart.category.name,
                    () => context.read<InventoryBloc>().add(
                      RestoreSparePartEvent(sparePartId: sparePart.id),
                    ),
                  ),
                  icon: const Icon(Icons.restore),
                  label: const Text('Restore'),
                  style: TextButton.styleFrom(
                    foregroundColor: context.appColors.successColor,
                  ),
                ),
                SizedBox(width: 8.w),
                TextButton.icon(
                  onPressed: () => _showPermanentDeleteDialog(
                    context,
                    'spare part',
                    sparePart.category.name,
                    () => context.read<InventoryBloc>().add(
                      PermanentlyDeleteSparePartEvent(
                        sparePartId: sparePart.id,
                      ),
                    ),
                  ),
                  icon: const Icon(Icons.delete_forever),
                  label: const Text('Delete Forever'),
                  style: TextButton.styleFrom(
                    foregroundColor: context.appColors.errorColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showRestoreDialog(
    BuildContext context,
    String itemType,
    String itemName,
    VoidCallback onConfirm,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: context.appColors.surfaceColor,
          title: Text(
            'Restore $itemType',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'Are you sure you want to restore "$itemName"? This will make it available again.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(dialogContext),
              child: Text(
                'Cancel',
                style: TextStyle(color: context.appColors.subtextColor),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(dialogContext);
                onConfirm();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: context.appColors.successColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Restore'),
            ),
          ],
        );
      },
    );
  }

  void _showPermanentDeleteDialog(
    BuildContext context,
    String itemType,
    String itemName,
    VoidCallback onConfirm,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: context.appColors.surfaceColor,
          title: Text(
            'Permanently Delete $itemType',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: context.appColors.errorColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'Are you sure you want to permanently delete "$itemName"? This action cannot be undone and will also delete any associated images.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(dialogContext),
              child: Text(
                'Cancel',
                style: TextStyle(color: context.appColors.subtextColor),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(dialogContext);
                onConfirm();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: context.appColors.errorColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Delete Forever'),
            ),
          ],
        );
      },
    );
  }
}
