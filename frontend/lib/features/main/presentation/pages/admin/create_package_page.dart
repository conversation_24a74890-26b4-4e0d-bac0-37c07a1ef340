import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/cylinder_material.dart';
import 'package:frontend/core/enums/spare_part_category.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/features/inventory/presentation/bloc/inventory_bloc.dart';
import 'package:frontend/features/inventory/domain/entities/cylinder_entity.dart';
import 'package:frontend/features/inventory/domain/entities/spare_part_entity.dart';
import 'package:frontend/features/inventory/domain/entities/package_item_entity.dart';
import 'package:frontend/features/inventory/domain/entities/package_entity.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_image_picker_card.dart';
import 'package:image_picker/image_picker.dart';

import '../../../../../core/utils/helpers/snack_bar_helper.dart';

class CreatePackagePage extends StatefulWidget {
  final bool isEditMode;
  final PackageEntity? existingPackage;

  const CreatePackagePage({
    super.key,
    this.isEditMode = false,
    this.existingPackage,
  });

  @override
  State<CreatePackagePage> createState() => _CreatePackagePageState();
}

class _CreatePackagePageState extends State<CreatePackagePage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _totalPriceController = TextEditingController();
  final _costPriceController = TextEditingController();
  final _discountController = TextEditingController();
  final _quantityController = TextEditingController();
  final _minimumStockController = TextEditingController();
  // final _imageUrlController = TextEditingController();

  CylinderEntity? _selectedCylinder;
  List<CylinderEntity> _availableCylinders = [];
  List<SparePartEntity> _availableSpareParts = [];
  final List<PackageItemEntity> _includedSpareParts = [];
  XFile? _selectedImageFile;

  // Track if user has manually overridden the calculated values
  bool _isPriceManuallyOverridden = false;
  bool _isCostManuallyOverridden = false;

  @override
  void initState() {
    super.initState();
    _loadData();
    if (widget.isEditMode && widget.existingPackage != null) {
      _initializeEditMode();
    }
  }

  void _initializeEditMode() {
    final package = widget.existingPackage!;
    _nameController.text = package.name;
    _descriptionController.text = package.description;
    _totalPriceController.text = package.totalPrice.toString();
    _costPriceController.text = package.costPrice.toString();
    _discountController.text = package.discount.toString();
    _quantityController.text = package.quantity.toString();
    _minimumStockController.text = package.minimumStockLevel.toString();

    // Set included spare parts
    _includedSpareParts.clear();
    _includedSpareParts.addAll(package.includedSpareParts);

    // Set selected cylinder - this will be called after cylinders are loaded
    _setSelectedCylinderFromPackage();

    // Mark prices as manually overridden since they're from existing package
    _isPriceManuallyOverridden = true;
    _isCostManuallyOverridden = true;
  }

  void _loadData() {
    final inventoryBloc = context.inventoryBloc;
    final cylinders = inventoryBloc.cylinders;
    final spareParts = inventoryBloc.spareParts;
    if (cylinders.isEmpty) {
      inventoryBloc.add(const GetCylindersEvent());
    }
    if (spareParts.isEmpty) {
      inventoryBloc.add(const GetSparePartsEvent());
    }

    setState(() {
      _availableCylinders = cylinders;
      _availableSpareParts = spareParts;
    });

    // Trigger auto-calculation if not in edit mode
    if (!widget.isEditMode) {
      _updateCalculatedPricing();
    }
  }

  void _setSelectedCylinderFromPackage() {
    if (widget.isEditMode &&
        widget.existingPackage != null &&
        _availableCylinders.isNotEmpty) {
      final package = widget.existingPackage!;

      // print('Package: $package');

      // print('Setting selected cylinder from package: ${package.cylinderId}');

      // Try to find the cylinder by ID
      try {
        final matchingCylinder = _availableCylinders
            // .where((cylinder) => cylinder.id == package.cylinderId)
            .where((cylinder) => cylinder.id == package.cylinder?.id)
            .firstOrNull;

        // print('Found matching cylinder: ${matchingCylinder?.id}');

        setState(() {
          _selectedCylinder = matchingCylinder;
          // _selectedCylinder = package.cylinder;
        });
      } catch (e) {
        // If cylinder not found, don't set any cylinder (let user select manually)
        // This can happen if the cylinder was deleted or is no longer available
        // print('Matching cylinder not found: ${package.cylinder?.id}, $e');
      }
    }
  }

  /// Calculate total price based on selected cylinder and spare parts
  double _calculateTotalPrice() {
    double total = 0.0;

    // Add cylinder price
    if (_selectedCylinder != null) {
      total += _selectedCylinder!.price;
    }

    // Add spare parts prices
    for (final item in _includedSpareParts) {
      total += item.totalPrice;
    }

    return total;
  }

  /// Calculate total cost based on selected cylinder and spare parts
  double _calculateTotalCost() {
    double total = 0.0;

    // Add cylinder cost
    if (_selectedCylinder != null) {
      total += _selectedCylinder!.cost;
    }

    // Add spare parts costs (need to find the spare part entity to get cost)
    for (final item in _includedSpareParts) {
      final sparePart = _availableSpareParts
          .where((part) => part.id == item.sparePartId)
          .firstOrNull;
      if (sparePart != null) {
        total += sparePart.cost * item.quantity;
      }
    }

    return total;
  }

  /// Update price and cost fields with calculated values if not manually overridden
  void _updateCalculatedPricing() {
    if (!_isPriceManuallyOverridden) {
      final calculatedPrice = _calculateTotalPrice();
      _totalPriceController.text = calculatedPrice.toStringAsFixed(2);
    }

    if (!_isCostManuallyOverridden) {
      final calculatedCost = _calculateTotalCost();
      _costPriceController.text = calculatedCost.toStringAsFixed(2);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _totalPriceController.dispose();
    _costPriceController.dispose();
    _discountController.dispose();
    _quantityController.dispose();
    _minimumStockController.dispose();
    // _imageUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.surfaceColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(Icons.arrow_back, color: context.appColors.textColor),
        ),
        title: Text(
          widget.isEditMode ? 'Edit Package' : 'Create New Package',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          TextButton(
            onPressed: _resetForm,
            child: Text(
              'Reset',
              style: TextStyle(
                color: context.appColors.subtextColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: BlocListener<InventoryBloc, InventoryState>(
        listener: (context, state) {
          if (state is CreatePackageSuccess) {
            SnackBarHelper.showSuccessSnackBar(
              context,
              message: 'Package created successfully!',
              onDismissed: () => Navigator.of(context).pop(),
            );

            context.inventoryBloc.add(const GetPackagesEvent());
          } else if (state is CreatePackageFailure) {
            SnackBarHelper.showErrorSnackBar(
              context,
              message:
                  'Failed to create package: ${state.appFailure.getErrorMessage()}',
            );
          } else if (state is UpdatePackageSuccess) {
            SnackBarHelper.showSuccessSnackBar(
              context,
              message: 'Package updated successfully!',
              onDismissed: () => Navigator.of(context).pop(),
            );

            // Refresh packages when returning from create page
            context.inventoryBloc.add(const GetPackagesEvent());
          } else if (state is UpdatePackageFailure) {
            SnackBarHelper.showErrorSnackBar(
              context,
              message:
                  'Failed to update package: ${state.appFailure.getErrorMessage()}',
            );
          } else if (state is GetCylindersSuccess) {
            setState(() {
              _availableCylinders = state.cylinders;
            });
            _setSelectedCylinderFromPackage();
          } else if (state is GetSparePartsSuccess) {
            setState(() {
              _availableSpareParts = state.spareParts;
            });
          }
        },
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader('Basic Information'),
                SizedBox(height: 16.h),
                _buildNameField(),
                SizedBox(height: 16.h),
                _buildDescriptionField(),
                SizedBox(height: 16.h),
                _buildCylinderSelection(),
                SizedBox(height: 24.h),
                _buildSectionHeader('Package Image'),
                SizedBox(height: 16.h),
                _buildImagePickerSection(),
                SizedBox(height: 24.h),
                _buildSectionHeader('Included Spare Parts'),
                SizedBox(height: 16.h),
                _buildSparePartsSection(),
                SizedBox(height: 24.h),
                _buildSectionHeader('Pricing'),
                SizedBox(height: 16.h),
                _buildPricingSection(),
                SizedBox(height: 24.h),
                _buildSectionHeader('Stock Management'),
                SizedBox(height: 16.h),
                _buildStockSection(),
                SizedBox(height: 24.h),
                // _buildSectionHeader('Additional Information'),
                // SizedBox(height: 16.h),
                // _buildImageUrlField(),
                SizedBox(height: 32.h),
                _buildCreateButton(),
                SizedBox(height: 50.h),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        color: context.appColors.textColor,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      decoration: InputDecoration(
        labelText: 'Package Name',
        hintText: 'Enter package name',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.dividerColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.primaryColor),
        ),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please enter a package name';
        }
        return null;
      },
    );
  }

  Widget _buildDescriptionField() {
    return TextFormField(
      controller: _descriptionController,
      maxLines: 3,
      decoration: InputDecoration(
        labelText: 'Description',
        hintText: 'Enter package description',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.dividerColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.primaryColor),
        ),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please enter a description';
        }
        return null;
      },
    );
  }

  Widget _buildImagePickerSection() {
    return Center(
      child: Column(
        children: [
          CustomImagePickerCard(
            imageFile: _selectedImageFile,
            imageUrl:
                widget.isEditMode &&
                    widget.existingPackage != null &&
                    widget.existingPackage!.imageUrl.isNotEmpty
                ? widget.existingPackage!.formattedImageUrl
                : null,
            radius: 60.0,
            showEditIcon: true,
            isLoading: false,
            imagePadding: EdgeInsets.symmetric(
              horizontal: 10.w,
              vertical: 10.h,
            ),
            onCamera: (XFile pickedImage) {
              setState(() {
                _selectedImageFile = pickedImage;
              });
            },
            onGallery: (XFile pickedImage) {
              setState(() {
                _selectedImageFile = pickedImage;
              });
            },
          ),
          SizedBox(height: 12.h),
          Text(
            widget.isEditMode
                ? 'Tap to change package image'
                : 'Tap to add package image',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
          if (_selectedImageFile != null) ...[
            SizedBox(height: 8.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.check_circle,
                  color: context.appColors.successColor,
                  size: 16.w,
                ),
                SizedBox(width: 4.w),
                Text(
                  'Image selected',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: context.appColors.successColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(width: 8.w),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedImageFile = null;
                    });
                  },
                  child: Icon(
                    Icons.close,
                    color: context.appColors.errorColor,
                    size: 16.w,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCylinderSelection() {
    return DropdownButtonFormField<CylinderEntity>(
      value: _selectedCylinder,
      decoration: InputDecoration(
        labelText: 'Select Cylinder',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.dividerColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.primaryColor),
        ),
      ),
      items: _availableCylinders.map((cylinder) {
        return DropdownMenuItem<CylinderEntity>(
          value: cylinder,
          child: Text(
            '${cylinder.type.displayName} - ${cylinder.material.displayName}',
          ),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedCylinder = value;
          _updateCalculatedPricing();
        });
      },
      validator: (value) => value == null ? 'Please select a cylinder' : null,
    );
  }

  Widget _buildSparePartsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Selected Parts (${_includedSpareParts.length})',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            ElevatedButton.icon(
              onPressed: _showAddSparePartDialog,
              icon: const Icon(Icons.add),
              label: const Text('Add Part'),
              style: ElevatedButton.styleFrom(
                backgroundColor: context.appColors.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        if (_includedSpareParts.isEmpty)
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: context.appColors.surfaceColor,
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(color: context.appColors.dividerColor),
            ),
            child: Center(
              child: Text(
                'No spare parts added yet',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.subtextColor,
                ),
              ),
            ),
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _includedSpareParts.length,
            itemBuilder: (context, index) {
              final item = _includedSpareParts[index];
              return _buildSparePartItem(item, index);
            },
          ),
      ],
    );
  }

  Widget _buildSparePartItem(PackageItemEntity item, int index) {
    return Card(
      margin: EdgeInsets.only(bottom: 8.h),
      child: ListTile(
        title: Text(item.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Unit Price: \$${item.unitPrice.toStringAsFixed(2)}'),
            Text('Total Price: \$${item.totalPrice.toStringAsFixed(2)}'),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Qty: ${item.quantity}'),
            SizedBox(width: 8.w),
            IconButton(
              onPressed: () => _removeSparePartItem(index),
              icon: Icon(Icons.delete, color: context.appColors.errorColor),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!_isPriceManuallyOverridden || !_isCostManuallyOverridden)
          Container(
            padding: EdgeInsets.all(12.w),
            margin: EdgeInsets.only(bottom: 16.h),
            decoration: BoxDecoration(
              color: context.appColors.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(
                color: context.appColors.primaryColor.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.auto_awesome,
                  color: context.appColors.primaryColor,
                  size: 16.w,
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    'Prices are auto-calculated based on selected cylinder and spare parts. You can override them manually.',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: context.appColors.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        Row(
          children: [
            Expanded(
              child: _buildNumberField(
                controller: _totalPriceController,
                label: 'Total Price',
                hint: '0.00',
                prefix: '\$',
                onChanged: (value) {
                  _isPriceManuallyOverridden = true;
                },
                validator: (value) {
                  if (value == null || value.isEmpty) return 'Required';
                  final price = double.tryParse(value);
                  if (price == null || price <= 0) return 'Invalid price';
                  return null;
                },
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: _buildNumberField(
                controller: _costPriceController,
                label: 'Cost Price',
                hint: '0.00',
                prefix: '\$',
                onChanged: (value) {
                  _isCostManuallyOverridden = true;
                },
                validator: (value) {
                  if (value == null || value.isEmpty) return 'Required';
                  final cost = double.tryParse(value);
                  if (cost == null || cost < 0) return 'Invalid cost';
                  return null;
                },
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: _buildNumberField(
                controller: _discountController,
                label: 'Discount Amount',
                hint: '0.00',
                prefix: '\$',
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final discount = double.tryParse(value);
                    if (discount == null || discount < 0) {
                      return 'Invalid discount';
                    }
                    final totalPrice = double.tryParse(
                      _totalPriceController.text,
                    );
                    if (totalPrice != null && discount > totalPrice) {
                      return 'Discount cannot exceed total price';
                    }
                  }
                  return null;
                },
              ),
            ),
            SizedBox(width: 16.w),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _isPriceManuallyOverridden = false;
                  _isCostManuallyOverridden = false;
                  _updateCalculatedPricing();
                });
              },
              icon: Icon(Icons.refresh, size: 16.w),
              label: const Text('Recalculate'),
              style: ElevatedButton.styleFrom(
                backgroundColor: context.appColors.primaryColor,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStockSection() {
    return Row(
      children: [
        Expanded(
          child: _buildNumberField(
            controller: _quantityController,
            label: 'Initial Quantity',
            hint: '0',
            validator: (value) {
              if (value == null || value.isEmpty) return 'Required';
              final quantity = int.tryParse(value);
              if (quantity == null || quantity < 0) return 'Invalid quantity';
              return null;
            },
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: _buildNumberField(
            controller: _minimumStockController,
            label: 'Minimum Stock Level',
            hint: '10',
            validator: (value) {
              if (value == null || value.isEmpty) return 'Required';
              final minStock = int.tryParse(value);
              if (minStock == null || minStock < 0) return 'Invalid value';
              return null;
            },
          ),
        ),
      ],
    );
  }

  // Widget _buildImageUrlField() {
  //   return TextFormField(
  //     controller: _imageUrlController,
  //     decoration: InputDecoration(
  //       labelText: 'Image URL (Optional)',
  //       hintText: 'https://example.com/image.jpg',
  //       prefixIcon: Icon(Icons.image, color: context.appColors.primaryColor),
  //       border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
  //       enabledBorder: OutlineInputBorder(
  //         borderRadius: BorderRadius.circular(8.r),
  //         borderSide: BorderSide(color: context.appColors.dividerColor),
  //       ),
  //       focusedBorder: OutlineInputBorder(
  //         borderRadius: BorderRadius.circular(8.r),
  //         borderSide: BorderSide(color: context.appColors.primaryColor),
  //       ),
  //     ),
  //   );
  // }

  Widget _buildNumberField({
    required TextEditingController controller,
    required String label,
    required String hint,
    String? prefix,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      onChanged: onChanged,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixText: prefix,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.dividerColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.primaryColor),
        ),
      ),
      validator: validator,
    );
  }

  Widget _buildCreateButton() {
    return BlocBuilder<InventoryBloc, InventoryState>(
      builder: (context, state) {
        final isLoading =
            state is CreatePackageLoading || state is UpdatePackageLoading;

        return SizedBox(
          width: double.infinity,
          height: 48.h,
          child: ElevatedButton(
            onPressed: isLoading ? null : _submitForm,
            style: ElevatedButton.styleFrom(
              backgroundColor: context.appColors.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: isLoading
                ? SizedBox(
                    height: 20.h,
                    width: 20.w,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    widget.isEditMode ? 'Update Package' : 'Create Package',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        );
      },
    );
  }

  void _showAddSparePartDialog() {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        SparePartEntity? selectedSparePart;
        int quantity = 1;

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Add Spare Part'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  DropdownButtonFormField<SparePartEntity>(
                    value: selectedSparePart,
                    decoration: const InputDecoration(
                      labelText: 'Select Spare Part',
                    ),
                    items: _availableSpareParts.map((sparePart) {
                      return DropdownMenuItem<SparePartEntity>(
                        value: sparePart,
                        child: Text(sparePart.category.displayName),
                      );
                    }).toList(),
                    onChanged: (value) =>
                        setState(() => selectedSparePart = value),
                  ),
                  SizedBox(height: 16.h),
                  TextFormField(
                    initialValue: quantity.toString(),
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(labelText: 'Quantity'),
                    onChanged: (value) => quantity = int.tryParse(value) ?? 1,
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(dialogContext).pop(),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (selectedSparePart != null) {
                      _addSparePartItem(selectedSparePart!, quantity);
                      Navigator.of(dialogContext).pop();
                    }
                  },
                  child: const Text('Add'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _addSparePartItem(SparePartEntity sparePart, int quantity) {
    final packageItem = PackageItemEntity(
      sparePartId: sparePart.id,
      name: sparePart.category.label,
      quantity: quantity,
      unitPrice: sparePart.price,
      totalPrice: sparePart.price * quantity,
    );

    setState(() {
      _includedSpareParts.add(packageItem);
      _updateCalculatedPricing();
    });
  }

  void _removeSparePartItem(int index) {
    setState(() {
      _includedSpareParts.removeAt(index);
      _updateCalculatedPricing();
    });
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      if (_selectedCylinder == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please select a cylinder')),
        );
        return;
      }

      if (widget.isEditMode) {
        // Update existing package
        final event = UpdatePackageEvent(
          packageId: widget.existingPackage!.id,
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
          cylinderId: _selectedCylinder!.id,
          includedSpareParts: _includedSpareParts,
          totalPrice: double.parse(_totalPriceController.text),
          costPrice: double.parse(_costPriceController.text),
          discount: double.tryParse(_discountController.text) ?? 0.0,
          minimumStockLevel: int.parse(_minimumStockController.text),
          imageFile: _selectedImageFile,
        );
        context.read<InventoryBloc>().add(event);
      } else {
        // Create new package
        final event = CreatePackageEvent(
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
          cylinderId: _selectedCylinder!.id,
          includedSpareParts: _includedSpareParts,
          totalPrice: double.parse(_totalPriceController.text),
          costPrice: double.parse(_costPriceController.text),
          discount: double.tryParse(_discountController.text) ?? 0.0,
          quantity: int.parse(_quantityController.text),
          minimumStockLevel: int.parse(_minimumStockController.text),
          imageFile: _selectedImageFile,
        );
        context.read<InventoryBloc>().add(event);
      }
    }
  }

  void _resetForm() {
    _formKey.currentState?.reset();
    _nameController.clear();
    _descriptionController.clear();
    _totalPriceController.clear();
    _costPriceController.clear();
    _discountController.clear();
    _quantityController.clear();
    _minimumStockController.clear();
    // _imageUrlController.clear();
    setState(() {
      _selectedCylinder = null;
      _includedSpareParts.clear();
      _selectedImageFile = null;
      _isPriceManuallyOverridden = false;
      _isCostManuallyOverridden = false;
    });

    // Trigger auto-calculation after reset
    _updateCalculatedPricing();
  }
}
