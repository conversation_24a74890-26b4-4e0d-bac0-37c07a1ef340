import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';

import '../../../../../core/enums/user_role.dart';
import '../../../../../core/enums/vehicle_type.dart';
import '../../../../../core/utils/helpers/somali_phone_number_formatter.dart';
import '../../../../user-management/presentation/bloc/user_bloc.dart';
import '../../../../user-management/domain/params/update_user_params.dart';
import '../../../../shared/presentation/widgets/custom_textfield.dart';

class AdminUsersPage extends StatefulWidget {
  const AdminUsersPage({super.key});

  @override
  State<AdminUsersPage> createState() => _AdminUsersPageState();
}

class _AdminUsersPageState extends State<AdminUsersPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  bool? _selectedStatus;
  String _searchQuery = '';
  List<UserEntity> _allUsers = [];
  List<UserEntity> _filteredUsers = [];
  int _currentPage = 1;
  final int _totalPages = 1;
  final int _totalUsers = 0;
  final int _usersPerPage = 20;
  bool _isPerformingAction = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadUsers();
  }

  void _loadUsers({int? page}) {
    final targetPage = page ?? _currentPage;
    context.read<UserBloc>().add(
      GetAllUsersEvent(
        page: targetPage,
        limit: _usersPerPage,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      ),
    );
  }

  void _applyFilters() {
    _filteredUsers = _allUsers.where((user) {
      // Apply search filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final matchesPhone = user.phone.toLowerCase().contains(query);
        final matchesEmail = user.email?.toLowerCase().contains(query) ?? false;
        final matchesUsername =
            user.username?.toLowerCase().contains(query) ?? false;

        if (!matchesPhone && !matchesEmail && !matchesUsername) {
          return false;
        }
      }

      // Apply status filter
      if (_selectedStatus != null && user.isActive != _selectedStatus) {
        return false;
      }

      return true;
    }).toList();
  }

  List<UserEntity> _getFilteredUsers(UserRole? role) {
    if (role == null) {
      return _filteredUsers;
    }
    return _filteredUsers.where((user) => user.role == role).toList();
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100.w,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: context.appColors.subtextColor,
              ),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Text(value, style: Theme.of(context).textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  Widget _buildPaginationControls() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        border: Border(top: BorderSide(color: context.appColors.dividerColor)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ElevatedButton.icon(
            onPressed: _currentPage > 1
                ? () {
                    setState(() {
                      _currentPage--;
                    });
                    _loadUsers(page: _currentPage);
                  }
                : null,
            icon: const Icon(Icons.chevron_left),
            label: const Text('Previous'),
          ),
          Text(
            'Page $_currentPage',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          ElevatedButton.icon(
            onPressed: _allUsers.length >= _usersPerPage
                ? () {
                    setState(() {
                      _currentPage++;
                    });
                    _loadUsers(page: _currentPage);
                  }
                : null,
            icon: const Icon(Icons.chevron_right),
            label: const Text('Next'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<UserBloc, UserState>(
      listener: (context, state) {
        if (state is UpdateUserLoading ||
            state is DeleteUserLoading ||
            state is RegisterAdminOrAgentLoading) {
          setState(() {
            _isPerformingAction = true;
          });
        } else if (state is UpdateUserSuccess) {
          setState(() {
            _isPerformingAction = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.green,
            ),
          );
          _loadUsers(); // Reload users after update
        } else if (state is UpdateUserFailure) {
          setState(() {
            _isPerformingAction = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.appFailure.message),
              backgroundColor: Colors.red,
            ),
          );
        } else if (state is DeleteUserSuccess) {
          setState(() {
            _isPerformingAction = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('User deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
          _loadUsers(); // Reload users after deletion
        } else if (state is DeleteUserFailure) {
          setState(() {
            _isPerformingAction = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.appFailure.message),
              backgroundColor: Colors.red,
            ),
          );
        } else if (state is RegisterAdminOrAgentSuccess) {
          setState(() {
            _isPerformingAction = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('User created successfully'),
              backgroundColor: Colors.green,
            ),
          );
          _loadUsers(); // Reload users after creation
        } else if (state is RegisterAdminOrAgentFailure) {
          setState(() {
            _isPerformingAction = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.appFailure.message),
              backgroundColor: Colors.red,
            ),
          );
        } else if (state is ChangeUserRoleSuccess) {
          setState(() {
            _isPerformingAction = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.green,
            ),
          );
          _loadUsers(); // Reload users after role change
        } else if (state is ChangeUserRoleFailure) {
          setState(() {
            _isPerformingAction = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.appFailure.getErrorMessage()),
              backgroundColor: Colors.red,
            ),
          );
        } else if (state is ChangeUserRoleLoading) {
          setState(() {
            _isPerformingAction = true;
          });
        }
      },
      child: Stack(
        children: [
          Scaffold(
            backgroundColor: context.appColors.backgroundColor,
            appBar: _buildAppBar(context),
            body: SafeArea(
              child: Column(
                children: [
                  _buildUserStatsCards(context),
                  _buildSearchAndFilters(context),
                  _buildTabBar(context),
                  Expanded(child: _buildTabBarView(context)),
                ],
              ),
            ),
            floatingActionButton: FloatingActionButton.extended(
              onPressed: () {
                _showAddUserDialog(context);
              },
              backgroundColor: context.appColors.primaryColor,
              icon: const Icon(Icons.add, color: Colors.white),
              label: const Text(
                'Add User',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ),
          if (_isPerformingAction)
            Container(
              color: Colors.black.withValues(alpha: 0.3),
              child: const Center(child: CircularProgressIndicator()),
            ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      elevation: 0,
      backgroundColor: context.appColors.primaryColor,
      foregroundColor: Colors.white,
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'User Management',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            'Manage customers, agents, and admins',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
        ],
      ),
      actions: [
        IconButton(
          onPressed: () {
            _loadUsers();
          },
          icon: const Icon(Icons.refresh, color: Colors.white),
          tooltip: 'Refresh Users',
        ),
        SizedBox(width: 8.w),
      ],
    );
  }

  Widget _buildUserStatsCards(BuildContext context) {
    final totalUsers = _allUsers.length;
    final activeUsers = _allUsers.where((user) => user.isActive).length;
    final inactiveUsers = totalUsers - activeUsers;

    final customerCount = _allUsers
        .where((user) => user.role == UserRole.customer)
        .length;
    final agentCount = _allUsers
        .where((user) => user.role == UserRole.agent)
        .length;
    final adminCount = _allUsers
        .where((user) => user.role == UserRole.admin)
        .length;
    final supervisorCount = _allUsers
        .where((user) => user.role == UserRole.supervisor)
        .length;

    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          // Status Cards Row
          Row(
            children: [
              Expanded(
                child: _buildStatsCard(
                  context,
                  'Total Users',
                  totalUsers.toString(),
                  Icons.people,
                  context.appColors.primaryColor,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildStatsCard(
                  context,
                  'Active',
                  activeUsers.toString(),
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildStatsCard(
                  context,
                  'Inactive',
                  inactiveUsers.toString(),
                  Icons.cancel,
                  Colors.red,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          // Role Cards Row
          Row(
            children: [
              Expanded(
                child: _buildStatsCard(
                  context,
                  'Customers',
                  customerCount.toString(),
                  Icons.person,
                  Colors.blue,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildStatsCard(
                  context,
                  'Agents',
                  agentCount.toString(),
                  Icons.delivery_dining,
                  Colors.green,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildStatsCard(
                  context,
                  'Admins',
                  adminCount.toString(),
                  Icons.admin_panel_settings,
                  Colors.purple,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildStatsCard(
                  context,
                  'Supervisors',
                  supervisorCount.toString(),
                  Icons.supervisor_account,
                  Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20.sp),
          SizedBox(height: 4.h),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: context.appColors.textColor,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: context.appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Column(
        children: [
          // Search Bar
          Container(
            decoration: BoxDecoration(
              color: context.appColors.surfaceColor,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(color: context.appColors.dividerColor),
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search users by phone, email...',
                hintStyle: TextStyle(color: context.appColors.subtextColor),
                prefixIcon: Icon(
                  Icons.search,
                  color: context.appColors.subtextColor,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(16.w),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                  _applyFilters();
                });
              },
            ),
          ),
          SizedBox(height: 12.h),
          // Filters
          Row(
            children: [
              Expanded(
                child: _buildFilterDropdown(
                  'Status',
                  _selectedStatus == null
                      ? 'All Status'
                      : _selectedStatus!
                      ? 'Active'
                      : 'Inactive',
                  ['All Status', 'Active', 'Inactive'],
                  (value) {
                    setState(() {
                      if (value == 'All Status') {
                        _selectedStatus = null;
                      } else {
                        _selectedStatus = value == 'Active';
                      }
                      _applyFilters();
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown(
    String label,
    String value,
    List<String> items,
    Function(String?) onChanged,
  ) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value,
          isExpanded: true,
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: context.appColors.subtextColor,
          ),
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: context.appColors.textColor),
          items: items.map((String item) {
            return DropdownMenuItem<String>(value: item, child: Text(item));
          }).toList(),
          onChanged: onChanged,
        ),
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: context.appColors.primaryColor,
          borderRadius: BorderRadius.circular(8.r),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: context.appColors.subtextColor,
        labelStyle: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w600),
        tabs: const [
          Tab(text: 'All'),
          Tab(text: 'Customers'),
          Tab(text: 'Agents'),
          Tab(text: 'Admins'),
        ],
      ),
    );
  }

  Widget _buildTabBarView(BuildContext context) {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildUsersList(context, null),
        _buildUsersList(context, UserRole.customer),
        _buildUsersList(context, UserRole.agent),
        _buildUsersList(context, UserRole.admin),
      ],
    );
  }

  Widget _buildUsersList(BuildContext context, UserRole? role) {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, state) {
        if (state is GetAllUsersLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is GetAllUsersFailure) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 48.sp, color: Colors.red),
                SizedBox(height: 8.h),
                Text(
                  'Failed to load users',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                SizedBox(height: 8.h),
                ElevatedButton(
                  onPressed: _loadUsers,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (state is GetAllUsersSuccess) {
          _allUsers = state.users;
          _applyFilters();

          final filteredUsers = _getFilteredUsers(role);

          if (filteredUsers.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.people_outline,
                    size: 48.sp,
                    color: context.appColors.subtextColor,
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'No users found',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              Expanded(
                child: ListView.builder(
                  padding: EdgeInsets.all(16.w),
                  itemCount: filteredUsers.length,
                  itemBuilder: (context, index) {
                    final user = filteredUsers[index];
                    return _buildUserCard(context, user);
                  },
                ),
              ),
              if (filteredUsers.length >= _usersPerPage)
                _buildPaginationControls(),
            ],
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildUserCard(BuildContext context, UserEntity user) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 24.r,
                backgroundColor: _getRoleColor(
                  user.role,
                ).withValues(alpha: 0.1),
                child: Icon(
                  _getRoleIcon(user.role),
                  color: _getRoleColor(user.role),
                  size: 20.sp,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          user.phone,
                          style: Theme.of(context).textTheme.bodyLarge
                              ?.copyWith(
                                color: context.appColors.textColor,
                                fontWeight: FontWeight.w600,
                              ),
                        ),
                        SizedBox(width: 8.w),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 8.w,
                            vertical: 2.h,
                          ),
                          decoration: BoxDecoration(
                            color: _getRoleColor(
                              user.role,
                            ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                          child: Text(
                            user.role.name.toUpperCase(),
                            style: TextStyle(
                              color: _getRoleColor(user.role),
                              fontSize: 10.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    if (user.email != null)
                      Text(
                        user.email!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                      ),
                    SizedBox(height: 4.h),
                    Row(
                      children: [
                        Icon(
                          user.isActive ? Icons.check_circle : Icons.cancel,
                          color: user.isActive ? Colors.green : Colors.red,
                          size: 16.sp,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          user.isActive ? 'Active' : 'Inactive',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: user.isActive
                                    ? Colors.green
                                    : Colors.red,
                                fontWeight: FontWeight.w500,
                              ),
                        ),
                        if (user.role == UserRole.agent &&
                            user.agentMetadata != null) ...[
                          SizedBox(width: 12.w),
                          Icon(
                            user.agentMetadata!.isOnDuty
                                ? Icons.work
                                : Icons.work_off,
                            color: user.agentMetadata!.isOnDuty
                                ? Colors.green
                                : Colors.orange,
                            size: 16.sp,
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            user.agentMetadata!.isOnDuty
                                ? 'On Duty'
                                : 'Off Duty',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: user.agentMetadata!.isOnDuty
                                      ? Colors.green
                                      : Colors.orange,
                                  fontWeight: FontWeight.w500,
                                ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
              PopupMenuButton<String>(
                icon: Icon(
                  Icons.more_vert,
                  color: context.appColors.subtextColor,
                ),
                onSelected: (value) {
                  _handleUserAction(context, user, value);
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'view',
                    child: Row(
                      children: [
                        Icon(Icons.visibility),
                        SizedBox(width: 8),
                        Text('View Details'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit),
                        SizedBox(width: 8),
                        Text('Edit'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'change_role',
                    child: Row(
                      children: [
                        Icon(Icons.admin_panel_settings, color: Colors.orange),
                        SizedBox(width: 8),
                        Text(
                          'Change Role',
                          style: TextStyle(color: Colors.orange),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: user.isActive ? 'deactivate' : 'activate',
                    child: Row(
                      children: [
                        Icon(user.isActive ? Icons.block : Icons.check_circle),
                        const SizedBox(width: 8),
                        Text(user.isActive ? 'Deactivate' : 'Activate'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Delete', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return Colors.blue;
      case UserRole.agent:
        return Colors.green;
      case UserRole.admin:
        return Colors.purple;
      case UserRole.supervisor:
        return Colors.orange;
    }
  }

  IconData _getRoleIcon(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return Icons.person;
      case UserRole.agent:
        return Icons.delivery_dining;
      case UserRole.admin:
        return Icons.admin_panel_settings;
      case UserRole.supervisor:
        return Icons.supervisor_account;
    }
  }

  void _handleUserAction(BuildContext context, UserEntity user, String action) {
    switch (action) {
      case 'view':
        _showUserDetails(context, user);
        break;
      case 'edit':
        _showEditUserDialog(context, user);
        break;
      case 'change_role':
        _showChangeRoleDialog(context, user);
        break;
      case 'activate':
      case 'deactivate':
        _toggleUserStatus(context, user);
        break;
      case 'delete':
        _showDeleteConfirmation(context, user);
        break;
    }
  }

  void _showUserDetails(BuildContext context, UserEntity user) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Container(
          width: 400.w,
          padding: EdgeInsets.all(24.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  CircleAvatar(
                    radius: 24.r,
                    backgroundColor: _getRoleColor(
                      user.role,
                    ).withValues(alpha: 0.1),
                    child: Icon(
                      _getRoleIcon(user.role),
                      color: _getRoleColor(user.role),
                      size: 20.sp,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user.username ?? 'No Username',
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          user.role.label,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(
                                color: _getRoleColor(user.role),
                                fontWeight: FontWeight.w500,
                              ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              SizedBox(height: 24.h),

              // User Details
              _buildDetailRow('Phone', user.phone),
              _buildDetailRow('Email', user.email ?? 'Not provided'),
              _buildDetailRow('Status', user.isActive ? 'Active' : 'Inactive'),
              _buildDetailRow('User ID', user.id),

              if (user.createdAt != null)
                _buildDetailRow('Created', _formatDate(user.createdAt!)),

              if (user.updatedAt != null)
                _buildDetailRow('Last Updated', _formatDate(user.updatedAt!)),

              // Agent specific details
              if (user.role == UserRole.agent &&
                  user.agentMetadata != null) ...[
                SizedBox(height: 16.h),
                Text(
                  'Agent Information',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8.h),
                _buildDetailRow(
                  'On Duty',
                  user.agentMetadata!.isOnDuty ? 'Yes' : 'No',
                ),
                if (user.agentMetadata!.rating != null)
                  _buildDetailRow(
                    'Rating',
                    '${user.agentMetadata!.rating!.toStringAsFixed(1)} ⭐',
                  ),
                if (user.agentMetadata!.vehicle != null) ...[
                  _buildDetailRow(
                    'Vehicle Type',
                    user.agentMetadata!.vehicle!.type.name,
                  ),
                  if (user.agentMetadata!.vehicle!.number != null)
                    _buildDetailRow(
                      'Vehicle Number',
                      user.agentMetadata!.vehicle!.number!,
                    ),
                ],
              ],

              // Addresses
              if (user.addresses.isNotEmpty) ...[
                SizedBox(height: 16.h),
                Text(
                  'Addresses (${user.addresses.length})',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8.h),
                ...user.addresses.map(
                  (address) => Padding(
                    padding: EdgeInsets.only(bottom: 8.h),
                    child: Container(
                      padding: EdgeInsets.all(12.w),
                      decoration: BoxDecoration(
                        color: context.appColors.surfaceColor,
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(
                          color: context.appColors.dividerColor,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (address.tag != null)
                            Text(
                              address.tag!.toUpperCase(),
                              style: Theme.of(context).textTheme.labelSmall
                                  ?.copyWith(
                                    color: context.appColors.primaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          Text(address.details),
                          if (address.contactPhone != null)
                            Text(
                              'Contact: ${address.contactPhone}',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],

              SizedBox(height: 24.h),

              // Action Buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Close'),
                  ),
                  SizedBox(width: 8.w),
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _showEditUserDialog(context, user);
                    },
                    icon: const Icon(Icons.edit),
                    label: const Text('Edit'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showEditUserDialog(BuildContext context, UserEntity user) {
    final emailController = TextEditingController(text: user.email ?? '');
    final usernameController = TextEditingController(text: user.username ?? '');
    bool isActive = user.isActive;
    bool isOnDuty = user.agentMetadata?.isOnDuty ?? false;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Container(
            width: 400.w,
            padding: EdgeInsets.all(24.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    CircleAvatar(
                      radius: 20.r,
                      backgroundColor: _getRoleColor(
                        user.role,
                      ).withValues(alpha: 0.1),
                      child: Icon(
                        Icons.edit,
                        color: _getRoleColor(user.role),
                        size: 16.sp,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Edit User',
                            style: Theme.of(context).textTheme.titleLarge
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            user.phone,
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(
                                  color: context.appColors.subtextColor,
                                ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                SizedBox(height: 24.h),

                // Form Fields
                TextField(
                  controller: emailController,
                  decoration: InputDecoration(
                    labelText: 'Email',
                    hintText: 'Enter email address',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    prefixIcon: const Icon(Icons.email),
                  ),
                  keyboardType: TextInputType.emailAddress,
                ),
                SizedBox(height: 16.h),

                TextField(
                  controller: usernameController,
                  decoration: InputDecoration(
                    labelText: 'Username',
                    hintText: 'Enter username',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    prefixIcon: const Icon(Icons.person),
                  ),
                ),
                SizedBox(height: 16.h),

                // Status Toggle
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    border: Border.all(color: context.appColors.dividerColor),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        isActive ? Icons.check_circle : Icons.cancel,
                        color: isActive ? Colors.green : Colors.red,
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: Text(
                          'Account Status',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                      Switch(
                        value: isActive,
                        onChanged: (value) {
                          setState(() {
                            isActive = value;
                          });
                        },
                      ),
                    ],
                  ),
                ),

                // Agent specific controls
                if (user.role == UserRole.agent) ...[
                  SizedBox(height: 16.h),
                  Container(
                    padding: EdgeInsets.all(12.w),
                    decoration: BoxDecoration(
                      border: Border.all(color: context.appColors.dividerColor),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          isOnDuty ? Icons.work : Icons.work_off,
                          color: isOnDuty ? Colors.green : Colors.orange,
                        ),
                        SizedBox(width: 12.w),
                        Expanded(
                          child: Text(
                            'On Duty Status',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ),
                        Switch(
                          value: isOnDuty,
                          onChanged: (value) {
                            setState(() {
                              isOnDuty = value;
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                ],

                SizedBox(height: 24.h),

                // Action Buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Cancel'),
                    ),
                    SizedBox(width: 8.w),
                    ElevatedButton.icon(
                      onPressed: () {
                        // Validate and update user
                        final email = emailController.text.trim();
                        final username = usernameController.text.trim();

                        // Basic validation
                        if (email.isNotEmpty && !email.contains('@')) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                'Please enter a valid email address',
                              ),
                              backgroundColor: Colors.red,
                            ),
                          );
                          return;
                        }

                        // Update user
                        context.read<UserBloc>().add(
                          UpdateUserEvent(
                            userId: user.id,
                            email: email.isEmpty ? null : email,
                            username: username.isEmpty ? null : username,
                            isActive: isActive,
                            agentMetadata: user.role == UserRole.agent
                                ? UpdateAgentMetadataParams(isOnDuty: isOnDuty)
                                : null,
                          ),
                        );

                        Navigator.pop(context);
                      },
                      icon: const Icon(Icons.save),
                      label: const Text('Save Changes'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showAddUserDialog(BuildContext context) {
    final phoneController = TextEditingController();
    final emailController = TextEditingController();
    final passwordController = TextEditingController();
    final vehicleNumberController = TextEditingController();
    UserRole selectedRole = UserRole.agent;
    VehicleType selectedVehicleType = VehicleType.motorcycle;
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Container(
            width: 450.w,
            padding: EdgeInsets.all(24.w),
            child: Form(
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 20.r,
                        backgroundColor: context.appColors.primaryColor
                            .withValues(alpha: 0.1),
                        child: Icon(
                          Icons.person_add,
                          color: context.appColors.primaryColor,
                          size: 16.sp,
                        ),
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: Text(
                          'Add New User',
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                  SizedBox(height: 24.h),

                  // Role Selection
                  Text(
                    'User Role',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 12.w),
                    decoration: BoxDecoration(
                      border: Border.all(color: context.appColors.dividerColor),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<UserRole>(
                        value: selectedRole,
                        isExpanded: true,
                        items: [UserRole.admin, UserRole.agent].map((role) {
                          return DropdownMenuItem(
                            value: role,
                            child: Row(
                              children: [
                                Icon(
                                  _getRoleIcon(role),
                                  color: _getRoleColor(role),
                                  size: 16.sp,
                                ),
                                SizedBox(width: 8.w),
                                Text(role.label),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              selectedRole = value;
                            });
                          }
                        },
                      ),
                    ),
                  ),
                  SizedBox(height: 16.h),

                  // Phone Number
                  CustomTextField(
                    controller: phoneController,
                    labelText: '61XXXXXXXX',
                    hintText: '61XXXXXXXX',
                    keyboardType: TextInputType.phone,
                    textInputAction: TextInputAction.next,
                    prefixIcon: Container(
                      padding: EdgeInsets.all(12.w),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '+252',
                            style: Theme.of(context).textTheme.bodyLarge
                                ?.copyWith(
                                  color: context.appColors.textColor,
                                  fontWeight: FontWeight.w500,
                                ),
                          ),
                          SizedBox(width: 8.w),
                          Container(
                            width: 1.w,
                            height: 20.h,
                            color: context.appColors.disabledColor,
                          ),
                        ],
                      ),
                    ),
                    inputFormatters: [
                      SomaliPhoneNumberFormatter(),
                      LengthLimitingTextInputFormatter(9),
                    ],
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Phone number is required';
                      }
                      if (value.length < 9) {
                        return 'Please enter a valid phone number';
                      }
                      // Regex that checks Somali number format 61xxxxxxx must start with 61
                      const pattern = r'^61[1-9]\d{6}$';
                      final regex = RegExp(pattern);
                      if (!regex.hasMatch(value.trim())) {
                        return 'Enter a valid phone number.';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 16.h),

                  // Email
                  TextFormField(
                    controller: emailController,
                    decoration: InputDecoration(
                      labelText: 'Email',
                      hintText: '<EMAIL>',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      prefixIcon: const Icon(Icons.email),
                    ),
                    keyboardType: TextInputType.emailAddress,
                    validator: (value) {
                      if (value != null &&
                          value.isNotEmpty &&
                          !value.contains('@')) {
                        return 'Please enter a valid email address';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 16.h),

                  // Password
                  TextFormField(
                    controller: passwordController,
                    decoration: InputDecoration(
                      labelText: 'Password *',
                      hintText: 'Enter password',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      prefixIcon: const Icon(Icons.lock),
                    ),
                    obscureText: true,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Password is required';
                      }
                      if (value.length < 6) {
                        return 'Password must be at least 6 characters';
                      }
                      return null;
                    },
                  ),

                  // Agent specific fields
                  if (selectedRole == UserRole.agent) ...[
                    SizedBox(height: 16.h),
                    Text(
                      'Vehicle Information',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 8.h),

                    // Vehicle Type
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 12.w),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: context.appColors.dividerColor,
                        ),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<VehicleType>(
                          value: selectedVehicleType,
                          isExpanded: true,
                          items: VehicleType.values.map((type) {
                            return DropdownMenuItem(
                              value: type,
                              child: Text(type.name.toUpperCase()),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                selectedVehicleType = value;
                              });
                            }
                          },
                        ),
                      ),
                    ),
                    SizedBox(height: 12.h),

                    // Vehicle Number
                    TextFormField(
                      controller: vehicleNumberController,
                      decoration: InputDecoration(
                        labelText: 'Vehicle Number *',
                        hintText: 'ABC-123',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        prefixIcon: const Icon(Icons.directions_car),
                      ),
                      validator: selectedRole == UserRole.agent
                          ? (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Vehicle number is required for agents';
                              }
                              return null;
                            }
                          : null,
                    ),
                  ],

                  SizedBox(height: 24.h),

                  // Action Buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('Cancel'),
                      ),
                      SizedBox(width: 8.w),
                      ElevatedButton.icon(
                        onPressed: () {
                          if (formKey.currentState!.validate()) {
                            // Register new user
                            final phone = '+252${phoneController.text.trim()}';
                            context.read<UserBloc>().add(
                              RegisterAdminOrAgentEvent(
                                phone: phone,
                                email: emailController.text.trim().isEmpty
                                    ? null
                                    : emailController.text.trim(),
                                password: passwordController.text.trim(),
                                role: selectedRole,
                                vehicleType: selectedVehicleType,
                                vehicleNumber: vehicleNumberController.text
                                    .trim(),
                              ),
                            );

                            Navigator.pop(context);
                          }
                        },
                        icon: const Icon(Icons.person_add),
                        label: const Text('Create User'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _toggleUserStatus(BuildContext context, UserEntity user) {
    context.read<UserBloc>().add(
      UpdateUserEvent(userId: user.id, isActive: !user.isActive),
    );
  }

  void _showDeleteConfirmation(BuildContext context, UserEntity user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete User'),
        content: Text('Are you sure you want to delete ${user.phone}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<UserBloc>().add(DeleteUserEvent(userId: user.id));
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showChangeRoleDialog(BuildContext context, UserEntity user) {
    UserRole selectedRole = user.role;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.admin_panel_settings,
                color: context.appColors.primaryColor,
              ),
              SizedBox(width: 8.w),
              const Text('Change User Role'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Current user: ${user.phone}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.subtextColor,
                ),
              ),
              SizedBox(height: 16.h),
              Text(
                'Select new role:',
                style: Theme.of(
                  context,
                ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12.h),
              ...UserRole.values.map(
                (role) => RadioListTile<UserRole>(
                  title: Row(
                    children: [
                      Icon(
                        _getRoleIcon(role),
                        size: 20.sp,
                        color: _getRoleColor(role),
                      ),
                      SizedBox(width: 8.w),
                      Text(_getRoleDisplayName(role)),
                    ],
                  ),
                  subtitle: Text(_getRoleDescription(role)),
                  value: role,
                  groupValue: selectedRole,
                  onChanged: (UserRole? value) {
                    if (value != null) {
                      setState(() {
                        selectedRole = value;
                      });
                    }
                  },
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: selectedRole == user.role
                  ? null
                  : () {
                      Navigator.pop(context);
                      _changeUserRole(context, user, selectedRole);
                    },
              style: ElevatedButton.styleFrom(
                backgroundColor: context.appColors.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Change Role'),
            ),
          ],
        ),
      ),
    );
  }

  void _changeUserRole(
    BuildContext context,
    UserEntity user,
    UserRole newRole,
  ) {
    context.read<UserBloc>().add(
      ChangeUserRoleEvent(userId: user.id, newRole: newRole),
    );
  }

  String _getRoleDisplayName(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return 'Customer';
      case UserRole.agent:
        return 'Agent';
      case UserRole.admin:
        return 'Admin';
      case UserRole.supervisor:
        return 'Supervisor';
    }
  }

  String _getRoleDescription(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return 'Can place orders and manage their account';
      case UserRole.agent:
        return 'Can deliver orders and manage deliveries';
      case UserRole.admin:
        return 'Full system access and management';
      case UserRole.supervisor:
        return 'Can supervise agents and view reports';
    }
  }
}
