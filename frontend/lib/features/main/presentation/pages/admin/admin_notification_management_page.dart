import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';

enum NotificationType {
  individual('Individual Notification', Icons.person, Colors.blue),
  topic('Topic Notification', Icons.topic, Colors.green),
  broadcast('Broadcast', Icons.campaign, Colors.purple);

  const NotificationType(this.label, this.icon, this.color);
  final String label;
  final IconData icon;
  final Color color;
}

enum NotificationTopic {
  general('General', 'General announcements'),
  orders('Orders', 'Order-related notifications'),
  promotions('Promotions', 'Promotional offers'),
  systemUpdates('System Updates', 'System maintenance and updates'),
  agentNotifications('Agent Notifications', 'Agent-specific notifications');

  const NotificationTopic(this.label, this.description);
  final String label;
  final String description;
}

class NotificationMetrics {
  final int totalSent;
  final int delivered;
  final int failed;
  final int successRate;

  const NotificationMetrics({
    required this.totalSent,
    required this.delivered,
    required this.failed,
    required this.successRate,
  });
}

class AdminNotificationManagementPage extends StatefulWidget {
  const AdminNotificationManagementPage({super.key});

  @override
  State<AdminNotificationManagementPage> createState() => _AdminNotificationManagementPageState();
}

class _AdminNotificationManagementPageState extends State<AdminNotificationManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  
  // Form controllers
  final _userIdController = TextEditingController();
  final _titleController = TextEditingController();
  final _bodyController = TextEditingController();
  final _imageUrlController = TextEditingController();
  
  // State variables
  NotificationType _selectedNotificationType = NotificationType.individual;
  NotificationTopic _selectedTopic = NotificationTopic.general;
  bool _isLoading = false;
  bool _onlyActiveUsers = true;
  bool _includeImage = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _userIdController.dispose();
    _titleController.dispose();
    _bodyController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(context),
            _buildMetricsOverview(context),
            _buildTabBar(context),
            Expanded(
              child: _buildTabBarView(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Notification Management',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'Send and manage push notifications',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: context.appColors.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              Icons.notifications,
              color: context.appColors.primaryColor,
              size: 24.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricsOverview(BuildContext context) {
    final metrics = _getNotificationMetrics();
    
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        children: [
          Expanded(
            child: _buildMetricCard(
              context,
              'Total Sent',
              metrics.totalSent.toString(),
              Icons.send,
              Colors.blue,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildMetricCard(
              context,
              'Delivered',
              metrics.delivered.toString(),
              Icons.check_circle,
              Colors.green,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildMetricCard(
              context,
              'Failed',
              metrics.failed.toString(),
              Icons.error,
              Colors.red,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildMetricCard(
              context,
              'Success Rate',
              '${metrics.successRate}%',
              Icons.trending_up,
              Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: context.appColors.dividerColor,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20.sp,
          ),
          SizedBox(height: 8.h),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 2.h),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: context.appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: context.appColors.primaryColor,
          borderRadius: BorderRadius.circular(6.r),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: context.appColors.subtextColor,
        labelStyle: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w600,
        ),
        tabs: const [
          Tab(text: 'Send Notification'),
          Tab(text: 'Topics'),
          Tab(text: 'Analytics'),
        ],
      ),
    );
  }

  Widget _buildTabBarView(BuildContext context) {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildSendNotificationTab(context),
        _buildTopicsTab(context),
        _buildAnalyticsTab(context),
      ],
    );
  }

  Widget _buildSendNotificationTab(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildNotificationTypeSelector(context),
            SizedBox(height: 24.h),
            _buildNotificationForm(context),
            SizedBox(height: 32.h),
            _buildSendButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationTypeSelector(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notification Type',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        Row(
          children: NotificationType.values.map((type) {
            final isSelected = _selectedNotificationType == type;
            return Expanded(
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedNotificationType = type;
                  });
                },
                child: Container(
                  margin: EdgeInsets.only(right: type != NotificationType.values.last ? 12.w : 0),
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? type.color.withValues(alpha: 0.1)
                        : context.appColors.surfaceColor,
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(
                      color: isSelected ? type.color : context.appColors.dividerColor,
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        type.icon,
                        color: isSelected ? type.color : context.appColors.subtextColor,
                        size: 24.sp,
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        type.label,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: isSelected ? type.color : context.appColors.textColor,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildNotificationForm(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_selectedNotificationType == NotificationType.individual) ...[
          _buildTextField(
            controller: _userIdController,
            label: 'User ID',
            hint: 'Enter user ID',
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'User ID is required';
              }
              return null;
            },
          ),
          SizedBox(height: 16.h),
        ],
        if (_selectedNotificationType == NotificationType.topic) ...[
          _buildDropdownField(
            label: 'Topic',
            value: _selectedTopic,
            items: NotificationTopic.values,
            onChanged: (value) {
              setState(() {
                _selectedTopic = value!;
              });
            },
            itemBuilder: (topic) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(topic.label),
                Text(
                  topic.description,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: context.appColors.subtextColor,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 16.h),
          _buildSwitchTile(
            title: 'Only Active Users',
            subtitle: 'Send only to active users',
            value: _onlyActiveUsers,
            onChanged: (value) {
              setState(() {
                _onlyActiveUsers = value;
              });
            },
          ),
          SizedBox(height: 16.h),
        ],
        _buildTextField(
          controller: _titleController,
          label: 'Title',
          hint: 'Notification title',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Title is required';
            }
            if (value.length > 100) {
              return 'Title too long (max 100 characters)';
            }
            return null;
          },
        ),
        SizedBox(height: 16.h),
        _buildTextField(
          controller: _bodyController,
          label: 'Message',
          hint: 'Notification message',
          maxLines: 4,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Message is required';
            }
            if (value.length > 500) {
              return 'Message too long (max 500 characters)';
            }
            return null;
          },
        ),
        SizedBox(height: 16.h),
        _buildSwitchTile(
          title: 'Include Image',
          subtitle: 'Add an image to the notification',
          value: _includeImage,
          onChanged: (value) {
            setState(() {
              _includeImage = value;
            });
          },
        ),
        if (_includeImage) ...[
          SizedBox(height: 16.h),
          _buildTextField(
            controller: _imageUrlController,
            label: 'Image URL',
            hint: 'https://example.com/image.jpg',
            validator: (value) {
              if (_includeImage && (value == null || value.isEmpty)) {
                return 'Image URL is required when image is enabled';
              }
              return null;
            },
          ),
        ],
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 8.h),
        TextFormField(
          controller: controller,
          validator: validator,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(color: context.appColors.subtextColor),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: context.appColors.dividerColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: context.appColors.dividerColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: context.appColors.primaryColor),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: const BorderSide(color: Colors.red),
            ),
            filled: true,
            fillColor: context.appColors.surfaceColor,
            contentPadding: EdgeInsets.all(12.w),
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField<T>({
    required String label,
    required T value,
    required List<T> items,
    required Function(T?) onChanged,
    required Widget Function(T) itemBuilder,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 8.h),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: context.appColors.surfaceColor,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(
              color: context.appColors.dividerColor,
              width: 1,
            ),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<T>(
              value: value,
              isExpanded: true,
              icon: Icon(
                Icons.keyboard_arrow_down,
                color: context.appColors.subtextColor,
              ),
              items: items.map((T item) {
                return DropdownMenuItem<T>(
                  value: item,
                  child: itemBuilder(item),
                );
              }).toList(),
              onChanged: onChanged,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: context.appColors.dividerColor,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: context.appColors.primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildSendButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _sendNotification,
        style: ElevatedButton.styleFrom(
          backgroundColor: context.appColors.primaryColor,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 16.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
        ),
        child: _isLoading
            ? SizedBox(
                height: 20.h,
                width: 20.w,
                child: const CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _selectedNotificationType.icon,
                    size: 20.sp,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'Send ${_selectedNotificationType.label}',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildTopicsTab(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: NotificationTopic.values.length,
      itemBuilder: (context, index) {
        final topic = NotificationTopic.values[index];
        return Container(
          margin: EdgeInsets.only(bottom: 12.h),
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: context.appColors.surfaceColor,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: context.appColors.dividerColor,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: context.appColors.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.topic,
                  color: context.appColors.primaryColor,
                  size: 24.sp,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      topic.label,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: context.appColors.textColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      topic.description,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: context.appColors.subtextColor,
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '${(index + 1) * 123} subscribers',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: context.appColors.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAnalyticsTab(BuildContext context) {
    return Center(
      child: Text(
        'Notification Analytics\n(Coming Soon)',
        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
          color: context.appColors.subtextColor,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  void _sendNotification() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        _isLoading = false;
      });

      // TODO: Implement actual notification sending
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${_selectedNotificationType.label} sent successfully!'),
          backgroundColor: Colors.green,
        ),
      );

      // Clear form
      _userIdController.clear();
      _titleController.clear();
      _bodyController.clear();
      _imageUrlController.clear();
    }
  }

  NotificationMetrics _getNotificationMetrics() {
    // Mock data - replace with actual API call
    return const NotificationMetrics(
      totalSent: 2156,
      delivered: 2089,
      failed: 67,
      successRate: 97,
    );
  }
}
