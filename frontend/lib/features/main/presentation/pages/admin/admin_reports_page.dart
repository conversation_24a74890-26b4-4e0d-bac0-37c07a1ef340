import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/features/dashboard/presentation/bloc/dashboard_bloc.dart';
import 'package:frontend/features/dashboard/domain/entities/dashboard_report_entity.dart';
import 'package:frontend/core/enums/dashboard_report_type.dart';
import 'package:frontend/core/utils/helpers/snack_bar_helper.dart';

import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'package:intl/intl.dart';

class AdminReportsPage extends StatefulWidget {
  const AdminReportsPage({super.key});

  @override
  State<AdminReportsPage> createState() => _AdminReportsPageState();
}

class _AdminReportsPageState extends State<AdminReportsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now().add(const Duration(days: 1));

  int _currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_onTabChanged);
    _loadCurrentTabReportIfNeeded();
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    if (_tabController.index != _currentTabIndex) {
      setState(() {
        _currentTabIndex = _tabController.index;
      });
      _loadCurrentTabReportIfNeeded();
    }
  }

  void _loadCurrentTabReport() {
    final reportType = _getReportTypeForTab(_currentTabIndex);
    _loadReport(reportType);
  }

  void _loadCurrentTabReportIfNeeded() {
    final reportType = _getReportTypeForTab(_currentTabIndex);
    final bloc = context.dashboardBloc;

    // Check if we already have cached data for this report type
    AdminDashboardReportEntity? cachedReport;

    switch (reportType) {
      case DashboardReportType.orders:
        cachedReport = bloc.ordersReport;
        break;
      case DashboardReportType.revenue:
        cachedReport = bloc.revenueReport;
        break;
      case DashboardReportType.inventory:
        cachedReport = bloc.inventoryReport;
        break;
      case DashboardReportType.agents:
        cachedReport = bloc.agentsReport;
        break;
    }

    // Only load if we don't have cached data
    if (cachedReport == null) {
      _loadReport(reportType);
    }
  }

  DashboardReportType _getReportTypeForTab(int tabIndex) {
    switch (tabIndex) {
      case 0:
        return DashboardReportType.orders;
      case 1:
        return DashboardReportType.revenue;
      case 2:
        return DashboardReportType.inventory;
      case 3:
        return DashboardReportType.agents;
      default:
        return DashboardReportType.orders;
    }
  }

  void _loadReport(DashboardReportType reportType) {
    // Always load fresh data when explicitly requested
    context.dashboardBloc.add(
      GetAdminDashboardReportEvent(
        reportType: reportType,
        startDate: _startDate,
        endDate: _endDate,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      body: SafeArea(
        child: BlocListener<DashboardBloc, DashboardState>(
          listener: _handleDashboardState,
          child: Column(
            children: [
              _buildHeader(context),
              _buildDateRangeSelector(context),
              _buildTabBar(context),
              Expanded(child: _buildTabContent(context)),
            ],
          ),
        ),
      ),
    );
  }

  void _refreshCurrentTab() {
    final reportType = _getReportTypeForTab(_currentTabIndex);
    _loadReport(reportType);
  }

  void _handleDashboardState(BuildContext context, DashboardState state) {
    if (state is GetAdminDashboardReportFailure) {
      SnackBarHelper.showErrorSnackBar(
        context,
        message: state.appFailure.getErrorMessage(),
      );
    }
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            context.appColors.primaryColor,
            context.appColors.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: Icon(Icons.arrow_back, color: Colors.white, size: 24.sp),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Business Reports',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'Comprehensive analytics and insights',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
          BlocBuilder<DashboardBloc, DashboardState>(
            builder: (context, state) {
              final isLoading = state is GetAdminDashboardReportLoading;
              return IconButton(
                onPressed: isLoading ? null : _refreshCurrentTab,
                icon: isLoading
                    ? SizedBox(
                        width: 20.w,
                        height: 20.h,
                        child: const CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : Icon(Icons.refresh, color: Colors.white, size: 24.sp),
                tooltip: 'Refresh Data',
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDateRangeSelector(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.date_range,
            color: context.appColors.primaryColor,
            size: 20.sp,
          ),
          SizedBox(width: 8.w),
          Text(
            'Date Range',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Text(
              '${DateFormat('MMM dd').format(_startDate)} - ${DateFormat('MMM dd, yyyy').format(_endDate)}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
          ),
          TextButton.icon(
            onPressed: _showDateRangePicker,
            icon: Icon(
              Icons.edit_calendar,
              size: 16.sp,
              color: context.appColors.primaryColor,
            ),
            label: Text(
              'Change',
              style: TextStyle(
                color: context.appColors.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        indicatorColor: context.appColors.primaryColor,
        labelColor: context.appColors.primaryColor,
        unselectedLabelColor: context.appColors.subtextColor,
        indicatorWeight: 3,
        indicatorSize: TabBarIndicatorSize.tab,
        tabs: [
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.shopping_cart, size: 16.sp),
                SizedBox(width: 4.w),
                const Flexible(
                  child: Text('Orders', overflow: TextOverflow.ellipsis),
                ),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.attach_money, size: 16.sp),
                SizedBox(width: 4.w),
                const Flexible(
                  child: Text('Revenue', overflow: TextOverflow.ellipsis),
                ),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.inventory, size: 16.sp),
                SizedBox(width: 4.w),
                const Flexible(
                  child: Text('Inventory', overflow: TextOverflow.ellipsis),
                ),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.people, size: 16.sp),
                SizedBox(width: 4.w),
                const Flexible(
                  child: Text('Agents', overflow: TextOverflow.ellipsis),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent(BuildContext context) {
    return BlocBuilder<DashboardBloc, DashboardState>(
      builder: (context, state) {
        // Show loading if currently loading
        if (state is GetAdminDashboardReportLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final bloc = context.dashboardBloc;

        switch (_currentTabIndex) {
          case 0:
            return bloc.ordersReport != null
                ? _buildOrdersTab(context, bloc.ordersReport!)
                : _buildEmptyState(context, 'No orders data available');
          case 1:
            return bloc.revenueReport != null
                ? _buildRevenueTab(context, bloc.revenueReport!)
                : _buildEmptyState(context, 'No revenue data available');
          case 2:
            return bloc.inventoryReport != null
                ? _buildInventoryTab(context, bloc.inventoryReport!)
                : _buildEmptyState(context, 'No inventory data available');
          case 3:
            return bloc.agentsReport != null
                ? _buildAgentsTab(context, bloc.agentsReport!)
                : _buildEmptyState(context, 'No agents data available');
          default:
            return _buildEmptyState(context, 'Select a tab to view data');
        }
      },
    );
  }

  Widget _buildEmptyState(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics_outlined,
            size: 64.sp,
            color: context.appColors.subtextColor,
          ),
          SizedBox(height: 16.h),
          Text(
            message,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
        ],
      ),
    );
  }

  void _showDateRangePicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Date Range'),
        content: SizedBox(
          height: 300.h,
          width: 300.w,
          child: SfDateRangePicker(
            onSelectionChanged: (DateRangePickerSelectionChangedArgs args) {
              if (args.value is PickerDateRange) {
                final range = args.value as PickerDateRange;
                if (range.startDate != null && range.endDate != null) {
                  setState(() {
                    _startDate = range.startDate!;
                    _endDate = range.endDate!;
                  });
                }
              }
            },
            selectionMode: DateRangePickerSelectionMode.range,
            initialSelectedRange: PickerDateRange(_startDate, _endDate),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _loadCurrentTabReport();
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  // Helper methods to safely access report data
  String _getReportValue(
    AdminDashboardReportEntity report,
    String key,
    String defaultValue,
  ) {
    try {
      final reportData = report.report;
      if (reportData is Map<String, dynamic>) {
        switch (report.reportType) {
          case DashboardReportType.orders:
            final salesOverview =
                reportData['salesOverview'] as Map<String, dynamic>?;
            if (salesOverview != null) {
              switch (key) {
                case 'totalOrders':
                  return salesOverview['totalSales']?.toString() ??
                      defaultValue;
                case 'completedOrders':
                  return salesOverview['completedOrders']?.toString() ??
                      defaultValue;
                case 'pendingOrders':
                  return salesOverview['pendingOrders']?.toString() ??
                      defaultValue;
                case 'totalRevenue':
                  return salesOverview['totalRevenue']?.toString() ??
                      defaultValue;
                default:
                  return salesOverview[key]?.toString() ?? defaultValue;
              }
            }
            break;
          case DashboardReportType.revenue:
            final financialOverview =
                reportData['financialOverview'] as Map<String, dynamic>?;
            if (financialOverview != null) {
              return financialOverview[key]?.toString() ?? defaultValue;
            }
            break;
          case DashboardReportType.inventory:
            final inventoryStatus =
                reportData['inventoryStatus'] as Map<String, dynamic>?;
            if (inventoryStatus != null) {
              return inventoryStatus[key]?.toString() ?? defaultValue;
            }
            break;
          case DashboardReportType.agents:
            final teamStats = reportData['teamStats'] as Map<String, dynamic>?;
            if (teamStats != null) {
              return teamStats[key]?.toString() ?? defaultValue;
            }
            break;
        }
        return reportData[key]?.toString() ?? defaultValue;
      }
      return defaultValue;
    } catch (e) {
      return defaultValue;
    }
  }

  double _getReportDoubleValue(
    AdminDashboardReportEntity report,
    String key,
    double defaultValue,
  ) {
    try {
      final reportData = report.report;
      if (reportData is Map<String, dynamic>) {
        switch (report.reportType) {
          case DashboardReportType.orders:
            final salesOverview =
                reportData['salesOverview'] as Map<String, dynamic>?;
            if (salesOverview != null) {
              final value = salesOverview[key];
              if (value is num) return value.toDouble();
              if (value is String)
                return double.tryParse(value) ?? defaultValue;
            }
            break;
          case DashboardReportType.revenue:
            final financialOverview =
                reportData['financialOverview'] as Map<String, dynamic>?;
            if (financialOverview != null) {
              final value = financialOverview[key];
              if (value is num) return value.toDouble();
              if (value is String)
                return double.tryParse(value) ?? defaultValue;
            }
            break;
          case DashboardReportType.inventory:
            final inventoryStatus =
                reportData['inventoryStatus'] as Map<String, dynamic>?;
            if (inventoryStatus != null) {
              final value = inventoryStatus[key];
              if (value is num) return value.toDouble();
              if (value is String)
                return double.tryParse(value) ?? defaultValue;
            }
            break;
          case DashboardReportType.agents:
            final teamStats = reportData['teamStats'] as Map<String, dynamic>?;
            if (teamStats != null) {
              final value = teamStats[key];
              if (value is num) return value.toDouble();
              if (value is String)
                return double.tryParse(value) ?? defaultValue;
            }
            break;
        }
        final value = reportData[key];
        if (value is num) return value.toDouble();
        if (value is String) return double.tryParse(value) ?? defaultValue;
      }
      return defaultValue;
    } catch (e) {
      return defaultValue;
    }
  }

  // ============ TAB-SPECIFIC CONTENT ============

  // ORDERS TAB - Only order-related content
  Widget _buildOrdersTab(
    BuildContext context,
    AdminDashboardReportEntity report,
  ) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildOrdersMetrics(context, report),
          SizedBox(height: 24.h),
          _buildOrdersChart(context, report),
          SizedBox(height: 24.h),
          _buildOrderStatusChart(context, report),
        ],
      ),
    );
  }

  // REVENUE TAB - Only financial content
  Widget _buildRevenueTab(
    BuildContext context,
    AdminDashboardReportEntity report,
  ) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildRevenueMetrics(context, report),
          SizedBox(height: 24.h),
          _buildRevenueChart(context, report),
          SizedBox(height: 24.h),
          _buildProfitChart(context, report),
        ],
      ),
    );
  }

  // INVENTORY TAB - Only inventory content
  Widget _buildInventoryTab(
    BuildContext context,
    AdminDashboardReportEntity report,
  ) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInventoryMetrics(context, report),
          SizedBox(height: 24.h),
          _buildInventoryChart(context, report),
          SizedBox(height: 24.h),
          _buildStockHealthChart(context, report),
        ],
      ),
    );
  }

  // AGENTS TAB - Only agent content
  Widget _buildAgentsTab(
    BuildContext context,
    AdminDashboardReportEntity report,
  ) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAgentsMetrics(context, report),
          SizedBox(height: 24.h),
          _buildAgentsChart(context, report),
          SizedBox(height: 24.h),
          _buildPerformanceChart(context, report),
        ],
      ),
    );
  }

  // ============ METRIC CARDS FOR EACH TAB ============

  Widget _buildOrdersMetrics(
    BuildContext context,
    AdminDashboardReportEntity report,
  ) {
    return _buildMetricsGrid(context, [
      _buildMetricCard(
        context,
        'Total Orders',
        _getReportValue(report, 'totalOrders', '0'),
        Icons.shopping_cart,
        Colors.blue,
      ),
      _buildMetricCard(
        context,
        'Completed',
        _getReportValue(report, 'completedOrders', '0'),
        Icons.check_circle,
        Colors.green,
      ),
      _buildMetricCard(
        context,
        'Pending',
        _getReportValue(report, 'pendingOrders', '0'),
        Icons.pending,
        Colors.orange,
      ),
      _buildMetricCard(
        context,
        'Revenue',
        '\$${NumberFormat('#,##0.00').format(_getReportDoubleValue(report, 'totalRevenue', 0.0))}',
        Icons.attach_money,
        Colors.purple,
      ),
    ]);
  }

  Widget _buildRevenueMetrics(
    BuildContext context,
    AdminDashboardReportEntity report,
  ) {
    return _buildMetricsGrid(context, [
      _buildMetricCard(
        context,
        'Total Revenue',
        '\$${NumberFormat('#,##0.00').format(_getReportDoubleValue(report, 'totalRevenue', 0.0))}',
        Icons.attach_money,
        Colors.green,
      ),
      _buildMetricCard(
        context,
        'Total Profit',
        '\$${NumberFormat('#,##0.00').format(_getReportDoubleValue(report, 'totalProfit', 0.0))}',
        Icons.trending_up,
        Colors.blue,
      ),
      _buildMetricCard(
        context,
        'Profit Margin',
        '${_getReportDoubleValue(report, 'profitMargin', 0.0).toStringAsFixed(1)}%',
        Icons.percent,
        Colors.orange,
      ),
      _buildMetricCard(
        context,
        'Total Cost',
        '\$${NumberFormat('#,##0.00').format(_getReportDoubleValue(report, 'totalCost', 0.0))}',
        Icons.money_off,
        Colors.red,
      ),
    ]);
  }

  Widget _buildInventoryMetrics(
    BuildContext context,
    AdminDashboardReportEntity report,
  ) {
    return _buildMetricsGrid(context, [
      _buildMetricCard(
        context,
        'Total Cylinders',
        _getReportValue(report, 'totalCylinders', '0'),
        Icons.propane_tank,
        Colors.blue,
      ),
      _buildMetricCard(
        context,
        'Available',
        _getReportValue(report, 'availableCylinders', '0'),
        Icons.check_circle,
        Colors.green,
      ),
      _buildMetricCard(
        context,
        'Spare Parts',
        _getReportValue(report, 'totalSpareParts', '0'),
        Icons.build,
        Colors.orange,
      ),
      _buildMetricCard(
        context,
        'Low Stock',
        _getReportValue(report, 'lowStockSpareParts', '0'),
        Icons.warning,
        Colors.red,
      ),
    ]);
  }

  Widget _buildAgentsMetrics(
    BuildContext context,
    AdminDashboardReportEntity report,
  ) {
    return _buildMetricsGrid(context, [
      _buildMetricCard(
        context,
        'Total Agents',
        _getReportValue(report, 'totalAgents', '0'),
        Icons.people,
        Colors.blue,
      ),
      _buildMetricCard(
        context,
        'Active Agents',
        _getReportValue(report, 'activeAgents', '0'),
        Icons.person,
        Colors.green,
      ),
      _buildMetricCard(
        context,
        'Avg Rating',
        _getReportDoubleValue(report, 'averageRating', 0.0).toStringAsFixed(1),
        Icons.star,
        Colors.orange,
      ),
      _buildMetricCard(
        context,
        'Total Deliveries',
        _getReportValue(report, 'totalDeliveries', '0'),
        Icons.local_shipping,
        Colors.purple,
      ),
    ]);
  }

  Widget _buildMetricsGrid(BuildContext context, List<Widget> metrics) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth < 600) {
          // Mobile: 2x2 grid
          return Column(
            children: [
              Row(
                children: [
                  Expanded(child: metrics[0]),
                  SizedBox(width: 8.w),
                  Expanded(child: metrics[1]),
                ],
              ),
              SizedBox(height: 8.h),
              Row(
                children: [
                  Expanded(child: metrics[2]),
                  SizedBox(width: 8.w),
                  Expanded(child: metrics[3]),
                ],
              ),
            ],
          );
        } else {
          // Desktop: single row
          return Row(
            children: [
              Expanded(child: metrics[0]),
              SizedBox(width: 8.w),
              Expanded(child: metrics[1]),
              SizedBox(width: 8.w),
              Expanded(child: metrics[2]),
              SizedBox(width: 8.w),
              Expanded(child: metrics[3]),
            ],
          );
        }
      },
    );
  }

  Widget _buildMetricCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(icon, color: color, size: 20.sp),
              ),
              const Spacer(),
              Icon(Icons.trending_up, color: Colors.green, size: 16.sp),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
        ],
      ),
    );
  }

  // ============ CHART COMPONENTS FOR EACH TAB ============

  // ORDERS CHARTS
  Widget _buildOrdersChart(
    BuildContext context,
    AdminDashboardReportEntity report,
  ) {
    final totalOrders = _getReportDoubleValue(report, 'totalOrders', 0.0);
    final completedOrders = _getReportDoubleValue(
      report,
      'completedOrders',
      0.0,
    );
    final pendingOrders = _getReportDoubleValue(report, 'pendingOrders', 0.0);

    final data = [
      ChartData('Total', totalOrders, Colors.blue),
      ChartData('Completed', completedOrders, Colors.green),
      ChartData('Pending', pendingOrders, Colors.orange),
    ];

    return _buildChartContainer(
      context,
      'Orders Overview',
      SfCartesianChart(
        primaryXAxis: const CategoryAxis(),
        series: <ColumnSeries<ChartData, String>>[
          ColumnSeries<ChartData, String>(
            dataSource: data,
            xValueMapper: (ChartData data, _) => data.category,
            yValueMapper: (ChartData data, _) => data.value,
            pointColorMapper: (ChartData data, _) => data.color,
            borderRadius: BorderRadius.circular(4.r),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderStatusChart(
    BuildContext context,
    AdminDashboardReportEntity report,
  ) {
    final completedOrders = _getReportDoubleValue(
      report,
      'completedOrders',
      0.0,
    );
    final pendingOrders = _getReportDoubleValue(report, 'pendingOrders', 0.0);

    final data = [
      ChartData('Completed', completedOrders, Colors.green),
      ChartData('Pending', pendingOrders, Colors.orange),
    ];

    return _buildChartContainer(
      context,
      'Order Status Distribution',
      SfCircularChart(
        series: <PieSeries<ChartData, String>>[
          PieSeries<ChartData, String>(
            dataSource: data,
            xValueMapper: (ChartData data, _) => data.category,
            yValueMapper: (ChartData data, _) => data.value,
            pointColorMapper: (ChartData data, _) => data.color,
            dataLabelSettings: const DataLabelSettings(isVisible: true),
          ),
        ],
      ),
    );
  }

  // REVENUE CHARTS
  Widget _buildRevenueChart(
    BuildContext context,
    AdminDashboardReportEntity report,
  ) {
    final totalRevenue = _getReportDoubleValue(report, 'totalRevenue', 0.0);
    final totalProfit = _getReportDoubleValue(report, 'totalProfit', 0.0);

    final data = [
      ChartData('Revenue', totalRevenue, Colors.green),
      ChartData('Profit', totalProfit, Colors.blue),
    ];

    return _buildChartContainer(
      context,
      'Revenue vs Profit',
      SfCartesianChart(
        primaryXAxis: const CategoryAxis(),
        series: <ColumnSeries<ChartData, String>>[
          ColumnSeries<ChartData, String>(
            dataSource: data,
            xValueMapper: (ChartData data, _) => data.category,
            yValueMapper: (ChartData data, _) => data.value,
            pointColorMapper: (ChartData data, _) => data.color,
            borderRadius: BorderRadius.circular(4.r),
          ),
        ],
      ),
    );
  }

  Widget _buildProfitChart(
    BuildContext context,
    AdminDashboardReportEntity report,
  ) {
    final profitMargin = _getReportDoubleValue(report, 'profitMargin', 0.0);
    final remaining = 100 - profitMargin;

    final data = [
      ChartData('Profit Margin', profitMargin, Colors.green),
      ChartData('Cost Ratio', remaining, Colors.red),
    ];

    return _buildChartContainer(
      context,
      'Profit Margin Analysis',
      SfCircularChart(
        series: <PieSeries<ChartData, String>>[
          PieSeries<ChartData, String>(
            dataSource: data,
            xValueMapper: (ChartData data, _) => data.category,
            yValueMapper: (ChartData data, _) => data.value,
            pointColorMapper: (ChartData data, _) => data.color,
            dataLabelSettings: const DataLabelSettings(isVisible: true),
          ),
        ],
      ),
    );
  }

  // INVENTORY CHARTS
  Widget _buildInventoryChart(
    BuildContext context,
    AdminDashboardReportEntity report,
  ) {
    final totalCylinders = _getReportDoubleValue(report, 'totalCylinders', 0.0);
    final availableCylinders = _getReportDoubleValue(
      report,
      'availableCylinders',
      0.0,
    );
    final totalSpareParts = _getReportDoubleValue(
      report,
      'totalSpareParts',
      0.0,
    );

    final data = [
      ChartData('Total Cylinders', totalCylinders, Colors.blue),
      ChartData('Available', availableCylinders, Colors.green),
      ChartData('Spare Parts', totalSpareParts, Colors.orange),
    ];

    return _buildChartContainer(
      context,
      'Inventory Overview',
      SfCartesianChart(
        primaryXAxis: const CategoryAxis(),
        series: <ColumnSeries<ChartData, String>>[
          ColumnSeries<ChartData, String>(
            dataSource: data,
            xValueMapper: (ChartData data, _) => data.category,
            yValueMapper: (ChartData data, _) => data.value,
            pointColorMapper: (ChartData data, _) => data.color,
            borderRadius: BorderRadius.circular(4.r),
          ),
        ],
      ),
    );
  }

  Widget _buildStockHealthChart(
    BuildContext context,
    AdminDashboardReportEntity report,
  ) {
    final availableCylinders = _getReportDoubleValue(
      report,
      'availableCylinders',
      0.0,
    );
    final lowStockSpareParts = _getReportDoubleValue(
      report,
      'lowStockSpareParts',
      0.0,
    );
    final totalSpareParts = _getReportDoubleValue(
      report,
      'totalSpareParts',
      0.0,
    );
    final healthySpareParts = totalSpareParts - lowStockSpareParts;

    final data = [
      ChartData('Available Cylinders', availableCylinders, Colors.green),
      ChartData('Healthy Spare Parts', healthySpareParts, Colors.blue),
      ChartData('Low Stock Items', lowStockSpareParts, Colors.red),
    ];

    return _buildChartContainer(
      context,
      'Stock Health Status',
      SfCircularChart(
        series: <PieSeries<ChartData, String>>[
          PieSeries<ChartData, String>(
            dataSource: data,
            xValueMapper: (ChartData data, _) => data.category,
            yValueMapper: (ChartData data, _) => data.value,
            pointColorMapper: (ChartData data, _) => data.color,
            dataLabelSettings: const DataLabelSettings(isVisible: true),
          ),
        ],
      ),
    );
  }

  // AGENTS CHARTS
  Widget _buildAgentsChart(
    BuildContext context,
    AdminDashboardReportEntity report,
  ) {
    final totalAgents = _getReportDoubleValue(report, 'totalAgents', 0.0);
    final activeAgents = _getReportDoubleValue(report, 'activeAgents', 0.0);
    final totalDeliveries = _getReportDoubleValue(
      report,
      'totalDeliveries',
      0.0,
    );

    final data = [
      ChartData('Total Agents', totalAgents, Colors.blue),
      ChartData('Active Agents', activeAgents, Colors.green),
      ChartData('Total Deliveries', totalDeliveries, Colors.purple),
    ];

    return _buildChartContainer(
      context,
      'Agent Overview',
      SfCartesianChart(
        primaryXAxis: const CategoryAxis(),
        series: <ColumnSeries<ChartData, String>>[
          ColumnSeries<ChartData, String>(
            dataSource: data,
            xValueMapper: (ChartData data, _) => data.category,
            yValueMapper: (ChartData data, _) => data.value,
            pointColorMapper: (ChartData data, _) => data.color,
            borderRadius: BorderRadius.circular(4.r),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceChart(
    BuildContext context,
    AdminDashboardReportEntity report,
  ) {
    final averageRating = _getReportDoubleValue(report, 'averageRating', 0.0);
    final maxRating = 5.0;
    final remaining = maxRating - averageRating;

    final data = [
      ChartData('Average Rating', averageRating, Colors.green),
      ChartData('Room for Improvement', remaining, Colors.grey),
    ];

    return _buildChartContainer(
      context,
      'Performance Rating',
      SfCircularChart(
        series: <PieSeries<ChartData, String>>[
          PieSeries<ChartData, String>(
            dataSource: data,
            xValueMapper: (ChartData data, _) => data.category,
            yValueMapper: (ChartData data, _) => data.value,
            pointColorMapper: (ChartData data, _) => data.color,
            dataLabelSettings: const DataLabelSettings(isVisible: true),
          ),
        ],
      ),
    );
  }

  // SHARED CHART CONTAINER
  Widget _buildChartContainer(
    BuildContext context,
    String title,
    Widget chart,
  ) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          SizedBox(height: 300.h, child: chart),
        ],
      ),
    );
  }
}

// Chart data model
class ChartData {
  ChartData(this.category, this.value, this.color);
  final String category;
  final double value;
  final Color color;
}
