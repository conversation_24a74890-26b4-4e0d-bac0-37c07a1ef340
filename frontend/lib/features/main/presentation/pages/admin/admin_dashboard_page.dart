import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:intl/intl.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/features/dashboard/presentation/bloc/dashboard_bloc.dart';
import 'package:frontend/features/dashboard/domain/entities/admin_dashboard_entity.dart';

import 'package:frontend/features/main/presentation/widgets/admin_drawer.dart';

class AdminDashboardPage extends StatefulWidget {
  const AdminDashboardPage({super.key});

  @override
  State<AdminDashboardPage> createState() => _AdminDashboardPageState();
}

class _AdminDashboardPageState extends State<AdminDashboardPage> {
  @override
  void initState() {
    super.initState();
    _loadDashboard();
  }

  void _loadDashboard({bool forceRefresh = false}) {
    final dashboardBloc = context.dashboardBloc;
    final canRefetch = forceRefresh || dashboardBloc.adminDashboard == null;
    if (!canRefetch) return;

    context.read<DashboardBloc>().add(GetAdminDashboardEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      drawer: const AdminDrawer(),
      appBar: AppBar(
        backgroundColor: context.appColors.surfaceColor,
        elevation: 0,
        leading: Builder(
          builder: (context) => IconButton(
            icon: Icon(Icons.menu, color: context.appColors.textColor),
            onPressed: () => Scaffold.of(context).openDrawer(),
          ),
        ),
        title: Text(
          'Admin Dashboard',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () => _loadDashboard(forceRefresh: true),
            icon: Icon(Icons.refresh, color: context.appColors.primaryColor),
          ),
        ],
      ),
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async => _loadDashboard(forceRefresh: true),
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [_buildDashboardContent(context)],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDashboardContent(BuildContext context) {
    return BlocBuilder<DashboardBloc, DashboardState>(
      builder: (context, state) {
        if (state is GetAdminDashboardLoading) {
          return _buildLoadingWidget(context);
        }

        if (state is GetAdminDashboardFailure) {
          return _buildErrorWidget(context, state.appFailure.getErrorMessage());
        }

        // if (state is GetAdminDashboardSuccess) {
        final dashboard = context.dashboardBloc.adminDashboard;
        if (dashboard != null) {
          return _buildDashboardData(context, dashboard);
        }
        // }

        return _buildEmptyWidget(context);
      },
    );
  }

  Widget _buildLoadingWidget(BuildContext context) {
    return SizedBox(
      height: 400.h,
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildErrorWidget(BuildContext context, String message) {
    return Container(
      height: 400.h,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: context.appColors.errorColor.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48.sp,
            color: context.appColors.errorColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'Failed to load dashboard',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _loadDashboard,
            style: ElevatedButton.styleFrom(
              backgroundColor: context.appColors.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget(BuildContext context) {
    return Container(
      height: 400.h,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.dashboard_outlined,
            size: 48.sp,
            color: context.appColors.subtextColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'No dashboard data available',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Pull to refresh or check back later',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardData(
    BuildContext context,
    AdminDashboardEntity dashboard,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildWelcomeHeader(context),
        SizedBox(height: 24.h),
        _buildKPICards(context, dashboard),
        SizedBox(height: 24.h),
        _buildSalesAndInventorySection(context, dashboard),
        SizedBox(height: 24.h),
        _buildFinancialSection(context, dashboard),
        SizedBox(height: 24.h),
        _buildAgentPerformanceSection(context, dashboard),
        SizedBox(height: 24.h),
        _buildSystemHealthSection(context, dashboard),
      ],
    );
  }

  Widget _buildWelcomeHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            context.appColors.primaryColor,
            context.appColors.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: context.appColors.primaryColor.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back, Admin!',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'Here\'s what\'s happening with your business today.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                SizedBox(height: 12.h),
                Text(
                  DateFormat('EEEE, MMMM d, y').format(DateTime.now()),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.dashboard,
            size: 60.sp,
            color: Colors.white.withValues(alpha: 0.3),
          ),
        ],
      ),
    );
  }

  Widget _buildKPICards(BuildContext context, AdminDashboardEntity dashboard) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Key Performance Indicators',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: _buildKPICard(
                context,
                'Total Sales',
                dashboard.salesOverview.totalSales.toString(),
                Icons.trending_up,
                Colors.blue,
                '${dashboard.salesOverview.weeklyTrend >= 0 ? '+' : ''}${dashboard.salesOverview.weeklyTrend}% this week',
                dashboard.salesOverview.weeklyTrend >= 0,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildKPICard(
                context,
                'Total Revenue',
                '\$${NumberFormat('#,##0.00').format(dashboard.salesOverview.totalRevenue)}',
                Icons.attach_money,
                Colors.green,
                'Daily avg: \$${NumberFormat('#,##0').format(dashboard.salesOverview.dailyAverage)}',
                true,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildKPICard(
                context,
                'Total Users',
                dashboard.systemHealth.totalUsers.toString(),
                Icons.people,
                Colors.purple,
                '${dashboard.systemHealth.activeUsers} active users',
                true,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildKPICard(
                context,
                'Orders This Month',
                dashboard.systemHealth.ordersThisMonth.toString(),
                Icons.shopping_cart,
                Colors.orange,
                '${dashboard.systemHealth.paymentSuccessRate.toStringAsFixed(1)}% success rate',
                true,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildKPICard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
    String subtitle,
    bool isPositive,
  ) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(icon, color: color, size: 20.sp),
              ),
              const Spacer(),
              Icon(
                isPositive ? Icons.trending_up : Icons.trending_down,
                color: isPositive ? Colors.green : Colors.red,
                size: 16.sp,
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: isPositive ? Colors.green : Colors.red,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSalesAndInventorySection(
    BuildContext context,
    AdminDashboardEntity dashboard,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: _buildSalesOverviewCard(context, dashboard.salesOverview),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: _buildInventoryStatusCard(context, dashboard.inventoryStatus),
        ),
      ],
    );
  }

  Widget _buildSalesOverviewCard(
    BuildContext context,
    SalesOverviewEntity salesOverview,
  ) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: context.appColors.dividerColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.trending_up, color: Colors.green, size: 24.sp),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  'Sales Overview',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 20.h),
          _buildSalesChart(context, salesOverview),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: _buildSalesMetric(
                  context,
                  'Total Sales',
                  salesOverview.totalSales.toString(),
                  Icons.shopping_cart,
                  Colors.blue,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: _buildSalesMetric(
                  context,
                  'Daily Average',
                  '\$${NumberFormat('#,##0').format(salesOverview.dailyAverage)}',
                  Icons.calendar_today,
                  Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInventoryStatusCard(
    BuildContext context,
    InventoryStatusEntity inventoryStatus,
  ) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: context.appColors.dividerColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.inventory_2, color: Colors.purple, size: 24.sp),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  'Inventory Status',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 20.h),
          _buildInventoryChart(context, inventoryStatus),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: _buildInventoryMetric(
                  context,
                  'Cylinders',
                  '${inventoryStatus.availableCylinders}/${inventoryStatus.totalCylinders}',
                  Icons.propane_tank,
                  Colors.blue,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: _buildInventoryMetric(
                  context,
                  'Spare Parts',
                  '${inventoryStatus.totalSpareParts - inventoryStatus.lowStockSpareParts}/${inventoryStatus.totalSpareParts}',
                  Icons.build,
                  inventoryStatus.lowStockSpareParts > 0
                      ? Colors.red
                      : Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialSection(
    BuildContext context,
    AdminDashboardEntity dashboard,
  ) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: context.appColors.dividerColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.account_balance_wallet,
                color: Colors.green,
                size: 24.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'Financial Overview',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 20.h),
          Row(
            children: [
              Expanded(
                child: _buildFinancialMetric(
                  context,
                  'Total Revenue',
                  '\$${NumberFormat('#,##0.00').format(dashboard.financialOverview.totalRevenue)}',
                  Icons.trending_up,
                  Colors.green,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: _buildFinancialMetric(
                  context,
                  'Total Profit',
                  '\$${NumberFormat('#,##0.00').format(dashboard.financialOverview.totalProfit)}',
                  Icons.monetization_on,
                  Colors.blue,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: _buildFinancialMetric(
                  context,
                  'Profit Margin',
                  '${dashboard.financialOverview.profitMargin.toStringAsFixed(1)}%',
                  Icons.percent,
                  Colors.purple,
                ),
              ),
            ],
          ),
          SizedBox(height: 20.h),
          _buildMostProfitableItems(
            context,
            dashboard.financialOverview.mostProfitableItems,
          ),
        ],
      ),
    );
  }

  Widget _buildAgentPerformanceSection(
    BuildContext context,
    AdminDashboardEntity dashboard,
  ) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: context.appColors.dividerColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(Icons.people, color: Colors.blue, size: 24.sp),
                  SizedBox(width: 8.w),
                  Text(
                    'Agent Performance',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: context.appColors.textColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              TextButton(
                onPressed: () {
                  // Navigate to agents page
                },
                child: Text(
                  'View All',
                  style: TextStyle(
                    color: context.appColors.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          ...dashboard.agentPerformance
              .take(5)
              .map((agent) => _buildAgentPerformanceItem(context, agent)),
        ],
      ),
    );
  }

  Widget _buildSystemHealthSection(
    BuildContext context,
    AdminDashboardEntity dashboard,
  ) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: context.appColors.dividerColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.health_and_safety, color: Colors.green, size: 24.sp),
              SizedBox(width: 8.w),
              Text(
                'System Health',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 20.h),
          _buildSystemHealthChart(context, dashboard.systemHealth),
        ],
      ),
    );
  }

  // Helper methods for building UI components
  Widget _buildSalesChart(
    BuildContext context,
    SalesOverviewEntity salesOverview,
  ) {
    final data = [
      ChartData('Revenue', salesOverview.totalRevenue, Colors.green),
      ChartData(
        'Daily Avg',
        salesOverview.dailyAverage * 30,
        Colors.blue,
      ), // Monthly estimate
    ];

    return SizedBox(
      height: 120.h,
      child: SfCartesianChart(
        primaryXAxis: CategoryAxis(
          labelStyle: TextStyle(
            color: context.appColors.subtextColor,
            fontSize: 10.sp,
          ),
        ),
        primaryYAxis: NumericAxis(
          labelStyle: TextStyle(
            color: context.appColors.subtextColor,
            fontSize: 10.sp,
          ),
          numberFormat: NumberFormat.compact(),
        ),
        series: <CartesianSeries>[
          ColumnSeries<ChartData, String>(
            dataSource: data,
            xValueMapper: (ChartData data, _) => data.category,
            yValueMapper: (ChartData data, _) => data.value,
            pointColorMapper: (ChartData data, _) => data.color,
          ),
        ],
      ),
    );
  }

  Widget _buildInventoryChart(
    BuildContext context,
    InventoryStatusEntity inventoryStatus,
  ) {
    final data = [
      ChartData(
        'Available Cylinders',
        inventoryStatus.availableCylinders.toDouble(),
        Colors.blue,
      ),
      ChartData(
        'Total Cylinders',
        inventoryStatus.totalCylinders.toDouble(),
        Colors.lightBlue,
      ),
      ChartData(
        'Spare Parts',
        inventoryStatus.totalSpareParts.toDouble(),
        Colors.green,
      ),
      ChartData(
        'Low Stock Parts',
        inventoryStatus.lowStockSpareParts.toDouble(),
        Colors.red,
      ),
    ];

    return SizedBox(
      height: 120.h,
      child: SfCircularChart(
        legend: Legend(
          isVisible: true,
          position: LegendPosition.bottom,
          textStyle: TextStyle(
            color: context.appColors.textColor,
            fontSize: 8.sp,
          ),
        ),
        series: <CircularSeries>[
          DoughnutSeries<ChartData, String>(
            dataSource: data,
            xValueMapper: (ChartData data, _) => data.category,
            yValueMapper: (ChartData data, _) => data.value,
            pointColorMapper: (ChartData data, _) => data.color,
          ),
        ],
      ),
    );
  }

  Widget _buildSalesMetric(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20.sp),
        SizedBox(height: 8.h),
        Text(
          value,
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          title,
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: context.appColors.subtextColor,
          ),
        ),
      ],
    );
  }

  Widget _buildInventoryMetric(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20.sp),
        SizedBox(height: 8.h),
        Text(
          value,
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          title,
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: context.appColors.subtextColor,
          ),
        ),
      ],
    );
  }

  Widget _buildFinancialMetric(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20.sp),
        SizedBox(height: 8.h),
        Text(
          value,
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          title,
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: context.appColors.subtextColor,
          ),
        ),
      ],
    );
  }

  Widget _buildMostProfitableItems(
    BuildContext context,
    List<MostProfitableItemEntity> items,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Most Profitable Items',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12.h),
        ...items
            .take(3)
            .map(
              (item) => Padding(
                padding: EdgeInsets.only(bottom: 8.h),
                child: Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(6.w),
                      decoration: BoxDecoration(
                        color: _getItemTypeColor(
                          item.type,
                        ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6.r),
                      ),
                      child: Icon(
                        _getItemTypeIcon(item.type),
                        color: _getItemTypeColor(item.type),
                        size: 16.sp,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item.name,
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(
                                  color: context.appColors.textColor,
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                          Text(
                            item.type.toUpperCase(),
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: context.appColors.subtextColor,
                                ),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      '\$${NumberFormat('#,##0.00').format(item.profit)}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
      ],
    );
  }

  Widget _buildAgentPerformanceItem(
    BuildContext context,
    AgentPerformanceEntity agent,
  ) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: context.appColors.primaryColor.withValues(
              alpha: 0.1,
            ),
            child: Text(
              agent.name.isNotEmpty ? agent.name[0].toUpperCase() : 'A',
              style: TextStyle(
                color: context.appColors.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  agent.name,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${agent.completedDeliveries} deliveries',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              Icon(Icons.star, color: Colors.amber, size: 16.sp),
              SizedBox(width: 4.w),
              Text(
                agent.avgRating.toStringAsFixed(1),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSystemHealthChart(
    BuildContext context,
    SystemHealthEntity systemHealth,
  ) {
    final data = [
      ChartData('Total Users', systemHealth.totalUsers.toDouble(), Colors.blue),
      ChartData(
        'Active Users',
        systemHealth.activeUsers.toDouble(),
        Colors.green,
      ),
      ChartData(
        'Orders This Month',
        systemHealth.ordersThisMonth.toDouble(),
        Colors.orange,
      ),
    ];

    return SizedBox(
      height: 150.h,
      child: SfCartesianChart(
        primaryXAxis: CategoryAxis(
          labelStyle: TextStyle(
            color: context.appColors.subtextColor,
            fontSize: 10.sp,
          ),
        ),
        primaryYAxis: NumericAxis(
          labelStyle: TextStyle(
            color: context.appColors.subtextColor,
            fontSize: 10.sp,
          ),
          numberFormat: NumberFormat.compact(),
        ),
        series: <CartesianSeries>[
          ColumnSeries<ChartData, String>(
            dataSource: data,
            xValueMapper: (ChartData data, _) => data.category,
            yValueMapper: (ChartData data, _) => data.value,
            pointColorMapper: (ChartData data, _) => data.color,
            dataLabelSettings: const DataLabelSettings(isVisible: true),
          ),
        ],
      ),
    );
  }

  Color _getItemTypeColor(String type) {
    switch (type.toLowerCase()) {
      case 'cylinder':
        return Colors.blue;
      case 'spare_part':
        return Colors.green;
      case 'package':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  IconData _getItemTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'cylinder':
        return Icons.propane_tank;
      case 'spare_part':
        return Icons.build;
      case 'package':
        return Icons.inventory_2;
      default:
        return Icons.category;
    }
  }
}

// Chart data model
class ChartData {
  final String category;
  final double value;
  final Color color;

  ChartData(this.category, this.value, this.color);
}
