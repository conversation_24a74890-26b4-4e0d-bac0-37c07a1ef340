import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/utils/helpers/form_validation_helper.dart';
import 'package:frontend/features/order/presentation/bloc/order_bloc.dart';
import 'package:frontend/features/inventory/presentation/bloc/inventory_bloc.dart';
import 'package:frontend/core/enums/selection_type.dart';
import 'package:frontend/core/enums/user_role.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_selected_filed_displayer.dart';
import 'package:frontend/features/shared/presentation/widgets/image_place_holder.dart';
import 'package:frontend/features/user-management/presentation/bloc/user_bloc.dart';

import '../../../../../core/utils/helpers/snack_bar_helper.dart';
import '../../../../order/domain/entities/order_item_entity.dart';

class CreateOrderPage extends StatefulWidget {
  const CreateOrderPage({super.key});

  @override
  State<CreateOrderPage> createState() => _CreateOrderPageState();
}

class _CreateOrderPageState extends State<CreateOrderPage>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _customerPhoneController = TextEditingController();
  final _deliveryAddressController = TextEditingController();

  late TabController _tabController;
  // String _selectedPaymentMethod = 'waafi_preauth';
  final List<OrderItemEntity> _selectedItems = [];
  double _totalAmount = 0.0;

  // Customer selection
  List<UserEntity> _availableCustomers = [];
  List<UserEntity> _selectedCustomers = [];
  bool _isLoadingCustomers = false;

  @override
  void initState() {
    super.initState();
    // _tabController = TabController(length: 3, vsync: this);
    _tabController = TabController(length: 2, vsync: this);
    _loadInventory();
    _fetchAvailableCustomers();
  }

  void _loadInventory() {
    context.read<InventoryBloc>().add(const GetCylindersEvent());
    context.read<InventoryBloc>().add(const GetPackagesEvent());
    context.read<InventoryBloc>().add(const GetSparePartsEvent());
  }

  void _fetchAvailableCustomers() {
    // Fetch customers using the user bloc
    context.userBloc.add(
      const GetAllUsersEvent(
        role: UserRole.customer,
        limit: 100, // Get all customers
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _customerPhoneController.dispose();
    _deliveryAddressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        title: const Text('Create New Order'),
        backgroundColor: context.appColors.surfaceColor,
        foregroundColor: context.appColors.textColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<OrderBloc, OrderState>(
            listener: (context, state) {
              if (state is CreateOrderSuccess) {
                SnackBarHelper.showSuccessSnackBar(
                  context,
                  message: state.message,
                );
                Navigator.of(context).pop();
              } else if (state is CreateOrderFailure) {
                SnackBarHelper.showErrorSnackBar(
                  context,
                  message: state.appFailure.getErrorMessage(),
                );
              }
            },
          ),
          BlocListener<UserBloc, UserState>(listener: _handleUserBlocState),
        ],
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              _buildHeader(context),
              _buildTabBar(context),
              Expanded(child: _buildTabBarView(context)),
              _buildBottomSection(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        border: Border(
          bottom: BorderSide(color: context.appColors.dividerColor),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Summary',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Items: ${_selectedItems.length}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.subtextColor,
                ),
              ),
              Text(
                'Total: \$${_totalAmount.toStringAsFixed(2)}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: context.appColors.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: context.appColors.primaryColor,
          borderRadius: BorderRadius.circular(6.r),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: context.appColors.subtextColor,
        labelStyle: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w600),
        tabs: const [
          Tab(text: 'Customer'),
          Tab(text: 'Products'),
          // Tab(text: 'Payment'),
        ],
      ),
    );
  }

  Widget _buildTabBarView(BuildContext context) {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildCustomerTab(context),
        _buildProductsTab(context),
        // _buildPaymentTab(context),
      ],
    );
  }

  Widget _buildCustomerTab(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Customer Information',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          BlocBuilder<UserBloc, UserState>(
            builder: (context, state) {
              if (_isLoadingCustomers) {
                return Container(
                  height: 60.h,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: const Center(child: CircularProgressIndicator()),
                );
              }

              return CustomSelectFieldDisplayer<UserEntity>(
                labelText: 'Select Customer',
                selectedItems: _selectedCustomers,
                selectionType: SelectionType.singleSelection,
                displayItem: _getCustomerDisplayName,
                displaySubTitle: _getCustomerSubtitle,
                displayImage: (user) =>
                    'https://cdn-icons-png.flaticon.com/128/16856/16856078.png',
                onSelectionChanged: _onCustomerSelectionChanged,
                options: _availableCustomers,
                bottomSheetTitle: 'Select Customer',
                validator: (value) {
                  if (_selectedCustomers.isEmpty) {
                    return 'Please select a customer';
                  }
                  return null;
                },
              );
            },
          ),
          if (_selectedCustomers.isNotEmpty) ...[
            SizedBox(height: 16.h),
            _buildSelectedCustomerInfo(context),
          ],
          SizedBox(height: 16.h),
          TextFormField(
            controller: _deliveryAddressController,
            decoration: InputDecoration(
              labelText: 'Delivery Address',
              hintText: 'Enter full delivery address',
              prefixIcon: const Icon(Icons.location_on),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            validator: (value) => FormValidationHelper.validateRequiredField(
              value: value,
              fieldName: 'Delivery address',
            ),
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildProductsTab(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select Products',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          BlocBuilder<InventoryBloc, InventoryState>(
            builder: (context, state) {
              return DefaultTabController(
                length: 3,
                child: Column(
                  children: [
                    TabBar(
                      labelColor: context.appColors.primaryColor,
                      unselectedLabelColor: context.appColors.subtextColor,
                      indicatorColor: context.appColors.primaryColor,
                      tabs: const [
                        Tab(text: 'Cylinders'),
                        Tab(text: 'Packages'),
                        Tab(text: 'Spare Parts'),
                      ],
                    ),
                    SizedBox(height: 16.h),
                    SizedBox(
                      height: 300.h,
                      child: TabBarView(
                        children: [
                          _buildCylindersList(context, state),
                          _buildPackagesList(context, state),
                          _buildSparePartsList(context, state),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          if (_selectedItems.isNotEmpty) ...[
            SizedBox(height: 16.h),
            _buildSelectedItemsList(context),
          ],
        ],
      ),
    );
  }

  // Widget _buildPaymentTab(BuildContext context) {
  //   return SingleChildScrollView(
  //     padding: EdgeInsets.all(16.w),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Text(
  //           'Payment Method',
  //           style: Theme.of(context).textTheme.titleMedium?.copyWith(
  //             color: context.appColors.textColor,
  //             fontWeight: FontWeight.bold,
  //           ),
  //         ),
  //         SizedBox(height: 16.h),
  //         _buildPaymentMethodTile('waafi_preauth', 'Waafi Pay', Icons.payment),
  //         SizedBox(height: 8.h),
  //         _buildPaymentMethodTile('cash', 'Cash on Delivery', Icons.money),
  //       ],
  //     ),
  //   );
  // }

  // Widget _buildPaymentMethodTile(String value, String title, IconData icon) {
  //   return Container(
  //     decoration: BoxDecoration(
  //       color: context.appColors.surfaceColor,
  //       borderRadius: BorderRadius.circular(8.r),
  //       border: Border.all(
  //         color: _selectedPaymentMethod == value
  //             ? context.appColors.primaryColor
  //             : context.appColors.dividerColor,
  //       ),
  //     ),
  //     child: RadioListTile<String>(
  //       value: value,
  //       groupValue: _selectedPaymentMethod,
  //       onChanged: (String? newValue) {
  //         setState(() {
  //           _selectedPaymentMethod = newValue!;
  //         });
  //       },
  //       title: Text(title),
  //       secondary: Icon(icon),
  //       activeColor: context.appColors.primaryColor,
  //     ),
  //   );
  // }

  Widget _buildBottomSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        border: Border(top: BorderSide(color: context.appColors.dividerColor)),
      ),
      child: BlocBuilder<OrderBloc, OrderState>(
        builder: (context, state) {
          final isLoading = state is CreateOrderLoading;

          return SizedBox(
            width: double.infinity,
            height: 48.h,
            child: ElevatedButton(
              onPressed: isLoading ? null : _createOrder,
              style: ElevatedButton.styleFrom(
                backgroundColor: context.appColors.primaryColor,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: isLoading
                  ? SizedBox(
                      height: 20.h,
                      width: 20.w,
                      child: const CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      'Create Order - \$${_totalAmount.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          );
        },
      ),
    );
  }

  void _createOrder() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one product'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (_selectedCustomers.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a customer'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    context.read<OrderBloc>().add(
      CreateOrderEvent(
        customerId: _selectedCustomers.first.id, // Use selected customer ID
        items: _selectedItems,
        deliveryAddress: _deliveryAddressController.text.trim(),
        // paymentMethod: _selectedPaymentMethod,
      ),
    );
  }

  // Product selection methods
  Widget _buildCylindersList(BuildContext context, InventoryState state) {
    if (state is GetCylindersLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is GetCylindersFailure) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48.sp, color: Colors.red),
            SizedBox(height: 8.h),
            const Text('Failed to load cylinders'),
            ElevatedButton(
              onPressed: () =>
                  context.read<InventoryBloc>().add(const GetCylindersEvent()),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    final cylinders = context.read<InventoryBloc>().cylinders;
    if (cylinders.isEmpty) {
      return Center(
        child: Text(
          'No cylinders available',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: context.appColors.subtextColor,
          ),
        ),
      );
    }

    return ListView.builder(
      itemCount: cylinders.length,
      itemBuilder: (context, index) {
        final cylinder = cylinders[index];
        return _buildProductCard(
          context,
          id: cylinder.id,
          name: '${cylinder.type.name} - ${cylinder.material.name}',
          price: cylinder.price,
          description: cylinder.description,
          imageUrl: cylinder.formattedImageUrl,
          availableQuantity: cylinder.availableQuantity,
          itemType: 'CYLINDER', // Cylinder
        );
      },
    );
  }

  Widget _buildPackagesList(BuildContext context, InventoryState state) {
    if (state is GetPackagesLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is GetPackagesFailure) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48.sp, color: Colors.red),
            SizedBox(height: 8.h),
            const Text('Failed to load packages'),
            ElevatedButton(
              onPressed: () =>
                  context.read<InventoryBloc>().add(const GetPackagesEvent()),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    final packages = context.read<InventoryBloc>().packages;
    if (packages.isEmpty) {
      return Center(
        child: Text(
          'No packages available',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: context.appColors.subtextColor,
          ),
        ),
      );
    }

    return ListView.builder(
      itemCount: packages.length,
      itemBuilder: (context, index) {
        final package = packages[index];
        return _buildProductCard(
          context,
          id: package.id,
          name: package.name,
          price: package.finalPrice,
          description: package.description,
          imageUrl: package.formattedImageUrl,
          availableQuantity: package.availableQuantity,
          itemType: 'PACKAGE', // package',
        );
      },
    );
  }

  Widget _buildSparePartsList(BuildContext context, InventoryState state) {
    if (state is GetSparePartsLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is GetSparePartsFailure) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48.sp, color: Colors.red),
            SizedBox(height: 8.h),
            const Text('Failed to load spare parts'),
            ElevatedButton(
              onPressed: () =>
                  context.read<InventoryBloc>().add(const GetSparePartsEvent()),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    final spareParts = context.read<InventoryBloc>().spareParts;
    if (spareParts.isEmpty) {
      return Center(
        child: Text(
          'No spare parts available',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: context.appColors.subtextColor,
          ),
        ),
      );
    }

    return ListView.builder(
      itemCount: spareParts.length,
      itemBuilder: (context, index) {
        final sparePart = spareParts[index];
        return _buildProductCard(
          context,
          id: sparePart.id,
          name: sparePart.category.label,
          price: sparePart.price,
          description: sparePart.description,
          imageUrl: sparePart.formattedImageUrl,
          availableQuantity: sparePart.availableQuantity,
          itemType: 'SPARE_PART', // 'spare_part',
        );
      },
    );
  }

  Widget _buildProductCard(
    BuildContext context, {
    required String id,
    required String name,
    required double price,
    required String description,
    required String imageUrl,
    required int availableQuantity,
    required String itemType,
  }) {
    final isSelected = _selectedItems.any((item) => item.itemId == id);
    final selectedItem = isSelected
        ? _selectedItems.firstWhere((item) => item.itemId == id)
        : null;

    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: isSelected
              ? context.appColors.primaryColor
              : context.appColors.dividerColor,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 60.w,
            height: 60.h,
            decoration: BoxDecoration(
              color: context.appColors.backgroundColor,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: imageUrl.isNotEmpty
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8.r),
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => ImagePlaceholder(
                        width: 60.w,
                        height: 60.h,
                        child: Icon(
                          _getItemIcon(itemType),
                          color: context.appColors.primaryColor,
                          size: 24.sp,
                        ),
                      ),
                      errorWidget: (context, url, error) => ImagePlaceholder(
                        width: 60.w,
                        height: 60.h,
                        child: Icon(
                          _getItemIcon(itemType),
                          color: context.appColors.primaryColor,
                          size: 24.sp,
                        ),
                      ),
                    ),
                  )
                : ImagePlaceholder(
                    width: 60.w,
                    height: 60.h,
                    child: Icon(
                      _getItemIcon(itemType),
                      color: context.appColors.primaryColor,
                      size: 24.sp,
                    ),
                  ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4.h),
                Row(
                  children: [
                    Text(
                      '\$${price.toStringAsFixed(2)}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: context.appColors.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      'Stock: $availableQuantity',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: availableQuantity > 0
                            ? Colors.green
                            : Colors.red,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          if (availableQuantity > 0) ...[
            if (isSelected) ...[
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    onPressed: () => _decreaseQuantity(id),
                    icon: const Icon(Icons.remove_circle_outline),
                    iconSize: 20.sp,
                  ),
                  Text(
                    '${selectedItem!.quantity}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    onPressed: selectedItem.quantity < availableQuantity
                        ? () => _increaseQuantity(id)
                        : null,
                    icon: const Icon(Icons.add_circle_outline),
                    iconSize: 20.sp,
                  ),
                ],
              ),
            ] else ...[
              ElevatedButton(
                onPressed: () => _addToCart(id, itemType, price),
                style: ElevatedButton.styleFrom(
                  backgroundColor: context.appColors.primaryColor,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 6.h,
                  ),
                ),
                child: const Text('Add'),
              ),
            ],
          ] else ...[
            Text(
              'Out of Stock',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSelectedItemsList(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Selected Items',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 12.h),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _selectedItems.length,
            separatorBuilder: (context, index) => SizedBox(height: 8.h),
            itemBuilder: (context, index) {
              final item = _selectedItems[index];
              return Row(
                children: [
                  Expanded(
                    child: Text(
                      '${item.itemType.replaceAll('_', ' ')} × ${item.quantity}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  IconButton(
                    onPressed: () => _removeFromCart(item.itemId),
                    icon: const Icon(Icons.delete_outline, color: Colors.red),
                    iconSize: 20.sp,
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  // Cart management methods
  void _addToCart(String itemId, String itemType, double price) {
    setState(() {
      _selectedItems.add(
        OrderItemEntity(
          id: '',
          itemId: itemId,
          itemType: itemType,
          quantity: 1,
        ),
      );
      _updateTotalAmount();
    });
  }

  void _increaseQuantity(String itemId) {
    setState(() {
      final index = _selectedItems.indexWhere((item) => item.itemId == itemId);
      if (index != -1) {
        final currentItem = _selectedItems[index];
        _selectedItems[index] = OrderItemEntity(
          id: currentItem.id,
          itemId: currentItem.itemId,
          itemType: currentItem.itemType,
          quantity: currentItem.quantity + 1,
        );
        _updateTotalAmount();
      }
    });
  }

  void _decreaseQuantity(String itemId) {
    setState(() {
      final index = _selectedItems.indexWhere((item) => item.itemId == itemId);
      if (index != -1) {
        if (_selectedItems[index].quantity > 1) {
          final currentItem = _selectedItems[index];
          _selectedItems[index] = OrderItemEntity(
            id: currentItem.id,
            itemId: currentItem.itemId,
            itemType: currentItem.itemType,
            quantity: currentItem.quantity - 1,
          );
        } else {
          _selectedItems.removeAt(index);
        }
        _updateTotalAmount();
      }
    });
  }

  void _removeFromCart(String itemId) {
    setState(() {
      _selectedItems.removeWhere((item) => item.itemId == itemId);
      _updateTotalAmount();
    });
  }

  void _updateTotalAmount() {
    double total = 0.0;
    final inventoryBloc = context.read<InventoryBloc>();

    print('🔍 Updating total amount for ${_selectedItems.length} items');

    for (final item in _selectedItems) {
      double itemPrice = 0.0;
      print(
        '📦 Processing item: ${item.itemType} - ${item.itemId} - Qty: ${item.quantity}',
      );

      try {
        if (item.itemType == 'CYLINDER') {
          final cylinder = inventoryBloc.cylinders.firstWhere(
            (c) => c.id == item.itemId,
          );
          itemPrice = cylinder.price;
          print('🔥 Cylinder price: \$$itemPrice');
        } else if (item.itemType == 'PACKAGE') {
          final package = inventoryBloc.packages.firstWhere(
            (p) => p.id == item.itemId,
          );
          itemPrice = package.finalPrice;
          print('📦 Package price: \$$itemPrice');
        } else if (item.itemType == 'SPARE_PART') {
          final sparePart = inventoryBloc.spareParts.firstWhere(
            (s) => s.id == item.itemId,
          );
          itemPrice = sparePart.price;
          print('🔧 Spare part price: \$$itemPrice');
        } else {
          print('❌ Unknown item type: ${item.itemType}');
        }
      } catch (e) {
        print(
          '❌ Error finding item ${item.itemId} of type ${item.itemType}: $e',
        );
        itemPrice = 0.0;
      }

      final itemTotal = itemPrice * item.quantity;
      total += itemTotal;
      print('💰 Item total: \$$itemTotal ($itemPrice × ${item.quantity})');
    }

    print('💵 Final total: \$$total');

    setState(() {
      _totalAmount = total;
    });
  }

  // Customer selection methods
  void _handleUserBlocState(BuildContext context, UserState state) {
    if (state is GetAllUsersLoading) {
      setState(() {
        _isLoadingCustomers = true;
      });
    } else if (state is GetAllUsersSuccess) {
      setState(() {
        _availableCustomers = state.users;
        _isLoadingCustomers = false;
      });
    } else if (state is GetAllUsersFailure) {
      setState(() {
        _isLoadingCustomers = false;
      });
      SnackBarHelper.showErrorSnackBar(
        context,
        message:
            'Failed to load customers: ${state.appFailure.getErrorMessage()}',
      );
    }
  }

  void _onCustomerSelectionChanged(List<UserEntity> selectedCustomers) {
    setState(() {
      _selectedCustomers = selectedCustomers;
      if (selectedCustomers.isNotEmpty) {
        _customerPhoneController.text = selectedCustomers.first.phone;
      } else {
        _customerPhoneController.clear();
      }
    });
  }

  String _getCustomerDisplayName(UserEntity customer) {
    return customer.phone;
  }

  String _getCustomerSubtitle(UserEntity customer) {
    return customer.email ?? 'No email';
  }

  Widget _buildSelectedCustomerInfo(BuildContext context) {
    final selectedCustomer = _selectedCustomers.first;
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: context.appColors.primaryColor.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Selected Customer',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: context.appColors.primaryColor,
            ),
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              CircleAvatar(
                radius: 20.r,
                backgroundColor: context.appColors.primaryColor,
                child: Icon(Icons.person, color: Colors.white, size: 20.sp),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getCustomerDisplayName(selectedCustomer),
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      _getCustomerSubtitle(selectedCustomer),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getItemIcon(String itemType) {
    switch (itemType) {
      case 'CYLINDER':
        return Icons.propane_tank;
      case 'PACKAGE':
        return Icons.inventory_2;
      case 'SPARE_PART':
        return Icons.build;
      default:
        return Icons.local_gas_station;
    }
  }
}
