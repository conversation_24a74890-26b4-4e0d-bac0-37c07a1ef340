import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/order_status.dart';
import 'package:frontend/core/utils/helpers/snack_bar_helper.dart';
import 'package:frontend/features/order/domain/entities/order_entity.dart';
import 'package:frontend/features/order/presentation/bloc/order_bloc.dart';
import 'package:frontend/features/shared/presentation/widgets/order_timeline_widget.dart';
import 'package:frontend/features/user-management/presentation/bloc/user_bloc.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';
import 'package:frontend/features/dashboard/domain/entities/customer_dashboard_entity.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../core/enums/user_role.dart';
import '../../../../../core/utils/helpers/url_laucher_helper.dart';
import '../../../../order/domain/entities/order_item_entity.dart';

class OrderDetailsPage extends StatefulWidget {
  final OrderEntity order;

  const OrderDetailsPage({super.key, required this.order});

  @override
  State<OrderDetailsPage> createState() => _OrderDetailsPageState();
}

class _OrderDetailsPageState extends State<OrderDetailsPage> {
  late OrderStatus _currentStatus;

  @override
  void initState() {
    super.initState();
    _currentStatus = widget.order.status;
  }

  void _handleOrderStateChanges(BuildContext context, OrderState state) {
    if (state is AssignAgentToOrderSuccess) {
      SnackBarHelper.showSuccessSnackBar(context, message: state.message);
      // Navigate back to refresh the order list
      Navigator.of(context).pop();
    } else if (state is AssignAgentToOrderFailure) {
      final message = state.appFailure.getErrorMessage();
      SnackBarHelper.showErrorSnackBar(context, message: message);
    } else if (state is CancelOrderSuccess) {
      SnackBarHelper.showSuccessSnackBar(context, message: state.message);
      // Navigate back to refresh the order list
      Navigator.of(context).pop();
    } else if (state is CancelOrderFailure) {
      final message = state.appFailure.getErrorMessage();
      SnackBarHelper.showErrorSnackBar(context, message: message);
    } else if (state is CompleteOrderSuccess) {
      SnackBarHelper.showSuccessSnackBar(context, message: state.message);
      // Navigate back to refresh the order list
      Navigator.of(context).pop();
    } else if (state is CompleteOrderFailure) {
      final message = state.appFailure.getErrorMessage();
      SnackBarHelper.showErrorSnackBar(context, message: message);
    } else if (state is UpdateOrderSuccess) {
      SnackBarHelper.showSuccessSnackBar(context, message: state.message);
      // Navigate back to refresh the order list
      Navigator.of(context).pop();
    } else if (state is UpdateOrderFailure) {
      final message = state.appFailure.getErrorMessage();
      SnackBarHelper.showErrorSnackBar(context, message: message);
    } else if (state is RegenerateQRCodeSuccess) {
      SnackBarHelper.showSuccessSnackBar(context, message: state.message);
      // No need to navigate back, just show success message
    } else if (state is RegenerateQRCodeFailure) {
      final message = state.appFailure.getErrorMessage();
      SnackBarHelper.showErrorSnackBar(context, message: message);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        title: Text(
          // 'Order #${widget.order.id.substring(widget.order.id.length - 6)}',
          'Order Details',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: context.appColors.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: _currentStatus == OrderStatus.delivered
            ? null
            : [
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert, color: Colors.white),
                  onSelected: (value) => _handleAction(context, value),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit_status',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('Edit Status'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'assign_agent',
                      child: Row(
                        children: [
                          Icon(Icons.person_add),
                          SizedBox(width: 8),
                          Text('Assign Agent'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'contact_customer',
                      child: Row(
                        children: [
                          Icon(Icons.phone),
                          SizedBox(width: 8),
                          Text('Contact Customer'),
                        ],
                      ),
                    ),
                    // QR Code regeneration for active orders
                    if (_currentStatus == OrderStatus.confirmed ||
                        _currentStatus == OrderStatus.inTransit)
                      const PopupMenuItem(
                        value: 'regenerate_qr',
                        child: Row(
                          children: [
                            Icon(Icons.qr_code, color: Colors.blue),
                            SizedBox(width: 8),
                            Text(
                              'Regenerate QR Code',
                              style: TextStyle(color: Colors.blue),
                            ),
                          ],
                        ),
                      ),
                    if (_currentStatus != OrderStatus.cancelled)
                      const PopupMenuItem(
                        value: 'cancel',
                        child: Row(
                          children: [
                            Icon(Icons.cancel, color: Colors.red),
                            SizedBox(width: 8),
                            Text(
                              'Cancel Order',
                              style: TextStyle(color: Colors.red),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ],
      ),
      body: BlocListener<OrderBloc, OrderState>(
        listener: (context, state) {
          _handleOrderStateChanges(context, state);
        },
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildOrderHeader(context),
              SizedBox(height: 24.h),
              OrderTimelineWidget(
                order: widget.order,
                style: TimelineStyle.detailed,
              ),
              SizedBox(height: 24.h),
              _buildCustomerInfo(context),
              SizedBox(height: 24.h),
              _buildProductInfo(context),
              SizedBox(height: 24.h),
              _buildDeliveryInfo(context),
              if (widget.order.deliveryAgent != null) ...[
                SizedBox(height: 24.h),
                _buildAgentInfo(context),
              ],
              SizedBox(height: 24.h),
              _buildOrderSummary(context),
              SizedBox(height: 32.h),
              _buildActionButtons(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOrderHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _currentStatus.color,
            _currentStatus.color.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(_currentStatus.icon, color: Colors.white, size: 24.sp),
              SizedBox(width: 8.w),
              Text(
                _currentStatus.label,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            'Order placed on ${_formatDate(widget.order.createdAt)}',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
          if (widget.order.deliveredAt != null) ...[
            SizedBox(height: 4.h),
            Text(
              'Delivered on: ${_formatDate(widget.order.deliveredAt!)}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCustomerInfo(BuildContext context) {
    return _buildSection(context, 'Customer Information', Icons.person, [
      _buildInfoCard(context, [
        _buildInfoRow(
          context,
          'Phone',
          widget.order.customer.phone,
          Icons.phone,
        ),
        if (widget.order.customer.email != null)
          _buildInfoRow(
            context,
            'Email',
            widget.order.customer.email!,
            Icons.email,
          ),
      ]),
    ]);
  }

  Widget _buildProductInfo(BuildContext context) {
    final totalQuantity = widget.order.items.fold<int>(
      0,
      (sum, item) => sum + item.quantity,
    );
    final productSummary = _getProductSummary(widget.order.items);

    return _buildSection(context, 'Product Details', Icons.local_gas_station, [
      _buildInfoCard(context, [
        _buildInfoRow(
          context,
          'Products',
          productSummary,
          Icons.local_gas_station,
        ),
        _buildInfoRow(
          context,
          'Total Quantity',
          '$totalQuantity items',
          Icons.numbers,
        ),
        _buildInfoRow(
          context,
          'Total Amount',
          '\$${widget.order.totalAmount.toStringAsFixed(2)}',
          Icons.attach_money,
        ),
      ]),
    ]);
  }

  String _getProductSummary(List<OrderItemEntity> items) {
    if (items.isEmpty) return 'No items';
    if (items.length == 1) {
      return '${items.first.itemType.replaceAll('_', ' ')} × ${items.first.quantity}';
    }
    return '${items.length} different products';
  }

  Widget _buildDeliveryInfo(BuildContext context) {
    return _buildSection(context, 'Delivery Information', Icons.location_on, [
      _buildInfoCard(context, [
        _buildInfoRow(
          context,
          'Address',
          widget.order.deliveryAddress,
          Icons.location_on,
        ),
        _buildInfoRow(
          context,
          'Payment Method',
          widget.order.paymentMethod.toUpperCase(),
          Icons.payment,
        ),
        if (widget.order.deliveredAt != null)
          _buildInfoRow(
            context,
            'Delivered At',
            _formatDateTime(widget.order.deliveredAt!),
            Icons.check_circle,
          ),
      ]),
    ]);
  }

  Widget _buildAgentInfo(BuildContext context) {
    return _buildSection(context, 'Agent Information', Icons.delivery_dining, [
      _buildInfoCard(
        context,
        [
          _buildInfoRow(
            context,
            'Agent Phone',
            widget.order.deliveryAgent!.phone,
            Icons.phone,
          ),
          if (widget.order.deliveryAgent!.email != null)
            _buildInfoRow(
              context,
              'Agent Email',
              widget.order.deliveryAgent!.email!,
              Icons.email,
            ),
        ],
        actions: [
          ElevatedButton.icon(
            onPressed: () => _callAgent(context),
            icon: Icon(Icons.phone, size: 16.sp),
            label: const Text('Call Agent'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            ),
          ),
        ],
      ),
    ]);
  }

  Widget _buildOrderSummary(BuildContext context) {
    const deliveryFee = 2.0;
    final subtotal = widget.order.totalAmount - deliveryFee;

    return _buildSection(context, 'Order Summary', Icons.receipt, [
      Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: context.appColors.surfaceColor,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: context.appColors.dividerColor),
        ),
        child: Column(
          children: [
            _buildSummaryRow(
              context,
              'Subtotal',
              '\$${subtotal.toStringAsFixed(2)}',
            ),
            SizedBox(height: 8.h),
            _buildSummaryRow(
              context,
              'Delivery Fee',
              '\$${deliveryFee.toStringAsFixed(2)}',
            ),
            SizedBox(height: 12.h),
            Divider(color: context.appColors.dividerColor),
            SizedBox(height: 12.h),
            _buildSummaryRow(
              context,
              'Total Amount',
              '\$${widget.order.totalAmount.toStringAsFixed(2)}',
              isTotal: true,
            ),
          ],
        ),
      ),
    ]);
  }

  Widget _buildSection(
    BuildContext context,
    String title,
    IconData icon,
    List<Widget> children,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: context.appColors.primaryColor, size: 20.sp),
            SizedBox(width: 8.w),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        ...children,
      ],
    );
  }

  Widget _buildInfoCard(
    BuildContext context,
    List<Widget> children, {
    List<Widget>? actions,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        children: [
          ...children,
          if (actions != null) ...[
            SizedBox(height: 16.h),
            Row(children: actions),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        children: [
          Icon(icon, color: context.appColors.subtextColor, size: 16.sp),
          SizedBox(width: 12.w),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(
    BuildContext context,
    String label,
    String value, {
    bool isTotal = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: context.appColors.textColor,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: isTotal
                ? context.appColors.primaryColor
                : context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        if (_currentStatus == OrderStatus.pending) ...[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _assignAgent(context),
              icon: Icon(Icons.person_add, size: 16.sp),
              label: const Text('Assign Agent'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 12.h),
              ),
            ),
          ),
          SizedBox(width: 12.w),
        ],
        if (_currentStatus != OrderStatus.delivered) ...[
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _editStatus(context),
              icon: Icon(Icons.edit, size: 16.sp),
              label: const Text('Edit Status'),
              style: OutlinedButton.styleFrom(
                foregroundColor: context.appColors.primaryColor,
                side: BorderSide(color: context.appColors.primaryColor),
                padding: EdgeInsets.symmetric(vertical: 12.h),
              ),
            ),
          ),
        ],
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime date) {
    return '${date.day}/${date.month}/${date.year} at ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  bool _isValidStatusTransition(
    OrderStatus currentStatus,
    OrderStatus newStatus,
  ) {
    // Define valid status transitions
    switch (currentStatus) {
      case OrderStatus.pending:
        return [
          OrderStatus.confirmed,
          OrderStatus.cancelled,
          OrderStatus.failed,
        ].contains(newStatus);

      case OrderStatus.confirmed:
        return [
          OrderStatus.inTransit,
          OrderStatus.cancelled,
        ].contains(newStatus);

      case OrderStatus.inTransit:
        return [
          OrderStatus.delivered,
          OrderStatus.cancelled,
        ].contains(newStatus);

      case OrderStatus.delivered:
        return false; // No transitions from delivered

      case OrderStatus.cancelled:
        return false; // No transitions from cancelled

      case OrderStatus.failed:
        return [
          OrderStatus.pending, // Allow retry
          OrderStatus.cancelled,
        ].contains(newStatus);
    }
  }

  void _confirmStatusUpdate(
    BuildContext context,
    OrderEntity order,
    OrderStatus newStatus,
  ) {
    Navigator.pop(context); // Close status dialog

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Status Update'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Order #${order.id.substring(order.id.length - 6)}'),
            SizedBox(height: 8.h),
            Row(
              children: [
                const Text('From: '),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                  decoration: BoxDecoration(
                    color: order.status.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Text(
                    order.status.label,
                    style: TextStyle(
                      color: order.status.color,
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 4.h),
            Row(
              children: [
                const Text('To: '),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                  decoration: BoxDecoration(
                    color: newStatus.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Text(
                    newStatus.label,
                    style: TextStyle(
                      color: newStatus.color,
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Text(
              _getStatusUpdateMessage(newStatus),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _updateOrderStatus(context, order, newStatus);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: newStatus.color,
              foregroundColor: Colors.white,
            ),
            child: const Text('Update Status'),
          ),
        ],
      ),
    );
  }

  String _getStatusUpdateMessage(OrderStatus status) {
    switch (status) {
      case OrderStatus.confirmed:
        return 'This will confirm the order and make it ready for assignment.';
      case OrderStatus.inTransit:
        return 'This will mark the order as out for delivery. Make sure an agent is assigned.';
      case OrderStatus.delivered:
        return 'This will mark the order as completed and delivered.';
      case OrderStatus.cancelled:
        return 'This will cancel the order and release any reserved inventory.';
      case OrderStatus.failed:
        return 'This will mark the order as failed. Inventory will be released.';
      case OrderStatus.pending:
        return 'This will reset the order to pending status.';
    }
  }

  void _updateOrderStatus(
    BuildContext context,
    OrderEntity order,
    OrderStatus newStatus,
  ) {
    // Use appropriate bloc events based on the status
    switch (newStatus) {
      case OrderStatus.cancelled:
        context.read<OrderBloc>().add(CancelOrderEvent(orderId: order.id));
        break;
      case OrderStatus.delivered:
        context.read<OrderBloc>().add(CompleteOrderEvent(orderId: order.id));
        break;
      default:
        // For other status updates, we would need a general UpdateOrderStatusEvent
        // For now, show a message that this functionality is coming soon
        SnackBarHelper.showWarningSnackBar(
          context,
          message: 'Status update to ${newStatus.label} is not yet implemented',
        );
        break;
    }

    // Update local state to reflect the change immediately
    setState(() {
      _currentStatus = newStatus;
    });
  }

  void _handleAction(BuildContext context, String action) {
    switch (action) {
      case 'edit_status':
        _editStatus(context);
        break;
      case 'assign_agent':
        _assignAgent(context);
        break;
      case 'contact_customer':
        _contactCustomer(context);
        break;
      case 'regenerate_qr':
        _regenerateQRCode(context);
        break;
      case 'cancel':
        _cancelOrder(context);
        break;
    }
  }

  void _editStatus(BuildContext context) {
    _showStatusEditDialog(context, widget.order);
  }

  void _showStatusEditDialog(BuildContext context, OrderEntity order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Order Status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Order #${order.id.substring(order.id.length - 6)}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Current Status: ${order.status.label}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: order.status.color,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 16.h),
            Text(
              'Select new status:',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 12.h),
            ...OrderStatus.values.map((status) {
              final isCurrentStatus = status == order.status;
              final isValidTransition = _isValidStatusTransition(
                order.status,
                status,
              );

              return Container(
                margin: EdgeInsets.only(bottom: 8.h),
                child: InkWell(
                  onTap: isCurrentStatus || !isValidTransition
                      ? null
                      : () => _confirmStatusUpdate(context, order, status),
                  child: Container(
                    padding: EdgeInsets.all(12.w),
                    decoration: BoxDecoration(
                      color: isCurrentStatus
                          ? status.color.withValues(alpha: 0.1)
                          : isValidTransition
                          ? context.appColors.surfaceColor
                          : context.appColors.backgroundColor,
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: isCurrentStatus
                            ? status.color
                            : isValidTransition
                            ? context.appColors.dividerColor
                            : context.appColors.subtextColor.withValues(
                                alpha: 0.3,
                              ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          status.icon,
                          color: isCurrentStatus || isValidTransition
                              ? status.color
                              : context.appColors.subtextColor,
                          size: 20.sp,
                        ),
                        SizedBox(width: 12.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                status.label,
                                style: Theme.of(context).textTheme.bodyMedium
                                    ?.copyWith(
                                      color:
                                          isCurrentStatus || isValidTransition
                                          ? context.appColors.textColor
                                          : context.appColors.subtextColor,
                                      fontWeight: isCurrentStatus
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                    ),
                              ),
                              if (isCurrentStatus)
                                Text(
                                  'Current',
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(
                                        color: status.color,
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                              if (!isValidTransition && !isCurrentStatus)
                                Text(
                                  'Not allowed',
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(
                                        color: context.appColors.subtextColor,
                                      ),
                                ),
                            ],
                          ),
                        ),
                        if (isValidTransition && !isCurrentStatus)
                          Icon(
                            Icons.arrow_forward_ios,
                            color: context.appColors.subtextColor,
                            size: 16.sp,
                          ),
                      ],
                    ),
                  ),
                ),
              );
            }),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _assignAgent(BuildContext context) {
    _showAgentSelectionDialog(context, widget.order);
  }

  void _contactCustomer(BuildContext context) {
    final customerPhone = widget.order.customer.phone;
    UrlLauncherHelper().launchTel(context: context, phoneNumber: customerPhone);
  }

  void _callAgent(BuildContext context) {
    final agentPhone = widget.order.deliveryAgent?.phone;

    if (agentPhone != null) {
      UrlLauncherHelper().launchTel(context: context, phoneNumber: agentPhone);
    }
  }

  void _cancelOrder(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Order'),
        content: Text(
          'Are you sure you want to cancel order #${widget.order.id}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _currentStatus = OrderStatus.cancelled;
              });
              Navigator.pop(context);
              context.read<OrderBloc>().add(
                CancelOrderEvent(orderId: widget.order.id),
              );
              // ScaffoldMessenger.of(context).showSnackBar(
              //   SnackBar(content: Text('Order #${widget.order.id} cancelled')),
              // );
              SnackBarHelper.showSuccessSnackBar(
                context,
                message: 'Order #${widget.order.id} cancelled',
              );
            },
            child: const Text(
              'Yes, Cancel',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  void _showAgentSelectionDialog(BuildContext context, OrderEntity order) {
    // Load agents when dialog opens
    context.read<UserBloc>().add(
      const GetAllUsersEvent(role: UserRole.agent, isActive: true),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Assign Agent'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400.h,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Order #${order.id.substring(order.id.length - 6)}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.subtextColor,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                'Customer: ${order.customer.phone}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.subtextColor,
                ),
              ),
              SizedBox(height: 16.h),
              Text(
                'Select an agent:',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
              SizedBox(height: 12.h),
              Expanded(
                child: BlocBuilder<UserBloc, UserState>(
                  builder: (context, state) {
                    if (state is GetAllUsersLoading) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    if (state is GetAllUsersFailure) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 48.sp,
                              color: Colors.red,
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              'Failed to load agents',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                            SizedBox(height: 8.h),
                            ElevatedButton(
                              onPressed: () => context.read<UserBloc>().add(
                                const GetAllUsersEvent(
                                  role: UserRole.agent,
                                  isActive: true,
                                ),
                              ),
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      );
                    }

                    if (state is GetAllUsersSuccess) {
                      final agents = state.users
                          .where(
                            (user) =>
                                user.role == UserRole.agent && user.isActive,
                          )
                          .toList();

                      if (agents.isEmpty) {
                        return Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.person_off,
                                size: 48.sp,
                                color: context.appColors.subtextColor,
                              ),
                              SizedBox(height: 8.h),
                              Text(
                                'No agents available',
                                style: Theme.of(context).textTheme.bodyMedium
                                    ?.copyWith(
                                      color: context.appColors.subtextColor,
                                    ),
                              ),
                            ],
                          ),
                        );
                      }

                      return ListView.builder(
                        itemCount: agents.length,
                        itemBuilder: (context, index) {
                          final agent = agents[index];
                          return _buildAgentCard(context, agent, order);
                        },
                      );
                    }

                    return const SizedBox.shrink();
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildAgentCard(
    BuildContext context,
    UserEntity agent,
    OrderEntity order,
  ) {
    final isOnDuty = agent.agentMetadata?.isOnDuty ?? false;
    final vehicleInfo = agent.agentMetadata?.vehicle;
    final rating = agent.agentMetadata?.rating;

    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      child: InkWell(
        onTap: () => _confirmAgentAssignment(context, agent, order),
        child: Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: context.appColors.surfaceColor,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(
              color: isOnDuty
                  ? Colors.green.withValues(alpha: 0.3)
                  : context.appColors.dividerColor,
            ),
          ),
          child: Row(
            children: [
              CircleAvatar(
                radius: 20.r,
                backgroundColor: isOnDuty ? Colors.green : Colors.grey,
                child: Icon(
                  Icons.delivery_dining,
                  color: Colors.white,
                  size: 20.sp,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          agent.phone,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(fontWeight: FontWeight.w600),
                        ),
                        SizedBox(width: 8.w),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 6.w,
                            vertical: 2.h,
                          ),
                          decoration: BoxDecoration(
                            color: isOnDuty
                                ? Colors.green.withValues(alpha: 0.1)
                                : Colors.orange.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                          child: Text(
                            isOnDuty ? 'On Duty' : 'Off Duty',
                            style: TextStyle(
                              color: isOnDuty ? Colors.green : Colors.orange,
                              fontSize: 10.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (agent.email != null) ...[
                      SizedBox(height: 2.h),
                      Text(
                        agent.email!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                      ),
                    ],
                    if (vehicleInfo != null) ...[
                      SizedBox(height: 2.h),
                      Text(
                        '${vehicleInfo.type.name.toUpperCase()}${vehicleInfo.number != null ? ' - ${vehicleInfo.number}' : ''}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                      ),
                    ],
                    if (rating != null) ...[
                      SizedBox(height: 4.h),
                      Row(
                        children: [
                          Icon(Icons.star, color: Colors.amber, size: 12.sp),
                          SizedBox(width: 2.w),
                          Text(
                            rating.toStringAsFixed(1),
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: context.appColors.subtextColor,
                                ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: context.appColors.subtextColor,
                size: 16.sp,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _confirmAgentAssignment(
    BuildContext context,
    UserEntity agent,
    OrderEntity order,
  ) {
    Navigator.pop(context); // Close agent selection dialog

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Agent Assignment'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Order #${order.id.substring(order.id.length - 6)}'),
            SizedBox(height: 8.h),
            Text('Customer: ${order.customer.phone}'),
            SizedBox(height: 8.h),
            Text('Agent: ${agent.phone}'),
            if (agent.email != null) ...[
              SizedBox(height: 4.h),
              Text('Email: ${agent.email}'),
            ],
            SizedBox(height: 12.h),
            Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Text(
                'This will assign the agent to the order and change the status to "Out for Delivery".',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.blue),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _assignAgentToOrder(context, order, agent);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Assign Agent'),
          ),
        ],
      ),
    );
  }

  void _assignAgentToOrder(
    BuildContext context,
    OrderEntity order,
    UserEntity agent,
  ) {
    context.read<OrderBloc>().add(
      AssignAgentToOrderEvent(orderId: order.id, agentId: agent.id),
    );

    SnackBarHelper.showInfoSnackBar(
      context,
      message: 'Assigning agent ${agent.phone} to order...',
    );
  }

  void _regenerateQRCode(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Regenerate QR Code'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Generate a new QR code for order #${widget.order.id.substring(widget.order.id.length - 6)}?',
            ),
            SizedBox(height: 8.h),
            Text(
              'This will invalidate the current QR code and create a new one.',
              style: TextStyle(color: Colors.orange, fontSize: 12.sp),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<OrderBloc>().add(
                RegenerateQRCodeEvent(orderId: widget.order.id),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('Regenerate'),
          ),
        ],
      ),
    );
  }
}
