import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class FlutterSecureStorageServices {
  final FlutterSecureStorage flutterSecureStorage;

  FlutterSecureStorageServices({required this.flutterSecureStorage});

  // final flutterSecureStorage = const FlutterSecureStorage();

  // store data
  Future<void> storeData({required String key, required String value}) async {
    await flutterSecureStorage.write(key: key, value: value);
  }

  // read data
  Future<String?> readData({required String key}) async {
    return await flutterSecureStorage.read(key: key);
  }

  // remove data
  Future<void> removeData({required String key}) async {
    await flutterSecureStorage.delete(key: key);
  }
}
