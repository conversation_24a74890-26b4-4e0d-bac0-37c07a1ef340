import 'dart:io';
import 'package:dio/dio.dart';
import 'package:http_parser/http_parser.dart';
import 'package:path/path.dart' as path;

import '../../../../core/network/api_client/dio_api_client.dart';
import '../../../../core/enums/http_method.dart';
import '../../../../core/utils/helpers/request_data.dart';
import '../model/api_response_model.dart';
import '../../../../core/enums/entity_type.dart';

/// Service for handling image uploads to the backend
class ImageUploadService {
  final DioApiClient _apiClient;

  ImageUploadService(this._apiClient);

  /// Upload image for a specific entity
  ///
  /// [imageFile] - The image file to upload
  /// [category] - Image category ('cylinders', 'spare-parts', 'packages', 'profiles')
  /// [entityType] - Optional entity type ('cylinder', 'sparepart', 'package')
  /// [entityId] - Optional entity ID to associate the image with
  Future<ApiResponseModel<ImageUploadResponse>> uploadImage({
    required File imageFile,
    required String category,
    String? entityType,
    String? entityId,
  }) async {
    try {
      // Prepare form data
      final formData = FormData();

      // Add the image file
      final fileName = path.basename(imageFile.path);
      final fileExtension = path.extension(fileName).toLowerCase();

      // Determine MIME type
      String mimeType = 'image/jpeg';
      if (fileExtension == '.png') {
        mimeType = 'image/png';
      } else if (fileExtension == '.webp') {
        mimeType = 'image/webp';
      } else if (fileExtension == '.gif') {
        mimeType = 'image/gif';
      }

      formData.files.add(
        MapEntry(
          'image',
          await MultipartFile.fromFile(
            imageFile.path,
            filename: fileName,
            contentType: MediaType.parse(mimeType),
          ),
        ),
      );

      // Add metadata
      formData.fields.add(MapEntry('category', category));
      if (entityType != null) {
        formData.fields.add(MapEntry('entityType', entityType));
      }
      if (entityId != null) {
        formData.fields.add(MapEntry('entityId', entityId));
      }

      // Make the request
      final response = await _apiClient.request(
        method: HttpMethod.post,
        endPointUrl: '/images/upload',
        data: RequestData.formData(formData),
      );

      return ApiResponseModel<ImageUploadResponse>.fromJson(
        json: response.data,
        fromJsonT: (data) =>
            ImageUploadResponse.fromJson(data as Map<String, dynamic>),
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Replace image for a specific entity
  Future<ApiResponseModel<ImageUploadResponse>> replaceImage({
    required File imageFile,
    required String category,
    required String entityType,
    required String entityId,
  }) async {
    try {
      // Prepare form data
      final formData = FormData();

      // Add the image file
      final fileName = path.basename(imageFile.path);
      final fileExtension = path.extension(fileName).toLowerCase();

      // Determine MIME type
      String mimeType = 'image/jpeg';
      if (fileExtension == '.png') {
        mimeType = 'image/png';
      } else if (fileExtension == '.webp') {
        mimeType = 'image/webp';
      } else if (fileExtension == '.gif') {
        mimeType = 'image/gif';
      }

      formData.files.add(
        MapEntry(
          'image',
          await MultipartFile.fromFile(
            imageFile.path,
            filename: fileName,
            contentType: MediaType.parse(mimeType),
          ),
        ),
      );

      // Add metadata
      formData.fields.add(MapEntry('category', category));

      // Make the request
      final response = await _apiClient.request(
        method: HttpMethod.put,
        endPointUrl: '/images/$entityType/$entityId',
        data: RequestData.formData(formData),
      );

      return ApiResponseModel<ImageUploadResponse>.fromJson(
        json: response.data,
        fromJsonT: (data) =>
            ImageUploadResponse.fromJson(data as Map<String, dynamic>),
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Delete image for a specific entity
  Future<ApiResponseModel<void>> deleteImage({
    required String entityType,
    required String entityId,
  }) async {
    try {
      final response = await _apiClient.request(
        method: HttpMethod.delete,
        endPointUrl: '/images/$entityType/$entityId',
      );

      return ApiResponseModel<void>.fromJson(
        json: response.data,
        fromJsonT: (data) {},
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Get image info for a specific entity
  Future<ApiResponseModel<ImageInfoResponse>> getImageInfo({
    required String entityType,
    required String entityId,
  }) async {
    try {
      final response = await _apiClient.request(
        method: HttpMethod.get,
        endPointUrl: '/images/info/$entityType/$entityId',
      );

      return ApiResponseModel<ImageInfoResponse>.fromJson(
        json: response.data,
        fromJsonT: (data) =>
            ImageInfoResponse.fromJson(data as Map<String, dynamic>),
      );
    } catch (e) {
      rethrow;
    }
  }
}

/// Response model for image upload
class ImageUploadResponse {
  final String imagePath;
  final String imageUrl;
  final String originalName;
  final int size;
  final String mimeType;

  ImageUploadResponse({
    required this.imagePath,
    required this.imageUrl,
    required this.originalName,
    required this.size,
    required this.mimeType,
  });

  factory ImageUploadResponse.fromJson(Map<String, dynamic> json) {
    return ImageUploadResponse(
      imagePath: json['imagePath'] as String,
      imageUrl: json['imageUrl'] as String,
      originalName: json['originalName'] as String,
      size: json['size'] as int,
      mimeType: json['mimeType'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'imagePath': imagePath,
      'imageUrl': imageUrl,
      'originalName': originalName,
      'size': size,
      'mimeType': mimeType,
    };
  }
}

/// Response model for image info
class ImageInfoResponse {
  final String? imagePath;
  final String? imageUrl;
  final String? legacyImageUrl;
  final FileInfo? fileInfo;

  ImageInfoResponse({
    this.imagePath,
    this.imageUrl,
    this.legacyImageUrl,
    this.fileInfo,
  });

  factory ImageInfoResponse.fromJson(Map<String, dynamic> json) {
    return ImageInfoResponse(
      imagePath: json['imagePath'] as String?,
      imageUrl: json['imageUrl'] as String?,
      legacyImageUrl: json['legacyImageUrl'] as String?,
      fileInfo: json['fileInfo'] != null
          ? FileInfo.fromJson(json['fileInfo'] as Map<String, dynamic>)
          : null,
    );
  }
}

/// File information model
class FileInfo {
  final int size;
  final String mimeType;
  final bool exists;

  FileInfo({required this.size, required this.mimeType, required this.exists});

  factory FileInfo.fromJson(Map<String, dynamic> json) {
    return FileInfo(
      size: json['size'] as int,
      mimeType: json['mimeType'] as String,
      exists: json['exists'] as bool,
    );
  }
}
