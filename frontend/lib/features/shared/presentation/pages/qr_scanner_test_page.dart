import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/features/shared/presentation/widgets/qr_scanner_widget.dart';
import 'package:frontend/core/config/router/extension/navigation_extension.dart';

/// Test page to verify QR scanner functionality
class QRScannerTestPage extends StatelessWidget {
  const QRScannerTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.surfaceColor,
        elevation: 0,
        title: Text(
          'QR Scanner Test',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Safe<PERSON><PERSON>(
        child: Padding(
          padding: EdgeInsets.all(24.w),
          child: <PERSON>umn(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.qr_code_scanner,
                size: 80.sp,
                color: context.appColors.primaryColor,
              ),
              SizedBox(height: 32.h),
              Text(
                'QR Scanner Test',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 16.h),
              Text(
                'Test the QR code scanning functionality with proper permission handling and error management.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.subtextColor,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 48.h),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => _openQRScanner(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.appColors.primaryColor,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  icon: const Icon(Icons.qr_code_scanner),
                  label: Text(
                    'Open QR Scanner',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              SizedBox(height: 24.h),
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: context.appColors.surfaceColor,
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(color: context.appColors.dividerColor),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Features Tested:',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: context.appColors.textColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 12.h),
                    _buildFeatureItem(context, 'Camera permission handling'),
                    _buildFeatureItem(context, 'Error handling and fallbacks'),
                    _buildFeatureItem(context, 'QR code detection'),
                    _buildFeatureItem(context, 'Flash toggle functionality'),
                    _buildFeatureItem(context, 'Proper resource disposal'),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem(BuildContext context, String feature) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            size: 16.sp,
            color: context.appColors.successColor,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              feature,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _openQRScanner(BuildContext context) {
    context.pushRoute(
      QRScannerWidget(
        title: 'Test QR Scanner',
        instruction: 'Scan any QR code to test the functionality',
        onQRScanned: (qrCode) => _handleQRScanned(context, qrCode),
      ),
    );
  }

  void _handleQRScanned(BuildContext context, String qrCode) {
    Navigator.pop(context); // Close scanner
    
    // Show result dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: context.appColors.surfaceColor,
          title: Text(
            'QR Code Scanned!',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Scanned Content:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 8.h),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: context.appColors.backgroundColor,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: context.appColors.dividerColor),
                ),
                child: Text(
                  qrCode,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: context.appColors.subtextColor,
                    fontFamily: 'monospace',
                  ),
                ),
              ),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: context.appColors.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }
}
