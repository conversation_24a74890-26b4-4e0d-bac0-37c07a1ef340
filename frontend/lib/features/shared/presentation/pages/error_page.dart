import 'package:flutter/material.dart';
import '../../../../app.dart';
import '../../../../core/constants/app_assets.dart';

class ModernErrorPage extends StatelessWidget {
  final FlutterErrorDetails details;
  final VoidCallback? onRetry;
  final bool isProduction;

  const ModernErrorPage({
    super.key,
    required this.details,
    this.onRetry,
    this.isProduction = const bool.fromEnvironment('dart.vm.product'),
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final isDarkMode = theme.brightness == Brightness.dark;

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: isDarkMode
                  ? [
                      Colors.grey.shade900,
                      Colors.grey.shade800,
                      Colors.grey.shade700,
                    ]
                  : [Colors.blue.shade50, Colors.white, Colors.white],
            ),
          ),
          child: LayoutBuilder(
            builder: (context, constraints) => SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(minHeight: constraints.maxHeight),
                child: IntrinsicHeight(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          // AppAssets.errorImage,
                          AppAssets.appLogo,
                          width: 200,
                          height: 200,
                          color: isDarkMode ? Colors.white70 : null,
                        ),
                        const SizedBox(height: 32),
                        Text(
                          'Oops! Something went wrong',
                          style: textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: isDarkMode
                                ? Colors.white
                                : Colors.blue.shade800,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: isDarkMode
                                ? Colors.grey.shade800
                                : Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              if (!isProduction) ...[
                                SelectableText(
                                  details.exceptionAsString(),
                                  style: textTheme.bodyMedium?.copyWith(
                                    color: isDarkMode
                                        ? Colors.white70
                                        : Colors.grey.shade700,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                if (details.stack != null) ...[
                                  const SizedBox(height: 12),
                                  SelectableText(
                                    'Stack trace:',
                                    style: textTheme.labelSmall?.copyWith(
                                      color: isDarkMode
                                          ? Colors.white54
                                          : Colors.grey.shade500,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  SelectableText(
                                    details.stack.toString(),
                                    style: textTheme.labelSmall?.copyWith(
                                      color: isDarkMode
                                          ? Colors.white54
                                          : Colors.grey.shade500,
                                    ),
                                  ),
                                ],
                              ] else ...[
                                SelectableText(
                                  'We encountered an unexpected error. Our team has been notified.',
                                  style: textTheme.bodyMedium?.copyWith(
                                    color: isDarkMode
                                        ? Colors.white70
                                        : Colors.grey.shade700,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 12),
                                SelectableText(
                                  'Error Reference: ${_generateErrorId()}',
                                  style: textTheme.labelSmall?.copyWith(
                                    color: isDarkMode
                                        ? Colors.white54
                                        : Colors.grey.shade500,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                        const SizedBox(height: 32),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            FilledButton.icon(
                              onPressed: () {
                                if (onRetry != null) {
                                  onRetry!();
                                } else {
                                  Navigator.pushReplacement(
                                    context,
                                    MaterialPageRoute(
                                      builder: (_) => const MyApp(),
                                    ),
                                  );
                                }
                              },
                              icon: const Icon(Icons.refresh),
                              label: const Text('Try Again'),
                              style: FilledButton.styleFrom(
                                backgroundColor: isDarkMode
                                    ? Colors.blue.shade600
                                    : Colors.blue,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 16,
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            OutlinedButton.icon(
                              onPressed: () => _contactSupport(context),
                              icon: const Icon(Icons.help_outline),
                              label: const Text('Get Help'),
                              style: OutlinedButton.styleFrom(
                                side: BorderSide(
                                  color: isDarkMode
                                      ? Colors.blue.shade400
                                      : Colors.blue,
                                ),
                                foregroundColor: isDarkMode
                                    ? Colors.blue.shade300
                                    : Colors.blue,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 16,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  String _generateErrorId() => 'ERR-${DateTime.now().millisecondsSinceEpoch}';

  void _contactSupport(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Contact Support'),
        content: const Text(
          'Something broke unexpectedly.\<NAME_EMAIL>',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
