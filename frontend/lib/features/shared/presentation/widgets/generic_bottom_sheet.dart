import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_textfield.dart';

import '../../../../core/enums/layout_type.dart';
import '../../../../core/enums/selection_type.dart';
import 'custom_list_grid_view.dart';

class GenericBottomSheet<T> extends StatefulWidget {
  final String title;
  final List<T> items;
  final List<T>? initiallySelectedItems;
  final SelectionType selectionType;
  final String Function(T) getTitle;
  final String Function(T)? getSubtitle;
  final String Function(T)? getAvatarUrl;
  final Widget Function(T)? getLeadingWidget;
  final void Function(List<T> selectedItems) onSelectionChanged;
  final bool showSearch;
  final void Function()? onRefresh;

  const GenericBottomSheet({
    super.key,
    required this.title,
    required this.items,
    required this.getTitle,
    this.getSubtitle,
    this.getAvatarUrl,
    this.getLeadingWidget,
    this.initiallySelectedItems,
    required this.selectionType,
    required this.onSelectionChanged,
    this.showSearch = true,
    this.onRefresh,
  });

  @override
  State<GenericBottomSheet<T>> createState() => _GenericBottomSheetState<T>();
}

class _GenericBottomSheetState<T> extends State<GenericBottomSheet<T>> {
  late List<T> _filteredItems;
  late List<T> _selectedItems;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _selectedItems = widget.initiallySelectedItems ?? [];
    _filteredItems = List.from(widget.items);
    _searchController.addListener(_filterItems);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterItems() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredItems = widget.items.where((item) {
        final title = widget.getTitle(item).toLowerCase();
        final subtitle = widget.getSubtitle?.call(item).toLowerCase() ?? '';
        return title.contains(query) || subtitle.contains(query);
      }).toList();
    });
  }

  void _toggleSelection(T item) {
    setState(() {
      switch (widget.selectionType) {
        case SelectionType.multiSelection:
          if (_selectedItems.contains(item)) {
            _selectedItems.remove(item);
          } else {
            _selectedItems.add(item);
          }
          break;
        case SelectionType.singleSelection:
          _selectedItems = [item];
          break;
      }
      widget.onSelectionChanged(_selectedItems);
    });
  }

  Widget _buildListItem(T item) {
    final isSelected = _selectedItems.contains(item);

    return switch (widget.selectionType) {
      SelectionType.singleSelection => _buildSingleSelectItem(item, isSelected),
      SelectionType.multiSelection => _buildMultiSelectItem(item, isSelected),
    };
  }

  Widget _buildSingleSelectItem(T item, bool isSelected) {
    return ListTile(
      leading: _buildLeadingWidget(item),
      title: Text(widget.getTitle(item)),
      subtitle: widget.getSubtitle != null
          ? Text(widget.getSubtitle!(item))
          : null,
      trailing: isSelected
          ? Icon(
              Icons.radio_button_checked,
              color: context.appColors.primaryColor,
            )
          : const Icon(Icons.radio_button_unchecked),
      onTap: () => _toggleSelection(item),
    );
  }

  Widget _buildMultiSelectItem(T item, bool isSelected) {
    return ListTile(
      leading: _buildLeadingWidget(item),
      title: Text(widget.getTitle(item)),
      subtitle: widget.getSubtitle != null
          ? Text(widget.getSubtitle!(item))
          : null,
      trailing: Checkbox(
        value: isSelected,
        onChanged: (_) => _toggleSelection(item),
        activeColor: context.appColors.primaryColor,
        checkColor: context.appColors.cardColor,
      ),
      onTap: () => _toggleSelection(item),
    );
  }

  Widget? _buildLeadingWidget(T item) {
    if (widget.getLeadingWidget != null) {
      return widget.getLeadingWidget!(item);
    }

    if (widget.getAvatarUrl != null) {
      final avatarUrl = widget.getAvatarUrl!(item);
      if (avatarUrl.isNotEmpty) {
        return CircleAvatar(backgroundImage: NetworkImage(avatarUrl));
      }
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              widget.title,
              style: Theme.of(context).textTheme.titleLarge,
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: CustomTextField(
              controller: _searchController,
              hintText: 'Search...',
            ),
          ),
          const SizedBox(height: 8.0),
          Expanded(
            child: CustomListGridView<T>(
              isEmpty: _filteredItems.isEmpty,
              isLoading: false,
              items: _filteredItems,
              itemCount: _filteredItems.length,
              layoutType: LayoutType.listView,
              padding: EdgeInsets.all(16.w),
              itemBuilder: (context, item) => _buildListItem(item),
              emptyDataBuilder: () => Center(
                child: Text(
                  'No items found',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ),
              onRefresh: widget.onRefresh,
            ),
          ),
          if (widget.selectionType == SelectionType.multiSelection)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pop(context, _selectedItems);
                },
                child: const Text('Done'),
              ),
            ),
        ],
      ),
    );
  }
}
