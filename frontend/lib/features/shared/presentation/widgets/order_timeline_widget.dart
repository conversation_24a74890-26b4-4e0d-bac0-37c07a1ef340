import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/order_status.dart';
import 'package:frontend/features/order/domain/entities/order_entity.dart';
import 'package:intl/intl.dart';

enum TimelineStyle {
  compact, // Simple icons, minimal spacing
  detailed, // Full timeline with connecting lines
  minimal, // Just status indicators
}

enum TimelineStep {
  orderPlaced,
  paymentProcessed,
  orderConfirmed,
  agentAssigned,
  inTransit,
  delivered,
  cancelled,
  failed,
}

class TimelineStepData {
  final TimelineStep step;
  final String title;
  final IconData icon;
  final Color color;
  final DateTime? timestamp;
  final bool isCompleted;
  final bool isError;

  const TimelineStepData({
    required this.step,
    required this.title,
    required this.icon,
    required this.color,
    this.timestamp,
    required this.isCompleted,
    this.isError = false,
  });
}

class OrderTimelineWidget extends StatelessWidget {
  final OrderEntity order;
  final TimelineStyle style;
  final bool showTimestamps;
  final bool showOnlyCompletedSteps;

  const OrderTimelineWidget({
    super.key,
    required this.order,
    this.style = TimelineStyle.detailed,
    this.showTimestamps = true,
    this.showOnlyCompletedSteps = false,
  });

  @override
  Widget build(BuildContext context) {
    final steps = _generateTimelineSteps();
    final visibleSteps = showOnlyCompletedSteps
        ? steps.where((step) => step.isCompleted).toList()
        : steps;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Timeline',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          ...visibleSteps.asMap().entries.map((entry) {
            final index = entry.key;
            final step = entry.value;
            final isLast = index == visibleSteps.length - 1;

            return _buildTimelineItem(context, step, isLast);
          }),
        ],
      ),
    );
  }

  List<TimelineStepData> _generateTimelineSteps() {
    final steps = <TimelineStepData>[];

    // 1. Order Placed (always completed)
    steps.add(
      TimelineStepData(
        step: TimelineStep.orderPlaced,
        title: 'Order Placed',
        icon: Icons.shopping_cart,
        color: Colors.blue,
        timestamp: order.createdAt,
        isCompleted: true,
      ),
    );

    // 2. Payment Processed (if payment exists)
    if (order.payment.trim().isNotEmpty) {
      steps.add(
        TimelineStepData(
          step: TimelineStep.paymentProcessed,
          title: 'Payment Processed',
          icon: Icons.payment,
          color: Colors.green,
          timestamp: order
              .createdAt, // Use createdAt as payment is processed immediately
          isCompleted: order.status != OrderStatus.pending,
        ),
      );
    }

    // 3. Order Confirmed
    steps.add(
      TimelineStepData(
        step: TimelineStep.orderConfirmed,
        title: 'Order Confirmed',
        icon: Icons.check_circle,
        color: Colors.green,
        timestamp:
            order.confirmedAt ??
            (order.status != OrderStatus.pending ? order.updatedAt : null),
        isCompleted: order.status != OrderStatus.pending,
      ),
    );

    // 4. Agent Assigned (if agent exists)
    if (order.deliveryAgent != null ||
        order.status == OrderStatus.inTransit ||
        order.status == OrderStatus.delivered) {
      steps.add(
        TimelineStepData(
          step: TimelineStep.agentAssigned,
          title: 'Agent Assigned',
          icon: Icons.person,
          color: Colors.indigo,
          timestamp: order.assignedAt,
          isCompleted:
              order.status == OrderStatus.inTransit ||
              order.status == OrderStatus.delivered,
        ),
      );
    }

    // 5. In Transit
    if (order.status == OrderStatus.inTransit ||
        order.status == OrderStatus.delivered) {
      steps.add(
        TimelineStepData(
          step: TimelineStep.inTransit,
          title: 'In Transit',
          icon: Icons.local_shipping,
          color: Colors.indigo,
          timestamp: order.assignedAt ?? order.updatedAt,
          isCompleted:
              order.status == OrderStatus.inTransit ||
              order.status == OrderStatus.delivered,
        ),
      );
    }

    // 6. Delivered
    if (order.deliveredAt != null || order.status == OrderStatus.delivered) {
      steps.add(
        TimelineStepData(
          step: TimelineStep.delivered,
          title: 'Delivered',
          icon: Icons.check_circle_outline,
          color: Colors.green,
          timestamp: order.deliveredAt,
          isCompleted: order.status == OrderStatus.delivered,
        ),
      );
    }

    // 7. Cancelled (if cancelled)
    if (order.cancelledAt != null || order.status == OrderStatus.cancelled) {
      steps.add(
        TimelineStepData(
          step: TimelineStep.cancelled,
          title: 'Cancelled',
          icon: Icons.cancel,
          color: Colors.red,
          timestamp: order.cancelledAt,
          isCompleted: true,
          isError: true,
        ),
      );
    }

    // 8. Failed (if failed)
    if (order.failedAt != null || order.status == OrderStatus.failed) {
      steps.add(
        TimelineStepData(
          step: TimelineStep.failed,
          title: 'Failed',
          icon: Icons.error,
          color: Colors.red,
          timestamp: order.failedAt,
          isCompleted: true,
          isError: true,
        ),
      );
    }

    return steps;
  }

  Widget _buildTimelineItem(
    BuildContext context,
    TimelineStepData step,
    bool isLast,
  ) {
    switch (style) {
      case TimelineStyle.compact:
        return _buildCompactTimelineItem(context, step, isLast);
      case TimelineStyle.detailed:
        return _buildDetailedTimelineItem(context, step, isLast);
      case TimelineStyle.minimal:
        return _buildMinimalTimelineItem(context, step, isLast);
    }
  }

  Widget _buildDetailedTimelineItem(
    BuildContext context,
    TimelineStepData step,
    bool isLast,
  ) {
    return Padding(
      padding: EdgeInsets.only(bottom: isLast ? 0 : 16.h),
      child: Row(
        children: [
          // Timeline indicator with connecting line
          Column(
            children: [
              Container(
                width: 40.w,
                height: 40.w,
                decoration: BoxDecoration(
                  color: step.isCompleted
                      ? step.color
                      : step.color.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                  border: step.isCompleted
                      ? null
                      : Border.all(color: step.color, width: 2),
                ),
                child: Icon(
                  step.icon,
                  color: step.isCompleted ? Colors.white : step.color,
                  size: 20.sp,
                ),
              ),
              if (!isLast)
                Container(
                  width: 2.w,
                  height: 40.h,
                  color: step.isCompleted
                      ? step.color
                      : context.appColors.dividerColor,
                ),
            ],
          ),
          SizedBox(width: 16.w),
          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  step.title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (showTimestamps && step.timestamp != null) ...[
                  SizedBox(height: 4.h),
                  Text(
                    _formatTimestamp(step.timestamp!),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: context.appColors.subtextColor,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactTimelineItem(
    BuildContext context,
    TimelineStepData step,
    bool isLast,
  ) {
    return Padding(
      padding: EdgeInsets.only(bottom: isLast ? 0 : 12.h),
      child: Row(
        children: [
          Container(
            width: 32.w,
            height: 32.h,
            decoration: BoxDecoration(
              color: step.isCompleted ? step.color : Colors.grey[300],
              shape: BoxShape.circle,
            ),
            child: Icon(
              step.icon,
              color: step.isCompleted ? Colors.white : Colors.grey[600],
              size: 16.sp,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  step.title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (showTimestamps && step.timestamp != null)
                  Text(
                    _formatTimestamp(step.timestamp!),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: context.appColors.subtextColor,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMinimalTimelineItem(
    BuildContext context,
    TimelineStepData step,
    bool isLast,
  ) {
    return Padding(
      padding: EdgeInsets.only(bottom: isLast ? 0 : 8.h),
      child: Row(
        children: [
          Icon(
            step.icon,
            color: step.isCompleted ? step.color : Colors.grey[400],
            size: 20.sp,
          ),
          SizedBox(width: 8.w),
          Text(
            step.title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: step.isCompleted
                  ? context.appColors.textColor
                  : context.appColors.subtextColor,
            ),
          ),
        ],
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return DateFormat('MMM dd, yyyy HH:mm').format(timestamp);
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
