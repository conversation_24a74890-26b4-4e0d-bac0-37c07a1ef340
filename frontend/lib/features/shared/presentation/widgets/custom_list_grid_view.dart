import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/enums/layout_type.dart';
import 'package:frontend/core/utils/extensions/build_context_extensions.dart';

import '../../../../core/enums/animation_direction.dart';
import 'animations/animated_list_item.dart';
import 'custom_button.dart';
import 'custom_footer.dart';
import 'custom_loading_skeleton.dart';

class CustomListGridView<T> extends StatelessWidget {
  final List<T> items;
  final int? itemCount;
  final Widget Function(BuildContext context, T item) itemBuilder;
  final Widget? seperatedWidget;
  final bool isLoading;
  final bool isEmpty;
  final bool showFooter;
  final String emtypWidgetMessage;
  final int loadingItemCount;
  final double loadingItemHeight;
  final Widget Function()? footerBuilder;
  final Widget Function()? emptyDataBuilder;
  final LayoutType layoutType;
  final int? gridCrossAxisCount;
  final double childAspectRatio;
  final ScrollPhysics? physics;
  final Axis scrollDirection;
  final Duration animationDuration;
  final double? marginTopEptyWidget;
  final double? marginBottomEptyWidget;
  final void Function()? onRefresh;
  final EdgeInsetsGeometry? padding;
  final bool shrinkWrap;

  const CustomListGridView({
    super.key,
    required this.items,
    this.itemCount,
    this.childAspectRatio = 1.0,
    required this.itemBuilder,
    this.seperatedWidget,
    required this.isLoading,
    required this.isEmpty,
    this.showFooter = false,
    this.emtypWidgetMessage = 'No data available',
    this.loadingItemCount = 12,
    this.loadingItemHeight = 70,
    this.footerBuilder,
    this.emptyDataBuilder,
    required this.layoutType,
    this.gridCrossAxisCount,
    this.physics,
    this.scrollDirection = Axis.vertical,
    this.animationDuration = const Duration(milliseconds: 500),
    this.marginTopEptyWidget,
    this.marginBottomEptyWidget,
    required this.onRefresh,
    this.padding,
    this.shrinkWrap = true,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return CustomLoadingSkeleton(
        loadingItems: [
          LoadingSkeletonItem(
            scrollDirection: scrollDirection,
            loadingType: layoutType,
            itemCount: loadingItemCount,
            height: loadingItemHeight,
          ),
        ],
      );
    }

    if (isEmpty) {
      return emptyDataBuilder != null
          ? emptyDataBuilder!.call()
          : Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  emtypWidgetMessage,
                  style: context.textTheme.bodyLarge,
                  textAlign: TextAlign.center,
                ),
                if (onRefresh != null) ...[
                  SizedBox(height: 10.h),
                  CustomButton(
                    buttonText: 'Refresh',
                    width: 80,
                    onTap: onRefresh,
                  ),
                ],
              ],
            );
    }

    // Use itemCount when items is empty or null
    // int displayItemCount = items.isEmpty ? (itemCount ?? 0) : items.length;
    final int displayItemCount = (items.isEmpty && itemCount != null)
        ? itemCount!
        : (itemCount != null && itemCount! < items.length && items.isNotEmpty)
        ? itemCount!
        : items.length;
    // ListView implementation
    if (layoutType == LayoutType.listView) {
      return ListView.separated(
        itemCount: showFooter ? displayItemCount + 1 : displayItemCount,
        shrinkWrap: shrinkWrap,
        padding:
            padding ?? EdgeInsets.symmetric(horizontal: 20.w, vertical: 15.h),
        scrollDirection: scrollDirection,
        physics: physics ?? const AlwaysScrollableScrollPhysics(),
        separatorBuilder: (context, index) =>
            seperatedWidget ?? SizedBox(height: 15.h),
        itemBuilder: (context, index) {
          if (showFooter && index == displayItemCount) {
            return footerBuilder?.call() ?? const CustomFooter();
          }

          // Handle rendering of individual item
          final T? item = items.isNotEmpty ? items[index] : null;

          // Handle case when item is null (e.g., empty list), render placeholder
          return CustomAnimatedItem(
            index: index,
            itemCount: displayItemCount,
            duration: animationDuration,
            animationDirection: index % 2 == 0
                ? (scrollDirection == Axis.vertical
                      ? AnimationDirection.topToBottom
                      : AnimationDirection.leftToRight)
                : (scrollDirection == Axis.vertical
                      ? AnimationDirection.bottomToTop
                      : AnimationDirection.rightToLeft),
            child: itemBuilder(context, item ?? null as T), // Use `null` safely
          );
        },
      );
    }

    // GridView implementation
    if (layoutType == LayoutType.gridView) {
      return GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: gridCrossAxisCount ?? 3,
          crossAxisSpacing: 10.w,
          mainAxisSpacing: 10.h,
          childAspectRatio: childAspectRatio,
        ),
        itemCount: showFooter ? displayItemCount + 1 : displayItemCount,
        shrinkWrap: shrinkWrap,
        scrollDirection: scrollDirection,
        padding:
            padding ?? EdgeInsets.symmetric(horizontal: 20.w, vertical: 15.h),
        physics: physics ?? const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          if (showFooter &&
              index == displayItemCount &&
              (items.isNotEmpty || (itemCount != null && itemCount! > 0))) {
            return footerBuilder?.call() ??
                const CustomFooter(bottomPadding: 0, topPadding: 0);
          }
          final T? item = items.isNotEmpty ? items[index] : null;

          // Handle case when item is null (e.g., empty list), render placeholder
          return CustomAnimatedItem(
            index: index,
            duration: animationDuration,
            itemCount: displayItemCount,
            animationDirection: index % 2 == 0
                ? (scrollDirection == Axis.vertical
                      ? AnimationDirection.topToBottom
                      : AnimationDirection.leftToRight)
                : (scrollDirection == Axis.vertical
                      ? AnimationDirection.bottomToTop
                      : AnimationDirection.rightToLeft),
            child: itemBuilder(context, item ?? null as T), // Use `null` safely
          );
        },
      );
    }

    return const SizedBox.shrink(); // Return a fallback if no valid content type
  }
}

// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
// import 'package:skeletonizer/skeletonizer.dart';
// import 'package:hodan_hospital/core/enums/animation_direction.dart';
// import 'package:hodan_hospital/core/enums/loading_type.dart';
// import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
// import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_list_item.dart';
// import 'package:hodan_hospital/features/shared/presentation/widgets/custom_button.dart';
// import 'package:hodan_hospital/features/shared/presentation/widgets/custom_footer.dart';

// class CustomListGridView<T> extends StatelessWidget {
//   final List<T> items;
//   final int? itemCount;
//   final Widget Function(BuildContext, T) itemBuilder;
//   final Widget? separatorWidget;
//   final bool isLoading;
//   final bool isEmpty;
//   final bool showFooter;
//   final String emptyWidgetMessage;
//   final Widget Function()? footerBuilder;
//   final Widget Function()? emptyDataBuilder;
//   final LoadingType contentType;
//   final int? gridCrossAxisCount;
//   final double childAspectRatio;
//   final ScrollPhysics? physics;
//   final Axis scrollDirection;
//   final Duration animationDuration;
//   final void Function()? onRefresh;
//   final EdgeInsetsGeometry? padding;
//   final bool shrinkWrap;

//   const CustomListGridView({
//     super.key,
//     required this.items,
//     this.itemCount,
//     this.childAspectRatio = 1.0,
//     required this.itemBuilder,
//     this.separatorWidget,
//     required this.isLoading,
//     required this.isEmpty,
//     this.showFooter = true,
//     this.emptyWidgetMessage = 'No data available.',
//     this.footerBuilder,
//     this.emptyDataBuilder,
//     required this.contentType,
//     this.gridCrossAxisCount,
//     this.physics,
//     this.scrollDirection = Axis.vertical,
//     this.animationDuration = const Duration(milliseconds: 500),
//     this.onRefresh,
//     this.padding,
//     this.shrinkWrap = true,
//   });

//   @override
//   Widget build(BuildContext context) {
//     if (isEmpty || (!isLoading && items.isEmpty)) {
//       // Check if emptyDataBuilder is provided, if not use default empty widget
//       return emptyDataBuilder?.call() ?? _buildEmptyWidget(context);
//     }

//     return Skeletonizer(
//       enabled: isLoading, // Automatically handles skeletons when loading
//       effect: ShimmerEffect(
//         baseColor: context.appColors.shimmerBaseColor,
//         highlightColor: context.appColors.shimmerHighlightColor,
//         // child: _buildContent(context),
//       ),
//       child: _buildContent(context),
//     );
//   }

//   Widget _buildContent(BuildContext context) {
//     final displayItemCount = _calculateItemCount();

//     switch (contentType) {
//       case LoadingType.listView:
//         return ListView.separated(
//           itemCount: showFooter ? displayItemCount + 1 : displayItemCount,
//           shrinkWrap: shrinkWrap,
//           padding:
//               padding ?? EdgeInsets.symmetric(horizontal: 20.w, vertical: 15.h),
//           physics: physics ?? const AlwaysScrollableScrollPhysics(),
//           separatorBuilder: (_, __) =>
//               separatorWidget ?? SizedBox(height: 15.h),
//           itemBuilder: (context, index) {
//             if (showFooter && index == displayItemCount) {
//               return footerBuilder?.call() ?? const CustomFooter();
//             }
//             return _buildAnimatedItem(context, index);
//           },
//         );
//       case LoadingType.gridView:
//         return GridView.builder(
//           gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//             crossAxisCount: gridCrossAxisCount ?? 3,
//             crossAxisSpacing: 10.w,
//             mainAxisSpacing: 10.h,
//             childAspectRatio: childAspectRatio,
//           ),
//           itemCount: showFooter ? displayItemCount + 1 : displayItemCount,
//           shrinkWrap: shrinkWrap,
//           padding:
//               padding ?? EdgeInsets.symmetric(horizontal: 20.w, vertical: 15.h),
//           physics: physics ?? const NeverScrollableScrollPhysics(),
//           itemBuilder: (context, index) {
//             if (showFooter && index == displayItemCount) {
//               return footerBuilder?.call() ?? const CustomFooter();
//             }
//             return _buildAnimatedItem(context, index);
//           },
//         );
//       default:
//         return const SizedBox.shrink();
//     }
//   }

//   Widget _buildAnimatedItem(BuildContext context, int index) {
//     return CustomAnimatedItem(
//       itemCount: _calculateItemCount(),
//       index: index,
//       duration: animationDuration,
//       animationDirection: _getAnimationDirection(index),
//       child: itemBuilder(context, items[index]),
//     );
//   }

//   int _calculateItemCount() {
//     if (items.isNotEmpty) {
//       return itemCount != null && itemCount! < items.length
//           ? itemCount!
//           : items.length;
//     }
//     return itemCount ?? 0;
//   }

//   AnimationDirection _getAnimationDirection(int index) {
//     final isEven = index % 2 == 0;
//     return isEven
//         ? (scrollDirection == Axis.vertical
//             ? AnimationDirection.topToBottom
//             : AnimationDirection.leftToRight)
//         : (scrollDirection == Axis.vertical
//             ? AnimationDirection.bottomToTop
//             : AnimationDirection.rightToLeft);
//   }

//   Widget _buildEmptyWidget(BuildContext context) {
//     return Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           Text(
//             emptyWidgetMessage,
//             style: context.textTheme.bodyLarge,
//             textAlign: TextAlign.center,
//           ),
//           if (onRefresh != null) ...[
//             SizedBox(height: 10.h),
//             CustomButton(
//               buttonText: "Refresh",
//               width: 80,
//               onTap: onRefresh,
//             ),
//           ],
//         ],
//       ),
//     );
//   }
// }
