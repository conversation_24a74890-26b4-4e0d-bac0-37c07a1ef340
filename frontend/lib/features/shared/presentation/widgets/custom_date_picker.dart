// ignore_for_file: unreachable_switch_default

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:intl/intl.dart';

import '../../../../core/config/logger/app_logger.dart';
import '../../../../core/enums/date_validation_type.dart';

// ignore: must_be_immutable
class CustomDatePickerField extends StatefulWidget {
  final String labelText;
  final IconData? suffixIcon;
  final Function(String)? onDateSelected;
  final DateValidationType dateValidationType;
  final FormFieldValidator<String>? validator;
  final DateTime? initialDate;
  final bool showDefaultDate;
  String? initialValue;
  final VoidCallback? onDateCleared;
  final bool showCloseIcon;

  CustomDatePickerField({
    super.key,
    this.labelText = 'Select day*',
    this.suffixIcon,
    this.onDateSelected,
    this.dateValidationType = DateValidationType.none,
    this.validator,
    this.initialDate,
    this.onDateCleared,
    this.showCloseIcon = true,
    this.showDefaultDate = false,
    this.initialValue,
  });

  @override
  CustomDatePickerFieldState createState() => CustomDatePickerFieldState();
}

class CustomDatePickerFieldState extends State<CustomDatePickerField> {
  String _selectedDate = '';
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    // Set the initial selected date to today
    if (widget.showDefaultDate) {
      _selectedDate = DateFormat('yyyy-MM-dd').format(DateTime.now());
    }
    if (widget.initialDate != null) {
      _selectedDate = DateFormat('yyyy-MM-dd').format(widget.initialDate!);
    }
    if (widget.initialValue != null) {
      widget.initialValue = _selectedDate;
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    try {
      final DateTime now = DateTime.now();
      final DateTime today = DateTime(now.year, now.month, now.day);

      // Set the first and last dates dynamically based on the validation type
      DateTime firstDate;
      DateTime lastDate;
      DateTime initialDate = now;

      switch (widget.dateValidationType) {
        case DateValidationType.exact:
          _selectedDate = '';
          firstDate = today; // Only today’s date is allowed
          lastDate = today;
          break;
        case DateValidationType.before:
          _selectedDate = '';
          firstDate = DateTime(2000); // Some arbitrary old date
          lastDate = today; // Include today in the selectable dates
          break;
        case DateValidationType.after:
          _selectedDate = '';
          firstDate = today; // From today onwards
          lastDate = DateTime.utc(2040); // Some future date
          break;
        case DateValidationType.none:
        default:
          _selectedDate = '';
          firstDate = DateTime(2000); // No restriction on the start date
          lastDate = DateTime.utc(2040); // No restriction on the end date
          break;
      }

      // Adjust initialDate if it's earlier than firstDate
      initialDate = initialDate.isBefore(firstDate) ? firstDate : initialDate;

      final DateTime? pickedDate = await showDatePicker(
        context: context,
        firstDate: firstDate,
        lastDate: lastDate,
        initialDate: initialDate, // Use the corrected initial date
        builder: (BuildContext context, Widget? child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: false),
            child: child!,
          );
        },
      );

      if (pickedDate != null) {
        // Validation logic remains unchanged
        switch (widget.dateValidationType) {
          case DateValidationType.exact:
            if (pickedDate != today) {
              setState(() {
                _errorMessage = 'Please select today\'s date.';
              });
              return;
            }
            break;
          case DateValidationType.before:
            if (pickedDate.isAfter(today)) {
              setState(() {
                _errorMessage = 'Please select a date before today.';
              });
              return;
            }
            break;
          case DateValidationType.after:
            if (pickedDate.isBefore(today)) {
              setState(() {
                _errorMessage = 'Please select today or a future date.';
              });
              return;
            }
            break;
          case DateValidationType.none:
          default:
            break;
        }

        if (widget.initialValue != null) {
          widget.initialValue = _selectedDate;
        }

        _selectedDate = DateFormat('yyyy-MM-dd').format(pickedDate);
        _errorMessage = '';
        widget.onDateSelected?.call(_selectedDate);
        setState(() {});
      }
    } catch (error, stackTrace) {
      setState(() {
        _errorMessage =
            'An error occurred while selecting the date. Please try again.';
      });
      AppLogger().error(
        'Error selecting date',
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  void _onDateCleared() {
    setState(() {
      _selectedDate = '';
      _errorMessage = '';
      widget.onDateSelected?.call('');
      widget.onDateCleared?.call();
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final iconColor = isDarkMode ? Colors.white : Colors.black;
    final closeIconColor = context.appColors.errorColor;
    return GestureDetector(
      onTap: () => _selectDate(context),
      child: FormField<String>(
        validator: widget.validator, // <-- Call validator
        builder: (FormFieldState<String> state) {
          return InputDecorator(
            decoration: InputDecoration(
              labelText: widget.labelText,
              prefixIcon: Padding(
                padding: EdgeInsets.all(10.h), // Add some padding if needed
                // child: Image.asset(
                //   ImageConstants.calenderPng,
                //   height: 10.h, // Set height directly here
                //   width: 10.w, // Set width directly here
                //   fit: BoxFit.contain,
                //   color: iconColor,
                // ),
                child: Icon(Icons.calendar_today, color: iconColor, size: 20.w),
              ),
              suffixIcon: widget.showCloseIcon
                  ? InkWell(
                      onTap: _onDateCleared,
                      child: Padding(
                        padding: EdgeInsets.all(10.h),
                        // child: SvgPicture.asset(
                        //   ImageConstants.closeSVG,
                        //   height: 10.h,
                        //   width: 10.w,
                        //   fit: BoxFit.contain,
                        //   colorFilter:
                        //       ColorFilter.mode(closeIconColor, BlendMode.srcIn),
                        // ),
                        child: Icon(
                          Icons.close,
                          color: closeIconColor,
                          size: 20.w,
                        ),
                      ),
                    )
                  : null,
              fillColor: context.appColors.surfaceColor,
              filled: true,
              errorText: state.hasError
                  ? state.errorText
                  : _errorMessage.isNotEmpty
                  ? _errorMessage
                  : null, // <-- Display form error
            ),
            child: Text(
              _selectedDate.isEmpty ? 'Select Date' : _selectedDate,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          );
        },
      ),
    );
  }
}
