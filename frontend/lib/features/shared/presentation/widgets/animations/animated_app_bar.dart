// ignore_for_file: unreachable_switch_default

import 'package:flutter/material.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';

import '../../../../../core/enums/animation_direction.dart';

class AnimatedAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String? title;
  final AnimationDirection animationDirection;
  final Duration duration;
  final double appBarHeight;
  final List<Widget>? actions;
  final Widget? leading;
  final PreferredSizeWidget? bottom;
  final TextStyle? style;
  final Widget? titleWidget;
  final Color? backgroundColor;
  final Color? leadingIconColor;

  const AnimatedAppBar({
    super.key,
    this.title,
    this.actions,
    this.bottom,
    this.animationDirection = AnimationDirection.topToBottom,
    this.duration = const Duration(milliseconds: 800),
    this.appBarHeight = kToolbarHeight,
    this.leading,
    this.style,
    this.titleWidget,
    this.backgroundColor,
    this.leadingIconColor,
  });

  @override
  AnimatedAppBarState createState() => AnimatedAppBarState();

  @override
  Size get preferredSize => Size.fromHeight(appBarHeight);
}

class AnimatedAppBarState extends State<AnimatedAppBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(vsync: this, duration: widget.duration);
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    Offset beginOffset;

    // Determine the animation direction
    switch (widget.animationDirection) {
      case AnimationDirection.leftToRight:
        beginOffset = const Offset(-1.0, 0.0); // Slide in from left
        break;
      case AnimationDirection.rightToLeft:
        beginOffset = const Offset(1.0, 0.0); // Slide in from right
        break;
      case AnimationDirection.topToBottom:
        beginOffset = const Offset(0.0, -1.0); // Slide in from top
        break;
      case AnimationDirection.bottomToTop:
        beginOffset = const Offset(0.0, 1.0); // Slide in from bottom
        break;
      default:
        beginOffset = Offset.zero;
    }

    // Slide and fade animations
    final slideAnimation = Tween<Offset>(
      begin: beginOffset,
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));

    final fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeIn));

    return SlideTransition(
      position: slideAnimation,
      child: FadeTransition(
        opacity: fadeAnimation,
        child: AppBar(
          backgroundColor:
              widget.backgroundColor ?? context.appColors.primaryColor,
          leading:
              widget.leading ??
              (Navigator.canPop(context)
                  ? IconButton(
                      icon: const Icon(Icons.arrow_back),
                      onPressed: () => Navigator.pop(context),
                      color:
                          widget.leadingIconColor ??
                          context.appColors.whiteColor,
                    )
                  : null),
          title:
              widget.titleWidget ??
              (widget.title != null
                  ? Text(
                      widget.title!,
                      style:
                          widget.style ??
                          textTheme.titleMedium?.copyWith(
                            color: context.appColors.whiteColor,
                          ),
                    )
                  : null),
          actions: widget.actions,
          bottom: widget.bottom,
          actionsIconTheme: const IconThemeData(color: Colors.white),
        ),
      ),
    );
  }
}
