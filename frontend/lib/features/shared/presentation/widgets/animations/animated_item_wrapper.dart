// ignore_for_file: unreachable_switch_default

import 'package:flutter/material.dart';

import '../../../../../core/enums/animation_direction.dart';

class AnimatedItemWrapper extends StatefulWidget {
  final Widget? child;
  final Duration delay;
  final AnimationDirection animationDirection;

  const AnimatedItemWrapper({
    super.key,
    required this.child,
    this.delay = const Duration(milliseconds: 300),
    this.animationDirection = AnimationDirection.bottomToTop,
  });

  @override
  AnimatedItemWrapperState createState() => AnimatedItemWrapperState();
}

class AnimatedItemWrapperState extends State<AnimatedItemWrapper>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
      // duration: Duration.zero,
    );

    // Start the animation after the specified delay
    Future.delayed(widget.delay, () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Offset beginOffset;

    // Determine the animation direction
    switch (widget.animationDirection) {
      case AnimationDirection.leftToRight:
        beginOffset = const Offset(-1.0, 0.0);
        break;
      case AnimationDirection.rightToLeft:
        beginOffset = const Offset(1.0, 0.0);
        break;
      case AnimationDirection.topToBottom:
        beginOffset = const Offset(0.0, -1.0);
        break;
      case AnimationDirection.bottomToTop:
        beginOffset = const Offset(0.0, 1.0);
        break;
      default:
        beginOffset = Offset.zero;
    }

    // Create animations for sliding and fading
    final slideAnimation = Tween<Offset>(
      begin: beginOffset,
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));

    final fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeIn));

    return SlideTransition(
      position: slideAnimation,
      child: FadeTransition(opacity: fadeAnimation, child: widget.child),
    );
  }
}
