// ignore_for_file: unreachable_switch_default

import 'package:flutter/material.dart';

import '../../../../../core/enums/animation_direction.dart';

class CustomAnimatedItem extends StatefulWidget {
  final int index;
  final int itemCount;
  final Widget child;
  final AnimationDirection animationDirection;
  final Duration? duration;

  const CustomAnimatedItem({
    super.key,
    required this.index,
    required this.child,
    required this.itemCount,
    this.animationDirection = AnimationDirection.rightToLeft,
    this.duration = const Duration(milliseconds: 400),
  });

  @override
  CustomAnimatedItemState createState() => CustomAnimatedItemState();
}

class CustomAnimatedItemState extends State<CustomAnimatedItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the animation controller
    _controller = AnimationController(vsync: this, duration: widget.duration)
      ..forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Offset beginOffset;

    // Determine the animation direction
    switch (widget.animationDirection) {
      case AnimationDirection.leftToRight:
        beginOffset = const Offset(-1.0, 0.0);
        break;
      case AnimationDirection.rightToLeft:
        beginOffset = const Offset(1.0, 0.0);
        break;
      case AnimationDirection.topToBottom:
        beginOffset = const Offset(0.0, -1.0);
        break;
      case AnimationDirection.bottomToTop:
        beginOffset = const Offset(0.0, 1.0);
        break;
      default:
        beginOffset = Offset.zero;
    }

    // Slide animation
    final slideAnimation = Tween<Offset>(begin: beginOffset, end: Offset.zero)
        .animate(
          CurvedAnimation(
            parent: _controller,
            curve: Interval(
              (1 / widget.itemCount) * widget.index,
              1.0,
              curve: Curves.easeOut,
            ),
          ),
        );

    // Fade animation
    final fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Interval(
          (1 / widget.itemCount) * widget.index,
          1.0,
          curve: Curves.easeIn,
        ),
      ),
    );

    return SlideTransition(
      position: slideAnimation,
      child: FadeTransition(opacity: fadeAnimation, child: widget.child),
    );
  }
}
