// ignore_for_file: unreachable_switch_default

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';

import '../../../../core/enums/shape_type.dart';

class CustomContainer extends StatelessWidget {
  final Widget? child;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final BorderRadius? customBorderRadius;
  final double borderRadius;
  final List<BoxShadow>? boxShadow;
  final BoxBorder? border;
  final ShapeType shapeType;
  final double? width;
  final double? height;
  final Color? color;
  final BoxConstraints? constraints;
  final Decoration? foregroundDecoration;
  final Decoration? decoration;
  final AlignmentGeometry? alignment;
  final Clip clipBehavior;
  final Matrix4? transform;
  final AlignmentGeometry? transformAlignment;

  const CustomContainer({
    super.key,
    this.child,
    this.padding = EdgeInsets.zero,
    this.margin = EdgeInsets.zero,
    this.customBorderRadius,
    this.borderRadius = 16,
    this.boxShadow,
    this.border,
    this.shapeType = ShapeType.rectangle,
    this.width,
    this.height,
    this.color,
    this.constraints,
    this.foregroundDecoration,
    this.decoration,
    this.alignment,
    this.clipBehavior = Clip.none,
    this.transform,
    this.transformAlignment,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      padding: padding,
      height: height?.h,
      width: width?.w ?? MediaQuery.of(context).size.width,
      constraints: constraints,
      foregroundDecoration: foregroundDecoration,
      alignment: alignment,
      clipBehavior: clipBehavior,
      transform: transform,
      transformAlignment: transformAlignment,
      decoration: decoration ??
          BoxDecoration(
            color: color ?? context.appColors.cardColor,
            borderRadius:
                _getBorderRadius(), // Set borderRadius based on shapeType
            shape: _getShape(), // Set shape based on shapeType
            boxShadow: boxShadow,
            border: border,
          ),
      child: child,
    );
  }

  // Return border radius based on the shapeType
  BorderRadius? _getBorderRadius() {
    if (shapeType == ShapeType.roundedRectangle ||
        shapeType == ShapeType.rectangle) {
      return customBorderRadius ?? BorderRadius.circular(borderRadius.r);
    }
    return null; // No border radius for circle or other shapes
  }

  // Return shape based on ShapeType enum
  BoxShape _getShape() {
    switch (shapeType) {
      case ShapeType.circular:
        return BoxShape.circle;
      case ShapeType.roundedRectangle:
      case ShapeType.rectangle:
      default:
        return BoxShape.rectangle;
    }
  }
}
