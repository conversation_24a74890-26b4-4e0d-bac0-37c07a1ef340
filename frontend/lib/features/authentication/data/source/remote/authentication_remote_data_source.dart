import 'package:fpdart/fpdart.dart';
import 'package:frontend/core/constants/api_end_points.dart';
import 'package:frontend/core/enums/http_method.dart';
import 'package:frontend/features/authentication/data/models/user_model.dart';

import '../../../../../core/errors/app_failure.dart';
import '../../../../../core/errors/http_error_handler.dart';
import '../../../../../core/network/api_client/dio_api_client.dart';
import '../../../../../core/utils/helpers/request_data.dart';

abstract class AuthenticationRemoteDataSource {
  /// Login user with phone and password
  FutureEitherFailOr<UserModel> login({required String phone});

  /// Get current authenticated user
  FutureEitherFailOr<UserModel> getCurrentUser();

  FutureEitherFailOr<String> resendOtp({required String mobile});

  FutureEitherFailOr<UserModel> verifyOtp({
    required String mobile,
    required String otp,
  });
}

class AuthenticationRemoteDataSourceImpl
    implements AuthenticationRemoteDataSource {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;

  const AuthenticationRemoteDataSourceImpl({
    required this.dioApiClient,
    required this.httpErrorHandler,
  });

  @override
  FutureEitherFailOr<UserModel> getCurrentUser() async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => UserModel.fromJson(json as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getCurrentUser,
      ),
    );

    return response.fold((failure) => left(failure), (apiResponse) {
      final user = apiResponse.getNonNullableData();
      return right(user);
    });
  }

  @override
  FutureEitherFailOr<UserModel> login({
    required String phone,
    // required String password,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => UserModel.fromJson(json as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.login,
        receiveTimeout: const Duration(seconds: 50),
        data: RequestData.json({
          'phone': phone,
          // 'password': password,
        }),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final user = apiResponse.getNonNullableData();
      return right(user);
    });
  }

  @override
  FutureEitherFailOr<String> resendOtp({required String mobile}) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.resendOtp,
        data: RequestData.json({'phone': mobile}),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final message = apiResponse.apiMessage;
      return right(message);
    });
  }

  @override
  FutureEitherFailOr<UserModel> verifyOtp({
    required String mobile,
    required String otp,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => UserModel.fromJson(json as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.verifyOtp,
        data: RequestData.json({'phone': mobile, 'otp': otp}),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final user = apiResponse.getNonNullableData();
      return right(user);
    });
  }
}
