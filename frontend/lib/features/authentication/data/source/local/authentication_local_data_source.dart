import 'package:fpdart/fpdart.dart';

import '../../../../../core/constants/local_storage_key_constants.dart';
import '../../../../../core/enums/app_start_state.dart';
import '../../../../../core/enums/cache_failure_type.dart';
import '../../../../../core/errors/app_failure.dart';
import '../../../../shared/data/source/local/flutter_secure_storage_services.dart';
// import '../../models/user_model.dart';

abstract class AuthenticationLocalDataSource {
  /// Store authentication token
  Future<void> storeToken(String token);

  /// Get stored authentication token
  // Future<String?> getStoredToken();

  /// Clear stored authentication token
  Future<void> clearToken();

  // /// Store user data
  // Future<void> storeUserData(UserModel user);

  // /// Get stored user data
  // Future<UserModel?> getStoredUserData();

  // /// Clear stored user data
  // Future<void> clearUserData();

  /// Check if user is authenticated (has valid token)
  FutureEitherFailOr<AppStartState> isAuthenticated();

  FutureEitherFailOr<void> completeOnboarding();
}

class AuthenticationLocalDataSourceImpl
    implements AuthenticationLocalDataSource {
  // final SharedPreferences sharedPreferences;
  final FlutterSecureStorageServices flutterSecureStorageServices;

  const AuthenticationLocalDataSourceImpl({
    required this.flutterSecureStorageServices,
  });

  static const String _tokenKey = LocalStorageKeyConstants.tokenKey;
  // static const String _userKey = LocalStorageKeyConstants.userDataKey;

  @override
  Future<void> storeToken(String token) async {
    try {
      await flutterSecureStorageServices.storeData(
        key: _tokenKey,
        value: token,
      );
    } catch (e) {
      throw CacheFailure(
        message: 'Failed to store authentication token',
        stackTrace: StackTrace.current,
        failureType: CacheFailureType.writeError,
      );
    }
  }

  // @override
  Future<String?> getStoredToken() async {
    try {
      return await flutterSecureStorageServices.readData(key: _tokenKey);
    } catch (e) {
      throw CacheFailure(
        message: 'Failed to retrieve authentication token',
        stackTrace: StackTrace.current,
        failureType: CacheFailureType.readError,
      );
    }
  }

  @override
  Future<void> clearToken() async {
    try {
      await flutterSecureStorageServices.removeData(key: _tokenKey);
    } catch (e) {
      throw CacheFailure(
        message: 'Failed to clear authentication token',
        stackTrace: StackTrace.current,
        failureType: CacheFailureType.deleteError,
      );
    }
  }

  // @override
  // Future<void> storeUserData(UserModel user) async {
  //   try {
  //     final userJson = jsonEncode(user.toJson());
  //     await sharedPreferences.setString(_userKey, userJson);
  //   } catch (e) {
  //     throw CacheFailure(
  //       message: 'Failed to store user data',
  //       stackTrace: StackTrace.current,
  //       failureType: CacheFailureType.writeError,
  //     );
  //   }
  // }

  // @override
  // Future<UserModel?> getStoredUserData() async {
  //   try {
  //     final userJson = sharedPreferences.getString(_userKey);
  //     if (userJson != null) {
  //       final userMap = jsonDecode(userJson) as Map<String, dynamic>;
  //       return UserModel.fromJson(userMap);
  //     }
  //     return null;
  //   } catch (e) {
  //     throw CacheFailure(
  //       message: 'Failed to retrieve user data',
  //       stackTrace: StackTrace.current,
  //       failureType: CacheFailureType.readError,
  //     );
  //   }
  // }

  // @override
  // Future<void> clearUserData() async {
  //   try {
  //     await sharedPreferences.remove(_userKey);
  //   } catch (e) {
  //     throw CacheFailure(
  //       message: 'Failed to clear user data',
  //       stackTrace: StackTrace.current,
  //       failureType: CacheFailureType.deleteError,
  //     );
  //   }
  // }

  @override
  FutureEitherFailOr<AppStartState> isAuthenticated() async {
    try {
      final onboarding = await flutterSecureStorageServices.readData(
        key: LocalStorageKeyConstants.onboardingCompletedKey,
      );
      if (onboarding == null || onboarding.isEmpty) {
        return right(AppStartState.onboardingRequired);
      }
      final token = await flutterSecureStorageServices.readData(
        key: LocalStorageKeyConstants.tokenKey,
      );
      if (token == null || token.trim().isEmpty) {
        return right(AppStartState.loggedOut);
      }
      return right(AppStartState.loggedIn);
    } catch (error) {
      return right(AppStartState.loggedOut);
    }
  }

  @override
  FutureEitherFailOr<void> completeOnboarding() async {
    try {
      await flutterSecureStorageServices.storeData(
        key: LocalStorageKeyConstants.onboardingCompletedKey,
        value: 'true',
      );
      return right(null);
    } catch (e, stackTrace) {
      return left(
        CacheFailure(
          message: 'Failed to complete onboarding: ${e.toString()}',
          failureType: CacheFailureType.readError,
          stackTrace: stackTrace,
        ),
      );
    }
  }
}
