import 'package:fpdart/fpdart.dart';
import 'package:frontend/core/enums/app_start_state.dart';
import 'package:frontend/core/errors/app_failure.dart';
import 'package:frontend/features/authentication/data/mappers/authentication_mapper.dart';
import 'package:frontend/features/authentication/data/source/remote/authentication_remote_data_source.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';
import 'package:frontend/features/authentication/domain/repository/authentication_repository.dart';

import '../source/local/authentication_local_data_source.dart';

class AuthenticationRepositoryImpl implements AuthenticationRepository {
  final AuthenticationRemoteDataSource authenticationRemoteDataSource;
  final AuthenticationLocalDataSource authenticationLocalDataSource;

  const AuthenticationRepositoryImpl({
    required this.authenticationRemoteDataSource,
    required this.authenticationLocalDataSource,
  });

  @override
  FutureEitherFailOr<UserEntity> getCurrentUser() async {
    final response = await authenticationRemoteDataSource.getCurrentUser();

    return response.fold((failure) => left(failure), (userModel) {
      final userEntity = AuthenticationMapper.toUserEntity(userModel);
      return right(userEntity);
    });
  }

  @override
  FutureEitherFailOr<AppStartState> isAuthenticated() async {
    final response = await authenticationLocalDataSource.isAuthenticated();

    return response;
  }

  @override
  FutureEitherFailOr<UserEntity> login({
    required String phone,
    // required String password,
  }) async {
    final response = await authenticationRemoteDataSource.login(
      phone: phone,
      // password: password,
    );

    return response.fold((failure) => left(failure), (userModel) async {
      final userEntity = AuthenticationMapper.toUserEntity(userModel);
      // await authenticationLocalDataSource.storeToken(userModel.token);
      return right(userEntity);
    });
  }

  @override
  FutureEitherFailOr<void> logout() async {
    try {
      await authenticationLocalDataSource.clearToken();
      return right(null);
    } on AppFailure catch (e) {
      return left(e);
    } catch (e) {
      return left(
        UnexpectedFailure(message: 'Failed to logout : ${e.toString()}'),
      );
    }
  }

  @override
  FutureEitherFailOr<void> completeOnboarding() async {
    final response = await authenticationLocalDataSource.completeOnboarding();

    return response;
  }

  @override
  FutureEitherFailOr<String> resendOtp({required String mobile}) async {
    final response = await authenticationRemoteDataSource.resendOtp(
      mobile: mobile,
    );

    return response.fold((failure) => left(failure), (message) {
      return right(message);
    });
  }

  @override
  FutureEitherFailOr<UserEntity> verifyOtp({
    required String mobile,
    required String otp,
  }) async {
    final response = await authenticationRemoteDataSource.verifyOtp(
      mobile: mobile,
      otp: otp,
    );

    return response.fold((failure) => left(failure), (userModel) async {
      final userEntity = AuthenticationMapper.toUserEntity(userModel);
      await authenticationLocalDataSource.storeToken(userModel.token);
      return right(userEntity);
    });
  }
}
