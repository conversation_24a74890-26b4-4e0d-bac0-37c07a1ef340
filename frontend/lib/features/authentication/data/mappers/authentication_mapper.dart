import '../../domain/entities/user_entity.dart';
import '../models/user_model.dart';

class AuthenticationMapper {
  static UserModel toUserModel(UserEntity user) {
    return UserModel(
      id: user.id,
      phone: user.phone,
      email: user.email,
      role: user.role,
      username: user.username,
      addresses: user.addresses,
      isActive: user.isActive,
      agentMetadata: user.agentMetadata,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      token: '',
    );
  }

  static UserEntity toUserEntity(UserModel user) {
    return UserEntity(
      id: user.id,
      phone: user.phone,
      email: user.email,
      role: user.role,
      username: user.username,
      addresses: user.addresses,
      isActive: user.isActive,
      agentMetadata: user.agentMetadata,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    );
  }

  static List<UserModel> toUserModelList(List<UserEntity> users) {
    return users.map((user) => toUserModel(user)).toList();
  }

  static List<UserEntity> toUserEntityList(List<UserModel> users) {
    return users.map((user) => toUserEntity(user)).toList();
  }
}
