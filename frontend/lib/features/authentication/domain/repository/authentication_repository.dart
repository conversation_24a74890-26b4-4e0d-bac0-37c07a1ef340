import 'package:frontend/core/enums/app_start_state.dart';

import '../../../../core/errors/app_failure.dart';
import '../entities/user_entity.dart';

abstract class AuthenticationRepository {
  /// Login user with phone and password
  FutureEitherFailOr<UserEntity> login({required String phone});

  /// Get current authenticated user
  FutureEitherFailOr<UserEntity> getCurrentUser();

  /// Logout user (clear local storage)
  FutureEitherFailOr<void> logout();

  /// Check if user is authenticated (has valid token)
  FutureEitherFailOr<AppStartState> isAuthenticated();

  /// Complete onboarding
  FutureEitherFailOr<void> completeOnboarding();

  /// Send OTP to user's phone
  FutureEitherFailOr<String> resendOtp({required String mobile});

  /// Verify OTP
  FutureEitherFailOr<UserEntity> verifyOtp({
    required String mobile,
    required String otp,
  });
}
