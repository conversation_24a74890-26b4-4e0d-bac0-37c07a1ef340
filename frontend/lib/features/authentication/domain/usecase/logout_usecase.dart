import 'package:fpdart/fpdart.dart';

import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../repository/authentication_repository.dart';

class LogoutUseCase implements UseCase<void, NoParams> {
  final AuthenticationRepository repository;

  const LogoutUseCase({required this.repository});

  @override
  Future<Either<AppFailure, void>> call({required NoParams params}) async {
    return await repository.logout();
  }
}
