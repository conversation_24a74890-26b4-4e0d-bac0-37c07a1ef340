import 'package:fpdart/fpdart.dart';

import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../entities/user_entity.dart';
import '../repository/authentication_repository.dart';

class GetCurrentUserUseCase implements UseCase<UserEntity, NoParams> {
  final AuthenticationRepository repository;

  const GetCurrentUserUseCase({required this.repository});

  @override
  Future<Either<AppFailure, UserEntity>> call({required NoParams params}) async {
    return await repository.getCurrentUser();
  }
}
