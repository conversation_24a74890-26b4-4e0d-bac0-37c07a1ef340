import 'package:fpdart/fpdart.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';

import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/login_params.dart';
import '../repository/authentication_repository.dart';

class LoginUseCase implements UseCase<UserEntity, LoginParams> {
  final AuthenticationRepository repository;

  const LoginUseCase({required this.repository});

  @override
  Future<Either<AppFailure, UserEntity>> call({
    required LoginParams params,
  }) async {
    return await repository.login(phone: params.phone);
  }
}
