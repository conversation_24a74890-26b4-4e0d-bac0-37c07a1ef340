import 'package:frontend/features/authentication/domain/repository/authentication_repository.dart';

import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/send_otp_params.dart';

// ✅ Concrete Use Case for Sending OTP
class ResendOtpUseCase implements UseCase<String, SendOtpParams> {
  final AuthenticationRepository repository;

  ResendOtpUseCase({required this.repository});

  @override
  FutureEitherFailOr<String> call({required SendOtpParams params}) {
    return repository.resendOtp(mobile: params.mobileNumber);
  }
}
