import 'package:frontend/features/authentication/domain/entities/user_entity.dart';
import 'package:frontend/features/authentication/domain/repository/authentication_repository.dart';

import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/verify_otp_params.dart';

// ✅ Concrete Use Case for Verifying OTP
class VerifyOtpUseCase implements UseCase<UserEntity, VerifyOtpParams> {
  final AuthenticationRepository repository;

  VerifyOtpUseCase({required this.repository});

  @override
  FutureEitherFailOr<UserEntity> call({required VerifyOtpParams params}) {
    return repository.verifyOtp(otp: params.otp, mobile: params.mobileNumber);
  }
}
