// ✅ Concrete Use Case for Completting onboarding
import 'package:frontend/features/authentication/domain/repository/authentication_repository.dart';

import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';

class CompleteOnboardingUseCase implements UseCase<void, NoParams> {
  final AuthenticationRepository repository;

  CompleteOnboardingUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({required NoParams params}) {
    return repository.completeOnboarding();
  }
}
