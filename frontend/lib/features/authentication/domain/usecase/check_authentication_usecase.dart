import 'package:fpdart/fpdart.dart';
import 'package:frontend/core/enums/app_start_state.dart';

import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../repository/authentication_repository.dart';

class CheckAuthenticationUseCase implements UseCase<AppStartState, NoParams> {
  final AuthenticationRepository repository;

  const CheckAuthenticationUseCase({required this.repository});

  @override
  Future<Either<AppFailure, AppStartState>> call({
    required NoParams params,
  }) async {
    return await repository.isAuthenticated();
  }
}
