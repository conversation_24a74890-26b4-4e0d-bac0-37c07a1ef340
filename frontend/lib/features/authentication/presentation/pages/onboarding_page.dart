import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/router/extension/navigation_extension.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_button.dart';

import '../../../../core/enums/button_state.dart';
import '../../../../core/utils/helpers/snack_bar_helper.dart';
import 'login_page.dart';

class OnboardingPage extends StatefulWidget {
  const OnboardingPage({super.key});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<OnboardingData> _onboardingData = [
    OnboardingData(
      title: 'Fast Gas Delivery',
      description:
          'Get your gas cylinders delivered to your doorstep quickly and safely. No more waiting in long queues.',
      icon: Icons.local_shipping_outlined,
      color: const Color(0xFF4CAF50),
    ),
    OnboardingData(
      title: 'Easy Ordering',
      description:
          'Order gas cylinders with just a few taps. Track your delivery in real-time and get notified when it arrives.',
      icon: Icons.smartphone_outlined,
      color: const Color(0xFF2196F3),
    ),
    OnboardingData(
      title: 'Safe & Reliable',
      description:
          'All our gas cylinders are quality checked and delivered by trained professionals for your safety.',
      icon: Icons.verified_user_outlined,
      color: const Color(0xFFFF9800),
    ),
    OnboardingData(
      title: 'Multiple Payment Options',
      description:
          'Pay with cash on delivery, mobile money, or card. Choose what works best for you.',
      icon: Icons.payment_outlined,
      color: const Color(0xFF9C27B0),
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _onboardingData.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _navigateToLogin();
    }
  }

  void _skipOnboarding() {
    _navigateToLogin();
  }

  void _navigateToLogin() {
    context.authenticationBloc.add(CompleteOnBoardingEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthenticationBloc, AuthenticationState>(
      listener: (context, state) {
        if (state is OnboardingCompleted) {
          context.pushAndRemoveUntilRoute(const LoginPage());
        }
        if (state is AuthenticationFailure) {
          final message = state.appFailure.getErrorMessage();
          SnackBarHelper.showErrorSnackBar(context, message: message);
        }
      },
      child: Scaffold(
        backgroundColor: context.appColors.backgroundColor,
        body: SafeArea(
          child: Column(
            children: [
              // Skip Button
              _buildSkipButton(context),

              // PageView
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      _currentPage = index;
                    });
                  },
                  itemCount: _onboardingData.length,
                  itemBuilder: (context, index) {
                    return _buildOnboardingPage(
                      context,
                      _onboardingData[index],
                    );
                  },
                ),
              ),

              // Bottom Section
              _buildBottomSection(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSkipButton(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
      child: Align(
        alignment: Alignment.centerRight,
        child: TextButton(
          onPressed: _skipOnboarding,
          child: Text(
            'Skip',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: context.appColors.subtextColor,
              fontWeight: FontWeight.w900,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOnboardingPage(BuildContext context, OnboardingData data) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 32.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon/Illustration
          Container(
            width: 120.w,
            height: 120.h,
            decoration: BoxDecoration(
              color: data.color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(60.r),
            ),
            child: Icon(data.icon, size: 60.sp, color: data.color),
          ),

          SizedBox(height: 48.h),

          // Title
          Text(
            data.title,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.headlineLarge?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),

          SizedBox(height: 24.h),

          // Description
          Text(
            data.description,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: context.appColors.subtextColor,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomSection(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 32.h),
      child: Column(
        children: [
          // Page Indicators
          _buildPageIndicators(context),

          SizedBox(height: 32.h),

          // Next/Get Started Button
          BlocBuilder<AuthenticationBloc, AuthenticationState>(
            buildWhen: (previous, current) {
              return current is AuthenticationLoading ||
                  current is AuthenticationFailure ||
                  current is OnboardingCompleted;
            },
            builder: (context, state) {
              final isLoading = state is AuthenticationLoading;
              final buttonState = isLoading
                  ? ButtonState.loading
                  : ButtonState.normal;
              return CustomButton(
                onTap: _nextPage,
                buttonText: _currentPage == _onboardingData.length - 1
                    ? 'Get Started'
                    : 'Next',
                height: 40.h,
                buttonState: buttonState,
                width: double.infinity,
                backgroundColor: context.appColors.primaryColor,
                textStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPageIndicators(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        _onboardingData.length,
        (index) => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          width: _currentPage == index ? 24.w : 8.w,
          height: 8.h,
          decoration: BoxDecoration(
            color: _currentPage == index
                ? context.appColors.primaryColor
                : context.appColors.dividerColor,
            borderRadius: BorderRadius.circular(4.r),
          ),
        ),
      ),
    );
  }
}

class OnboardingData {
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  OnboardingData({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}
