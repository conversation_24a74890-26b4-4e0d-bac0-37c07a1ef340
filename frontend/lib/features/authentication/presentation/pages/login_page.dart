import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/logger/app_logger.dart';
import 'package:frontend/core/config/router/extension/navigation_extension.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/constants/app_assets.dart';
import 'package:frontend/core/enums/button_state.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/core/utils/helpers/form_validation_helper.dart';
import 'package:frontend/core/utils/helpers/snack_bar_helper.dart';
import 'package:frontend/core/utils/helpers/somali_phone_number_formatter.dart';
import 'package:frontend/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:frontend/features/authentication/presentation/pages/otp_verification_page.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_button.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_textfield.dart';

import '../../../main/presentation/pages/main_page.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  void _handleLogin() {
    if (_formKey.currentState?.validate() ?? false) {
      final phone = '+252${_phoneController.text.trim()}';
      context.authenticationBloc.add(LoginEvent(mobileNumber: phone));
    }
  }

  void clearFields() {
    // Clear Fields
    _formKey.currentState?.reset();
    _phoneController.clear();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthenticationBloc, AuthenticationState>(
      listener: (context, state) {
        if (state is Authenticated) {
          clearFields();

          context.pushAndRemoveUntilRoute(const MainPage());
        }
        if (state is LoginSuccess) {
          clearFields();

          context.pushRoute(
            OtpVerificationPage(
              // phoneNumber: '+252${_phoneController.text.trim()}',
              user: state.user,
              isLogin: true,
            ),
          );
        }
        if (state is LoginFailure) {
          clearFields();

          final message = state.appFailure.getErrorMessage();
          AppLogger().error(
            'Login failed: $message error ${state.appFailure.toString()}',
          );
          SnackBarHelper.showErrorSnackBar(context, message: message);
        }
      },
      child: Scaffold(
        backgroundColor: context.appColors.backgroundColor,
        body: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 60.h),

                  // Logo and Welcome Section
                  _buildWelcomeSection(context),

                  SizedBox(height: 50.h),

                  // Login Form
                  _buildLoginForm(context),

                  SizedBox(height: 24.h),

                  // Forgot Password Link
                  // _buildForgotPasswordLink(context),
                  SizedBox(height: 40.h),

                  // Login Button
                  _buildLoginButton(context),

                  SizedBox(height: 100.h),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // App Logo/Icon
        Center(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20.r),
            child: Image.asset(
              AppAssets.appLogo,
              fit: BoxFit.cover,
              height: 100.h,
              width: 120.h,
            ),
          ),
        ),

        SizedBox(height: 24.h),

        // Welcome Text
        Text(
          'Welcome Back!',
          style: Theme.of(context).textTheme.headlineLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),

        SizedBox(height: 8.h),

        Text(
          'Sign in to continue to your gas delivery service',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: context.appColors.subtextColor,
          ),
        ),
      ],
    );
  }

  Widget _buildLoginForm(BuildContext context) {
    return Column(
      children: [
        // Phone Number Field
        CustomTextField(
          controller: _phoneController,
          labelText: '61XXXXXXXX',
          hintText: '61XXXXXXXX',
          keyboardType: TextInputType.phone,
          textInputAction: TextInputAction.done,
          // autovalidateMode: AutovalidateMode.onUnfocus,
          prefixIcon: Container(
            padding: EdgeInsets.all(12.w),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // SizedBox(width: 2.w),
                Text(
                  '+252',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(width: 8.w),
                Container(
                  width: 1.w,
                  height: 20.h,
                  color: context.appColors.disabledColor,
                ),
              ],
            ),
          ),
          inputFormatters: [
            SomaliPhoneNumberFormatter(),
            LengthLimitingTextInputFormatter(9),
          ],
          validator: (value) {
            // First check if required
            final requiredError = FormValidationHelper.validateRequiredField(
              value: value,
              fieldName: 'Phone number',
            );
            if (requiredError != null) return requiredError;

            // Then check length for Somali phone numbers
            if (value!.length < 9) {
              return 'Please enter a valid phone number';
            }

            // regex that check like this number format 61xxxxxxx must start with 61
            const pattern = r'^61[1-9]\d{6}$';
            final regex = RegExp(pattern);

            if (!regex.hasMatch(value.trim())) {
              return 'Enter a valid phone number.';
            }

            return null;
          },
        ),

        SizedBox(height: 20.h),
      ],
    );
  }

  Widget _buildLoginButton(BuildContext context) {
    return BlocBuilder<AuthenticationBloc, AuthenticationState>(
      buildWhen: (previous, current) {
        return current is AuthenticationLoginLoading ||
            current is LoginFailure ||
            current is LoginSuccess;
      },
      builder: (context, state) {
        final isLoading = state is AuthenticationLoginLoading;

        return CustomButton.filled(
          onTap: _handleLogin,
          buttonText: 'Sign In',
          loadingText: 'Signing In...',
          buttonState: isLoading ? ButtonState.loading : ButtonState.normal,
          height: 40.h,
          width: double.infinity,
          backgroundColor: context.appColors.primaryColor,
        );
      },
    );
  }
}
