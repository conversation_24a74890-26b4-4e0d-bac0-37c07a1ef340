import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/router/extension/navigation_extension.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/button_state.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_button.dart';

import '../../../../core/utils/helpers/snack_bar_helper.dart';
import '../../../main/presentation/pages/main_page.dart';
import '../../domain/entities/user_entity.dart';
import '../bloc/authentication_bloc.dart';

class OtpVerificationPage extends StatefulWidget {
  // final String phoneNumber;
  final UserEntity user;
  final bool isLogin;

  const OtpVerificationPage({
    super.key,
    required this.user,
    this.isLogin = false,
  });

  @override
  State<OtpVerificationPage> createState() => _OtpVerificationPageState();
}

class _OtpVerificationPageState extends State<OtpVerificationPage> {
  final List<TextEditingController> _otpControllers = List.generate(
    6,
    (index) => TextEditingController(),
  );
  final List<FocusNode> _focusNodes = List.generate(6, (index) => FocusNode());

  int _resendCountdown = 60;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _startResendTimer();
  }

  @override
  void dispose() {
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    _timer?.cancel();
    super.dispose();
  }

  void _startResendTimer() {
    _resendCountdown = 60;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_resendCountdown > 0) {
        setState(() {
          _resendCountdown--;
        });
      } else {
        timer.cancel();
      }
    });
  }

  void _handleOtpChange(String value, int index) {
    if (value.isNotEmpty) {
      if (index < 5) {
        _focusNodes[index + 1].requestFocus();
      } else {
        _focusNodes[index].unfocus();
        _verifyOtp();
      }
    }
  }

  void _handleBackspace(int index) {
    if (index > 0) {
      _otpControllers[index - 1].clear();
      _focusNodes[index - 1].requestFocus();
    }
  }

  String _getOtpCode() {
    return _otpControllers.map((controller) => controller.text).join();
  }

  void _verifyOtp() {
    final otpCode = _getOtpCode();
    if (otpCode.length == 6) {
      context.authenticationBloc.add(
        VerifyOtpEvent(mobile: widget.user.phone, otp: otpCode),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter complete OTP')),
      );
    }
  }

  void _resendOtp() {
    if (_resendCountdown == 0) {
      context.authenticationBloc.add(ResendOtpEvent(mobile: widget.user.phone));
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthenticationBloc, AuthenticationState>(
      listener: (context, state) {
        if (state is Authenticated) {
          context.pushAndRemoveUntilRoute(const MainPage());
        }
        if (state is OtpVerificationFailure) {
          final message = state.appFailure.getErrorMessage();
          SnackBarHelper.showErrorSnackBar(context, message: message);
        }
        if (state is ResendOtpSuccess) {
          _startResendTimer();
          SnackBarHelper.showSuccessSnackBar(
            context,
            message: 'OTP sent successfully!',
          );
        }
        if (state is ResendOtpFailure) {
          final message = state.appFailure.getErrorMessage();
          SnackBarHelper.showErrorSnackBar(context, message: message);
        }
      },
      child: Scaffold(
        backgroundColor: context.appColors.backgroundColor,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            onPressed: () => Navigator.pop(context),
            icon: Icon(
              Icons.arrow_back_ios,
              color: context.appColors.textColor,
            ),
          ),
        ),

        body: SafeArea(
          child: LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                child: ConstrainedBox(
                  constraints: BoxConstraints(minHeight: constraints.maxHeight),
                  child: IntrinsicHeight(
                    child: Column(
                      // crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 40.h),

                        _buildHeaderSection(context),
                        SizedBox(height: 50.h),

                        _buildOtpInputFields(context),
                        SizedBox(height: 40.h),

                        _buildVerifyButton(context),
                        SizedBox(height: 30.h),

                        _buildResendSection(context),

                        const Spacer(), // 👈 This is now safe because of IntrinsicHeight

                        _buildHelpText(context),
                        SizedBox(height: 40.h),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context) {
    return Column(
      children: [
        // OTP Icon
        Container(
          width: 80.w,
          height: 80.h,
          decoration: BoxDecoration(
            color: context.appColors.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Icon(
            Icons.sms_outlined,
            size: 40.sp,
            color: context.appColors.primaryColor,
          ),
        ),

        SizedBox(height: 24.h),

        Text(
          'Verify Your Phone',
          style: Theme.of(context).textTheme.headlineLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),

        SizedBox(height: 12.h),

        RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: context.appColors.subtextColor,
            ),
            children: [
              const TextSpan(text: 'We sent a 6-digit code to\n'),
              TextSpan(
                text: widget.user.phone,
                style: TextStyle(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOtpInputFields(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: List.generate(6, (index) {
        return Container(
          width: 45.w,
          height: 55.h,
          decoration: BoxDecoration(
            color: context.appColors.surfaceColor,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: _otpControllers[index].text.isNotEmpty
                  ? context.appColors.primaryColor
                  : context.appColors.dividerColor,
              width: 1.5,
            ),
          ),
          child: TextFormField(
            controller: _otpControllers[index],
            focusNode: _focusNodes[index],
            textAlign: TextAlign.center,
            keyboardType: TextInputType.number,
            maxLength: 1,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            decoration: const InputDecoration(
              counterText: '',
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
            ),
            onChanged: (value) {
              _handleOtpChange(value, index);
              setState(() {}); // Rebuild to update border color
            },
            onTap: () {
              _otpControllers[index].selection = TextSelection.fromPosition(
                TextPosition(offset: _otpControllers[index].text.length),
              );
            },
            onEditingComplete: () {
              if (_otpControllers[index].text.isEmpty && index > 0) {
                _handleBackspace(index);
              }
            },
            onTapOutside: (event) {
              FocusScope.of(context).unfocus();
            },
          ),
        );
      }),
    );
  }

  Widget _buildVerifyButton(BuildContext context) {
    return BlocBuilder<AuthenticationBloc, AuthenticationState>(
      buildWhen: (previous, current) {
        return current is OtpVerificationLoading ||
            current is OtpVerificationFailure ||
            current is Authenticated;
      },
      builder: (context, state) {
        final isLoading = state is OtpVerificationLoading;
        final buttonState = isLoading
            ? ButtonState.loading
            : ButtonState.normal;
        return CustomButton(
          onTap: _verifyOtp,
          buttonText: 'Verify OTP',
          loadingText: 'Verifying...',
          buttonState: buttonState,
          height: 40.h,
          width: double.infinity,
          backgroundColor: context.appColors.primaryColor,
        );
      },
    );
  }

  Widget _buildResendSection(BuildContext context) {
    return BlocBuilder<AuthenticationBloc, AuthenticationState>(
      buildWhen: (previous, current) {
        return current is ResendOtpLoading ||
            current is ResendOtpSuccess ||
            current is ResendOtpFailure;
      },
      builder: (context, state) {
        final isResendLoading = state is ResendOtpLoading;

        return Column(
          children: [
            Text(
              "Didn't receive the code?",
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),

            SizedBox(height: 8.h),

            if (_resendCountdown > 0)
              Text(
                'Resend in ${_resendCountdown}s',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.subtextColor,
                  fontWeight: FontWeight.w500,
                ),
              )
            else
              TextButton(
                onPressed: isResendLoading ? null : _resendOtp,
                child: isResendLoading
                    ? SizedBox(
                        width: 20.w,
                        height: 20.h,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.w,
                          color: context.appColors.primaryColor,
                        ),
                      )
                    : Text(
                        'Resend OTP',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: context.appColors.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildHelpText(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: context.appColors.primaryColor,
            size: 20.sp,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              'Enter the 6-digit code sent to your phone number. If you don\'t receive it, you can request a new one.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
