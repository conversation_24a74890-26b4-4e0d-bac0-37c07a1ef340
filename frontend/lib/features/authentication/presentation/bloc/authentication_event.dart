part of 'authentication_bloc.dart';

abstract class AuthenticationEvent extends Equatable {
  const AuthenticationEvent();

  @override
  List<Object> get props => [];
}

// ✅ Login Event
class LoginEvent extends AuthenticationEvent {
  final String mobileNumber;

  const LoginEvent({required this.mobileNumber});

  @override
  List<Object> get props => [mobileNumber];
}

// ✅ Logout Event
class LogoutEvent extends AuthenticationEvent {}

// ✅ CheckUserAuthentication Event
class CheckUserAuthenticationEvent extends AuthenticationEvent {}

// ✅ Complete Onboarding Event
class CompleteOnBoardingEvent extends AuthenticationEvent {}

// ✅ Re-Send OTP Event
class ResendOtpEvent extends AuthenticationEvent {
  final String mobile;

  const ResendOtpEvent({required this.mobile});

  @override
  List<Object> get props => [mobile];
}

// ✅ Verify OTP Event
class VerifyOtpEvent extends AuthenticationEvent {
  final String mobile;
  final String otp;

  const VerifyOtpEvent({required this.mobile, required this.otp});

  @override
  List<Object> get props => [mobile, otp];
}

// ✅ Check User Exists Event
class CheckUserExistsEvent extends AuthenticationEvent {
  final String phone;

  const CheckUserExistsEvent({required this.phone});

  @override
  List<Object> get props => [phone];
}
