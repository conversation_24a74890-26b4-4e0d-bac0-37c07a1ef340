part of 'authentication_bloc.dart';

// ✅ Authentication States
abstract class AuthenticationState extends Equatable {
  const AuthenticationState();

  @override
  List<Object> get props => [];
}

// ✅ Initial State
class AuthenticationInitial extends AuthenticationState {}

// ✅ Loading State
class AuthenticationLoading extends AuthenticationState {}

class AuthenticationChecking extends AuthenticationState {}

class AuthenticationLoginLoading extends AuthenticationState {}

class AuthenticationRegisterLoading extends AuthenticationState {}

// ✅ Onboarding State
class OnboardingRequired extends AuthenticationState {}

class OnboardingCompleted extends AuthenticationState {}

// ✅ Authenticated State
class Authenticated extends AuthenticationState {
  final UserEntity? user;

  const Authenticated({this.user});
}

// ✅ Login Successful State
class LoginSuccess extends AuthenticationState {
  final String message;
  final UserEntity user;

  const LoginSuccess({required this.message, required this.user});

  @override
  List<Object> get props => [message, user];
}

// ✅ Unauthenticated State (For Logout or Failed Auth)
class Unauthenticated extends AuthenticationState {}

// ✅ Error State
class AuthenticationFailure extends AuthenticationState {
  final AppFailure appFailure;

  const AuthenticationFailure({required this.appFailure});

  @override
  List<Object> get props => [appFailure];
}

// Login Failure
class LoginFailure extends AuthenticationState {
  final AppFailure appFailure;

  const LoginFailure({required this.appFailure});

  @override
  List<Object> get props => [appFailure];
}

class ResendOtpLoading extends AuthenticationState {}

class ResendOtpSuccess extends AuthenticationState {
  final String message;

  const ResendOtpSuccess({required this.message});

  @override
  List<Object> get props => [message];
}

class ResendOtpFailure extends AuthenticationState {
  final AppFailure appFailure;

  const ResendOtpFailure({required this.appFailure});

  @override
  List<Object> get props => [appFailure];
}

class OtpVerificationLoading extends AuthenticationState {}

class OtpVerificationFailure extends AuthenticationState {
  final AppFailure appFailure;

  const OtpVerificationFailure({required this.appFailure});

  @override
  List<Object> get props => [appFailure];
}
