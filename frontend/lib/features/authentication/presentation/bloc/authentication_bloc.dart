// ignore_for_file: unreachable_switch_default

import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/enums/app_start_state.dart';
import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../../../../core/utils/helpers/bloc_helper.dart';
import '../../domain/entities/user_entity.dart';
import '../../domain/params/login_params.dart';
import '../../domain/params/send_otp_params.dart';
import '../../domain/params/verify_otp_params.dart';
import '../../domain/usecase/check_authentication_usecase.dart';
import '../../domain/usecase/complete_onboarding_use_case.dart';
import '../../domain/usecase/login_usecase.dart';
import '../../domain/usecase/logout_usecase.dart';
import '../../domain/usecase/resend_otp_use_case.dart';
import '../../domain/usecase/verify_otp_use_case.dart';

part 'authentication_event.dart';
part 'authentication_state.dart';

class AuthenticationBloc
    extends Bloc<AuthenticationEvent, AuthenticationState> {
  final LoginUseCase loginUseCase;
  final LogoutUseCase logoutUseCase;
  final CheckAuthenticationUseCase checkAuthenticationUseCase;
  final CompleteOnboardingUseCase completeOnboardingUseCase;
  final ResendOtpUseCase resendOtpUseCase;
  final VerifyOtpUseCase verifyOtpUseCase;

  AuthenticationBloc({
    required this.loginUseCase,
    required this.logoutUseCase,
    required this.checkAuthenticationUseCase,
    required this.completeOnboardingUseCase,
    required this.resendOtpUseCase,
    required this.verifyOtpUseCase,
  }) : super(AuthenticationInitial()) {
    /// ✅ Handle Login
    on<LoginEvent>(_onLogin, transformer: BlocHelper.debounceHelper());

    /// ✅ Handle Logout
    on<LogoutEvent>(_onLogout, transformer: BlocHelper.debounceHelper());

    /// ✅ Check User Authentication
    on<CheckUserAuthenticationEvent>(
      _onCheckUserAuthentication,
      transformer: BlocHelper.debounceHelper(),
    );

    /// ✅ Complete onbaording
    on<CompleteOnBoardingEvent>(
      _onCompleteOnBoardingEvent,
      transformer: BlocHelper.debounceHelper(),
    );

    /// ✅ Send OTP
    on<ResendOtpEvent>(_onReSendOtp, transformer: BlocHelper.debounceHelper());

    /// ✅ Verify OTP
    on<VerifyOtpEvent>(_onVerifyOtp, transformer: BlocHelper.debounceHelper());
  }

  // Timer? _otpTimer;
  // int _otpCountdown = 0;

  // void _startOtpTimer(Emitter<AuthenticationState> emit) {
  //   _otpCountdown = 60;
  //   _otpTimer?.cancel();
  //   _otpTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
  //     _otpCountdown--;
  //     if (_otpCountdown <= 0) {
  //       timer.cancel();
  //       emit(const OtpTimerState(remainingSeconds: 0, canResend: true));
  //     } else {
  //       emit(OtpTimerState(remainingSeconds: _otpCountdown, canResend: false));
  //     }
  //   });
  //   // Emit initial state
  //   emit(OtpTimerState(remainingSeconds: _otpCountdown, canResend: false));
  // }

  /// 🟢 Handle Login
  Future<void> _onLogin(
    LoginEvent event,
    Emitter<AuthenticationState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<UserEntity, AuthenticationState>(
      emit: emit,
      loadingState: AuthenticationLoginLoading(),
      callUseCase: loginUseCase(params: LoginParams(phone: event.mobileNumber)),
      onSuccess: (userEntity) {
        // _currentUser = userEntity;
        // return Authenticated(user: userEntity);
        return LoginSuccess(message: 'Login successful', user: userEntity);
      },
      onFailure: (failure) => LoginFailure(appFailure: failure),
    );
  }

  /// 🟢 Handle Logout
  Future<void> _onLogout(
    LogoutEvent event,
    Emitter<AuthenticationState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, AuthenticationState>(
      emit: emit,
      loadingState: AuthenticationLoading(),
      callUseCase: logoutUseCase(params: NoParams()),
      onSuccess: (_) => Unauthenticated(),
      onFailure: (failure) => AuthenticationFailure(appFailure: failure),
    );
  }

  /// 🟢 Handle Logout
  Future<void> _onCompleteOnBoardingEvent(
    CompleteOnBoardingEvent event,
    Emitter<AuthenticationState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, AuthenticationState>(
      emit: emit,
      loadingState: AuthenticationLoading(),
      callUseCase: completeOnboardingUseCase(params: NoParams()),
      onSuccess: (_) => OnboardingCompleted(),
      onFailure: (failure) => AuthenticationFailure(appFailure: failure),
    );
  }

  /// 🟢  Check User Authentication
  Future<void> _onCheckUserAuthentication(
    CheckUserAuthenticationEvent event,
    Emitter<AuthenticationState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<AppStartState, AuthenticationState>(
      emit: emit,
      loadingState: AuthenticationChecking(),
      callUseCase: checkAuthenticationUseCase(params: NoParams()),
      onSuccess: (state) {
        switch (state) {
          case AppStartState.loggedIn:
            return const Authenticated();
          case AppStartState.onboardingRequired:
            return OnboardingRequired();
          case AppStartState.loggedOut:
          default:
            return Unauthenticated();
        }
      },
      onFailure: (failure) => Unauthenticated(),
    );
  }

  /// 🟢 Send OTP
  Future<void> _onReSendOtp(
    ResendOtpEvent event,
    Emitter<AuthenticationState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<String, AuthenticationState>(
      emit: emit,
      loadingState: ResendOtpLoading(),
      callUseCase: resendOtpUseCase(
        params: SendOtpParams(mobileNumber: event.mobile),
      ),
      onSuccess: (message) => ResendOtpSuccess(message: message),
      onFailure: (failure) => ResendOtpFailure(appFailure: failure),
    );
  }

  /// 🟢 Verify OTP
  Future<void> _onVerifyOtp(
    VerifyOtpEvent event,
    Emitter<AuthenticationState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<UserEntity, AuthenticationState>(
      emit: emit,
      loadingState: OtpVerificationLoading(),
      callUseCase: verifyOtpUseCase(
        params: VerifyOtpParams(otp: event.otp, mobileNumber: event.mobile),
      ),
      onSuccess: (user) => Authenticated(user: user),
      onFailure: (failure) => OtpVerificationFailure(appFailure: failure),
    );
  }
}
