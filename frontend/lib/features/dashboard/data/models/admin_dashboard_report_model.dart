// admin_dashboard_report_model.dart
import '../../../../core/enums/dashboard_report_type.dart';
import '../../../../core/enums/parse_failure_type.dart';
import '../../../../core/errors/app_failure.dart';
import '../../domain/entities/dashboard_report_entity.dart';
import 'admin_dashboard_model.dart';

class AdminDashboardReportModel extends AdminDashboardReportEntity {
  AdminDashboardReportModel({
    required super.reportType,
    required super.dateRange,
    required super.report,
  });

  factory AdminDashboardReportModel.fromJson(Map<String, dynamic> json) {
    try {
      final reportType = _parseReportType(json['reportType']);
      final dateRange = DateRangeModel.fromJson(json['dateRange']);

      // Use the report data directly from backend
      final report = json['report'] as Map<String, dynamic>;

      return AdminDashboardReportModel(
        reportType: reportType,
        dateRange: dateRange,
        report: report,
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message:
            'Failed to parse AdminDashboardReportModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: AdminDashboardReportModel,
        stackTrace: stackTrace,
      );
    }
  }

  static DashboardReportType _parseReportType(String type) {
    switch (type.toLowerCase()) {
      case 'orders':
        return DashboardReportType.orders;
      case 'revenue':
        return DashboardReportType.revenue;
      case 'inventory':
        return DashboardReportType.inventory;
      case 'agents':
        return DashboardReportType.agents;
      default:
        throw ArgumentError('Unknown report type: $type');
    }
  }
}

class DateRangeModel extends DateRangeEntity {
  DateRangeModel({required super.startDate, required super.endDate});

  factory DateRangeModel.fromJson(Map<String, dynamic> json) {
    try {
      return DateRangeModel(
        startDate: DateTime.parse(json['startDate']),
        endDate: DateTime.parse(json['endDate']),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse DateRangeModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: DateRangeModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class OrdersReportModel extends OrdersReportEntity {
  OrdersReportModel({required super.salesOverview});

  factory OrdersReportModel.fromJson(Map<String, dynamic> json) {
    try {
      return OrdersReportModel(
        salesOverview: SalesOverviewModel.fromJson(json['salesOverview']),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse OrdersReportModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: OrdersReportModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class RevenueReportModel extends RevenueReportEntity {
  RevenueReportModel({required super.financialOverview});

  factory RevenueReportModel.fromJson(Map<String, dynamic> json) {
    try {
      return RevenueReportModel(
        financialOverview: FinancialOverviewModel.fromJson(
          json['financialOverview'],
        ),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse RevenueReportModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: RevenueReportModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class InventoryReportModel extends InventoryReportEntity {
  InventoryReportModel({required super.inventoryStatus});

  factory InventoryReportModel.fromJson(Map<String, dynamic> json) {
    try {
      return InventoryReportModel(
        inventoryStatus: InventoryStatusModel.fromJson(json['inventoryStatus']),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse InventoryReportModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: InventoryReportModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class AgentsReportModel extends AgentsReportEntity {
  AgentsReportModel({required super.agentPerformance});

  factory AgentsReportModel.fromJson(Map<String, dynamic> json) {
    try {
      return AgentsReportModel(
        agentPerformance: AgentPerformanceModel.fromJsonList(
          json['agentPerformance'],
        ),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse AgentsReportModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: AgentsReportModel,
        stackTrace: stackTrace,
      );
    }
  }
}
