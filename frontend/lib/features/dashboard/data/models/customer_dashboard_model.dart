// data/models/customer_dashboard_model.dart

import '../../../../core/enums/parse_failure_type.dart';
import '../../../../core/errors/app_failure.dart';
import '../../domain/entities/customer_dashboard_entity.dart';

class CustomerDashboardModel extends CustomerDashboardEntity {
  CustomerDashboardModel({
    required super.recentOrders,
    required super.orderStats,
    required super.favoriteItems,
  });

  factory CustomerDashboardModel.fromJson(Map<String, dynamic> json) {
    try {
      return CustomerDashboardModel(
        recentOrders: RecentOrderModel.fromJsonList(json['recentOrders']),
        orderStats: OrderStatsModel.fromJson(json['orderStats']),
        favoriteItems: FavoriteItemModel.fromJsonList(json['favoriteItems']),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse CustomerDashboardModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: CustomerDashboardModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class OrderStatsModel extends OrderStatsEntity {
  OrderStatsModel({
    required super.total,
    required super.pending,
    required super.delivered,
    required super.cancelled,
  });

  factory OrderStatsModel.fromJson(Map<String, dynamic> json) {
    try {
      return OrderStatsModel(
        total: json['total'] ?? 0,
        pending: json['pending'] ?? 0,
        delivered: json['delivered'] ?? 0,
        cancelled: json['cancelled'] ?? 0,
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse OrderStatsModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: OrderStatsModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class RecentOrderModel extends RecentOrderEntity {
  RecentOrderModel({
    required super.id,
    required super.status,
    required super.totalAmount,
    required super.createdAt,
  });

  factory RecentOrderModel.fromJson(Map<String, dynamic> json) {
    try {
      return RecentOrderModel(
        id: json['id'] ?? '',
        status: json['status'] ?? '',
        totalAmount: (json['totalAmount'] as num).toDouble(),
        createdAt: DateTime.parse(json['createdAt']),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse RecentOrderModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: RecentOrderModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<RecentOrderModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => RecentOrderModel.fromJson(json)).toList();
  }
}

class FavoriteItemModel extends FavoriteItemEntity {
  FavoriteItemModel({
    required super.id,
    required super.name,
    required super.type,
    required super.orderCount,
  });

  factory FavoriteItemModel.fromJson(Map<String, dynamic> json) {
    try {
      return FavoriteItemModel(
        id: json['id'] ?? '',
        name: json['name'] ?? '',
        type: json['type'] ?? '',
        orderCount: json['orderCount'] ?? 0,
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse FavoriteItemModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: FavoriteItemModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<FavoriteItemModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => FavoriteItemModel.fromJson(json)).toList();
  }
}
