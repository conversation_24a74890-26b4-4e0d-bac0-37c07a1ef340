import '../../../../core/enums/parse_failure_type.dart';
import '../../../../core/errors/app_failure.dart';
import '../../domain/entities/agent_dashboard_entity.dart';

class AgentDashboardModel extends AgentDashboardEntity {
  AgentDashboardModel({
    required super.agentInfo,
    required super.orderStats,
    required super.earnings,
    required super.recentDeliveries,
    required super.performanceMetrics,
    required super.lastUpdated,
  });

  factory AgentDashboardModel.fromJson(Map<String, dynamic> json) {
    try {
      return AgentDashboardModel(
        agentInfo: AgentInfoModel.fromJson(json['agentInfo']),
        orderStats: OrderStatsModel.fromJson(json['orderStats']),
        earnings: EarningsModel.fromJson(json['earnings']),
        recentDeliveries:
            (json['recentDeliveries'] as List<dynamic>?)
                ?.map((e) => RecentDeliveryModel.from<PERSON>son(e))
                .toList() ??
            [],
        performanceMetrics: PerformanceMetricsModel.fromJson(
          json['performanceMetrics'],
        ),
        lastUpdated: DateTime.parse(json['lastUpdated']),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse AgentDashboardModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: AgentDashboardModel,
        stackTrace: stackTrace,
      );
    }
  }
}

// Model classes for agent dashboard entities
class AgentInfoModel extends AgentInfoEntity {
  AgentInfoModel({
    required super.id,
    required super.phone,
    super.email,
    required super.isOnDuty,
    required super.rating,
  });

  factory AgentInfoModel.fromJson(Map<String, dynamic> json) {
    return AgentInfoModel(
      id: json['id'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'],
      isOnDuty: json['isOnDuty'] ?? false,
      rating: (json['rating'] ?? 0).toDouble(),
    );
  }
}

class OrderStatsModel extends OrderStatsEntity {
  OrderStatsModel({
    required super.total,
    required super.pending,
    required super.outForDelivery,
    required super.delivered,
    required super.failed,
    required super.cancelled,
  });

  factory OrderStatsModel.fromJson(Map<String, dynamic> json) {
    return OrderStatsModel(
      total: json['total'] ?? 0,
      pending: json['pending'] ?? 0,
      outForDelivery: json['outForDelivery'] ?? 0,
      delivered: json['delivered'] ?? 0,
      failed: json['failed'] ?? 0,
      cancelled: json['cancelled'] ?? 0,
    );
  }
}

class EarningsModel extends EarningsEntity {
  EarningsModel({
    required super.totalEarnings,
    required super.totalOrders,
    required super.avgOrderValue,
  });

  factory EarningsModel.fromJson(Map<String, dynamic> json) {
    return EarningsModel(
      totalEarnings: (json['totalEarnings'] ?? 0).toDouble(),
      totalOrders: json['totalOrders'] ?? 0,
      avgOrderValue: (json['avgOrderValue'] ?? 0).toDouble(),
    );
  }
}

class RecentDeliveryModel extends RecentDeliveryEntity {
  RecentDeliveryModel({
    required super.id,
    required super.customer,
    required super.totalAmount,
    required super.deliveryAddress,
    required super.deliveredAt,
  });

  factory RecentDeliveryModel.fromJson(Map<String, dynamic> json) {
    return RecentDeliveryModel(
      id: json['_id'] ?? json['id'] ?? '',
      customer: CustomerInfoModel.fromJson(json['customer'] ?? {}),
      totalAmount: (json['totalAmount'] ?? 0).toDouble(),
      deliveryAddress: json['deliveryAddress'] ?? '',
      deliveredAt: DateTime.parse(json['deliveredAt']),
    );
  }
}

class CustomerInfoModel extends CustomerInfoEntity {
  CustomerInfoModel({required super.phone, required super.address});

  factory CustomerInfoModel.fromJson(Map<String, dynamic> json) {
    return CustomerInfoModel(
      phone: json['phone'] ?? '',
      address: json['address'] ?? '',
    );
  }
}

class PerformanceMetricsModel extends PerformanceMetricsEntity {
  PerformanceMetricsModel({
    required super.monthlyStats,
    required super.overallMetrics,
  });

  factory PerformanceMetricsModel.fromJson(Map<String, dynamic> json) {
    return PerformanceMetricsModel(
      monthlyStats:
          (json['monthlyStats'] as List<dynamic>?)
              ?.map((e) => MonthlyStatsModel.fromJson(e))
              .toList() ??
          [],
      overallMetrics: OverallMetricsModel.fromJson(
        json['overallMetrics'] ?? {},
      ),
    );
  }
}

class MonthlyStatsModel extends MonthlyStatsEntity {
  MonthlyStatsModel({
    required super.id,
    required super.totalOrders,
    required super.completedOrders,
    required super.totalRevenue,
  });

  factory MonthlyStatsModel.fromJson(Map<String, dynamic> json) {
    return MonthlyStatsModel(
      id: MonthYearModel.fromJson(json['_id'] ?? {}),
      totalOrders: json['totalOrders'] ?? 0,
      completedOrders: json['completedOrders'] ?? 0,
      totalRevenue: (json['totalRevenue'] ?? 0).toDouble(),
    );
  }
}

class MonthYearModel extends MonthYearEntity {
  MonthYearModel({required super.year, required super.month});

  factory MonthYearModel.fromJson(Map<String, dynamic> json) {
    return MonthYearModel(year: json['year'] ?? 0, month: json['month'] ?? 0);
  }
}

class OverallMetricsModel extends OverallMetricsEntity {
  OverallMetricsModel({
    required super.totalOrders,
    required super.totalCompleted,
    required super.completionRate,
    required super.totalRevenue,
    required super.avgOrderValue,
  });

  factory OverallMetricsModel.fromJson(Map<String, dynamic> json) {
    return OverallMetricsModel(
      totalOrders: json['totalOrders'] ?? 0,
      totalCompleted: json['totalCompleted'] ?? 0,
      completionRate: (json['completionRate'] ?? 0).toDouble(),
      totalRevenue: (json['totalRevenue'] ?? 0).toDouble(),
      avgOrderValue: (json['avgOrderValue'] ?? 0).toDouble(),
    );
  }
}
