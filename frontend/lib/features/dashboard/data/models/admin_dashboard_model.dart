// admin_dashboard_model.dart
import '../../../../core/enums/parse_failure_type.dart';
import '../../../../core/errors/app_failure.dart';
import '../../domain/entities/admin_dashboard_entity.dart';

class AdminDashboardModel extends AdminDashboardEntity {
  AdminDashboardModel({
    required super.salesOverview,
    required super.inventoryStatus,
    required super.agentPerformance,
    required super.financialOverview,
    required super.systemHealth,
  });

  factory AdminDashboardModel.fromJson(Map<String, dynamic> json) {
    try {
      return AdminDashboardModel(
        salesOverview: SalesOverviewModel.fromJson(json['salesOverview']),
        inventoryStatus: InventoryStatusModel.fromJson(json['inventoryStatus']),
        agentPerformance: AgentPerformanceModel.fromJsonList(
          json['agentPerformance'],
        ),
        financialOverview: FinancialOverviewModel.from<PERSON>son(
          json['financialOverview'],
        ),
        systemHealth: SystemHealthModel.fromJson(json['systemHealth']),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse AdminDashboardModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: AdminDashboardModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class SalesOverviewModel extends SalesOverviewEntity {
  SalesOverviewModel({
    required super.totalSales,
    required super.totalRevenue,
    required super.dailyAverage,
    required super.weeklyTrend,
  });

  factory SalesOverviewModel.fromJson(Map<String, dynamic> json) {
    try {
      return SalesOverviewModel(
        totalSales: json['totalSales'] ?? 0,
        totalRevenue: (json['totalRevenue'] as num).toDouble(),
        dailyAverage: (json['dailyAverage'] as num).toDouble(),
        weeklyTrend: json['weeklyTrend'] ?? 0,
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse SalesOverviewModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: SalesOverviewModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class InventoryStatusModel extends InventoryStatusEntity {
  InventoryStatusModel({
    required super.totalCylinders,
    required super.availableCylinders,
    required super.totalSpareParts,
    required super.lowStockSpareParts,
  });

  factory InventoryStatusModel.fromJson(Map<String, dynamic> json) {
    try {
      return InventoryStatusModel(
        totalCylinders: json['totalCylinders'] ?? 0,
        availableCylinders: json['availableCylinders'] ?? 0,
        totalSpareParts: json['totalSpareParts'] ?? 0,
        lowStockSpareParts: json['lowStockSpareParts'] ?? 0,
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse InventoryStatusModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: InventoryStatusModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class AgentPerformanceModel extends AgentPerformanceEntity {
  AgentPerformanceModel({
    required super.agentId,
    required super.name,
    required super.completedDeliveries,
    required super.avgRating,
    super.lastActive,
  });

  factory AgentPerformanceModel.fromJson(Map<String, dynamic> json) {
    try {
      return AgentPerformanceModel(
        agentId: json['agentId'] ?? '',
        name: json['name'] ?? '',
        completedDeliveries: json['completedDeliveries'] ?? 0,
        avgRating: (json['avgRating'] as num).toDouble(),
        lastActive: json['lastActive'] != null
            ? DateTime.parse(json['lastActive'])
            : null,
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse AgentPerformanceModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: AgentPerformanceModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<AgentPerformanceModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList
        .map((json) => AgentPerformanceModel.fromJson(json))
        .toList();
  }
}

class FinancialOverviewModel extends FinancialOverviewEntity {
  FinancialOverviewModel({
    required super.totalRevenue,
    required super.totalProfit,
    required super.profitMargin,
    required super.mostProfitableItems,
  });

  factory FinancialOverviewModel.fromJson(Map<String, dynamic> json) {
    try {
      return FinancialOverviewModel(
        totalRevenue: (json['totalRevenue'] as num).toDouble(),
        totalProfit: (json['totalProfit'] as num).toDouble(),
        profitMargin: (json['profitMargin'] as num).toDouble(),
        mostProfitableItems: MostProfitableItemModel.fromJsonList(
          json['mostProfitableItems'],
        ),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse FinancialOverviewModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: FinancialOverviewModel,
        stackTrace: stackTrace,
      );
    }
  }
}

class MostProfitableItemModel extends MostProfitableItemEntity {
  MostProfitableItemModel({
    required super.id,
    required super.name,
    required super.type,
    required super.profit,
  });

  factory MostProfitableItemModel.fromJson(Map<String, dynamic> json) {
    try {
      return MostProfitableItemModel(
        id: json['id'] ?? '',
        name: json['name'] ?? '',
        type: json['type'] ?? '',
        profit: (json['profit'] as num).toDouble(),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse MostProfitableItemModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: MostProfitableItemModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<MostProfitableItemModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList
        .map((json) => MostProfitableItemModel.fromJson(json))
        .toList();
  }
}

class SystemHealthModel extends SystemHealthEntity {
  SystemHealthModel({
    required super.totalUsers,
    required super.activeUsers,
    required super.ordersThisMonth,
    required super.paymentSuccessRate,
  });

  factory SystemHealthModel.fromJson(Map<String, dynamic> json) {
    try {
      return SystemHealthModel(
        totalUsers: json['totalUsers'] ?? 0,
        activeUsers: json['activeUsers'] ?? 0,
        ordersThisMonth: json['ordersThisMonth'] ?? 0,
        paymentSuccessRate: (json['paymentSuccessRate'] as num).toDouble(),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse SystemHealthModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: SystemHealthModel,
        stackTrace: stackTrace,
      );
    }
  }
}
