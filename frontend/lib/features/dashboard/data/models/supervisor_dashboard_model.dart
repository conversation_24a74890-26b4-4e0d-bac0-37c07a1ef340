// supervisor_dashboard_model.dart
import '../../../../core/enums/parse_failure_type.dart';
import '../../../../core/errors/app_failure.dart';
import '../../domain/entities/admin_dashboard_entity.dart';
import '../../domain/entities/supervisor_dashboard_entity.dart';
import 'admin_dashboard_model.dart';

class SupervisorDashboardModel extends SupervisorDashboardEntity {
  SupervisorDashboardModel({
    required super.supervisorInfo,
    required super.todayStats,
    required super.availableAgents,
    required super.todayOrders,
    required super.todayTopProducts,
    required super.restrictions,
    required super.lastUpdated,
  });

  factory SupervisorDashboardModel.fromJson(Map<String, dynamic> json) {
    try {
      return SupervisorDashboardModel(
        supervisorInfo: SupervisorInfoModel.fromJson(
          json['supervisorInfo'] ?? {},
        ),
        todayStats: SupervisorTodayStatsModel.fromJson(json['todayStats']),
        availableAgents:
            (json['availableAgents'] as List<dynamic>?)
                ?.map((e) => AvailableAgentModel.fromJson(e))
                .toList() ??
            [],
        todayOrders:
            (json['todayOrders'] as List<dynamic>?)
                ?.map((e) => TodayOrderModel.fromJson(e))
                .toList() ??
            [],
        todayTopProducts:
            (json['todayTopProducts'] as List<dynamic>?)
                ?.map((e) => TodayTopProductModel.fromJson(e))
                .toList() ??
            [],
        restrictions: SupervisorRestrictionsModel.fromJson(
          json['restrictions'],
        ),
        lastUpdated: DateTime.parse(json['lastUpdated']),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message:
            'Failed to parse SupervisorDashboardModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: SupervisorDashboardModel,
        stackTrace: stackTrace,
      );
    }
  }
}

// Model classes for supervisor dashboard entities
class SupervisorInfoModel extends SupervisorInfoEntity {
  SupervisorInfoModel({super.id, super.phone, super.email});

  factory SupervisorInfoModel.fromJson(Map<String, dynamic> json) {
    return SupervisorInfoModel(
      id: json['id'],
      phone: json['phone'],
      email: json['email'],
    );
  }
}

class SupervisorTodayStatsModel extends SupervisorTodayStatsEntity {
  SupervisorTodayStatsModel({required super.orders, required super.sales});

  factory SupervisorTodayStatsModel.fromJson(Map<String, dynamic> json) {
    return SupervisorTodayStatsModel(
      orders: SupervisorOrderStatsModel.fromJson(json['orders']),
      sales: SupervisorSalesStatsModel.fromJson(json['sales']),
    );
  }
}

class SupervisorOrderStatsModel extends SupervisorOrderStatsEntity {
  SupervisorOrderStatsModel({
    required super.total,
    required super.pending,
    required super.confirmed,
    required super.outForDelivery,
    required super.delivered,
    required super.cancelled,
    required super.failed,
    required super.totalRevenue,
  });

  factory SupervisorOrderStatsModel.fromJson(Map<String, dynamic> json) {
    return SupervisorOrderStatsModel(
      total: json['total'] ?? 0,
      pending: json['pending'] ?? 0,
      confirmed: json['confirmed'] ?? 0,
      outForDelivery: json['outForDelivery'] ?? 0,
      delivered: json['delivered'] ?? 0,
      cancelled: json['cancelled'] ?? 0,
      failed: json['failed'] ?? 0,
      totalRevenue: (json['totalRevenue'] ?? 0).toDouble(),
    );
  }
}

class SupervisorSalesStatsModel extends SupervisorSalesStatsEntity {
  SupervisorSalesStatsModel({
    required super.itemsSold,
    required super.totalValue,
  });

  factory SupervisorSalesStatsModel.fromJson(Map<String, dynamic> json) {
    return SupervisorSalesStatsModel(
      itemsSold: json['itemsSold'] ?? 0,
      totalValue: (json['totalValue'] ?? 0).toDouble(),
    );
  }
}

class AvailableAgentModel extends AvailableAgentEntity {
  AvailableAgentModel({
    required super.id,
    required super.phone,
    super.email,
    required super.agentMetadata,
  });

  factory AvailableAgentModel.fromJson(Map<String, dynamic> json) {
    return AvailableAgentModel(
      id: json['id'] ?? json['_id'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'],
      agentMetadata: AgentMetadataModel.fromJson(json['agentMetadata'] ?? {}),
    );
  }
}

class AgentMetadataModel extends AgentMetadataEntity {
  AgentMetadataModel({
    required super.rating,
    super.vehicle,
    required super.isOnDuty,
  });

  factory AgentMetadataModel.fromJson(Map<String, dynamic> json) {
    return AgentMetadataModel(
      rating: (json['rating'] ?? 0).toDouble(),
      vehicle: json['vehicle'] != null
          ? VehicleModel.fromJson(json['vehicle'])
          : null,
      isOnDuty: json['isOnDuty'] ?? false,
    );
  }
}

class VehicleModel extends VehicleEntity {
  VehicleModel({required super.type, super.plateNumber});

  factory VehicleModel.fromJson(Map<String, dynamic> json) {
    return VehicleModel(
      type: json['type'] ?? '',
      plateNumber: json['plateNumber'],
    );
  }
}

class TodayOrderModel extends TodayOrderEntity {
  TodayOrderModel({
    required super.id,
    required super.status,
    required super.totalAmount,
    required super.customer,
    required super.deliveryAddress,
    required super.createdAt,
  });

  factory TodayOrderModel.fromJson(Map<String, dynamic> json) {
    return TodayOrderModel(
      id: json['id'] ?? json['_id'] ?? '',
      status: json['status'] ?? '',
      totalAmount: (json['totalAmount'] ?? 0).toDouble(),
      customer: CustomerModel.fromJson(json['customer'] ?? {}),
      deliveryAddress: json['deliveryAddress'] ?? '',
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

class TodayTopProductModel extends TodayTopProductEntity {
  TodayTopProductModel({
    required super.id,
    required super.totalQuantity,
    required super.productDetails,
  });

  factory TodayTopProductModel.fromJson(Map<String, dynamic> json) {
    return TodayTopProductModel(
      id: ProductIdModel.fromJson(json['id'] ?? {}),
      totalQuantity: json['totalQuantity'] ?? 0,
      productDetails: ProductDetailsModel.fromJson(
        json['productDetails'] ?? {},
      ),
    );
  }
}

class ProductIdModel extends ProductIdEntity {
  ProductIdModel({required super.itemId, required super.itemType});

  factory ProductIdModel.fromJson(Map<String, dynamic> json) {
    return ProductIdModel(
      itemId: json['itemId'] ?? '',
      itemType: json['itemType'] ?? '',
    );
  }
}

class ProductDetailsModel extends ProductDetailsEntity {
  ProductDetailsModel({super.name, super.type, super.description});

  factory ProductDetailsModel.fromJson(Map<String, dynamic> json) {
    return ProductDetailsModel(
      name: json['name'],
      type: json['type'],
      description: json['description'],
    );
  }
}

class SupervisorRestrictionsModel extends SupervisorRestrictionsEntity {
  SupervisorRestrictionsModel({
    required super.dataScope,
    required super.canAssignOrders,
    required super.canEditInventory,
    required super.canSeeCostData,
  });

  factory SupervisorRestrictionsModel.fromJson(Map<String, dynamic> json) {
    return SupervisorRestrictionsModel(
      dataScope: json['dataScope'] ?? '',
      canAssignOrders: json['canAssignOrders'] ?? false,
      canEditInventory: json['canEditInventory'] ?? false,
      canSeeCostData: json['canSeeCostData'] ?? false,
    );
  }
}

class CustomerModel extends CustomerEntity {
  CustomerModel({required super.id, required super.phone});

  factory CustomerModel.fromJson(Map<String, dynamic> json) {
    return CustomerModel(
      id: json['id'] ?? json['_id'] ?? '',
      phone: json['phone'] ?? '',
    );
  }
}
