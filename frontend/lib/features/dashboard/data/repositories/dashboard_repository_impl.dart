import 'package:fpdart/fpdart.dart';
import 'package:frontend/core/enums/dashboard_report_type.dart';
import 'package:frontend/features/dashboard/data/mappers/admin_dashboard_report_mapper.dart';
import 'package:frontend/features/dashboard/domain/entities/dashboard_report_entity.dart';

import '../../../../../core/errors/app_failure.dart';
import '../../domain/entities/admin_dashboard_entity.dart';
import '../../domain/entities/agent_dashboard_entity.dart';
import '../../domain/entities/customer_dashboard_entity.dart';
import '../../domain/entities/supervisor_dashboard_entity.dart';
import '../../domain/repositories/dashboard_reposiory.dart';
import '../mappers/admin_dashboard_mapper.dart';
import '../mappers/agent_dashboard_mapper.dart';
import '../mappers/customer_dashboard_mapper.dart';
import '../mappers/supervisor_dashboard_mapper.dart';
import '../source/remote/dashboard_remote_datasource.dart';

class DashboardRepositoryImpl implements DashboardRepository {
  final DashboardRemoteDataSource dashboardRemoteDataSource;

  const DashboardRepositoryImpl({required this.dashboardRemoteDataSource});

  @override
  FutureEitherFailOr<AdminDashboardEntity> getAdminDashboard() async {
    final response = await dashboardRemoteDataSource.getAdminDashboard();
    return response.fold((failure) => left(failure), (dashboardModel) {
      final dashboardEntity = AdminDashboardMapper.toEntity(dashboardModel);
      return right(dashboardEntity);
    });
  }

  @override
  FutureEitherFailOr<CustomerDashboardEntity> getCustomerDashboard() async {
    final response = await dashboardRemoteDataSource.getCustomerDashboard();
    return response.fold((failure) => left(failure), (dashboardModel) {
      final dashboardEntity = CustomerDashboardMapper.toEntity(dashboardModel);
      return right(dashboardEntity);
    });
  }

  @override
  FutureEitherFailOr<AgentDashboardEntity> getAgentDashboard() async {
    final response = await dashboardRemoteDataSource.getAgentDashboard();
    return response.fold((failure) => left(failure), (dashboardModel) {
      final dashboardEntity = AgentDashboardMapper.toEntity(dashboardModel);
      return right(dashboardEntity);
    });
  }

  @override
  FutureEitherFailOr<SupervisorDashboardEntity> getSupervisorDashboard() async {
    final response = await dashboardRemoteDataSource.getSupervisorDashboard();
    return response.fold((failure) => left(failure), (dashboardModel) {
      final dashboardEntity = SupervisorDashboardMapper.toEntity(
        dashboardModel,
      );
      return right(dashboardEntity);
    });
  }

  @override
  FutureEitherFailOr<AdminDashboardReportEntity> getAdminDashboardReport({
    required DashboardReportType reportType,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final response = await dashboardRemoteDataSource.getAdminDashboardReport(
      reportType: reportType,
      startDate: startDate,
      endDate: endDate,
    );
    return response.fold((failure) => left(failure), (dashboardModel) {
      final dashboardEntity = AdminDashboardReportMapper.toEntity(
        dashboardModel,
      );
      return right(dashboardEntity);
    });
  }
}
