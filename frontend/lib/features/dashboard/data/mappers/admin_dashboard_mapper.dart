// admin_dashboard_mapper.dart
import '../models/admin_dashboard_model.dart';
import '../../domain/entities/admin_dashboard_entity.dart';

class AdminDashboardMapper {
  static AdminDashboardEntity toEntity(AdminDashboardModel model) {
    return AdminDashboardEntity(
      salesOverview: model.salesOverview,
      inventoryStatus: model.inventoryStatus,
      agentPerformance: model.agentPerformance,
      financialOverview: model.financialOverview,
      systemHealth: model.systemHealth,
    );
  }

  static AdminDashboardModel toModel(AdminDashboardEntity entity) {
    return AdminDashboardModel(
      salesOverview: entity.salesOverview,
      inventoryStatus: entity.inventoryStatus,
      agentPerformance: entity.agentPerformance,
      financialOverview: entity.financialOverview,
      systemHealth: entity.systemHealth,
    );
  }

  static List<AdminDashboardEntity> toEntityList(
    List<AdminDashboardModel> models,
  ) {
    return models.map((model) => toEntity(model)).toList();
  }

  static List<AdminDashboardModel> toModelList(
    List<AdminDashboardEntity> entities,
  ) {
    return entities.map((entity) => toModel(entity)).toList();
  }
}
