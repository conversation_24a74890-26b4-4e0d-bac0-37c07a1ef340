// data/mappers/customer_dashboard_mapper.dart
import 'package:frontend/features/dashboard/data/models/customer_dashboard_model.dart';
import 'package:frontend/features/dashboard/domain/entities/customer_dashboard_entity.dart';

class CustomerDashboardMapper {
  static CustomerDashboardEntity toEntity(CustomerDashboardModel model) {
    return CustomerDashboardEntity(
      recentOrders: model.recentOrders,
      orderStats: model.orderStats,
      favoriteItems: model.favoriteItems,
    );
  }

  static CustomerDashboardModel toModel(CustomerDashboardEntity entity) {
    return CustomerDashboardModel(
      recentOrders: entity.recentOrders,
      orderStats: entity.orderStats,
      favoriteItems: entity.favoriteItems,
    );
  }

  static List<CustomerDashboardEntity> toEntityList(
    List<CustomerDashboardModel> models,
  ) {
    return models.map((model) => toEntity(model)).toList();
  }

  static List<CustomerDashboardModel> toModelList(
    List<CustomerDashboardEntity> entities,
  ) {
    return entities.map((entity) => toModel(entity)).toList();
  }
}
