import '../../domain/entities/dashboard_report_entity.dart';
import '../models/admin_dashboard_report_model.dart';

class AdminDashboardReportMapper {
  static AdminDashboardReportEntity toEntity(AdminDashboardReportModel model) {
    return AdminDashboardReportEntity(
      reportType: model.reportType,
      dateRange: model.dateRange,
      report: model.report,
    );
  }

  static AdminDashboardReportModel toModel(AdminDashboardReportEntity entity) {
    return AdminDashboardReportModel(
      reportType: entity.reportType,
      dateRange: entity.dateRange,
      report: entity.report,
    );
  }

  static List<AdminDashboardReportEntity> toEntityList(
    List<AdminDashboardReportModel> models,
  ) {
    return models.map((model) => toEntity(model)).toList();
  }

  static List<AdminDashboardReportModel> toModelList(
    List<AdminDashboardReportEntity> entities,
  ) {
    return entities.map((entity) => toModel(entity)).toList();
  }
}
