import '../../domain/entities/agent_dashboard_entity.dart';
import '../models/agent_dashboard_model.dart';

class AgentDashboardMapper {
  static AgentDashboardEntity toEntity(AgentDashboardModel model) {
    return AgentDashboardEntity(
      agentInfo: model.agentInfo,
      orderStats: model.orderStats,
      earnings: model.earnings,
      recentDeliveries: model.recentDeliveries,
      performanceMetrics: model.performanceMetrics,
      lastUpdated: model.lastUpdated,
    );
  }

  static AgentDashboardModel toModel(AgentDashboardEntity entity) {
    return AgentDashboardModel(
      agentInfo: entity.agentInfo,
      orderStats: entity.orderStats,
      earnings: entity.earnings,
      recentDeliveries: entity.recentDeliveries,
      performanceMetrics: entity.performanceMetrics,
      lastUpdated: entity.lastUpdated,
    );
  }

  static List<AgentDashboardEntity> toEntityList(
    List<AgentDashboardModel> models,
  ) {
    return models.map((model) => toEntity(model)).toList();
  }

  static List<AgentDashboardModel> toModelList(
    List<AgentDashboardEntity> entities,
  ) {
    return entities.map((entity) => toModel(entity)).toList();
  }
}
