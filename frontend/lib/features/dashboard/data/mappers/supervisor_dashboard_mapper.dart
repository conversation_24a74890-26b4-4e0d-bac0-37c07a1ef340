// supervisor_dashboard_mapper.dart
import '../../domain/entities/supervisor_dashboard_entity.dart';
import '../models/supervisor_dashboard_model.dart';

class SupervisorDashboardMapper {
  static SupervisorDashboardEntity toEntity(SupervisorDashboardModel model) {
    return SupervisorDashboardEntity(
      supervisorInfo: model.supervisorInfo,
      todayStats: model.todayStats,
      availableAgents: model.availableAgents,
      todayOrders: model.todayOrders,
      todayTopProducts: model.todayTopProducts,
      restrictions: model.restrictions,
      lastUpdated: model.lastUpdated,
    );
  }

  static SupervisorDashboardModel toModel(SupervisorDashboardEntity entity) {
    return SupervisorDashboardModel(
      supervisorInfo: entity.supervisorInfo,
      todayStats: entity.todayStats,
      availableAgents: entity.availableAgents,
      todayOrders: entity.todayOrders,
      todayTopProducts: entity.todayTopProducts,
      restrictions: entity.restrictions,
      lastUpdated: entity.lastUpdated,
    );
  }
}
