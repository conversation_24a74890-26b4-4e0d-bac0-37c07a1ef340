part of 'dashboard_bloc.dart';

sealed class DashboardEvent extends Equatable {
  const DashboardEvent();

  @override
  List<Object?> get props => [];
}

final class GetAdminDashboardEvent extends DashboardEvent {}

final class GetCustomerDashboardEvent extends DashboardEvent {}

final class GetAgentDashboardEvent extends DashboardEvent {}

final class GetSupervisorDashboardEvent extends DashboardEvent {}

final class GetAdminDashboardReportEvent extends DashboardEvent {
  final DashboardReportType reportType;
  final DateTime startDate;
  final DateTime endDate;

  const GetAdminDashboardReportEvent({
    required this.reportType,
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object?> get props => [reportType, startDate, endDate];
}
