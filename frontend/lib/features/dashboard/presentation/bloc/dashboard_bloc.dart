import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/enums/dashboard_report_type.dart';
import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../../../../core/utils/helpers/bloc_helper.dart';
import '../../domain/entities/admin_dashboard_entity.dart';
import '../../domain/entities/agent_dashboard_entity.dart';
import '../../domain/entities/customer_dashboard_entity.dart';
import '../../domain/entities/dashboard_report_entity.dart';
import '../../domain/entities/supervisor_dashboard_entity.dart';
import '../../domain/params/get_admin_dashboard_report_params.dart';
import '../../domain/usecases/get_admin_dashboard_report.dart';
import '../../domain/usecases/get_admin_dashboard_usecase.dart';
import '../../domain/usecases/get_agent_dashboard_usecase.dart';
import '../../domain/usecases/get_customer_dashboard_usecase.dart';
import '../../domain/usecases/get_supervisor_dashboard_usecase.dart';

part 'dashboard_event.dart';
part 'dashboard_state.dart';

class DashboardBloc extends Bloc<DashboardEvent, DashboardState> {
  final GetAdminDashboardUseCase getAdminDashboardUseCase;
  final GetCustomerDashboardUseCase getCustomerDashboardUseCase;
  final GetAgentDashboardUseCase getAgentDashboardUseCase;
  final GetSupervisorDashboardUseCase getSupervisorDashboardUseCase;
  final GetAdminDashboardReportUseCase getAdminDashboardReportUseCase;

  DashboardBloc({
    required this.getAdminDashboardUseCase,
    required this.getCustomerDashboardUseCase,
    required this.getAgentDashboardUseCase,
    required this.getSupervisorDashboardUseCase,
    required this.getAdminDashboardReportUseCase,
  }) : super(DashboardInitial()) {
    on<GetAdminDashboardEvent>(_onGetAdminDashboard);
    on<GetCustomerDashboardEvent>(_onGetCustomerDashboard);
    on<GetAgentDashboardEvent>(_onGetAgentDashboard);
    on<GetSupervisorDashboardEvent>(_onGetSupervisorDashboard);
    on<GetAdminDashboardReportEvent>(_onGetAdminDashboardReport);
  }

  AdminDashboardEntity? _adminDashboard;
  AdminDashboardEntity? get adminDashboard => _adminDashboard;

  CustomerDashboardEntity? _customerDashboard;
  CustomerDashboardEntity? get customerDashboard => _customerDashboard;

  AgentDashboardEntity? _agentDashboard;
  AgentDashboardEntity? get agentDashboard => _agentDashboard;

  SupervisorDashboardEntity? _supervisorDashboard;
  SupervisorDashboardEntity? get supervisorDashboard => _supervisorDashboard;

  AdminDashboardReportEntity? _adminDashboardReport;
  AdminDashboardReportEntity? get adminDashboardReport => _adminDashboardReport;
  AdminDashboardReportEntity? _ordersReport;
  AdminDashboardReportEntity? get ordersReport => _ordersReport;
  AdminDashboardReportEntity? _revenueReport;
  AdminDashboardReportEntity? get revenueReport => _revenueReport;
  AdminDashboardReportEntity? _inventoryReport;
  AdminDashboardReportEntity? get inventoryReport => _inventoryReport;
  AdminDashboardReportEntity? _agentsReport;
  AdminDashboardReportEntity? get agentsReport => _agentsReport;

  Future<void> _onGetAdminDashboard(
    GetAdminDashboardEvent event,
    Emitter<DashboardState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<AdminDashboardEntity, DashboardState>(
      emit: emit,
      loadingState: GetAdminDashboardLoading(),
      callUseCase: getAdminDashboardUseCase(params: NoParams()),
      onSuccess: (dashboard) {
        _adminDashboard = dashboard;
        return GetAdminDashboardSuccess(dashboard: dashboard);
      },
      onFailure: (failure) => GetAdminDashboardFailure(appFailure: failure),
    );
  }

  Future<void> _onGetCustomerDashboard(
    GetCustomerDashboardEvent event,
    Emitter<DashboardState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<
      CustomerDashboardEntity,
      DashboardState
    >(
      emit: emit,
      loadingState: GetCustomerDashboardLoading(),
      callUseCase: getCustomerDashboardUseCase(params: NoParams()),
      onSuccess: (dashboard) {
        _customerDashboard = dashboard;
        return GetCustomerDashboardSuccess(dashboard: dashboard);
      },
      onFailure: (failure) => GetCustomerDashboardFailure(appFailure: failure),
    );
  }

  Future<void> _onGetAgentDashboard(
    GetAgentDashboardEvent event,
    Emitter<DashboardState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<AgentDashboardEntity, DashboardState>(
      emit: emit,
      loadingState: GetAgentDashboardLoading(),
      callUseCase: getAgentDashboardUseCase(params: NoParams()),
      onSuccess: (dashboard) {
        _agentDashboard = dashboard;
        return GetAgentDashboardSuccess(dashboard: dashboard);
      },
      onFailure: (failure) => GetAgentDashboardFailure(appFailure: failure),
    );
  }

  Future<void> _onGetSupervisorDashboard(
    GetSupervisorDashboardEvent event,
    Emitter<DashboardState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<
      SupervisorDashboardEntity,
      DashboardState
    >(
      emit: emit,
      loadingState: GetSupervisorDashboardLoading(),
      callUseCase: getSupervisorDashboardUseCase(params: NoParams()),
      onSuccess: (dashboard) {
        _supervisorDashboard = dashboard;
        return GetSupervisorDashboardSuccess(dashboard: dashboard);
      },
      onFailure: (failure) =>
          GetSupervisorDashboardFailure(appFailure: failure),
    );
  }

  Future<void> _onGetAdminDashboardReport(
    GetAdminDashboardReportEvent event,
    Emitter<DashboardState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<
      AdminDashboardReportEntity,
      DashboardState
    >(
      emit: emit,
      loadingState: GetAdminDashboardReportLoading(),
      callUseCase: getAdminDashboardReportUseCase(
        params: GetAdminDashboardParams(
          startDate: event.startDate,
          endDate: event.endDate,
          reportType: event.reportType,
        ),
      ),
      onSuccess: (report) {
        _adminDashboardReport = report;
        switch (report.reportType) {
          case DashboardReportType.orders:
            _ordersReport = report;
            break;
          case DashboardReportType.revenue:
            _revenueReport = report;
            break;
          case DashboardReportType.inventory:
            _inventoryReport = report;
            break;
          case DashboardReportType.agents:
            _agentsReport = report;
            break;
        }
        return GetAdminDashboardReportSuccess(report: report);
      },
      onFailure: (failure) =>
          GetAdminDashboardReportFailure(appFailure: failure),
    );
  }

  @override
  Future<void> close() async {
    _adminDashboard = null;
    _customerDashboard = null;
    _agentDashboard = null;
    _supervisorDashboard = null;
    _adminDashboardReport = null;
    _ordersReport = null;
    _revenueReport = null;
    _inventoryReport = null;
    _agentsReport = null;
    await super.close();
  }
}
