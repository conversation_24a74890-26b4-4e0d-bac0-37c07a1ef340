import 'package:fpdart/fpdart.dart';

import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../entities/admin_dashboard_entity.dart';
import '../repositories/dashboard_reposiory.dart';

class GetAdminDashboardUseCase
    implements UseCase<AdminDashboardEntity, NoParams> {
  final DashboardRepository repository;

  const GetAdminDashboardUseCase({required this.repository});

  @override
  Future<Either<AppFailure, AdminDashboardEntity>> call({
    required NoParams params,
  }) async {
    return await repository.getAdminDashboard();
  }
}
