// get_supervisor_dashboard_usecase.dart
import 'package:fpdart/fpdart.dart';

import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../entities/supervisor_dashboard_entity.dart';
import '../repositories/dashboard_reposiory.dart';

class GetSupervisorDashboardUseCase implements UseCase<SupervisorDashboardEntity, NoParams> {
  final DashboardRepository repository;

  const GetSupervisorDashboardUseCase({required this.repository});

  @override
  Future<Either<AppFailure, SupervisorDashboardEntity>> call({required NoParams params}) async {
    return await repository.getSupervisorDashboard();
  }
}
