import 'package:fpdart/fpdart.dart';

import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../entities/dashboard_report_entity.dart';
import '../params/get_admin_dashboard_report_params.dart';
import '../repositories/dashboard_reposiory.dart';

class GetAdminDashboardReportUseCase
    implements UseCase<AdminDashboardReportEntity, GetAdminDashboardParams> {
  final DashboardRepository repository;

  const GetAdminDashboardReportUseCase({required this.repository});

  @override
  Future<Either<AppFailure, AdminDashboardReportEntity>> call({
    required GetAdminDashboardParams params,
  }) async {
    return await repository.getAdminDashboardReport(
      reportType: params.reportType,
      startDate: params.startDate,
      endDate: params.endDate,
    );
  }
}
