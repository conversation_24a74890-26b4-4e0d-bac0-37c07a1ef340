import 'package:fpdart/fpdart.dart';

import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../entities/agent_dashboard_entity.dart';
import '../repositories/dashboard_reposiory.dart';

class GetAgentDashboardUseCase
    implements UseCase<AgentDashboardEntity, NoParams> {
  final DashboardRepository repository;

  const GetAgentDashboardUseCase({required this.repository});

  @override
  Future<Either<AppFailure, AgentDashboardEntity>> call({
    required NoParams params,
  }) async {
    return await repository.getAgentDashboard();
  }
}
