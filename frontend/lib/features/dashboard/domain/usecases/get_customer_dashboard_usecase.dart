import 'package:fpdart/fpdart.dart';

import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../entities/customer_dashboard_entity.dart';
import '../repositories/dashboard_reposiory.dart';

class GetCustomerDashboardUseCase
    implements UseCase<CustomerDashboardEntity, NoParams> {
  final DashboardRepository repository;

  const GetCustomerDashboardUseCase({required this.repository});

  @override
  Future<Either<AppFailure, CustomerDashboardEntity>> call({
    required NoParams params,
  }) async {
    return await repository.getCustomerDashboard();
  }
}
