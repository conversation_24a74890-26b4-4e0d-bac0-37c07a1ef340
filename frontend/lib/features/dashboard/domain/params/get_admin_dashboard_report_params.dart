// get_admin_dashboard_params.dart
import 'package:equatable/equatable.dart';

import '../../../../core/enums/dashboard_report_type.dart';

class GetAdminDashboardParams extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final DashboardReportType reportType;

  const GetAdminDashboardParams({
    required this.startDate,
    required this.endDate,
    required this.reportType,
  });

  Map<String, dynamic> toJson() {
    return {
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'reportType': reportType.name,
    };
  }

  @override
  List<Object?> get props => [startDate, endDate, reportType];
}
