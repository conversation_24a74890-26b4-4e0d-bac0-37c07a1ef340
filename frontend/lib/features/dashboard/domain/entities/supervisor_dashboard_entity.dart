// supervisor_dashboard_entity.dart
import 'admin_dashboard_entity.dart';

class SupervisorDashboardEntity {
  final SupervisorInfoEntity supervisorInfo;
  final SupervisorTodayStatsEntity todayStats;
  final List<AvailableAgentEntity> availableAgents;
  final List<TodayOrderEntity> todayOrders;
  final List<TodayTopProductEntity> todayTopProducts;
  final SupervisorRestrictionsEntity restrictions;
  final DateTime lastUpdated;

  SupervisorDashboardEntity({
    required this.supervisorInfo,
    required this.todayStats,
    required this.availableAgents,
    required this.todayOrders,
    required this.todayTopProducts,
    required this.restrictions,
    required this.lastUpdated,
  });
}

class SupervisorInfoEntity {
  final String? id;
  final String? phone;
  final String? email;

  SupervisorInfoEntity({this.id, this.phone, this.email});
}

class SupervisorTodayStatsEntity {
  final SupervisorOrderStatsEntity orders;
  final SupervisorSalesStatsEntity sales;

  SupervisorTodayStatsEntity({required this.orders, required this.sales});
}

class SupervisorOrderStatsEntity {
  final int total;
  final int pending;
  final int confirmed;
  final int outForDelivery;
  final int delivered;
  final int cancelled;
  final int failed;
  final double totalRevenue;

  SupervisorOrderStatsEntity({
    required this.total,
    required this.pending,
    required this.confirmed,
    required this.outForDelivery,
    required this.delivered,
    required this.cancelled,
    required this.failed,
    required this.totalRevenue,
  });
}

class SupervisorSalesStatsEntity {
  final int itemsSold;
  final double totalValue;

  SupervisorSalesStatsEntity({
    required this.itemsSold,
    required this.totalValue,
  });
}

class AvailableAgentEntity {
  final String id;
  final String phone;
  final String? email;
  final AgentMetadataEntity agentMetadata;

  AvailableAgentEntity({
    required this.id,
    required this.phone,
    this.email,
    required this.agentMetadata,
  });
}

class AgentMetadataEntity {
  final double rating;
  final VehicleEntity? vehicle;
  final bool isOnDuty;

  AgentMetadataEntity({
    required this.rating,
    this.vehicle,
    required this.isOnDuty,
  });
}

class VehicleEntity {
  final String type;
  final String? plateNumber;

  VehicleEntity({required this.type, this.plateNumber});
}

class TodayOrderEntity {
  final String id;
  final String status;
  final double totalAmount;
  final CustomerEntity customer;
  final String deliveryAddress;
  final DateTime createdAt;

  TodayOrderEntity({
    required this.id,
    required this.status,
    required this.totalAmount,
    required this.customer,
    required this.deliveryAddress,
    required this.createdAt,
  });
}

class TodayTopProductEntity {
  final ProductIdEntity id;
  final int totalQuantity;
  final ProductDetailsEntity productDetails;

  TodayTopProductEntity({
    required this.id,
    required this.totalQuantity,
    required this.productDetails,
  });
}

class ProductIdEntity {
  final String itemId;
  final String itemType;

  ProductIdEntity({required this.itemId, required this.itemType});
}

class ProductDetailsEntity {
  final String? name;
  final String? type;
  final String? description;

  ProductDetailsEntity({this.name, this.type, this.description});
}

class SupervisorRestrictionsEntity {
  final String dataScope;
  final bool canAssignOrders;
  final bool canEditInventory;
  final bool canSeeCostData;

  SupervisorRestrictionsEntity({
    required this.dataScope,
    required this.canAssignOrders,
    required this.canEditInventory,
    required this.canSeeCostData,
  });
}
