/*

 {
        "agentInfo": {
            "id": "6852ce18cc36bca49308b922",
            "phone": "+252619124489",
            "email": "<EMAIL>",
            "isOnDuty": false,
            "rating": 0
        },
        "orderStats": {
            "total": 1,
            "completed": 1,
            "pending": 0,
            "completionRate": 100
        },
        "earnings": {
            "_id": null,
            "totalEarnings": 91.98,
            "totalOrders": 1,
            "avgOrderValue": 91.98
        },
        "recentDeliveries": [
            {
                "_id": "6852cd462893bded98587f51",
                "customer": {
                    "_id": "684f671dabea9ca7a8c7f3e9",
                    "phone": "+252613656021"
                },
                "totalAmount": 91.98,
                "deliveryAddress": "123 Main Street, Hargeisa, Somaliland",
                "deliveredAt": "2025-06-18T14:36:01.262Z"
            }
        ],
        "performanceMetrics": {
            "monthlyStats": [
                {
                    "_id": {
                        "year": 2025,
                        "month": 6
                    },
                    "totalOrders": 1,
                    "completedOrders": 1,
                    "totalRevenue": 91.98
                }
            ],
            "overallMetrics": {
                "totalOrders": 1,
                "totalCompleted": 1,
                "completionRate": 100,
                "totalRevenue": 91.98,
                "avgOrderValue": 91.98
            }
        },
        "lastUpdated": "2025-06-18T17:40:16.456Z"
    }

*/

class AgentDashboardEntity {
  final AgentInfoEntity agentInfo;
  final OrderStatsEntity orderStats;
  final EarningsEntity earnings;
  final List<RecentDeliveryEntity> recentDeliveries;
  final PerformanceMetricsEntity performanceMetrics;
  final DateTime lastUpdated;

  AgentDashboardEntity({
    required this.agentInfo,
    required this.orderStats,
    required this.earnings,
    required this.recentDeliveries,
    required this.performanceMetrics,
    required this.lastUpdated,
  });
}

class AgentInfoEntity {
  final String id;
  final String phone;
  final String? email;
  final bool isOnDuty;
  final double rating;

  AgentInfoEntity({
    required this.id,
    required this.phone,
    this.email,
    required this.isOnDuty,
    required this.rating,
  });
}

class OrderStatsEntity {
  final int total;
  final int pending;
  final int outForDelivery;
  final int delivered;
  final int failed;
  final int cancelled;

  OrderStatsEntity({
    required this.total,
    required this.pending,
    required this.outForDelivery,
    required this.delivered,
    required this.failed,
    required this.cancelled,
  });
}

class EarningsEntity {
  final double totalEarnings;
  final int totalOrders;
  final double avgOrderValue;

  EarningsEntity({
    required this.totalEarnings,
    required this.totalOrders,
    required this.avgOrderValue,
  });
}

class RecentDeliveryEntity {
  final String id;
  final CustomerInfoEntity customer;
  final double totalAmount;
  final String deliveryAddress;
  final DateTime deliveredAt;

  RecentDeliveryEntity({
    required this.id,
    required this.customer,
    required this.totalAmount,
    required this.deliveryAddress,
    required this.deliveredAt,
  });
}

class PerformanceMetricsEntity {
  final List<MonthlyStatsEntity> monthlyStats;
  final OverallMetricsEntity overallMetrics;

  PerformanceMetricsEntity({
    required this.monthlyStats,
    required this.overallMetrics,
  });
}

class MonthlyStatsEntity {
  final MonthYearEntity id;
  final int totalOrders;
  final int completedOrders;
  final double totalRevenue;

  MonthlyStatsEntity({
    required this.id,
    required this.totalOrders,
    required this.completedOrders,
    required this.totalRevenue,
  });
}

class MonthYearEntity {
  final int year;
  final int month;

  MonthYearEntity({required this.year, required this.month});
}

class OverallMetricsEntity {
  final int totalOrders;
  final int totalCompleted;
  final double completionRate;
  final double totalRevenue;
  final double avgOrderValue;

  OverallMetricsEntity({
    required this.totalOrders,
    required this.totalCompleted,
    required this.completionRate,
    required this.totalRevenue,
    required this.avgOrderValue,
  });
}

class AssignedOrderEntity {
  final String id;
  final String status;
  final double totalAmount;
  final CustomerInfoEntity customer;
  final DateTime createdAt;

  AssignedOrderEntity({
    required this.id,
    required this.status,
    required this.totalAmount,
    required this.customer,
    required this.createdAt,
  });
}

class CustomerInfoEntity {
  final String phone;
  final String address;

  CustomerInfoEntity({required this.phone, required this.address});
}

class DeliveryStatsEntity {
  final int total;
  final int delivered;
  final int pending;
  final int failed;
  final double? avgDeliveryTime; // in minutes

  DeliveryStatsEntity({
    required this.total,
    required this.delivered,
    required this.pending,
    required this.failed,
    this.avgDeliveryTime,
  });
}
