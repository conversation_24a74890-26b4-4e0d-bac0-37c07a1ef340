import '../../../../core/enums/dashboard_report_type.dart';
import 'admin_dashboard_entity.dart';

class DateRangeEntity {
  final DateTime startDate;
  final DateTime endDate;

  DateRangeEntity({required this.startDate, required this.endDate});
}

class AdminDashboardReportEntity {
  final DashboardReportType reportType;
  final DateRangeEntity dateRange;
  final dynamic report; // Will be one of the specific report types below

  AdminDashboardReportEntity({
    required this.reportType,
    required this.dateRange,
    required this.report,
  });
}

// Specific report types
class OrdersReportEntity {
  final SalesOverviewEntity salesOverview;

  OrdersReportEntity({required this.salesOverview});
}

class RevenueReportEntity {
  final FinancialOverviewEntity financialOverview;

  RevenueReportEntity({required this.financialOverview});
}

class InventoryReportEntity {
  final InventoryStatusEntity inventoryStatus;

  InventoryReportEntity({required this.inventoryStatus});
}

class AgentsReportEntity {
  final List<AgentPerformanceEntity> agentPerformance;

  AgentsReportEntity({required this.agentPerformance});
}
