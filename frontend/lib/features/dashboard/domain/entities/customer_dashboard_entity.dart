// domain/entities/customer_dashboard_entity.dart
class CustomerDashboardEntity {
  final List<RecentOrderEntity> recentOrders;
  final OrderStatsEntity orderStats;
  final List<FavoriteItemEntity> favoriteItems;

  CustomerDashboardEntity({
    required this.recentOrders,
    required this.orderStats,
    required this.favoriteItems,
  });
}

class OrderStatsEntity {
  final int total;
  final int pending;
  final int delivered;
  final int cancelled;

  OrderStatsEntity({
    required this.total,
    required this.pending,
    required this.delivered,
    required this.cancelled,
  });
}

class FavoriteItemEntity {
  final String id;
  final String name;
  final String type; // 'cylinder' | 'spare_part' | 'package'
  final int orderCount;

  FavoriteItemEntity({
    required this.id,
    required this.name,
    required this.type,
    required this.orderCount,
  });
}

class RecentOrderEntity {
  final String id;
  final String status;
  final double totalAmount;
  final DateTime createdAt;

  RecentOrderEntity({
    required this.id,
    required this.status,
    required this.totalAmount,
    required this.createdAt,
  });
}
