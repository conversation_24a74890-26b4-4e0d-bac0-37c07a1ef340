// admin_dashboard_entity.dart

class AdminDashboardEntity {
  final SalesOverviewEntity salesOverview;
  final InventoryStatusEntity inventoryStatus;
  final List<AgentPerformanceEntity> agentPerformance;
  final FinancialOverviewEntity financialOverview;
  final SystemHealthEntity systemHealth;

  AdminDashboardEntity({
    required this.salesOverview,
    required this.inventoryStatus,
    required this.agentPerformance,
    required this.financialOverview,
    required this.systemHealth,
  });
}

class SalesOverviewEntity {
  final int totalSales;
  final double totalRevenue;
  final double dailyAverage;
  final int weeklyTrend; // percentage change

  SalesOverviewEntity({
    required this.totalSales,
    required this.totalRevenue,
    required this.dailyAverage,
    required this.weeklyTrend,
  });
}

class InventoryStatusEntity {
  final int totalCylinders;
  final int availableCylinders;
  final int totalSpareParts;
  final int lowStockSpareParts;

  InventoryStatusEntity({
    required this.totalCylinders,
    required this.availableCylinders,
    required this.totalSpareParts,
    required this.lowStockSpareParts,
  });
}

class AgentPerformanceEntity {
  final String agentId;
  final String name;
  final int completedDeliveries;
  final double avgRating;
  final DateTime? lastActive;

  AgentPerformanceEntity({
    required this.agentId,
    required this.name,
    required this.completedDeliveries,
    required this.avgRating,
    this.lastActive,
  });
}

class FinancialOverviewEntity {
  final double totalRevenue;
  final double totalProfit;
  final double profitMargin;
  final List<MostProfitableItemEntity> mostProfitableItems;

  FinancialOverviewEntity({
    required this.totalRevenue,
    required this.totalProfit,
    required this.profitMargin,
    required this.mostProfitableItems,
  });
}

class MostProfitableItemEntity {
  final String id;
  final String name;
  final String type; // 'cylinder' | 'spare_part' | 'package'
  final double profit;

  MostProfitableItemEntity({
    required this.id,
    required this.name,
    required this.type,
    required this.profit,
  });
}

class SystemHealthEntity {
  final int totalUsers;
  final int activeUsers;
  final int ordersThisMonth;
  final double paymentSuccessRate;

  SystemHealthEntity({
    required this.totalUsers,
    required this.activeUsers,
    required this.ordersThisMonth,
    required this.paymentSuccessRate,
  });
}

// Legacy entities kept for backward compatibility with existing code
class CustomerEntity {
  final String id;
  final String phone;

  CustomerEntity({required this.id, required this.phone});
}
