import '../../domain/entities/cylinder_entity.dart';
import '../model/cylinder_model.dart';

class Cy<PERSON>erMapper {
  static CylinderEntity toEntity(CylinderModel model) {
    return CylinderEntity(
      id: model.id,
      type: model.type,
      material: model.material,
      price: model.price,
      cost: model.cost,
      imageUrl: model.imageUrl,
      description: model.description,
      stock: model.stock,
      reserved: model.reserved,
      sold: model.sold,
      minimumStockLevel: model.minimumStockLevel,
      status: model.status,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  static CylinderModel toModel(CylinderEntity entity) {
    return CylinderModel(
      id: entity.id,
      type: entity.type,
      material: entity.material,
      price: entity.price,
      cost: entity.cost,
      imageUrl: entity.imageUrl,
      description: entity.description,
      stock: entity.stock,
      reserved: entity.reserved,
      sold: entity.sold,
      minimumStockLevel: entity.minimumStockLevel,
      status: entity.status,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }

  static List<CylinderEntity> toEntityList(List<CylinderModel> models) {
    return models.map((model) => toEntity(model)).toList();
  }

  static List<CylinderModel> toModelList(List<CylinderEntity> entities) {
    return entities.map((entity) => toModel(entity)).toList();
  }
}
