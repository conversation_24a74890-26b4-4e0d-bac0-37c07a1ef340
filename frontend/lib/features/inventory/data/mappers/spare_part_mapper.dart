import '../../domain/entities/spare_part_entity.dart';
import '../model/spare_part_model.dart';

class SparePartMapper {
  static SparePartEntity toEntity(SparePartModel model) {
    return SparePartEntity(
      id: model.id,
      description: model.description,
      price: model.price,
      cost: model.cost,
      category: model.category,
      compatibleCylinderTypes: model.compatibleCylinderTypes,
      barcode: model.barcode,
      quantity: model.quantity,
      reserved: model.reserved,
      sold: model.sold,
      minimumStockLevel: model.minimumStockLevel,
      status: model.status,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      imageUrl: model.imageUrl,
    );
  }

  static SparePartModel toModel(SparePartEntity entity) {
    return SparePartModel(
      id: entity.id,
      description: entity.description,
      price: entity.price,
      cost: entity.cost,
      category: entity.category,
      compatibleCylinderTypes: entity.compatibleCylinderTypes,
      barcode: entity.barcode,
      quantity: entity.quantity,
      reserved: entity.reserved,
      sold: entity.sold,
      minimumStockLevel: entity.minimumStockLevel,
      status: entity.status,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      imageUrl: entity.imageUrl,
    );
  }

  static List<SparePartEntity> toEntityList(List<SparePartModel> models) {
    return models.map((model) => toEntity(model)).toList();
  }

  static List<SparePartModel> toModelList(List<SparePartEntity> entities) {
    return entities.map((entity) => toModel(entity)).toList();
  }
}
