import '../../domain/entities/package_entity.dart';
import '../../domain/entities/package_item_entity.dart';
import '../model/package_model.dart';
import '../model/package_item_model.dart';
import 'cylinder_mapper.dart';

class PackageMapper {
  static PackageEntity toEntity(PackageModel model) {
    return PackageEntity(
      id: model.id,
      name: model.name,
      description: model.description,
      // cylinderId: model.cylinderId,
      cylinder: model.cylinder,
      includedSpareParts: model.includedSpareParts,
      totalPrice: model.totalPrice,
      costPrice: model.costPrice,
      discount: model.discount,
      imageUrl: model.imageUrl,
      quantity: model.quantity,
      reserved: model.reserved,
      sold: model.sold,
      minimumStockLevel: model.minimumStockLevel,
      status: model.status,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  static PackageModel toModel(PackageEntity entity) {
    return PackageModel(
      id: entity.id,
      name: entity.name,
      description: entity.description,
      // cylinderId: entity.cylinderId,
      cylinder: entity.cylinder != null
          ? CylinderMapper.toModel(entity.cylinder!)
          : null,
      includedSpareParts: entity.includedSpareParts
          .map((item) => PackageItemMapper.toModel(item))
          .toList(),
      totalPrice: entity.totalPrice,
      costPrice: entity.costPrice,
      discount: entity.discount,
      imageUrl: entity.imageUrl,
      quantity: entity.quantity,
      reserved: entity.reserved,
      sold: entity.sold,
      minimumStockLevel: entity.minimumStockLevel,
      status: entity.status,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }

  static List<PackageEntity> toEntityList(List<PackageModel> models) {
    return models.map((model) => toEntity(model)).toList();
  }

  static List<PackageModel> toModelList(List<PackageEntity> entities) {
    return entities.map((entity) => toModel(entity)).toList();
  }
}

class PackageItemMapper {
  static PackageItemEntity toEntity(PackageItemModel model) {
    return PackageItemEntity(
      sparePartId: model.sparePartId,
      name: model.name,
      quantity: model.quantity,
      unitPrice: model.unitPrice,
      totalPrice: model.totalPrice,
    );
  }

  static PackageItemModel toModel(PackageItemEntity entity) {
    return PackageItemModel(
      sparePartId: entity.sparePartId,
      name: entity.name,
      quantity: entity.quantity,
      unitPrice: entity.unitPrice,
      totalPrice: entity.totalPrice,
    );
  }

  static List<PackageItemEntity> toEntityList(List<PackageItemModel> models) {
    return models.map((model) => toEntity(model)).toList();
  }

  static List<PackageItemModel> toModelList(List<PackageItemEntity> entities) {
    return entities.map((entity) => toModel(entity)).toList();
  }
}
