import '../../../../core/enums/parse_failure_type.dart';
import '../../../../core/errors/app_failure.dart';
import '../../../../core/enums/cylinder_type.dart';
import '../../../../core/enums/cylinder_material.dart';
import '../../../../core/enums/cylinder_status.dart';
import '../../domain/entities/cylinder_entity.dart';

class CylinderModel extends CylinderEntity {
  const CylinderModel({
    required super.id,
    required super.type,
    required super.material,
    required super.price,
    required super.cost,
    required super.imageUrl,
    required super.description,
    required super.stock,
    required super.reserved,
    required super.sold,
    required super.minimumStockLevel,
    required super.status,
    required super.createdAt,
    required super.updatedAt,
  });

  factory CylinderModel.fromJson(Map<String, dynamic> json) {
    try {
      return CylinderModel(
        id: json['_id'] ?? '',
        type: cylinderTypeFromString(json['type'] ?? ''),
        material: cylinderMaterialFromString(json['material'] ?? ''),
        price: (json['price'] as num).toDouble(),
        cost: (json['cost'] as num).toDouble(),
        imageUrl: json['currentImageUrl'] ?? json['imageUrl'] ?? '',
        description: json['description'] ?? '',
        stock: json['quantity'] ?? json['stock'] ?? 0,
        reserved: json['reserved'] ?? 0,
        sold: json['sold'] ?? 0,
        minimumStockLevel: json['minimumStockLevel'] ?? 0,
        status: cylinderStatusFromString(json['status'] ?? ''),
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse CylinderModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: CylinderModel,
        stackTrace: stackTrace,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'type': type.name,
      'material': material.label,
      'price': price,
      'cost': cost,
      'imageUrl': imageUrl,
      'description': description,
      'quantity': stock,
      'reserved': reserved,
      'sold': sold,
      'minimumStockLevel': minimumStockLevel,
      'status': status.label,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  static List<CylinderModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => CylinderModel.fromJson(json)).toList();
  }
}
