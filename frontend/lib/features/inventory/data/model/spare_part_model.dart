import '../../../../core/enums/parse_failure_type.dart';
import '../../../../core/errors/app_failure.dart';
import '../../../../core/enums/cylinder_type.dart';
import '../../../../core/enums/spare_part_category.dart';
import '../../../../core/enums/spare_part_status.dart';
import '../../domain/entities/spare_part_entity.dart';

class SparePartModel extends SparePartEntity {
  const SparePartModel({
    required super.id,
    required super.description,
    required super.price,
    required super.cost,
    required super.category,
    required super.compatibleCylinderTypes,
    required super.barcode,
    required super.quantity,
    required super.reserved,
    required super.sold,
    required super.minimumStockLevel,
    required super.status,
    required super.createdAt,
    required super.updatedAt,
    required super.imageUrl,
  });

  factory SparePartModel.fromJson(Map<String, dynamic> json) {
    try {
      return SparePartModel(
        id: json['_id'] ?? '',
        description: json['description'] ?? '',
        price: (json['price'] as num).toDouble(),
        cost: (json['cost'] as num).toDouble(),
        category: sparePartCategoryFromString(json['category'] ?? ''),
        compatibleCylinderTypes:
            (json['compatibleCylinderTypes'] as List<dynamic>)
                .map((type) => cylinderTypeFromString(type.toString()))
                .toList(),
        barcode: json['barcode'] ?? '',
        quantity: json['stock'] ?? 0,
        reserved: json['reserved'] ?? 0,
        sold: json['sold'] ?? 0,
        minimumStockLevel: json['minimumStockLevel'] ?? 0,
        status: sparePartStatusFromString(json['status'] ?? ''),
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        imageUrl: json['currentImageUrl'] ?? json['imageUrl'] ?? '',
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse SparePartModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: SparePartModel,
        stackTrace: stackTrace,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'description': description,
      'price': price,
      'cost': cost,
      'category': category.label, // Use category.label for backend
      'compatibleCylinderTypes': compatibleCylinderTypes
          .map((type) => type.name)
          .toList(),
      'barcode': barcode,
      'stock': quantity,
      'reserved': reserved,
      'sold': sold,
      'minimumStockLevel': minimumStockLevel,
      'status': status.name, // Use status.name for backend
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'imageUrl': imageUrl,
    };
  }

  static List<SparePartModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => SparePartModel.fromJson(json)).toList();
  }
}
