import '../../../../core/enums/parse_failure_type.dart';
import '../../../../core/errors/app_failure.dart';
import '../../../../core/enums/package_status.dart';
import '../../domain/entities/package_entity.dart';
import 'cylinder_model.dart';
import 'package_item_model.dart';

class PackageModel extends PackageEntity {
  PackageModel({
    required super.id,
    required super.name,
    required super.description,
    // required super.cylinderId,
    super.cylinder,
    required super.includedSpareParts,
    required super.totalPrice,
    required super.costPrice,
    required super.discount,
    required super.imageUrl,
    required super.quantity,
    required super.reserved,
    required super.sold,
    required super.minimumStockLevel,
    required super.status,
    required super.createdAt,
    required super.updatedAt,
  });

  factory PackageModel.fromJson(Map<String, dynamic> json) {
    try {
      return PackageModel(
        id: json['_id'] ?? '',
        name: json['name'] ?? '',
        description: json['description'] ?? '',
        // cylinderId: json['cylinderId'] ?? '',
        cylinder: json['cylinder'] != null
            ? CylinderModel.fromJson(json['cylinder'])
            : null,
        includedSpareParts: PackageItemModel.fromJsonList(
          json['includedSpareParts'] ?? [],
        ),
        totalPrice: (json['totalPrice'] as num).toDouble(),
        costPrice: (json['costPrice'] as num).toDouble(),
        discount: (json['discount'] as num).toDouble(),
        imageUrl: json['currentImageUrl'] ?? json['imageUrl'] ?? '',
        quantity: json['quantity'] ?? 0,
        reserved: json['reserved'] ?? 0,
        sold: json['sold'] ?? 0,
        minimumStockLevel: json['minimumStockLevel'] ?? 0,
        status: json['isActive'] == true
            ? PackageStatus.active
            : PackageStatus.discontinued,
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse PackageModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: PackageModel,
        stackTrace: stackTrace,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      'description': description,
      // 'cylinderId': cylinderId,
      if (cylinder != null) 'cylinder': (cylinder as CylinderModel).toJson(),
      'includedSpareParts': includedSpareParts
          .map((item) => (item as PackageItemModel).toJson())
          .toList(),
      'totalPrice': totalPrice,
      'costPrice': costPrice,
      'discount': discount,
      'imageUrl': imageUrl,
      'quantity': quantity,
      'reserved': reserved,
      'sold': sold,
      'minimumStockLevel': minimumStockLevel,
      'isActive': status == PackageStatus.active,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  static List<PackageModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => PackageModel.fromJson(json)).toList();
  }
}
