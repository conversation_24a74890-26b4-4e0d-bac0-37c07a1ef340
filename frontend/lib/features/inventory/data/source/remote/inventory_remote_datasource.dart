import 'package:fpdart/fpdart.dart';
import 'package:image_picker/image_picker.dart';

import '../../../../../core/constants/api_end_points.dart';
import '../../../../../core/enums/cylinder_type.dart';
import '../../../../../core/enums/cylinder_material.dart';
import '../../../../../core/enums/cylinder_status.dart';
import '../../../../../core/enums/package_status.dart';
import '../../../../../core/enums/spare_part_status.dart';
import '../../../../../core/enums/spare_part_category.dart';
import '../../../../../core/enums/http_method.dart';
import '../../../../../core/errors/app_failure.dart';
import '../../../../../core/errors/http_error_handler.dart';
import '../../../../../core/network/api_client/dio_api_client.dart';
import '../../../../../core/utils/helpers/request_data.dart';
import '../../../../../core/utils/helpers/multipart_helper.dart';
import '../../model/cylinder_model.dart';
import '../../model/package_model.dart';
import '../../model/spare_part_model.dart';
import '../../../domain/entities/package_item_entity.dart';

abstract class InventoryRemoteDataSource {
  // Get methods
  FutureEitherFailOr<List<CylinderModel>> getCylinders({
    CylinderType? type,
    CylinderMaterial? material,
    CylinderStatus? status,
    int? page,
    int? limit,
  });

  FutureEitherFailOr<CylinderModel> getCylinderById({
    required String cylinderId,
  });

  FutureEitherFailOr<List<PackageModel>> getPackages({
    PackageStatus? status,
    int? page,
    int? limit,
  });

  FutureEitherFailOr<PackageModel> getPackageById({required String packageId});

  FutureEitherFailOr<List<SparePartModel>> getSpareParts({
    SparePartCategory? category,
    SparePartStatus? status,
    List<CylinderType>? compatibleWith,
    int? page,
    int? limit,
  });

  FutureEitherFailOr<SparePartModel> getSparePartById({
    required String sparePartId,
  });

  // Cylinder management
  FutureEitherFailOr<void> restockCylinder({
    required String cylinderId,
    required int quantity,
  });

  FutureEitherFailOr<void> adjustCylinderStock({
    required String cylinderId,
    required int adjustment,
    required String reason,
  });

  FutureEitherFailOr<void> bulkUpdateCylinderStatus({
    required List<String> cylinderIds,
    required CylinderStatus status,
  });

  FutureEitherFailOr<String> createCylinder({
    required CylinderType type,
    required CylinderMaterial material,
    required double price,
    required double cost,
    String? imageUrl,
    required String description,
    required int quantity,
    required int minimumStockLevel,
    required CylinderStatus status,
    XFile? imageFile,
  });

  FutureEitherFailOr<void> updateCylinder({
    required String cylinderId,
    CylinderType? type,
    CylinderMaterial? material,
    double? price,
    double? cost,
    String? imageUrl,
    String? description,
    int? quantity,
    int? minimumStockLevel,
    CylinderStatus? status,
    XFile? imageFile,
  });

  FutureEitherFailOr<void> deleteCylinder({required String cylinderId});

  // Package management
  FutureEitherFailOr<void> restockPackage({
    required String packageId,
    required int quantity,
  });

  FutureEitherFailOr<void> adjustPackageStock({
    required String packageId,
    required int adjustment,
    required String reason,
  });

  FutureEitherFailOr<void> bulkUpdatePackageStatus({
    required List<String> packageIds,
    required PackageStatus status,
  });

  FutureEitherFailOr<String> createPackage({
    required String name,
    required String description,
    required String cylinderId,
    required List<PackageItemEntity> includedSpareParts,
    required double totalPrice,
    required double costPrice,
    required double discount,
    String? imageUrl,
    required int quantity,
    required int minimumStockLevel,
    XFile? imageFile,
  });

  FutureEitherFailOr<void> updatePackage({
    required String packageId,
    String? name,
    String? description,
    String? cylinderId,
    List<PackageItemEntity>? includedSpareParts,
    double? totalPrice,
    double? costPrice,
    double? discount,
    String? imageUrl,
    int? quantity,
    int? minimumStockLevel,
    XFile? imageFile,
  });

  FutureEitherFailOr<void> deletePackage({required String packageId});

  // Spare Part management
  FutureEitherFailOr<void> restockSparePart({
    required String sparePartId,
    required int quantity,
  });

  FutureEitherFailOr<void> adjustSparePartStock({
    required String sparePartId,
    required int adjustment,
    required String reason,
  });

  FutureEitherFailOr<void> bulkUpdateSparePartStatus({
    required List<String> sparePartIds,
    required SparePartStatus status,
  });

  FutureEitherFailOr<String> createSparePart({
    required String description,
    required double price,
    required double cost,
    required SparePartCategory category,
    required List<CylinderType> compatibleCylinderTypes,
    required String barcode,
    required int minimumStockLevel,
    required int initialStock,
    required int initialReserved,
    required int initialSold,
    XFile? imageFile,
  });

  FutureEitherFailOr<void> updateSparePart({
    required String sparePartId,
    String? name,
    String? description,
    double? price,
    double? cost,
    SparePartCategory? category,
    List<CylinderType>? compatibleCylinderTypes,
    String? barcode,
    int? minimumStockLevel,
    XFile? imageFile,
  });

  FutureEitherFailOr<void> deleteSparePart({required String sparePartId});

  // Restore methods
  FutureEitherFailOr<void> restoreCylinder(String cylinderId);
  FutureEitherFailOr<void> restorePackage(String packageId);
  FutureEitherFailOr<void> restoreSparePart(String sparePartId);

  // Get deleted methods
  FutureEitherFailOr<List<CylinderModel>> getDeletedCylinders({
    int? page,
    int? limit,
  });

  FutureEitherFailOr<List<PackageModel>> getDeletedPackages({
    int? page,
    int? limit,
  });

  FutureEitherFailOr<List<SparePartModel>> getDeletedSpareParts({
    int? page,
    int? limit,
  });

  // Permanent delete methods
  FutureEitherFailOr<void> permanentlyDeleteCylinder({
    required String cylinderId,
  });
  FutureEitherFailOr<void> permanentlyDeletePackage({
    required String packageId,
  });
  FutureEitherFailOr<void> permanentlyDeleteSparePart({
    required String sparePartId,
  });
}

class InventoryRemoteDataSourceImpl implements InventoryRemoteDataSource {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;

  const InventoryRemoteDataSourceImpl({
    required this.dioApiClient,
    required this.httpErrorHandler,
  });

  @override
  FutureEitherFailOr<List<CylinderModel>> getCylinders({
    CylinderType? type,
    CylinderMaterial? material,
    CylinderStatus? status,
    int? page,
    int? limit,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => CylinderModel.fromJsonList(json as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getCylinders,
        queryParameters: {
          if (type != null) 'type': type.name,
          if (material != null) 'material': material.label,
          if (status != null) 'status': status.label,
          if (page != null) 'page': page,
          if (limit != null) 'limit': limit,
        },
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final cylinders = apiResponse.getNonNullableData();
      return right(cylinders);
    });
  }

  @override
  FutureEitherFailOr<CylinderModel> getCylinderById({
    required String cylinderId,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => CylinderModel.fromJson(json as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getCylinderById(cylinderId),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final cylinder = apiResponse.getNonNullableData();
      return right(cylinder);
    });
  }

  @override
  FutureEitherFailOr<String> createCylinder({
    required CylinderType type,
    required CylinderMaterial material,
    required double price,
    required double cost,
    String? imageUrl,
    required String description,
    required int quantity,
    required int minimumStockLevel,
    required CylinderStatus status,
    XFile? imageFile,
  }) async {
    final formData = await MultipartHelper.createCylinderFormData(
      type: type,
      material: material,
      price: price,
      cost: cost,
      imageUrl: imageUrl,
      description: description,
      quantity: quantity,
      minimumStockLevel: minimumStockLevel,
      status: status,
      imageFile: imageFile,
    );

    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.createCylinder,
        data: RequestData.formData(formData),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final message = apiResponse.apiMessage;
      return right(message);
    });
  }

  @override
  FutureEitherFailOr<void> updateCylinder({
    required String cylinderId,
    CylinderType? type,
    CylinderMaterial? material,
    double? price,
    double? cost,
    String? imageUrl,
    String? description,
    int? quantity,
    int? minimumStockLevel,
    CylinderStatus? status,
    XFile? imageFile,
  }) async {
    final formData = await MultipartHelper.updateCylinderFormData(
      cylinderId: cylinderId,
      type: type,
      material: material,
      price: price,
      cost: cost,
      imageUrl: imageUrl,
      description: description,
      quantity: quantity,
      minimumStockLevel: minimumStockLevel,
      status: status,
      imageFile: imageFile,
    );

    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.put,
        endPointUrl: ApiEndpoints.updateCylinder(cylinderId),
        data: RequestData.formData(formData),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> deleteCylinder({required String cylinderId}) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.delete,
        endPointUrl: ApiEndpoints.deleteCylinder(cylinderId),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> restockCylinder({
    required String cylinderId,
    required int quantity,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.restockCylinder(cylinderId),
        data: RequestData.json({'quantity': quantity}),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> adjustCylinderStock({
    required String cylinderId,
    required int adjustment,
    required String reason,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.adjustCylinderStock(cylinderId),
        data: RequestData.json({'adjustment': adjustment, 'reason': reason}),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> bulkUpdateCylinderStatus({
    required List<String> cylinderIds,
    required CylinderStatus status,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.bulkUpdateCylinderStatus,
        data: RequestData.json({
          'cylinderIds': cylinderIds,
          'status': status.label,
        }),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  // Package methods
  @override
  FutureEitherFailOr<List<PackageModel>> getPackages({
    PackageStatus? status,
    int? page,
    int? limit,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => PackageModel.fromJsonList(json as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getPackages,
        queryParameters: {
          if (status != null) 'status': status.label,
          if (page != null) 'page': page,
          if (limit != null) 'limit': limit,
        },
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final packages = apiResponse.getNonNullableData();
      return right(packages);
    });
  }

  @override
  FutureEitherFailOr<PackageModel> getPackageById({
    required String packageId,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => PackageModel.fromJson(json as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getPackageById(packageId),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final package = apiResponse.getNonNullableData();
      return right(package);
    });
  }

  @override
  FutureEitherFailOr<String> createPackage({
    required String name,
    required String description,
    required String cylinderId,
    required List<PackageItemEntity> includedSpareParts,
    required double totalPrice,
    required double costPrice,
    required double discount,
    String? imageUrl,
    required int quantity,
    required int minimumStockLevel,
    XFile? imageFile,
  }) async {
    final formData = await MultipartHelper.createPackageFormData(
      name: name,
      description: description,
      cylinderId: cylinderId,
      includedSpareParts: includedSpareParts,
      totalPrice: totalPrice,
      costPrice: costPrice,
      discount: discount,
      imageUrl: imageUrl,
      quantity: quantity,
      minimumStockLevel: minimumStockLevel,
      imageFile: imageFile,
    );

    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.createPackage,
        data: RequestData.formData(formData),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final message = apiResponse.apiMessage;
      return right(message);
    });
  }

  @override
  FutureEitherFailOr<void> updatePackage({
    required String packageId,
    String? name,
    String? description,
    String? cylinderId,
    List<PackageItemEntity>? includedSpareParts,
    double? totalPrice,
    double? costPrice,
    double? discount,
    String? imageUrl,
    int? quantity,
    int? minimumStockLevel,
    XFile? imageFile,
  }) async {
    final formData = await MultipartHelper.updatePackageFormData(
      packageId: packageId,
      name: name,
      description: description,
      cylinderId: cylinderId,
      includedSpareParts: includedSpareParts,
      totalPrice: totalPrice,
      costPrice: costPrice,
      discount: discount,
      imageUrl: imageUrl,
      quantity: quantity,
      minimumStockLevel: minimumStockLevel,
      imageFile: imageFile,
    );

    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.put,
        endPointUrl: ApiEndpoints.updatePackage(packageId),
        data: RequestData.formData(formData),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> deletePackage({required String packageId}) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.delete,
        endPointUrl: ApiEndpoints.deletePackage(packageId),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> restockPackage({
    required String packageId,
    required int quantity,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.restockPackage(packageId),
        data: RequestData.json({'quantity': quantity}),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> adjustPackageStock({
    required String packageId,
    required int adjustment,
    required String reason,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.adjustPackageStock(packageId),
        data: RequestData.json({'adjustment': adjustment, 'reason': reason}),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> bulkUpdatePackageStatus({
    required List<String> packageIds,
    required PackageStatus status,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.bulkUpdatePackageStatus,
        data: RequestData.json({
          'packageIds': packageIds,
          'status': status.label,
        }),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  // Spare Part methods
  @override
  FutureEitherFailOr<List<SparePartModel>> getSpareParts({
    SparePartCategory? category,
    SparePartStatus? status,
    List<CylinderType>? compatibleWith,
    int? page,
    int? limit,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => SparePartModel.fromJsonList(json as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getSpareParts,
        queryParameters: {
          if (category != null) 'category': category.label,
          // if (status != null) 'status': status.label,
          if (compatibleWith != null)
            'compatibleWith': compatibleWith.map((type) => type.name).join(','),
          if (page != null) 'page': page,
          if (limit != null) 'limit': limit,
        },
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final spareParts = apiResponse.getNonNullableData();
      return right(spareParts);
    });
  }

  @override
  FutureEitherFailOr<SparePartModel> getSparePartById({
    required String sparePartId,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) =>
          SparePartModel.fromJson(json as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getSparePartById(sparePartId),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final sparePart = apiResponse.getNonNullableData();
      return right(sparePart);
    });
  }

  @override
  FutureEitherFailOr<String> createSparePart({
    required String description,
    required double price,
    required double cost,
    required SparePartCategory category,
    required List<CylinderType> compatibleCylinderTypes,
    required String barcode,
    required int minimumStockLevel,
    required int initialStock,
    required int initialReserved,
    required int initialSold,
    XFile? imageFile,
  }) async {
    final formData = await MultipartHelper.createSparePartFormData(
      description: description,
      price: price,
      cost: cost,
      category: category,
      compatibleCylinderTypes: compatibleCylinderTypes,
      barcode: barcode,
      minimumStockLevel: minimumStockLevel,
      initialStock: initialStock,
      initialReserved: initialReserved,
      initialSold: initialSold,
      imageFile: imageFile,
    );

    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.createSparePart,
        data: RequestData.formData(formData),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final message = apiResponse.apiMessage;
      return right(message);
    });
  }

  @override
  FutureEitherFailOr<void> updateSparePart({
    required String sparePartId,
    String? name,
    String? description,
    double? price,
    double? cost,
    SparePartCategory? category,
    List<CylinderType>? compatibleCylinderTypes,
    String? barcode,
    int? minimumStockLevel,
    XFile? imageFile,
  }) async {
    final formData = await MultipartHelper.updateSparePartFormData(
      sparePartId: sparePartId,
      description: description,
      price: price,
      cost: cost,
      category: category,
      compatibleCylinderTypes: compatibleCylinderTypes,
      barcode: barcode,
      minimumStockLevel: minimumStockLevel,
      imageFile: imageFile,
    );

    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.put,
        endPointUrl: ApiEndpoints.updateSparePart(sparePartId),
        data: RequestData.formData(formData),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> deleteSparePart({
    required String sparePartId,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.delete,
        endPointUrl: ApiEndpoints.deleteSparePart(sparePartId),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> restockSparePart({
    required String sparePartId,
    required int quantity,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.restockSparePart(sparePartId),
        data: RequestData.json({'quantity': quantity}),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> adjustSparePartStock({
    required String sparePartId,
    required int adjustment,
    required String reason,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.adjustSparePartStock(sparePartId),
        data: RequestData.json({'adjustment': adjustment, 'reason': reason}),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> bulkUpdateSparePartStatus({
    required List<String> sparePartIds,
    required SparePartStatus status,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.bulkUpdateSparePartStatus,
        data: RequestData.json({
          'sparePartIds': sparePartIds,
          'status': status.label,
        }),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  // Restore methods
  @override
  FutureEitherFailOr<void> restoreCylinder(String cylinderId) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.restoreCylinder(cylinderId),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> restorePackage(String packageId) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.restorePackage(packageId),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> restoreSparePart(String sparePartId) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.restoreSparePart(sparePartId),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  // Get deleted methods
  @override
  FutureEitherFailOr<List<CylinderModel>> getDeletedCylinders({
    int? page,
    int? limit,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => CylinderModel.fromJsonList(json as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getDeletedCylinders,
        queryParameters: {
          if (page != null) 'page': page,
          if (limit != null) 'limit': limit,
        },
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final cylinders = apiResponse.getNonNullableData();
      return right(cylinders);
    });
  }

  @override
  FutureEitherFailOr<List<PackageModel>> getDeletedPackages({
    int? page,
    int? limit,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => PackageModel.fromJsonList(json as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getDeletedPackages,
        queryParameters: {
          if (page != null) 'page': page,
          if (limit != null) 'limit': limit,
        },
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final packages = apiResponse.getNonNullableData();
      return right(packages);
    });
  }

  @override
  FutureEitherFailOr<List<SparePartModel>> getDeletedSpareParts({
    int? page,
    int? limit,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => SparePartModel.fromJsonList(json as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getDeletedSpareParts,
        queryParameters: {
          if (page != null) 'page': page,
          if (limit != null) 'limit': limit,
        },
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final spareParts = apiResponse.getNonNullableData();
      return right(spareParts);
    });
  }

  // Permanent delete methods
  @override
  FutureEitherFailOr<void> permanentlyDeleteCylinder({
    required String cylinderId,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.delete,
        endPointUrl: ApiEndpoints.permanentlyDeleteCylinder(cylinderId),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> permanentlyDeletePackage({
    required String packageId,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.delete,
        endPointUrl: ApiEndpoints.permanentlyDeletePackage(packageId),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> permanentlyDeleteSparePart({
    required String sparePartId,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => null,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.delete,
        endPointUrl: ApiEndpoints.permanentlyDeleteSparePart(sparePartId),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }
}
