import 'package:equatable/equatable.dart';

class AdjustCylinderStockParams extends Equatable {
  final String cylinderId;
  final int adjustment;
  final String reason;

  const AdjustCylinderStockParams({
    required this.cylinderId,
    required this.adjustment,
    required this.reason,
  });

  Map<String, dynamic> toJson() {
    return {
      'cylinderId': cylinderId,
      'adjustment': adjustment,
      'reason': reason,
    };
  }

  @override
  List<Object?> get props => [cylinderId, adjustment, reason];
}
