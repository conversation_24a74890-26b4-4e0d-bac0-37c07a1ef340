import 'package:equatable/equatable.dart';

class RestockCylinderParams extends Equatable {
  final String cylinderId;
  final int quantity;

  const RestockCylinderParams({
    required this.cylinderId,
    required this.quantity,
  });

  Map<String, dynamic> toJson() {
    return {
      'cylinderId': cylinderId,
      'quantity': quantity,
    };
  }

  @override
  List<Object?> get props => [cylinderId, quantity];
}
