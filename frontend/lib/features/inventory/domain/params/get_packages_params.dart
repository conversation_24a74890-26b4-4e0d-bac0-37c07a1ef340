import 'package:equatable/equatable.dart';
import '../../../../core/enums/package_status.dart';

class GetPackagesParams extends Equatable {
  final PackageStatus? status;
  final int? page;
  final int? limit;

  const GetPackagesParams({
    this.status,
    this.page,
    this.limit,
  });

  Map<String, dynamic> toJson() {
    return {
      if (status != null) 'status': status!.label,
      if (page != null) 'page': page,
      if (limit != null) 'limit': limit,
    };
  }

  @override
  List<Object?> get props => [status, page, limit];
}
