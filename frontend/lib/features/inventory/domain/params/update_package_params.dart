import 'package:equatable/equatable.dart';
import 'package:image_picker/image_picker.dart';

import '../entities/package_item_entity.dart';

class UpdatePackageParams extends Equatable {
  final String packageId;
  final String? name;
  final String? description;
  final String? cylinderId;
  final List<PackageItemEntity>? includedSpareParts;
  final double? totalPrice;
  final double? costPrice;
  final double? discount;
  final String? imageUrl;
  final int? quantity;
  final int? minimumStockLevel;
  final XFile? imageFile;

  const UpdatePackageParams({
    required this.packageId,
    this.name,
    this.description,
    this.cylinderId,
    this.includedSpareParts,
    this.totalPrice,
    this.costPrice,
    this.discount,
    this.imageUrl,
    this.quantity,
    this.minimumStockLevel,
    this.imageFile,
  });


  Map<String, dynamic> toJson() {
    return {
      'packageId': packageId,
      if (name != null) 'name': name,
      if (description != null) 'description': description,
      if (cylinderId != null) 'cylinderId': cylinderId, 
      if (includedSpareParts != null) 'includedSpareParts': includedSpareParts!.map((item) => {
        'sparePartId': item.sparePartId,
        'name': item.name,
        'quantity': item.quantity,
        'unitPrice': item.unitPrice,
        'totalPrice': item.totalPrice,
      }).toList(),
      if (totalPrice != null) 'totalPrice': totalPrice,
      if (costPrice != null) 'costPrice': costPrice,
      if (discount != null) 'discount': discount,
      if (imageUrl != null) 'imageUrl': imageUrl,
      if (quantity != null) 'quantity': quantity,
      if (minimumStockLevel != null) 'minimumStockLevel': minimumStockLevel,
      if (imageFile != null) 'image': imageFile,
    };
  }

  @override
  List<Object?> get props => [
    packageId,
    name,
    description,
    cylinderId,
    includedSpareParts,
    totalPrice,
    costPrice,
    discount,
    imageUrl,
    quantity,
    minimumStockLevel,
    imageFile,
  ];
}
