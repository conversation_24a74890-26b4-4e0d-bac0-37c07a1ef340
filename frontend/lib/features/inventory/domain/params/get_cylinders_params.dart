import 'package:equatable/equatable.dart';
import '../../../../core/enums/cylinder_type.dart';
import '../../../../core/enums/cylinder_material.dart';
import '../../../../core/enums/cylinder_status.dart';

class GetCylindersParams extends Equatable {
  final CylinderType? type;
  final CylinderMaterial? material;
  final CylinderStatus? status;
  final int? page;
  final int? limit;

  const GetCylindersParams({
    this.type,
    this.material,
    this.status,
    this.page,
    this.limit,
  });

  Map<String, dynamic> toJson() {
    return {
      if (type != null) 'type': type!.name,
      if (material != null) 'material': material!.label,
      if (status != null) 'status': status!.label,
      if (page != null) 'page': page,
      if (limit != null) 'limit': limit,
    };
  }

  @override
  List<Object?> get props => [type, material, status, page, limit];
}
