import 'package:equatable/equatable.dart';
import '../../../../core/enums/cylinder_type.dart';
import '../../../../core/enums/spare_part_category.dart';
import '../../../../core/enums/spare_part_status.dart';

class GetSparePartsParams extends Equatable {
  final SparePartCategory? category;
  final SparePartStatus? status;
  final List<CylinderType>? compatibleWith;
  final int? page;
  final int? limit;

  const GetSparePartsParams({
    this.category,
    this.status,
    this.compatibleWith,
    this.page,
    this.limit,
  });

  Map<String, dynamic> toJson() {
    return {
      if (category != null) 'category': category!.label,
      if (status != null) 'status': status!.label,
      if (compatibleWith != null) 
        'compatibleWith': compatibleWith!.map((type) => type.name).toList(),
      if (page != null) 'page': page,
      if (limit != null) 'limit': limit,
    };
  }

  @override
  List<Object?> get props => [category, status, compatibleWith, page, limit];
}
