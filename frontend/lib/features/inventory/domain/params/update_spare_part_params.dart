import 'package:equatable/equatable.dart';
import 'package:image_picker/image_picker.dart';

import '../../../../core/enums/cylinder_type.dart';
import '../../../../core/enums/spare_part_category.dart';

class UpdateSparePartParams extends Equatable {
  final String sparePartId;
  final String? name;
  final String? description;
  final double? price;
  final double? cost;
  final SparePartCategory? category;
  final List<CylinderType>? compatibleCylinderTypes;
  final String? barcode;
  final int? minimumStockLevel;
  final XFile? imageFile;

  const UpdateSparePartParams({
    required this.sparePartId,
    this.name,
    this.description,
    this.price,
    this.cost,
    this.category,
    this.compatibleCylinderTypes,
    this.barcode,
    this.minimumStockLevel,
    this.imageFile,
  });

  Map<String, dynamic> toJson() {
    return {
      'sparePartId': sparePartId,
      if (name != null) 'name': name, 
      if (description != null) 'description': description,
      if (price != null) 'price': price,
      if (cost != null) 'cost': cost,
      if (category != null) 'category': category!.label,
      if (compatibleCylinderTypes != null) 
        'compatibleCylinderTypes': compatibleCylinderTypes!.map((type) => type.name).toList(),
      if (barcode != null) 'barcode': barcode,
      if (minimumStockLevel != null) 'minimumStockLevel': minimumStockLevel,
      if (imageFile != null) 'image': imageFile,
    };
  }

  @override
  List<Object?> get props => [
    sparePartId,
    name,
    description,
    price,
    cost,
    category,
    compatibleCylinderTypes,
    barcode,
    minimumStockLevel,
    imageFile,
  ];
}
