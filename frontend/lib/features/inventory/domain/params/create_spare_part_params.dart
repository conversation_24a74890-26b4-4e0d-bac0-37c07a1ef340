import 'package:equatable/equatable.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/enums/cylinder_type.dart';
import '../../../../core/enums/spare_part_category.dart';

class CreateSparePartParams extends Equatable {
  final String description;
  final double price;
  final double cost;
  final SparePartCategory category;
  final List<CylinderType> compatibleCylinderTypes;
  final String barcode;
  final int minimumStockLevel;
  final int initialStock;
  final int initialReserved;
  final int initialSold;
  final XFile? imageFile;

  const CreateSparePartParams({
    required this.description,
    required this.price,
    required this.cost,
    required this.category,
    required this.compatibleCylinderTypes,
    required this.barcode,
    required this.minimumStockLevel,
    required this.initialStock,
    required this.initialReserved,
    required this.initialSold,
    this.imageFile,
  });

  Map<String, dynamic> toJson() {
    return {
      'description': description,
      'price': price,
      'cost': cost,
      'category': category.label,
      'compatibleCylinderTypes': compatibleCylinderTypes.map((type) => type.name).toList(),
      'barcode': barcode,
      'minimumStockLevel': minimumStockLevel,
      'initialStock': initialStock,
      'initialReserved': initialReserved,
      'initialSold': initialSold,
      'image': imageFile,
    };
  }

  @override
  List<Object?> get props => [
    description,
    price,
    cost,
    category,
    compatibleCylinderTypes,
    barcode,
    minimumStockLevel,
    initialStock,
    initialReserved,
    initialSold,
    imageFile,
  ];
}
