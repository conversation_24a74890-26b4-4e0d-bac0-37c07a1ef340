import 'package:equatable/equatable.dart';
import 'package:frontend/core/constants/api_end_points.dart';

import '../../../../core/enums/cylinder_type.dart';
import '../../../../core/enums/cylinder_material.dart';
import '../../../../core/enums/cylinder_status.dart';

class CylinderEntity extends Equatable {
  final String id;
  final CylinderType type;
  final CylinderMaterial material;
  final double price;
  final double cost;
  final String imageUrl;
  final String description;
  final int stock;
  final int reserved;
  final int sold;
  final int minimumStockLevel;
  final CylinderStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CylinderEntity({
    required this.id,
    required this.type,
    required this.material,
    required this.price,
    required this.cost,
    required this.imageUrl,
    required this.description,
    required this.stock,
    required this.reserved,
    required this.sold,
    required this.minimumStockLevel,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  // Computed properties
  int get availableQuantity => stock - reserved;
  int get currentPhysicalStock => stock; // Current stock in warehouse
  int get totalStock =>
      stock + sold; // Total stock ever received (current + sold)
  int get reservedQuantity => reserved; // Stock reserved for orders
  int get soldQuantity => sold; // Total units sold

  // Stock status
  bool get isLowStock => availableQuantity <= minimumStockLevel;
  bool get isOutOfStock => availableQuantity <= 0;
  bool get hasReservedStock => reserved > 0;
  bool get hasSoldItems => sold > 0;

  // Financial calculations
  double get profitMargin => price - cost;
  double get profitPercentage => cost > 0 ? (profitMargin / cost) * 100 : 0;
  double get totalRevenue => sold * price;
  double get totalCost => sold * cost;
  double get totalProfit => totalRevenue - totalCost;

  // Helper methods
  bool canReserve(int requestedQuantity) {
    return availableQuantity >= requestedQuantity;
  }

  String get formattedImageUrl => '${ApiEndpoints.host}$imageUrl';

  CylinderEntity copyWith({
    String? id,
    CylinderType? type,
    CylinderMaterial? material,
    double? price,
    double? cost,
    String? imageUrl,
    String? description,
    int? stock,
    int? reserved,
    int? sold,
    int? minimumStockLevel,
    CylinderStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CylinderEntity(
      id: id ?? this.id,
      type: type ?? this.type,
      material: material ?? this.material,
      price: price ?? this.price,
      cost: cost ?? this.cost,
      imageUrl: imageUrl ?? this.imageUrl,
      description: description ?? this.description,
      stock: stock ?? this.stock,
      reserved: reserved ?? this.reserved,
      sold: sold ?? this.sold,
      minimumStockLevel: minimumStockLevel ?? this.minimumStockLevel,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'CylinderEntity(id: $id, type: $type, material: $material, price: $price, stock: $stock, status: $status)';
  }

  @override
  List<Object?> get props => [
    id,
    type,
    material,
    price,
    cost,
    imageUrl,
    description,
    stock,
    reserved,
    sold,
    minimumStockLevel,
    status,
    createdAt,
    updatedAt,
  ];
}
