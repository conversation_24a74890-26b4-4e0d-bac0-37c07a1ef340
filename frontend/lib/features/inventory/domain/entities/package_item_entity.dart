import 'package:equatable/equatable.dart';

class PackageItemEntity extends Equatable {
  final String sparePartId;
  final String name;
  final int quantity;
  final double unitPrice;
  final double totalPrice;

  const PackageItemEntity({
    required this.sparePartId,
    required this.name,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
  });

  PackageItemEntity copyWith({
    String? sparePartId,
    String? name,
    int? quantity,
    double? unitPrice,
    double? totalPrice,
  }) {
    return PackageItemEntity(
      sparePartId: sparePartId ?? this.sparePartId,
      name: name ?? this.name,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
    );
  }

  @override
  String toString() {
    return 'PackageItemEntity(sparePartId: $sparePartId, name: $name, quantity: $quantity, unitPrice: $unitPrice, totalPrice: $totalPrice)';
  }

  @override
  List<Object?> get props => [
    sparePartId,
    name,
    quantity,
    unitPrice,
    totalPrice,
  ];
}
