import 'package:equatable/equatable.dart';

import '../../../../core/constants/api_end_points.dart';
import '../../../../core/enums/cylinder_type.dart';
import '../../../../core/enums/spare_part_category.dart';
import '../../../../core/enums/spare_part_status.dart';

class SparePartEntity extends Equatable {
  final String id;
  final String description;
  final double price;
  final double cost;
  final SparePartCategory category;
  final List<CylinderType> compatibleCylinderTypes;
  final String barcode;
  final int quantity;
  final int reserved;
  final int sold;
  final int minimumStockLevel;
  final SparePartStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String imageUrl;

  const SparePartEntity({
    required this.id,
    required this.description,
    required this.price,
    required this.cost,
    required this.category,
    required this.compatibleCylinderTypes,
    required this.barcode,
    required this.quantity,
    required this.reserved,
    required this.sold,
    required this.minimumStockLevel,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    required this.imageUrl,
  });

  // Computed properties
  int get availableQuantity => quantity - reserved;
  int get currentPhysicalStock => quantity; // Current stock in warehouse
  int get totalStock =>
      quantity + sold; // Total stock ever received (current + sold)
  int get reservedQuantity => reserved; // Stock reserved for orders
  int get soldQuantity => sold; // Total units sold

  // Stock status
  bool get isLowStock => availableQuantity <= minimumStockLevel;
  bool get isOutOfStock => availableQuantity <= 0;
  bool get hasReservedStock => reserved > 0;
  bool get hasSoldItems => sold > 0;

  // Financial calculations
  double get profitMargin => price - cost;
  double get profitPercentage => cost > 0 ? (profitMargin / cost) * 100 : 0;
  double get totalRevenue => sold * price;
  double get totalCost => sold * cost;
  double get totalProfit => totalRevenue - totalCost;

  // Helper methods
  bool canReserve(int requestedQuantity) {
    return availableQuantity >= requestedQuantity;
  }

  bool isCompatibleWith(CylinderType cylinderType) {
    return compatibleCylinderTypes.contains(cylinderType);
  }

  String get formattedImageUrl => '${ApiEndpoints.host}$imageUrl';

  SparePartEntity copyWith({
    String? id,
    String? description,
    double? price,
    double? cost,
    SparePartCategory? category,
    List<CylinderType>? compatibleCylinderTypes,
    String? barcode,
    int? quantity,
    int? reserved,
    int? sold,
    int? minimumStockLevel,
    SparePartStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? imageUrl,
  }) {
    return SparePartEntity(
      id: id ?? this.id,
      description: description ?? this.description,
      price: price ?? this.price,
      cost: cost ?? this.cost,
      category: category ?? this.category,
      compatibleCylinderTypes:
          compatibleCylinderTypes ?? this.compatibleCylinderTypes,
      barcode: barcode ?? this.barcode,
      quantity: quantity ?? this.quantity,
      reserved: reserved ?? this.reserved,
      sold: sold ?? this.sold,
      minimumStockLevel: minimumStockLevel ?? this.minimumStockLevel,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }

  @override
  String toString() {
    return 'SparePartEntity(id: $id, category: $category, price: $price, quantity: $quantity, status: $status)';
  }

  @override
  List<Object?> get props => [
    id,
    description,
    price,
    cost,
    category,
    compatibleCylinderTypes,
    barcode,
    quantity,
    reserved,
    sold,
    minimumStockLevel,
    status,
    createdAt,
    updatedAt,
    imageUrl,
  ];
}
