import '../entities/spare_part_entity.dart';
import '../repository/inventory_repository.dart';
import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';

class GetDeletedSparePartsUseCase
    implements UseCase<List<SparePartEntity>, GetDeletedSparePartsParams> {
  final InventoryRepository repository;

  GetDeletedSparePartsUseCase({required this.repository});

  @override
  FutureEitherFailOr<List<SparePartEntity>> call({
    required GetDeletedSparePartsParams params,
  }) async {
    return await repository.getDeletedSpareParts(
      page: params.page,
      limit: params.limit,
    );
  }
}

class GetDeletedSparePartsParams {
  final int? page;
  final int? limit;

  GetDeletedSparePartsParams({this.page, this.limit});
}
