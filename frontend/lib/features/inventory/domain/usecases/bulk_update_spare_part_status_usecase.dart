import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/bulk_update_spare_part_status_params.dart';
import '../repository/inventory_repository.dart';

class BulkUpdateSparePartStatusUseCase implements UseCase<void, BulkUpdateSparePartStatusParams> {
  final InventoryRepository repository;

  const BulkUpdateSparePartStatusUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required BulkUpdateSparePartStatusParams params,
  }) async {
    final response = await repository.bulkUpdateSparePartStatus(
      sparePartIds: params.sparePartIds,
      status: params.status,
    );
    return response;
  }
}
