import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/create_cylinder_params.dart';
import '../repository/inventory_repository.dart';

class CreateCylinderUseCase implements UseCase<String, CreateCylinderParams> {
  final InventoryRepository repository;

  const CreateCylinderUseCase({required this.repository});

  @override
  FutureEitherFailOr<String> call({
    required CreateCylinderParams params,
  }) async {
    final response = await repository.createCylinder(
      type: params.type,
      material: params.material,
      price: params.price,
      cost: params.cost,
      imageUrl: params.imageUrl,
      description: params.description,
      quantity: params.quantity,
      minimumStockLevel: params.minimumStockLevel,
      status: params.status,
      imageFile: params.imageFile,
    );
    return response;
  }
}
