import '../entities/cylinder_entity.dart';
import '../repository/inventory_repository.dart';
import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';

class GetDeletedCylindersUseCase
    implements UseCase<List<CylinderEntity>, GetDeletedCylindersParams> {
  final InventoryRepository repository;

  GetDeletedCylindersUseCase({ required this.repository});

  @override
  FutureEitherFailOr<List<CylinderEntity>> call({
    required GetDeletedCylindersParams params,
  }) async {
    return await repository.getDeletedCylinders(
      page: params.page,
      limit: params.limit,
    );
  }
}

class GetDeletedCylindersParams {
  final int? page;
  final int? limit;

  GetDeletedCylindersParams({this.page, this.limit});
}
