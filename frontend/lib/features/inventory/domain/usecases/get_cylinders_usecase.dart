import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../entities/cylinder_entity.dart';
import '../params/get_cylinders_params.dart';
import '../repository/inventory_repository.dart';

class GetCylindersUseCase implements UseCase<List<CylinderEntity>, GetCylindersParams> {
  final InventoryRepository repository;

  const GetCylindersUseCase({required this.repository});

  @override
  FutureEitherFailOr<List<CylinderEntity>> call({
    required GetCylindersParams params,
  }) async {
    final response = await repository.getCylinders(
      type: params.type,
      material: params.material,
      status: params.status,
      page: params.page,
      limit: params.limit,
    );
    return response;
  }
}
