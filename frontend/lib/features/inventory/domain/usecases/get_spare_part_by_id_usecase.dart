import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../entities/spare_part_entity.dart';
import '../params/get_spare_part_by_id_params.dart';
import '../repository/inventory_repository.dart';

class GetSparePartByIdUseCase implements UseCase<SparePartEntity, GetSparePartByIdParams> {
  final InventoryRepository repository;

  const GetSparePartByIdUseCase({required this.repository});

  @override
  FutureEitherFailOr<SparePartEntity> call({
    required GetSparePartByIdParams params,
  }) async {
    final response = await repository.getSparePartById(
      sparePartId: params.sparePartId,
    );
    return response;
  }
}
