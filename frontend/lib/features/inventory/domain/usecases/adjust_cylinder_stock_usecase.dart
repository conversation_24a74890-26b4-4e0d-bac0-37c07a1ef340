import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/adjust_cylinder_stock_params.dart';
import '../repository/inventory_repository.dart';

class AdjustCylinderStockUseCase implements UseCase<void, AdjustCylinderStockParams> {
  final InventoryRepository repository;

  const AdjustCylinderStockUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required AdjustCylinderStockParams params,
  }) async {
    final response = await repository.adjustCylinderStock(
      cylinderId: params.cylinderId,
      adjustment: params.adjustment,
      reason: params.reason,
    );
    return response;
  }
}
