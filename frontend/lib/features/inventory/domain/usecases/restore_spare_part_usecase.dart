import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../repository/inventory_repository.dart';

class RestoreSparePartUseCase implements UseCase<void, RestoreSparePartParams> {
  final InventoryRepository repository;

  RestoreSparePartUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required RestoreSparePartParams params,
  }) async {
    return await repository.restoreSparePart(params.sparePartId);
  }
}

class RestoreSparePartParams {
  final String sparePartId;

  RestoreSparePartParams({required this.sparePartId});
}
