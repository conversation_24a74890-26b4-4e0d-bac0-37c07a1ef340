import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/restock_spare_part_params.dart';
import '../repository/inventory_repository.dart';

class RestockSparePartUseCase implements UseCase<void, RestockSparePartParams> {
  final InventoryRepository repository;

  const RestockSparePartUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required RestockSparePartParams params,
  }) async {
    final response = await repository.restockSparePart(
      sparePartId: params.sparePartId,
      quantity: params.quantity,
    );
    return response;
  }
}
