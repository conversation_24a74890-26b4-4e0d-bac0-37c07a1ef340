import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../entities/cylinder_entity.dart';
import '../params/get_cylinder_by_id_params.dart';
import '../repository/inventory_repository.dart';

class GetCylinderByIdUseCase implements UseCase<CylinderEntity, GetCylinderByIdParams> {
  final InventoryRepository repository;

  const GetCylinderByIdUseCase({required this.repository});

  @override
  FutureEitherFailOr<CylinderEntity> call({
    required GetCylinderByIdParams params,
  }) async {
    final response = await repository.getCylinderById(
      cylinderId: params.cylinderId,
    );
    return response;
  }
}
