import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/restock_cylinder_params.dart';
import '../repository/inventory_repository.dart';

class RestockCylinderUseCase implements UseCase<void, RestockCylinderParams> {
  final InventoryRepository repository;

  const RestockCylinderUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required RestockCylinderParams params,
  }) async {
    final response = await repository.restockCylinder(
      cylinderId: params.cylinderId,
      quantity: params.quantity,
    );
    return response;
  }
}
