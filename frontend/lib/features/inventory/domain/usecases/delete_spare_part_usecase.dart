import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/delete_spare_part_params.dart';
import '../repository/inventory_repository.dart';

class DeleteSparePartUseCase implements UseCase<void, DeleteSparePartParams> {
  final InventoryRepository repository;

  const DeleteSparePartUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required DeleteSparePartParams params,
  }) async {
    final response = await repository.deleteSparePart(
      sparePartId: params.sparePartId,
    );
    return response;
  }
}
