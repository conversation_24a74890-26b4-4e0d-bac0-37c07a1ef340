import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../repository/inventory_repository.dart';

class RestorePackageUseCase implements UseCase<void, RestorePackageParams> {
  final InventoryRepository repository;

  RestorePackageUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({required RestorePackageParams params}) async {
    return await repository.restorePackage(params.packageId);
  }
}

class RestorePackageParams {
  final String packageId;

  RestorePackageParams({required this.packageId});
}
