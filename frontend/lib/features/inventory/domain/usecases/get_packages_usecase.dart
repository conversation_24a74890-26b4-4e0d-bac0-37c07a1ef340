import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../entities/package_entity.dart';
import '../params/get_packages_params.dart';
import '../repository/inventory_repository.dart';

class GetPackagesUseCase implements UseCase<List<PackageEntity>, GetPackagesParams> {
  final InventoryRepository repository;

  const GetPackagesUseCase({required this.repository});

  @override
  FutureEitherFailOr<List<PackageEntity>> call({
    required GetPackagesParams params,
  }) async {
    final response = await repository.getPackages(
      status: params.status,
      page: params.page,
      limit: params.limit,
    );
    return response;
  }
}
