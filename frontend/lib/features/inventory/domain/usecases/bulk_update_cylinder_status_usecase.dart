import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/bulk_update_cylinder_status_params.dart';
import '../repository/inventory_repository.dart';

class BulkUpdateCylinderStatusUseCase implements UseCase<void, BulkUpdateCylinderStatusParams> {
  final InventoryRepository repository;

  const BulkUpdateCylinderStatusUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required BulkUpdateCylinderStatusParams params,
  }) async {
    final response = await repository.bulkUpdateCylinderStatus(
      cylinderIds: params.cylinderIds,
      status: params.status,
    );
    return response;
  }
}
