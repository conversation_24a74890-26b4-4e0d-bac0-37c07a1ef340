import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../entities/spare_part_entity.dart';
import '../params/get_spare_parts_params.dart';
import '../repository/inventory_repository.dart';

class GetSparePartsUseCase implements UseCase<List<SparePartEntity>, GetSparePartsParams> {
  final InventoryRepository repository;

  const GetSparePartsUseCase({required this.repository});

  @override
  FutureEitherFailOr<List<SparePartEntity>> call({
    required GetSparePartsParams params,
  }) async {
    final response = await repository.getSpareParts(
      category: params.category,
      status: params.status,
      compatibleWith: params.compatibleWith,
      page: params.page,
      limit: params.limit,
    );
    return response;
  }
}
