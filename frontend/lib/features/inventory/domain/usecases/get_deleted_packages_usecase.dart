import '../entities/package_entity.dart';
import '../repository/inventory_repository.dart';
import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';

class GetDeletedPackagesUseCase
    implements UseCase<List<PackageEntity>, GetDeletedPackagesParams> {
  final InventoryRepository repository;

  GetDeletedPackagesUseCase({required this.repository});

  @override
  FutureEitherFailOr<List<PackageEntity>> call({
    required GetDeletedPackagesParams params,
  }) async {
    return await repository.getDeletedPackages(
      page: params.page,
      limit: params.limit,
    );
  }
}

class GetDeletedPackagesParams {
  final int? page;
  final int? limit;

  GetDeletedPackagesParams({this.page, this.limit});
}
