import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/update_package_params.dart';
import '../repository/inventory_repository.dart';

class UpdatePackageUseCase implements UseCase<void, UpdatePackageParams> {
  final InventoryRepository repository;

  const UpdatePackageUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required UpdatePackageParams params,
  }) async {
    final response = await repository.updatePackage(
      packageId: params.packageId,
      name: params.name,
      description: params.description,
      cylinderId: params.cylinderId,
      includedSpareParts: params.includedSpareParts,
      totalPrice: params.totalPrice,
      costPrice: params.costPrice,
      discount: params.discount,
      imageUrl: params.imageUrl,
      quantity: params.quantity,
      minimumStockLevel: params.minimumStockLevel,
      imageFile: params.imageFile,
    );
    return response;
  }
}
