import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../entities/package_entity.dart';
import '../params/get_package_by_id_params.dart';
import '../repository/inventory_repository.dart';

class GetPackageByIdUseCase implements UseCase<PackageEntity, GetPackageByIdParams> {
  final InventoryRepository repository;

  const GetPackageByIdUseCase({required this.repository});

  @override
  FutureEitherFailOr<PackageEntity> call({
    required GetPackageByIdParams params,
  }) async {
    final response = await repository.getPackageById(
      packageId: params.packageId,
    );
    return response;
  }
}
