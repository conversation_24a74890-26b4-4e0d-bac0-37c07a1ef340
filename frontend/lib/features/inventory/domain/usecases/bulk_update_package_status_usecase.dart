import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/bulk_update_package_status_params.dart';
import '../repository/inventory_repository.dart';

class BulkUpdatePackageStatusUseCase implements UseCase<void, BulkUpdatePackageStatusParams> {
  final InventoryRepository repository;

  const BulkUpdatePackageStatusUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required BulkUpdatePackageStatusParams params,
  }) async {
    final response = await repository.bulkUpdatePackageStatus(
      packageIds: params.packageIds,
      status: params.status,
    );
    return response;
  }
}
