import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/restock_package_params.dart';
import '../repository/inventory_repository.dart';

class RestockPackageUseCase implements UseCase<void, RestockPackageParams> {
  final InventoryRepository repository;

  const RestockPackageUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required RestockPackageParams params,
  }) async {
    final response = await repository.restockPackage(
      packageId: params.packageId,
      quantity: params.quantity,
    );
    return response;
  }
}
