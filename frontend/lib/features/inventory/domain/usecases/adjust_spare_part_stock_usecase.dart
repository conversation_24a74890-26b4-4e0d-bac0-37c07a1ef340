import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/adjust_spare_part_stock_params.dart';
import '../repository/inventory_repository.dart';

class AdjustSparePartStockUseCase implements UseCase<void, AdjustSparePartStockParams> {
  final InventoryRepository repository;

  const AdjustSparePartStockUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required AdjustSparePartStockParams params,
  }) async {
    final response = await repository.adjustSparePartStock(
      sparePartId: params.sparePartId,
      adjustment: params.adjustment,
      reason: params.reason,
    );
    return response;
  }
}
