import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../repository/inventory_repository.dart';
import '../params/permanently_delete_spare_part_params.dart';

class PermanentlyDeleteSparePartUseCase
    implements UseCase<void, PermanentlyDeleteSparePartParams> {
  final InventoryRepository repository;

  const PermanentlyDeleteSparePartUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required PermanentlyDeleteSparePartParams params,
  }) async {
    return await repository.permanentlyDeleteSparePart(
      sparePartId: params.sparePartId,
    );
  }
}
