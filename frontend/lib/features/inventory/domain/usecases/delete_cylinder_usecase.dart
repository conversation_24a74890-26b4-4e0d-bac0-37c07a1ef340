import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/delete_cylinder_params.dart';
import '../repository/inventory_repository.dart';

class DeleteCylinderUseCase implements UseCase<void, DeleteCylinderParams> {
  final InventoryRepository repository;

  const DeleteCylinderUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required DeleteCylinderParams params,
  }) async {
    final response = await repository.deleteCylinder(
      cylinderId: params.cylinderId,
    );
    return response;
  }
}
