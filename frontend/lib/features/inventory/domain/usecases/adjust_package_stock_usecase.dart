import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/adjust_package_stock_params.dart';
import '../repository/inventory_repository.dart';

class AdjustPackageStockUseCase implements UseCase<void, AdjustPackageStockParams> {
  final InventoryRepository repository;

  const AdjustPackageStockUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required AdjustPackageStockParams params,
  }) async {
    final response = await repository.adjustPackageStock(
      packageId: params.packageId,
      adjustment: params.adjustment,
      reason: params.reason,
    );
    return response;
  }
}
