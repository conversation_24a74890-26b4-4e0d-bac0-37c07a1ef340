import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/create_spare_part_params.dart';
import '../repository/inventory_repository.dart';

class CreateSparePartUseCase implements UseCase<String, CreateSparePartParams> {
  final InventoryRepository repository;

  const CreateSparePartUseCase({required this.repository});

  @override
  FutureEitherFailOr<String> call({
    required CreateSparePartParams params,
  }) async {
    final response = await repository.createSparePart(
      description: params.description,
      price: params.price,
      cost: params.cost,
      category: params.category,
      compatibleCylinderTypes: params.compatibleCylinderTypes,
      barcode: params.barcode,
      minimumStockLevel: params.minimumStockLevel,
      initialStock: params.initialStock,
      initialReserved: params.initialReserved,
      initialSold: params.initialSold,
      imageFile: params.imageFile,
    );
    return response;
  }
}
