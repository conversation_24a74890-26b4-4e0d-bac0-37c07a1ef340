import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../repository/inventory_repository.dart';

class RestoreCylinderUseCase implements UseCase<void, RestoreCylinderParams> {
  final InventoryRepository repository;

  RestoreCylinderUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({required RestoreCylinderParams params}) async {
    return await repository.restoreCylinder(params.cylinderId);
  }
}

class RestoreCylinderParams {
  final String cylinderId;

  RestoreCylinderParams({required this.cylinderId});
}
