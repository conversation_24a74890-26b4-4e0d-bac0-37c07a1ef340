import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../repository/inventory_repository.dart';
import '../params/permanently_delete_cylinder_params.dart';

class PermanentlyDeleteCylinderUseCase
    implements UseCase<void, PermanentlyDeleteCylinderParams> {
  final InventoryRepository repository;

  const PermanentlyDeleteCylinderUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required PermanentlyDeleteCylinderParams params,
  }) async {
    return await repository.permanentlyDeleteCylinder(
      cylinderId: params.cylinderId,
    );
  }
}
