import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../repository/inventory_repository.dart';
import '../params/permanently_delete_package_params.dart';

class PermanentlyDeletePackageUseCase
    implements UseCase<void, PermanentlyDeletePackageParams> {
  final InventoryRepository repository;

  const PermanentlyDeletePackageUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required PermanentlyDeletePackageParams params,
  }) async {
    return await repository.permanentlyDeletePackage(
      packageId: params.packageId,
    );
  }
}
