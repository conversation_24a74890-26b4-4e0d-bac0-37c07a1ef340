import 'package:frontend/core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../params/delete_package_params.dart';
import '../repository/inventory_repository.dart';

class DeletePackageUseCase implements UseCase<void, DeletePackageParams> {
  final InventoryRepository repository;

  const DeletePackageUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({
    required DeletePackageParams params,
  }) async {
    final response = await repository.deletePackage(
      packageId: params.packageId,
    );
    return response;
  }
}
