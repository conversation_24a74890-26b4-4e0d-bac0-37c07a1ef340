import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';

import '../../../../core/enums/cylinder_type.dart';
import '../../../../core/enums/cylinder_material.dart';
import '../../../../core/enums/cylinder_status.dart';
import '../../../../core/enums/package_status.dart';
import '../../../../core/enums/spare_part_status.dart';
import '../../../../core/enums/spare_part_category.dart';
import '../../../../core/errors/app_failure.dart';
import '../../../../core/utils/helpers/bloc_helper.dart';
import '../../domain/entities/cylinder_entity.dart';
import '../../domain/entities/package_entity.dart';
import '../../domain/entities/spare_part_entity.dart';
import '../../domain/entities/package_item_entity.dart';
import '../../domain/params/get_cylinders_params.dart';
import '../../domain/params/get_cylinder_by_id_params.dart';
import '../../domain/params/create_cylinder_params.dart';
import '../../domain/params/update_cylinder_params.dart';
import '../../domain/params/delete_cylinder_params.dart';
import '../../domain/params/restock_cylinder_params.dart';
import '../../domain/params/adjust_cylinder_stock_params.dart';
import '../../domain/params/get_packages_params.dart';
import '../../domain/params/get_package_by_id_params.dart';
import '../../domain/params/create_package_params.dart';
import '../../domain/params/get_spare_parts_params.dart';
import '../../domain/params/get_spare_part_by_id_params.dart';
import '../../domain/params/create_spare_part_params.dart';
import '../../domain/params/update_package_params.dart';
import '../../domain/params/delete_package_params.dart';
import '../../domain/params/restock_package_params.dart';
import '../../domain/params/adjust_package_stock_params.dart';
import '../../domain/params/bulk_update_package_status_params.dart';
import '../../domain/params/update_spare_part_params.dart';
import '../../domain/params/delete_spare_part_params.dart';
import '../../domain/params/restock_spare_part_params.dart';
import '../../domain/params/adjust_spare_part_stock_params.dart';
import '../../domain/params/bulk_update_spare_part_status_params.dart';
import '../../domain/params/bulk_update_cylinder_status_params.dart';
import '../../domain/usecases/get_cylinders_usecase.dart';
import '../../domain/usecases/get_cylinder_by_id_usecase.dart';
import '../../domain/usecases/create_cylinder_usecase.dart';
import '../../domain/usecases/update_cylinder_usecase.dart';
import '../../domain/usecases/delete_cylinder_usecase.dart';
import '../../domain/usecases/restock_cylinder_usecase.dart';
import '../../domain/usecases/adjust_cylinder_stock_usecase.dart';
import '../../domain/usecases/bulk_update_cylinder_status_usecase.dart';
import '../../domain/usecases/get_packages_usecase.dart';
import '../../domain/usecases/get_package_by_id_usecase.dart';
import '../../domain/usecases/create_package_usecase.dart';
import '../../domain/usecases/update_package_usecase.dart';
import '../../domain/usecases/delete_package_usecase.dart';
import '../../domain/usecases/restock_package_usecase.dart';
import '../../domain/usecases/adjust_package_stock_usecase.dart';
import '../../domain/usecases/bulk_update_package_status_usecase.dart';
import '../../domain/usecases/get_spare_parts_usecase.dart';
import '../../domain/usecases/get_spare_part_by_id_usecase.dart';
import '../../domain/usecases/create_spare_part_usecase.dart';
import '../../domain/usecases/update_spare_part_usecase.dart';
import '../../domain/usecases/delete_spare_part_usecase.dart';
import '../../domain/usecases/restock_spare_part_usecase.dart';
import '../../domain/usecases/adjust_spare_part_stock_usecase.dart';
import '../../domain/usecases/bulk_update_spare_part_status_usecase.dart';
import '../../domain/usecases/restore_cylinder_usecase.dart';
import '../../domain/usecases/restore_package_usecase.dart';
import '../../domain/usecases/restore_spare_part_usecase.dart';
import '../../domain/usecases/get_deleted_cylinders_usecase.dart';
import '../../domain/usecases/get_deleted_packages_usecase.dart';
import '../../domain/usecases/get_deleted_spare_parts_usecase.dart';
import '../../domain/usecases/permanently_delete_cylinder_usecase.dart';
import '../../domain/usecases/permanently_delete_package_usecase.dart';
import '../../domain/usecases/permanently_delete_spare_part_usecase.dart';
import '../../domain/params/permanently_delete_cylinder_params.dart';
import '../../domain/params/permanently_delete_package_params.dart';
import '../../domain/params/permanently_delete_spare_part_params.dart';

part 'inventory_event.dart';
part 'inventory_state.dart';

class InventoryBloc extends Bloc<InventoryEvent, InventoryState> {
  // Cylinder use cases
  final GetCylindersUseCase getCylindersUseCase;
  final GetCylinderByIdUseCase getCylinderByIdUseCase;
  final CreateCylinderUseCase createCylinderUseCase;
  final UpdateCylinderUseCase updateCylinderUseCase;
  final DeleteCylinderUseCase deleteCylinderUseCase;
  final RestockCylinderUseCase restockCylinderUseCase;
  final AdjustCylinderStockUseCase adjustCylinderStockUseCase;
  final BulkUpdateCylinderStatusUseCase bulkUpdateCylinderStatusUseCase;

  // Package use cases
  final GetPackagesUseCase getPackagesUseCase;
  final GetPackageByIdUseCase getPackageByIdUseCase;
  final CreatePackageUseCase createPackageUseCase;
  final UpdatePackageUseCase updatePackageUseCase;
  final DeletePackageUseCase deletePackageUseCase;
  final RestockPackageUseCase restockPackageUseCase;
  final AdjustPackageStockUseCase adjustPackageStockUseCase;
  final BulkUpdatePackageStatusUseCase bulkUpdatePackageStatusUseCase;

  // Spare part use cases
  final GetSparePartsUseCase getSparePartsUseCase;
  final GetSparePartByIdUseCase getSparePartByIdUseCase;
  final CreateSparePartUseCase createSparePartUseCase;
  final UpdateSparePartUseCase updateSparePartUseCase;
  final DeleteSparePartUseCase deleteSparePartUseCase;
  final RestockSparePartUseCase restockSparePartUseCase;
  final AdjustSparePartStockUseCase adjustSparePartStockUseCase;
  final BulkUpdateSparePartStatusUseCase bulkUpdateSparePartStatusUseCase;

  // Restore use cases
  final RestoreCylinderUseCase restoreCylinderUseCase;
  final RestorePackageUseCase restorePackageUseCase;
  final RestoreSparePartUseCase restoreSparePartUseCase;

  // Get deleted use cases
  final GetDeletedCylindersUseCase getDeletedCylindersUseCase;
  final GetDeletedPackagesUseCase getDeletedPackagesUseCase;
  final GetDeletedSparePartsUseCase getDeletedSparePartsUseCase;

  // Permanent delete use cases
  final PermanentlyDeleteCylinderUseCase permanentlyDeleteCylinderUseCase;
  final PermanentlyDeletePackageUseCase permanentlyDeletePackageUseCase;
  final PermanentlyDeleteSparePartUseCase permanentlyDeleteSparePartUseCase;

  InventoryBloc({
    // Cylinder use cases
    required this.getCylindersUseCase,
    required this.getCylinderByIdUseCase,
    required this.createCylinderUseCase,
    required this.updateCylinderUseCase,
    required this.deleteCylinderUseCase,
    required this.restockCylinderUseCase,
    required this.adjustCylinderStockUseCase,
    required this.bulkUpdateCylinderStatusUseCase,
    // Package use cases
    required this.getPackagesUseCase,
    required this.getPackageByIdUseCase,
    required this.createPackageUseCase,
    required this.updatePackageUseCase,
    required this.deletePackageUseCase,
    required this.restockPackageUseCase,
    required this.adjustPackageStockUseCase,
    required this.bulkUpdatePackageStatusUseCase,
    // Spare part use cases
    required this.getSparePartsUseCase,
    required this.getSparePartByIdUseCase,
    required this.createSparePartUseCase,
    required this.updateSparePartUseCase,
    required this.deleteSparePartUseCase,
    required this.restockSparePartUseCase,
    required this.adjustSparePartStockUseCase,
    required this.bulkUpdateSparePartStatusUseCase,
    // Restore use cases
    required this.restoreCylinderUseCase,
    required this.restorePackageUseCase,
    required this.restoreSparePartUseCase,
    // Get deleted use cases
    required this.getDeletedCylindersUseCase,
    required this.getDeletedPackagesUseCase,
    required this.getDeletedSparePartsUseCase,
    // Permanent delete use cases
    required this.permanentlyDeleteCylinderUseCase,
    required this.permanentlyDeletePackageUseCase,
    required this.permanentlyDeleteSparePartUseCase,
  }) : super(InventoryInitial()) {
    // Cylinder events
    on<GetCylindersEvent>(_onGetCylinders);
    on<GetCylinderByIdEvent>(_onGetCylinderById);
    on<CreateCylinderEvent>(_onCreateCylinder);
    on<UpdateCylinderEvent>(_onUpdateCylinder);
    on<DeleteCylinderEvent>(_onDeleteCylinder);
    on<RestockCylinderEvent>(_onRestockCylinder);
    on<AdjustCylinderStockEvent>(_onAdjustCylinderStock);
    on<BulkUpdateCylinderStatusEvent>(_onBulkUpdateCylinderStatus);
    // Package events
    on<GetPackagesEvent>(_onGetPackages);
    on<GetPackageByIdEvent>(_onGetPackageById);
    on<CreatePackageEvent>(_onCreatePackage);
    on<UpdatePackageEvent>(_onUpdatePackage);
    on<DeletePackageEvent>(_onDeletePackage);
    on<RestockPackageEvent>(_onRestockPackage);
    on<AdjustPackageStockEvent>(_onAdjustPackageStock);
    on<BulkUpdatePackageStatusEvent>(_onBulkUpdatePackageStatus);
    // Spare part events
    on<GetSparePartsEvent>(_onGetSpareParts);
    on<GetSparePartByIdEvent>(_onGetSparePartById);
    on<CreateSparePartEvent>(_onCreateSparePart);
    on<UpdateSparePartEvent>(_onUpdateSparePart);
    on<DeleteSparePartEvent>(_onDeleteSparePart);
    on<RestockSparePartEvent>(_onRestockSparePart);
    on<AdjustSparePartStockEvent>(_onAdjustSparePartStock);
    on<BulkUpdateSparePartStatusEvent>(_onBulkUpdateSparePartStatus);
    // Restore events
    on<RestoreCylinderEvent>(_onRestoreCylinder);
    on<RestorePackageEvent>(_onRestorePackage);
    on<RestoreSparePartEvent>(_onRestoreSparePart);
    // Get deleted events
    on<GetDeletedCylindersEvent>(_onGetDeletedCylinders);
    on<GetDeletedPackagesEvent>(_onGetDeletedPackages);
    on<GetDeletedSparePartsEvent>(_onGetDeletedSpareParts);

    // Permanent delete events
    on<PermanentlyDeleteCylinderEvent>(_onPermanentlyDeleteCylinder);
    on<PermanentlyDeletePackageEvent>(_onPermanentlyDeletePackage);
    on<PermanentlyDeleteSparePartEvent>(_onPermanentlyDeleteSparePart);
  }

  @override
  Future<void> close() async {
    _cylinders.clear();
    _packages.clear();
    _spareParts.clear();
    _deletedCylinders.clear();
    _deletedPackages.clear();
    _deletedSpareParts.clear();
    await super.close();
  }
  
  List<CylinderEntity> _cylinders = [];
  List<CylinderEntity> get cylinders => _cylinders;

  List<PackageEntity> _packages = [];
  List<PackageEntity> get packages => _packages;

  List<SparePartEntity> _spareParts = [];
  List<SparePartEntity> get spareParts => _spareParts;

  List<CylinderEntity> _deletedCylinders = [];
  List<CylinderEntity> get deletedCylinders => _deletedCylinders;
  List<PackageEntity> _deletedPackages = [];
  List<PackageEntity> get deletedPackages => _deletedPackages;
  List<SparePartEntity> _deletedSpareParts = [];
  List<SparePartEntity> get deletedSpareParts => _deletedSpareParts;

  // Cylinder event handlers
  Future<void> _onGetCylinders(
    GetCylindersEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<List<CylinderEntity>, InventoryState>(
      emit: emit,
      loadingState: GetCylindersLoading(),
      callUseCase: getCylindersUseCase(
        params: GetCylindersParams(
          type: event.type,
          material: event.material,
          status: event.status,
          page: event.page,
          limit: event.limit,
        ),
      ),
      onSuccess: (cylinders) {
        _cylinders = cylinders;
        return GetCylindersSuccess(cylinders: cylinders);
      },
      onFailure: (failure) => GetCylindersFailure(appFailure: failure),
    );
  }

  Future<void> _onGetCylinderById(
    GetCylinderByIdEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<CylinderEntity, InventoryState>(
      emit: emit,
      loadingState: GetCylinderByIdLoading(),
      callUseCase: getCylinderByIdUseCase(
        params: GetCylinderByIdParams(cylinderId: event.cylinderId),
      ),
      onSuccess: (cylinder) => GetCylinderByIdSuccess(cylinder: cylinder),
      onFailure: (failure) => GetCylinderByIdFailure(appFailure: failure),
    );
  }

  Future<void> _onCreateCylinder(
    CreateCylinderEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<String, InventoryState>(
      emit: emit,
      loadingState: CreateCylinderLoading(),
      callUseCase: createCylinderUseCase(
        params: CreateCylinderParams(
          type: event.type,
          material: event.material,
          price: event.price,
          cost: event.cost,
          description: event.description,
          quantity: event.quantity,
          minimumStockLevel: event.minimumStockLevel,
          status: event.status,
          imageUrl: event.imageUrl,
          imageFile: event.imageFile,
        ),
      ),
      onSuccess: (cylinderId) =>
          const CreateCylinderSuccess(message: 'Cylinder created successfully'),
      onFailure: (failure) => CreateCylinderFailure(appFailure: failure),
    );
  }

  Future<void> _onUpdateCylinder(
    UpdateCylinderEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: UpdateCylinderLoading(),
      callUseCase: updateCylinderUseCase(
        params: UpdateCylinderParams(
          cylinderId: event.cylinderId,
          type: event.type,
          material: event.material,
          price: event.price,
          cost: event.cost,
          description: event.description,
          quantity: event.quantity,
          minimumStockLevel: event.minimumStockLevel,
          status: event.status,
          imageUrl: event.imageUrl,
          imageFile: event.imageFile,
        ),
      ),
      onSuccess: (_) =>
          const UpdateCylinderSuccess(message: 'Cylinder updated successfully'),
      onFailure: (failure) => UpdateCylinderFailure(appFailure: failure),
    );
  }

  Future<void> _onDeleteCylinder(
    DeleteCylinderEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: DeleteCylinderLoading(),
      callUseCase: deleteCylinderUseCase(
        params: DeleteCylinderParams(cylinderId: event.cylinderId),
      ),
      onSuccess: (_) =>
          const DeleteCylinderSuccess(message: 'Cylinder deleted successfully'),
      onFailure: (failure) => DeleteCylinderFailure(appFailure: failure),
    );
  }

  Future<void> _onRestockCylinder(
    RestockCylinderEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: RestockCylinderLoading(),
      callUseCase: restockCylinderUseCase(
        params: RestockCylinderParams(
          cylinderId: event.cylinderId,
          quantity: event.quantity,
        ),
      ),
      onSuccess: (_) => const RestockCylinderSuccess(
        message: 'Cylinder restocked successfully',
      ),
      onFailure: (failure) => RestockCylinderFailure(appFailure: failure),
    );
  }

  Future<void> _onAdjustCylinderStock(
    AdjustCylinderStockEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: AdjustCylinderStockLoading(),
      callUseCase: adjustCylinderStockUseCase(
        params: AdjustCylinderStockParams(
          cylinderId: event.cylinderId,
          adjustment: event.adjustment,
          reason: event.reason,
        ),
      ),
      onSuccess: (_) => const AdjustCylinderStockSuccess(
        message: 'Cylinder stock adjusted successfully',
      ),
      onFailure: (failure) => AdjustCylinderStockFailure(appFailure: failure),
    );
  }

  Future<void> _onBulkUpdateCylinderStatus(
    BulkUpdateCylinderStatusEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: BulkUpdateCylinderStatusLoading(),
      callUseCase: bulkUpdateCylinderStatusUseCase(
        params: BulkUpdateCylinderStatusParams(
          cylinderIds: event.cylinderIds,
          status: event.status,
        ),
      ),
      onSuccess: (_) => const BulkUpdateCylinderStatusSuccess(
        message: 'Cylinder status updated successfully',
      ),
      onFailure: (failure) =>
          BulkUpdateCylinderStatusFailure(appFailure: failure),
    );
  }

  // Package event handlers
  Future<void> _onGetPackages(
    GetPackagesEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<List<PackageEntity>, InventoryState>(
      emit: emit,
      loadingState: GetPackagesLoading(),
      callUseCase: getPackagesUseCase(
        params: GetPackagesParams(
          status: event.status,
          page: event.page,
          limit: event.limit,
        ),
      ),
      onSuccess: (packages) {
        _packages = packages;
        return GetPackagesSuccess(packages: packages);
      },
      onFailure: (failure) => GetPackagesFailure(appFailure: failure),
    );
  }

  Future<void> _onGetPackageById(
    GetPackageByIdEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<PackageEntity, InventoryState>(
      emit: emit,
      loadingState: GetPackageByIdLoading(),
      callUseCase: getPackageByIdUseCase(
        params: GetPackageByIdParams(packageId: event.packageId),
      ),
      onSuccess: (package) => GetPackageByIdSuccess(package: package),
      onFailure: (failure) => GetPackageByIdFailure(appFailure: failure),
    );
  }

  Future<void> _onCreatePackage(
    CreatePackageEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<String, InventoryState>(
      emit: emit,
      loadingState: CreatePackageLoading(),
      callUseCase: createPackageUseCase(
        params: CreatePackageParams(
          name: event.name,
          description: event.description,
          cylinderId: event.cylinderId,
          includedSpareParts: event.includedSpareParts,
          totalPrice: event.totalPrice,
          costPrice: event.costPrice,
          discount: event.discount,
          quantity: event.quantity,
          minimumStockLevel: event.minimumStockLevel,
          imageUrl: event.imageUrl,
          imageFile: event.imageFile,
        ),
      ),
      onSuccess: (packageId) =>
          const CreatePackageSuccess(message: 'Package created successfully'),
      onFailure: (failure) => CreatePackageFailure(appFailure: failure),
    );
  }

  Future<void> _onUpdatePackage(
    UpdatePackageEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: UpdatePackageLoading(),
      callUseCase: updatePackageUseCase(
        params: UpdatePackageParams(
          packageId: event.packageId,
          name: event.name,
          description: event.description,
          cylinderId: event.cylinderId,
          includedSpareParts: event.includedSpareParts,
          totalPrice: event.totalPrice,
          costPrice: event.costPrice,
          discount: event.discount,
          quantity: event.quantity,
          minimumStockLevel: event.minimumStockLevel,
          imageUrl: event.imageUrl,
          imageFile: event.imageFile,
        ),
      ),
      onSuccess: (_) =>
          const UpdatePackageSuccess(message: 'Package updated successfully'),
      onFailure: (failure) => UpdatePackageFailure(appFailure: failure),
    );
  }

  Future<void> _onDeletePackage(
    DeletePackageEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: DeletePackageLoading(),
      callUseCase: deletePackageUseCase(
        params: DeletePackageParams(packageId: event.packageId),
      ),
      onSuccess: (_) =>
          const DeletePackageSuccess(message: 'Package deleted successfully'),
      onFailure: (failure) => DeletePackageFailure(appFailure: failure),
    );
  }

  Future<void> _onRestockPackage(
    RestockPackageEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: RestockPackageLoading(),
      callUseCase: restockPackageUseCase(
        params: RestockPackageParams(
          packageId: event.packageId,
          quantity: event.quantity,
        ),
      ),
      onSuccess: (_) => const RestockPackageSuccess(
        message: 'Package restocked successfully',
      ),
      onFailure: (failure) => RestockPackageFailure(appFailure: failure),
    );
  }

  Future<void> _onAdjustPackageStock(
    AdjustPackageStockEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: AdjustPackageStockLoading(),
      callUseCase: adjustPackageStockUseCase(
        params: AdjustPackageStockParams(
          packageId: event.packageId,
          adjustment: event.adjustment,
          reason: event.reason,
        ),
      ),
      onSuccess: (_) => const AdjustPackageStockSuccess(
        message: 'Package stock adjusted successfully',
      ),
      onFailure: (failure) => AdjustPackageStockFailure(appFailure: failure),
    );
  }

  Future<void> _onBulkUpdatePackageStatus(
    BulkUpdatePackageStatusEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: BulkUpdatePackageStatusLoading(),
      callUseCase: bulkUpdatePackageStatusUseCase(
        params: BulkUpdatePackageStatusParams(
          packageIds: event.packageIds,
          status: event.status,
        ),
      ),
      onSuccess: (_) => const BulkUpdatePackageStatusSuccess(
        message: 'Package status updated successfully',
      ),
      onFailure: (failure) =>
          BulkUpdatePackageStatusFailure(appFailure: failure),
    );
  }

  // Spare Part event handlers
  Future<void> _onGetSpareParts(
    GetSparePartsEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<List<SparePartEntity>, InventoryState>(
      emit: emit,
      loadingState: GetSparePartsLoading(),
      callUseCase: getSparePartsUseCase(
        params: GetSparePartsParams(
          category: event.category,
          status: event.status,
          compatibleWith: event.compatibleWith,
          page: event.page,
          limit: event.limit,
        ),
      ),
      onSuccess: (spareParts) {
        _spareParts = spareParts;
        return GetSparePartsSuccess(spareParts: spareParts);
      },
      onFailure: (failure) => GetSparePartsFailure(appFailure: failure),
    );
  }

  Future<void> _onGetSparePartById(
    GetSparePartByIdEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<SparePartEntity, InventoryState>(
      emit: emit,
      loadingState: GetSparePartByIdLoading(),
      callUseCase: getSparePartByIdUseCase(
        params: GetSparePartByIdParams(sparePartId: event.sparePartId),
      ),
      onSuccess: (sparePart) => GetSparePartByIdSuccess(sparePart: sparePart),
      onFailure: (failure) => GetSparePartByIdFailure(appFailure: failure),
    );
  }

  Future<void> _onCreateSparePart(
    CreateSparePartEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<String, InventoryState>(
      emit: emit,
      loadingState: CreateSparePartLoading(),
      callUseCase: createSparePartUseCase(
        params: CreateSparePartParams(
          description: event.description,
          price: event.price,
          cost: event.cost,
          category: event.category,
          compatibleCylinderTypes: event.compatibleCylinderTypes,
          barcode: event.barcode,
          minimumStockLevel: event.minimumStockLevel,
          initialStock: event.initialStock,
          initialReserved: event.initialReserved,
          initialSold: event.initialSold,
          // imageUrl: event.imageUrl,
          imageFile: event.imageFile,
        ),
      ),
      onSuccess: (sparePartId) => const CreateSparePartSuccess(
        message: 'Spare part created successfully',
      ),
      onFailure: (failure) => CreateSparePartFailure(appFailure: failure),
    );
  }

  Future<void> _onUpdateSparePart(
    UpdateSparePartEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: UpdateSparePartLoading(),
      callUseCase: updateSparePartUseCase(
        params: UpdateSparePartParams(
          sparePartId: event.sparePartId,
          name: event.name,
          description: event.description,
          price: event.price,
          cost: event.cost,
          category: event.category,
          compatibleCylinderTypes: event.compatibleCylinderTypes,
          barcode: event.barcode,
          minimumStockLevel: event.minimumStockLevel,
          // imageUrl: event.imageUrl,
          imageFile: event.imageFile,
        ),
      ),
      onSuccess: (_) => const UpdateSparePartSuccess(
        message: 'Spare part updated successfully',
      ),
      onFailure: (failure) => UpdateSparePartFailure(appFailure: failure),
    );
  }

  Future<void> _onDeleteSparePart(
    DeleteSparePartEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: DeleteSparePartLoading(),
      callUseCase: deleteSparePartUseCase(
        params: DeleteSparePartParams(sparePartId: event.sparePartId),
      ),
      onSuccess: (_) => const DeleteSparePartSuccess(
        message: 'Spare part deleted successfully',
      ),
      onFailure: (failure) => DeleteSparePartFailure(appFailure: failure),
    );
  }

  Future<void> _onRestockSparePart(
    RestockSparePartEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: RestockSparePartLoading(),
      callUseCase: restockSparePartUseCase(
        params: RestockSparePartParams(
          sparePartId: event.sparePartId,
          quantity: event.quantity,
        ),
      ),
      onSuccess: (_) => const RestockSparePartSuccess(
        message: 'Spare part restocked successfully',
      ),
      onFailure: (failure) => RestockSparePartFailure(appFailure: failure),
    );
  }

  Future<void> _onAdjustSparePartStock(
    AdjustSparePartStockEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: AdjustSparePartStockLoading(),
      callUseCase: adjustSparePartStockUseCase(
        params: AdjustSparePartStockParams(
          sparePartId: event.sparePartId,
          adjustment: event.adjustment,
          reason: event.reason,
        ),
      ),
      onSuccess: (_) => const AdjustSparePartStockSuccess(
        message: 'Spare part stock adjusted successfully',
      ),
      onFailure: (failure) => AdjustSparePartStockFailure(appFailure: failure),
    );
  }

  Future<void> _onBulkUpdateSparePartStatus(
    BulkUpdateSparePartStatusEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: BulkUpdateSparePartStatusLoading(),
      callUseCase: bulkUpdateSparePartStatusUseCase(
        params: BulkUpdateSparePartStatusParams(
          sparePartIds: event.sparePartIds,
          status: event.status,
        ),
      ),
      onSuccess: (_) => const BulkUpdateSparePartStatusSuccess(
        message: 'Spare part status updated successfully',
      ),
      onFailure: (failure) =>
          BulkUpdateSparePartStatusFailure(appFailure: failure),
    );
  }

  // Restore event handlers
  Future<void> _onRestoreCylinder(
    RestoreCylinderEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: RestoreCylinderLoading(),
      callUseCase: restoreCylinderUseCase(
        params: RestoreCylinderParams(cylinderId: event.cylinderId),
      ),
      onSuccess: (_) => const RestoreCylinderSuccess(
        message: 'Cylinder restored successfully',
      ),
      onFailure: (failure) => RestoreCylinderFailure(appFailure: failure),
    );
  }

  Future<void> _onRestorePackage(
    RestorePackageEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: RestorePackageLoading(),
      callUseCase: restorePackageUseCase(
        params: RestorePackageParams(packageId: event.packageId),
      ),
      onSuccess: (_) =>
          const RestorePackageSuccess(message: 'Package restored successfully'),
      onFailure: (failure) => RestorePackageFailure(appFailure: failure),
    );
  }

  Future<void> _onRestoreSparePart(
    RestoreSparePartEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: RestoreSparePartLoading(),
      callUseCase: restoreSparePartUseCase(
        params: RestoreSparePartParams(sparePartId: event.sparePartId),
      ),
      onSuccess: (_) => const RestoreSparePartSuccess(
        message: 'Spare part restored successfully',
      ),
      onFailure: (failure) => RestoreSparePartFailure(appFailure: failure),
    );
  }

  // Get deleted event handlers
  Future<void> _onGetDeletedCylinders(
    GetDeletedCylindersEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<List<CylinderEntity>, InventoryState>(
      emit: emit,
      loadingState: GetDeletedCylindersLoading(),
      callUseCase: getDeletedCylindersUseCase(
        params: GetDeletedCylindersParams(page: event.page, limit: event.limit),
      ),
      onSuccess: (cylinders) {
        _deletedCylinders = cylinders;
        return GetDeletedCylindersSuccess(cylinders: cylinders);
      },
      onFailure: (failure) => GetDeletedCylindersFailure(appFailure: failure),
    );
  }

  Future<void> _onGetDeletedPackages(
    GetDeletedPackagesEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<List<PackageEntity>, InventoryState>(
      emit: emit,
      loadingState: GetDeletedPackagesLoading(),
      callUseCase: getDeletedPackagesUseCase(
        params: GetDeletedPackagesParams(page: event.page, limit: event.limit),
      ),
      onSuccess: (packages) {
        _deletedPackages = packages;
        return GetDeletedPackagesSuccess(packages: packages);
      },
      onFailure: (failure) => GetDeletedPackagesFailure(appFailure: failure),
    );
  }

  Future<void> _onGetDeletedSpareParts(
    GetDeletedSparePartsEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<List<SparePartEntity>, InventoryState>(
      emit: emit,
      loadingState: GetDeletedSparePartsLoading(),
      callUseCase: getDeletedSparePartsUseCase(
        params: GetDeletedSparePartsParams(
          page: event.page,
          limit: event.limit,
        ),
      ),
      onSuccess: (spareParts) {
        _deletedSpareParts = spareParts;
        return GetDeletedSparePartsSuccess(spareParts: spareParts);
      },
      onFailure: (failure) => GetDeletedSparePartsFailure(appFailure: failure),
    );
  }

  // Permanent delete event handlers
  Future<void> _onPermanentlyDeleteCylinder(
    PermanentlyDeleteCylinderEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: PermanentlyDeleteCylinderLoading(),
      callUseCase: permanentlyDeleteCylinderUseCase(
        params: PermanentlyDeleteCylinderParams(cylinderId: event.cylinderId),
      ),
      onSuccess: (_) => PermanentlyDeleteCylinderSuccess(),
      onFailure: (failure) =>
          PermanentlyDeleteCylinderFailure(appFailure: failure),
    );
  }

  Future<void> _onPermanentlyDeletePackage(
    PermanentlyDeletePackageEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: PermanentlyDeletePackageLoading(),
      callUseCase: permanentlyDeletePackageUseCase(
        params: PermanentlyDeletePackageParams(packageId: event.packageId),
      ),
      onSuccess: (_) => PermanentlyDeletePackageSuccess(),
      onFailure: (failure) =>
          PermanentlyDeletePackageFailure(appFailure: failure),
    );
  }

  Future<void> _onPermanentlyDeleteSparePart(
    PermanentlyDeleteSparePartEvent event,
    Emitter<InventoryState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, InventoryState>(
      emit: emit,
      loadingState: PermanentlyDeleteSparePartLoading(),
      callUseCase: permanentlyDeleteSparePartUseCase(
        params: PermanentlyDeleteSparePartParams(
          sparePartId: event.sparePartId,
        ),
      ),
      onSuccess: (_) => PermanentlyDeleteSparePartSuccess(),
      onFailure: (failure) =>
          PermanentlyDeleteSparePartFailure(appFailure: failure),
    );
  }
}
