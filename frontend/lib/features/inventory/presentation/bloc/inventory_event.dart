part of 'inventory_bloc.dart';

sealed class InventoryEvent extends Equatable {
  const InventoryEvent();

  @override
  List<Object?> get props => [];
}

// Cylinder Events
final class GetCylindersEvent extends InventoryEvent {
  final CylinderType? type;
  final CylinderMaterial? material;
  final CylinderStatus? status;
  final int? page;
  final int? limit;

  const GetCylindersEvent({
    this.type,
    this.material,
    this.status,
    this.page,
    this.limit,
  });

  @override
  List<Object?> get props => [type, material, status, page, limit];
}

final class GetCylinderByIdEvent extends InventoryEvent {
  final String cylinderId;

  const GetCylinderByIdEvent({required this.cylinderId});

  @override
  List<Object?> get props => [cylinderId];
}

final class CreateCylinderEvent extends InventoryEvent {
  final CylinderType type;
  final CylinderMaterial material;
  final double price;
  final double cost;
  final String? imageUrl;
  final String description;
  final int quantity;
  final int minimumStockLevel;
  final CylinderStatus status;
  final XFile? imageFile;

  const CreateCylinderEvent({
    required this.type,
    required this.material,
    required this.price,
    required this.cost,
    this.imageUrl,
    required this.description,
    required this.quantity,
    required this.minimumStockLevel,
    required this.status,
    this.imageFile,
  });

  @override
  List<Object?> get props => [
    type,
    material,
    price,
    cost,
    imageUrl,
    description,
    quantity,
    minimumStockLevel,
    status,
    imageFile,
  ];
}

final class UpdateCylinderEvent extends InventoryEvent {
  final String cylinderId;
  final CylinderType? type;
  final CylinderMaterial? material;
  final double? price;
  final double? cost;
  final String? imageUrl;
  final String? description;
  final int? quantity;
  final int? minimumStockLevel;
  final CylinderStatus? status;
  final XFile? imageFile;

  const UpdateCylinderEvent({
    required this.cylinderId,
    this.type,
    this.material,
    this.price,
    this.cost,
    this.imageUrl,
    this.description,
    this.quantity,
    this.minimumStockLevel,
    this.status,
    this.imageFile,
  });

  @override
  List<Object?> get props => [
    cylinderId,
    type,
    material,
    price,
    cost,
    imageUrl,
    description,
    quantity,
    minimumStockLevel,
    status,
    imageFile,
  ];
}

final class DeleteCylinderEvent extends InventoryEvent {
  final String cylinderId;

  const DeleteCylinderEvent({required this.cylinderId});

  @override
  List<Object?> get props => [cylinderId];
}

final class RestoreCylinderEvent extends InventoryEvent {
  final String cylinderId;

  const RestoreCylinderEvent({required this.cylinderId});

  @override
  List<Object?> get props => [cylinderId];
}

final class GetDeletedCylindersEvent extends InventoryEvent {
  final int? page;
  final int? limit;

  const GetDeletedCylindersEvent({this.page, this.limit});

  @override
  List<Object?> get props => [page, limit];
}

final class PermanentlyDeleteCylinderEvent extends InventoryEvent {
  final String cylinderId;

  const PermanentlyDeleteCylinderEvent({required this.cylinderId});

  @override
  List<Object?> get props => [cylinderId];
}

final class RestockCylinderEvent extends InventoryEvent {
  final String cylinderId;
  final int quantity;

  const RestockCylinderEvent({
    required this.cylinderId,
    required this.quantity,
  });

  @override
  List<Object?> get props => [cylinderId, quantity];
}

final class AdjustCylinderStockEvent extends InventoryEvent {
  final String cylinderId;
  final int adjustment;
  final String reason;

  const AdjustCylinderStockEvent({
    required this.cylinderId,
    required this.adjustment,
    required this.reason,
  });

  @override
  List<Object?> get props => [cylinderId, adjustment, reason];
}

final class BulkUpdateCylinderStatusEvent extends InventoryEvent {
  final List<String> cylinderIds;
  final CylinderStatus status;

  const BulkUpdateCylinderStatusEvent({
    required this.cylinderIds,
    required this.status,
  });

  @override
  List<Object?> get props => [cylinderIds, status];
}

// Package Events
final class GetPackagesEvent extends InventoryEvent {
  final PackageStatus? status;
  final int? page;
  final int? limit;

  const GetPackagesEvent({this.status, this.page, this.limit});

  @override
  List<Object?> get props => [status, page, limit];
}

final class GetPackageByIdEvent extends InventoryEvent {
  final String packageId;

  const GetPackageByIdEvent({required this.packageId});

  @override
  List<Object?> get props => [packageId];
}

final class CreatePackageEvent extends InventoryEvent {
  final String name;
  final String description;
  final String cylinderId;
  final List<PackageItemEntity> includedSpareParts;
  final double totalPrice;
  final double costPrice;
  final double discount;
  final String? imageUrl;
  final int quantity;
  final int minimumStockLevel;
  final XFile? imageFile;

  const CreatePackageEvent({
    required this.name,
    required this.description,
    required this.cylinderId,
    required this.includedSpareParts,
    required this.totalPrice,
    required this.costPrice,
    required this.discount,
    this.imageUrl,
    required this.quantity,
    required this.minimumStockLevel,
    this.imageFile,
  });

  @override
  List<Object?> get props => [
    name,
    description,
    cylinderId,
    includedSpareParts,
    totalPrice,
    costPrice,
    discount,
    imageUrl,
    quantity,
    minimumStockLevel,
    imageFile,
  ];
}

final class UpdatePackageEvent extends InventoryEvent {
  final String packageId;
  final String? name;
  final String? description;
  final String? cylinderId;
  final List<PackageItemEntity>? includedSpareParts;
  final double? totalPrice;
  final double? costPrice;
  final double? discount;
  final String? imageUrl;
  final int? quantity;
  final int? minimumStockLevel;
  final XFile? imageFile;

  const UpdatePackageEvent({
    required this.packageId,
    this.name,
    this.description,
    this.cylinderId,
    this.includedSpareParts,
    this.totalPrice,
    this.costPrice,
    this.discount,
    this.imageUrl,
    this.quantity,
    this.minimumStockLevel,
    this.imageFile,
  });

  @override
  List<Object?> get props => [
    packageId,
    name,
    description,
    cylinderId,
    includedSpareParts,
    totalPrice,
    costPrice,
    discount,
    imageUrl,
    quantity,
    minimumStockLevel,
    imageFile,
  ];
}

final class DeletePackageEvent extends InventoryEvent {
  final String packageId;

  const DeletePackageEvent({required this.packageId});

  @override
  List<Object?> get props => [packageId];
}

final class RestorePackageEvent extends InventoryEvent {
  final String packageId;

  const RestorePackageEvent({required this.packageId});

  @override
  List<Object?> get props => [packageId];
}

final class GetDeletedPackagesEvent extends InventoryEvent {
  final int? page;
  final int? limit;

  const GetDeletedPackagesEvent({this.page, this.limit});

  @override
  List<Object?> get props => [page, limit];
}

final class PermanentlyDeletePackageEvent extends InventoryEvent {
  final String packageId;

  const PermanentlyDeletePackageEvent({required this.packageId});

  @override
  List<Object?> get props => [packageId];
}

final class RestockPackageEvent extends InventoryEvent {
  final String packageId;
  final int quantity;

  const RestockPackageEvent({required this.packageId, required this.quantity});

  @override
  List<Object?> get props => [packageId, quantity];
}

final class AdjustPackageStockEvent extends InventoryEvent {
  final String packageId;
  final int adjustment;
  final String reason;

  const AdjustPackageStockEvent({
    required this.packageId,
    required this.adjustment,
    required this.reason,
  });

  @override
  List<Object?> get props => [packageId, adjustment, reason];
}

final class BulkUpdatePackageStatusEvent extends InventoryEvent {
  final List<String> packageIds;
  final PackageStatus status;

  const BulkUpdatePackageStatusEvent({
    required this.packageIds,
    required this.status,
  });

  @override
  List<Object?> get props => [packageIds, status];
}

// Spare Part Events
final class GetSparePartsEvent extends InventoryEvent {
  final SparePartCategory? category;
  final SparePartStatus? status;
  final List<CylinderType>? compatibleWith;
  final int? page;
  final int? limit;

  const GetSparePartsEvent({
    this.category,
    this.status,
    this.compatibleWith,
    this.page,
    this.limit,
  });

  @override
  List<Object?> get props => [category, status, compatibleWith, page, limit];
}

final class GetSparePartByIdEvent extends InventoryEvent {
  final String sparePartId;

  const GetSparePartByIdEvent({required this.sparePartId});

  @override
  List<Object?> get props => [sparePartId];
}

final class CreateSparePartEvent extends InventoryEvent {
  final String description;
  final double price;
  final double cost;
  final SparePartCategory category;
  final List<CylinderType> compatibleCylinderTypes;
  final String barcode;
  final int minimumStockLevel;
  final int initialStock;
  final int initialReserved;
  final int initialSold;
  final XFile? imageFile;

  const CreateSparePartEvent({
    required this.description,
    required this.price,
    required this.cost,
    required this.category,
    required this.compatibleCylinderTypes,
    required this.barcode,
    required this.minimumStockLevel,
    required this.initialStock,
    this.initialReserved = 0,
    this.initialSold = 0,
    this.imageFile,
  });

  @override
  List<Object?> get props => [
    description,
    price,
    cost,
    category,
    compatibleCylinderTypes,
    barcode,
    minimumStockLevel,
    initialStock,
    initialReserved,
    initialSold,
    imageFile,
  ];
}

final class UpdateSparePartEvent extends InventoryEvent {
  final String sparePartId;
  final String? name;
  final String? description;
  final double? price;
  final double? cost;
  final SparePartCategory? category;
  final List<CylinderType>? compatibleCylinderTypes;
  final String? barcode;
  final int? minimumStockLevel;
  final XFile? imageFile;

  const UpdateSparePartEvent({
    required this.sparePartId,
    this.name,
    this.description,
    this.price,
    this.cost,
    this.category,
    this.compatibleCylinderTypes,
    this.barcode,
    this.minimumStockLevel,
    this.imageFile,
  });

  @override
  List<Object?> get props => [
    sparePartId,
    name,
    description,
    price,
    cost,
    category,
    compatibleCylinderTypes,
    barcode,
    minimumStockLevel,
    imageFile,
  ];
}

final class DeleteSparePartEvent extends InventoryEvent {
  final String sparePartId;

  const DeleteSparePartEvent({required this.sparePartId});

  @override
  List<Object?> get props => [sparePartId];
}

final class RestoreSparePartEvent extends InventoryEvent {
  final String sparePartId;

  const RestoreSparePartEvent({required this.sparePartId});

  @override
  List<Object?> get props => [sparePartId];
}

final class GetDeletedSparePartsEvent extends InventoryEvent {
  final int? page;
  final int? limit;

  const GetDeletedSparePartsEvent({this.page, this.limit});

  @override
  List<Object?> get props => [page, limit];
}

final class PermanentlyDeleteSparePartEvent extends InventoryEvent {
  final String sparePartId;

  const PermanentlyDeleteSparePartEvent({required this.sparePartId});

  @override
  List<Object?> get props => [sparePartId];
}

final class RestockSparePartEvent extends InventoryEvent {
  final String sparePartId;
  final int quantity;

  const RestockSparePartEvent({
    required this.sparePartId,
    required this.quantity,
  });

  @override
  List<Object?> get props => [sparePartId, quantity];
}

final class AdjustSparePartStockEvent extends InventoryEvent {
  final String sparePartId;
  final int adjustment;
  final String reason;

  const AdjustSparePartStockEvent({
    required this.sparePartId,
    required this.adjustment,
    required this.reason,
  });

  @override
  List<Object?> get props => [sparePartId, adjustment, reason];
}

final class BulkUpdateSparePartStatusEvent extends InventoryEvent {
  final List<String> sparePartIds;
  final SparePartStatus status;

  const BulkUpdateSparePartStatusEvent({
    required this.sparePartIds,
    required this.status,
  });

  @override
  List<Object?> get props => [sparePartIds, status];
}
