part of 'inventory_bloc.dart';

sealed class InventoryState extends Equatable {
  const InventoryState();

  @override
  List<Object?> get props => [];
}

final class InventoryInitial extends InventoryState {}

// Cylinder States
/// Get Cylinders States
class GetCylindersLoading extends InventoryState {}

class GetCylindersSuccess extends InventoryState {
  final List<CylinderEntity> cylinders;

  const GetCylindersSuccess({required this.cylinders});

  @override
  List<Object?> get props => [cylinders];
}

class GetCylindersFailure extends InventoryState {
  final AppFailure appFailure;

  const GetCylindersFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Get Cylinder By Id States
class GetCylinderByIdLoading extends InventoryState {}

class GetCylinderByIdSuccess extends InventoryState {
  final CylinderEntity cylinder;

  const GetCylinderByIdSuccess({required this.cylinder});

  @override
  List<Object?> get props => [cylinder];
}

class GetCylinderByIdFailure extends InventoryState {
  final AppFailure appFailure;

  const GetCylinderByIdFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Create Cylinder States
class CreateCylinderLoading extends InventoryState {}

class CreateCylinderSuccess extends InventoryState {
  final String message;

  const CreateCylinderSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class CreateCylinderFailure extends InventoryState {
  final AppFailure appFailure;

  const CreateCylinderFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Update Cylinder States
class UpdateCylinderLoading extends InventoryState {}

class UpdateCylinderSuccess extends InventoryState {
  final String message;

  const UpdateCylinderSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class UpdateCylinderFailure extends InventoryState {
  final AppFailure appFailure;

  const UpdateCylinderFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Delete Cylinder States
class DeleteCylinderLoading extends InventoryState {}

class DeleteCylinderSuccess extends InventoryState {
  final String message;

  const DeleteCylinderSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class DeleteCylinderFailure extends InventoryState {
  final AppFailure appFailure;

  const DeleteCylinderFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Restore Cylinder States
class RestoreCylinderLoading extends InventoryState {}

class RestoreCylinderSuccess extends InventoryState {
  final String message;

  const RestoreCylinderSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class RestoreCylinderFailure extends InventoryState {
  final AppFailure appFailure;

  const RestoreCylinderFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Get Deleted Cylinders States
class GetDeletedCylindersLoading extends InventoryState {}

class GetDeletedCylindersSuccess extends InventoryState {
  final List<CylinderEntity> cylinders;

  const GetDeletedCylindersSuccess({required this.cylinders});

  @override
  List<Object?> get props => [cylinders];
}

class GetDeletedCylindersFailure extends InventoryState {
  final AppFailure appFailure;

  const GetDeletedCylindersFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Permanently Delete Cylinder States
class PermanentlyDeleteCylinderLoading extends InventoryState {}

class PermanentlyDeleteCylinderSuccess extends InventoryState {}

class PermanentlyDeleteCylinderFailure extends InventoryState {
  final AppFailure appFailure;

  const PermanentlyDeleteCylinderFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Restock Cylinder States
class RestockCylinderLoading extends InventoryState {}

class RestockCylinderSuccess extends InventoryState {
  final String message;

  const RestockCylinderSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class RestockCylinderFailure extends InventoryState {
  final AppFailure appFailure;

  const RestockCylinderFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Adjust Cylinder Stock States
class AdjustCylinderStockLoading extends InventoryState {}

class AdjustCylinderStockSuccess extends InventoryState {
  final String message;

  const AdjustCylinderStockSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class AdjustCylinderStockFailure extends InventoryState {
  final AppFailure appFailure;

  const AdjustCylinderStockFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Bulk Update Cylinder Status States
class BulkUpdateCylinderStatusLoading extends InventoryState {}

class BulkUpdateCylinderStatusSuccess extends InventoryState {
  final String message;

  const BulkUpdateCylinderStatusSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class BulkUpdateCylinderStatusFailure extends InventoryState {
  final AppFailure appFailure;

  const BulkUpdateCylinderStatusFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

// Package States
/// Get Packages States
class GetPackagesLoading extends InventoryState {}

class GetPackagesSuccess extends InventoryState {
  final List<PackageEntity> packages;

  const GetPackagesSuccess({required this.packages});

  @override
  List<Object?> get props => [packages];
}

class GetPackagesFailure extends InventoryState {
  final AppFailure appFailure;

  const GetPackagesFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Get Package By Id States
class GetPackageByIdLoading extends InventoryState {}

class GetPackageByIdSuccess extends InventoryState {
  final PackageEntity package;

  const GetPackageByIdSuccess({required this.package});

  @override
  List<Object?> get props => [package];
}

class GetPackageByIdFailure extends InventoryState {
  final AppFailure appFailure;

  const GetPackageByIdFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Create Package States
class CreatePackageLoading extends InventoryState {}

class CreatePackageSuccess extends InventoryState {
  final String message;

  const CreatePackageSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class CreatePackageFailure extends InventoryState {
  final AppFailure appFailure;

  const CreatePackageFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Update Package States
class UpdatePackageLoading extends InventoryState {}

class UpdatePackageSuccess extends InventoryState {
  final String message;

  const UpdatePackageSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class UpdatePackageFailure extends InventoryState {
  final AppFailure appFailure;

  const UpdatePackageFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Delete Package States
class DeletePackageLoading extends InventoryState {}

class DeletePackageSuccess extends InventoryState {
  final String message;

  const DeletePackageSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class DeletePackageFailure extends InventoryState {
  final AppFailure appFailure;

  const DeletePackageFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Restore Package States
class RestorePackageLoading extends InventoryState {}

class RestorePackageSuccess extends InventoryState {
  final String message;

  const RestorePackageSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class RestorePackageFailure extends InventoryState {
  final AppFailure appFailure;

  const RestorePackageFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Get Deleted Packages States
class GetDeletedPackagesLoading extends InventoryState {}

class GetDeletedPackagesSuccess extends InventoryState {
  final List<PackageEntity> packages;

  const GetDeletedPackagesSuccess({required this.packages});

  @override
  List<Object?> get props => [packages];
}

class GetDeletedPackagesFailure extends InventoryState {
  final AppFailure appFailure;

  const GetDeletedPackagesFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Permanently Delete Package States
class PermanentlyDeletePackageLoading extends InventoryState {}

class PermanentlyDeletePackageSuccess extends InventoryState {}

class PermanentlyDeletePackageFailure extends InventoryState {
  final AppFailure appFailure;

  const PermanentlyDeletePackageFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Restock Package States
class RestockPackageLoading extends InventoryState {}

class RestockPackageSuccess extends InventoryState {
  final String message;

  const RestockPackageSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class RestockPackageFailure extends InventoryState {
  final AppFailure appFailure;

  const RestockPackageFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Adjust Package Stock States
class AdjustPackageStockLoading extends InventoryState {}

class AdjustPackageStockSuccess extends InventoryState {
  final String message;

  const AdjustPackageStockSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class AdjustPackageStockFailure extends InventoryState {
  final AppFailure appFailure;

  const AdjustPackageStockFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Bulk Update Package Status States
class BulkUpdatePackageStatusLoading extends InventoryState {}

class BulkUpdatePackageStatusSuccess extends InventoryState {
  final String message;

  const BulkUpdatePackageStatusSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class BulkUpdatePackageStatusFailure extends InventoryState {
  final AppFailure appFailure;

  const BulkUpdatePackageStatusFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

// Spare Part States
/// Get Spare Parts States
class GetSparePartsLoading extends InventoryState {}

class GetSparePartsSuccess extends InventoryState {
  final List<SparePartEntity> spareParts;

  const GetSparePartsSuccess({required this.spareParts});

  @override
  List<Object?> get props => [spareParts];
}

class GetSparePartsFailure extends InventoryState {
  final AppFailure appFailure;

  const GetSparePartsFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Get Spare Part By Id States
class GetSparePartByIdLoading extends InventoryState {}

class GetSparePartByIdSuccess extends InventoryState {
  final SparePartEntity sparePart;

  const GetSparePartByIdSuccess({required this.sparePart});

  @override
  List<Object?> get props => [sparePart];
}

class GetSparePartByIdFailure extends InventoryState {
  final AppFailure appFailure;

  const GetSparePartByIdFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Create Spare Part States
class CreateSparePartLoading extends InventoryState {}

class CreateSparePartSuccess extends InventoryState {
  final String message;

  const CreateSparePartSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class CreateSparePartFailure extends InventoryState {
  final AppFailure appFailure;

  const CreateSparePartFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Update Spare Part States
class UpdateSparePartLoading extends InventoryState {}

class UpdateSparePartSuccess extends InventoryState {
  final String message;

  const UpdateSparePartSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class UpdateSparePartFailure extends InventoryState {
  final AppFailure appFailure;

  const UpdateSparePartFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Delete Spare Part States
class DeleteSparePartLoading extends InventoryState {}

class DeleteSparePartSuccess extends InventoryState {
  final String message;

  const DeleteSparePartSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class DeleteSparePartFailure extends InventoryState {
  final AppFailure appFailure;

  const DeleteSparePartFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Restore Spare Part States
class RestoreSparePartLoading extends InventoryState {}

class RestoreSparePartSuccess extends InventoryState {
  final String message;

  const RestoreSparePartSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class RestoreSparePartFailure extends InventoryState {
  final AppFailure appFailure;

  const RestoreSparePartFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Get Deleted Spare Parts States
class GetDeletedSparePartsLoading extends InventoryState {}

class GetDeletedSparePartsSuccess extends InventoryState {
  final List<SparePartEntity> spareParts;

  const GetDeletedSparePartsSuccess({required this.spareParts});

  @override
  List<Object?> get props => [spareParts];
}

class GetDeletedSparePartsFailure extends InventoryState {
  final AppFailure appFailure;

  const GetDeletedSparePartsFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Permanently Delete Spare Part States
class PermanentlyDeleteSparePartLoading extends InventoryState {}

class PermanentlyDeleteSparePartSuccess extends InventoryState {}

class PermanentlyDeleteSparePartFailure extends InventoryState {
  final AppFailure appFailure;

  const PermanentlyDeleteSparePartFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Restock Spare Part States
class RestockSparePartLoading extends InventoryState {}

class RestockSparePartSuccess extends InventoryState {
  final String message;

  const RestockSparePartSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class RestockSparePartFailure extends InventoryState {
  final AppFailure appFailure;

  const RestockSparePartFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Adjust Spare Part Stock States
class AdjustSparePartStockLoading extends InventoryState {}

class AdjustSparePartStockSuccess extends InventoryState {
  final String message;

  const AdjustSparePartStockSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class AdjustSparePartStockFailure extends InventoryState {
  final AppFailure appFailure;

  const AdjustSparePartStockFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

/// Bulk Update Spare Part Status States
class BulkUpdateSparePartStatusLoading extends InventoryState {}

class BulkUpdateSparePartStatusSuccess extends InventoryState {
  final String message;

  const BulkUpdateSparePartStatusSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class BulkUpdateSparePartStatusFailure extends InventoryState {
  final AppFailure appFailure;

  const BulkUpdateSparePartStatusFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}
