// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCgAjDQKlBWBzvLQw59kFb3kIBVo7GlCUo',
    appId: '1:276480440301:web:bbf4aa99185379b84a59e4',
    messagingSenderId: '276480440301',
    projectId: 'ciribey-system-project',
    authDomain: 'ciribey-system-project.firebaseapp.com',
    storageBucket: 'ciribey-system-project.firebasestorage.app',
    measurementId: 'G-GVRY96VREM',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyA68qV9y6_-wcqrkPV-vy4PoqQAM7-Trjw',
    appId: '1:276480440301:android:03445e9eb0e60eb04a59e4',
    messagingSenderId: '276480440301',
    projectId: 'ciribey-system-project',
    storageBucket: 'ciribey-system-project.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDDnaFr429rbzZpHH1ueB6vqdiHPwQxcLI',
    appId: '1:276480440301:ios:12c82efa2043cee34a59e4',
    messagingSenderId: '276480440301',
    projectId: 'ciribey-system-project',
    storageBucket: 'ciribey-system-project.firebasestorage.app',
    iosBundleId: 'com.example.frontend',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDDnaFr429rbzZpHH1ueB6vqdiHPwQxcLI',
    appId: '1:276480440301:ios:12c82efa2043cee34a59e4',
    messagingSenderId: '276480440301',
    projectId: 'ciribey-system-project',
    storageBucket: 'ciribey-system-project.firebasestorage.app',
    iosBundleId: 'com.example.frontend',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCgAjDQKlBWBzvLQw59kFb3kIBVo7GlCUo',
    appId: '1:276480440301:web:d2a47e815b2dfae24a59e4',
    messagingSenderId: '276480440301',
    projectId: 'ciribey-system-project',
    authDomain: 'ciribey-system-project.firebaseapp.com',
    storageBucket: 'ciribey-system-project.firebasestorage.app',
    measurementId: 'G-26HJ7Z43XZ',
  );
}
