name: frontend
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  # UI & Styling
  cupertino_icons: ^1.0.8
  google_fonts: ^6.2.1
  flutter_screenutil: ^5.9.3
  another_flushbar: ^1.12.30
  shimmer: ^3.0.0

  # QR Code AND Scanning
  qr_flutter: ^4.1.0
  mobile_scanner: ^7.0.1
  permission_handler: ^11.3.1



  # State Management
  flutter_bloc: ^9.1.1
  equatable: ^2.0.7

  # Navigation
  go_router: ^14.6.2

  # Networking
  dio: ^5.8.0+1
  cached_network_image: ^3.4.1

  # Dependency Injection
  get_it: ^8.0.2
  injectable: ^2.5.0

  # Local Storage
  shared_preferences: ^2.3.2
  flutter_secure_storage: ^9.2.4

  # Firebase
  firebase_core: ^3.14.0
  firebase_messaging: ^15.2.7
  flutter_local_notifications: ^19.2.1

  # Functional Programming
  fpdart: ^1.1.1
  stream_transform: ^2.1.1

  # Utilities
  logger: ^2.5.0
  intl: ^0.20.2
  flutter_dotenv: ^5.2.1
  internet_connection_checker_plus: ^2.7.2
  share_plus: ^11.0.0

  # Media
  image_picker: ^1.1.2
  syncfusion_flutter_charts: ^29.2.11
  syncfusion_flutter_gauges: ^29.2.11
  url_launcher: ^6.3.1
  syncfusion_flutter_datepicker: ^29.2.11
  path: ^1.9.1
  http_parser: ^4.1.2
  in_app_review: ^2.0.10

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0

  # Code Generation
  # flutter_launcher_icons: ^0.14.3
  # flutter_native_splash: ^2.4.4
  # flutter_gen_runner: ^5.9.0
  # objectbox_generator: ^4.0.3
  build_runner: ^2.4.15
  injectable_generator: ^2.7.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/icons/
    - assets/images/
    - .env.production

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
