<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gas Delivery System - Business Presentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 30px rgba(0,0,0,0.2);
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 80px 40px;
            position: relative;
            overflow: hidden;
        }
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        .hero-content {
            position: relative;
            z-index: 1;
        }
        .hero-section h1 {
            font-size: 3.5em;
            margin: 0 0 20px 0;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .hero-section .subtitle {
            font-size: 1.4em;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        .hero-section .tagline {
            font-size: 1.1em;
            background: rgba(255,255,255,0.2);
            padding: 15px 30px;
            border-radius: 50px;
            display: inline-block;
            backdrop-filter: blur(10px);
        }
        .content-section {
            padding: 60px 40px;
        }
        .section-title {
            text-align: center;
            font-size: 2.5em;
            color: #333;
            margin-bottom: 20px;
            position: relative;
        }
        .section-title::after {
            content: '';
            display: block;
            width: 80px;
            height: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 20px auto;
            border-radius: 2px;
        }
        .section-subtitle {
            text-align: center;
            font-size: 1.2em;
            color: #666;
            margin-bottom: 50px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin: 50px 0;
        }
        .benefit-card {
            background: white;
            border-radius: 15px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid #f0f0f0;
        }
        .benefit-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        .benefit-icon {
            font-size: 3em;
            margin-bottom: 20px;
            display: block;
        }
        .benefit-card h3 {
            color: #333;
            font-size: 1.4em;
            margin-bottom: 15px;
        }
        .benefit-card p {
            color: #666;
            line-height: 1.6;
        }
        .features-section {
            background: #f8f9fa;
            padding: 80px 40px;
        }
        .feature-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin: 50px 0;
        }
        .feature-item {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        }
        .feature-item h4 {
            color: #667eea;
            font-size: 1.3em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .feature-item ul {
            list-style: none;
            padding: 0;
        }
        .feature-item li {
            padding: 8px 0;
            position: relative;
            padding-left: 25px;
        }
        .feature-item li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        .roles-section {
            padding: 80px 40px;
            background: white;
        }
        .roles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin: 50px 0;
        }
        .role-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .role-card.customer { border-top: 5px solid #007bff; }
        .role-card.agent { border-top: 5px solid #28a745; }
        .role-card.supervisor { border-top: 5px solid #ffc107; }
        .role-card.admin { border-top: 5px solid #dc3545; }
        .role-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
            display: block;
        }
        .role-card h3 {
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        .role-card p {
            color: #666;
            font-size: 0.95em;
        }
        .cta-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 80px 40px;
        }
        .cta-section h2 {
            font-size: 2.5em;
            margin-bottom: 20px;
        }
        .cta-section p {
            font-size: 1.2em;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        .cta-button {
            display: inline-block;
            background: white;
            color: #667eea;
            padding: 15px 40px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1em;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        .stats-section {
            background: #2c3e50;
            color: white;
            padding: 60px 40px;
            text-align: center;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            margin: 40px 0;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 3em;
            font-weight: 700;
            color: #3498db;
            display: block;
        }
        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
            margin-top: 10px;
        }
        @media print {
            body { background: white; }
            .hero-section { background: #667eea !important; }
            .page-break { page-break-before: always; }
        }
        @media (max-width: 768px) {
            .hero-section { padding: 60px 20px; }
            .hero-section h1 { font-size: 2.5em; }
            .content-section { padding: 40px 20px; }
            .benefits-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-content">
                <h1>🔥 Gas Delivery System</h1>
                <p class="subtitle">Revolutionary Digital Platform for Gas Cylinder Delivery</p>
                <div class="tagline">Streamline Operations • Enhance Customer Experience • Maximize Efficiency</div>
            </div>
        </section>

        <!-- Business Benefits Section -->
        <section class="content-section">
            <h2 class="section-title">Transform Your Business</h2>
            <p class="section-subtitle">Our comprehensive platform revolutionizes gas delivery operations, providing seamless experiences for customers while empowering your team with powerful management tools.</p>
            
            <div class="benefits-grid">
                <div class="benefit-card">
                    <span class="benefit-icon">📱</span>
                    <h3>Mobile-First Experience</h3>
                    <p>Intuitive mobile app that customers love to use. Easy ordering, real-time tracking, and seamless payment processing all in one place.</p>
                </div>
                
                <div class="benefit-card">
                    <span class="benefit-icon">⚡</span>
                    <h3>Instant Order Processing</h3>
                    <p>Automated order management from creation to delivery. Reduce manual work and eliminate errors with our intelligent system.</p>
                </div>
                
                <div class="benefit-card">
                    <span class="benefit-icon">💰</span>
                    <h3>Secure Payment Integration</h3>
                    <p>WaaFi mobile money integration ensures secure, fast payments. No more cash handling or payment delays.</p>
                </div>
                
                <div class="benefit-card">
                    <span class="benefit-icon">📊</span>
                    <h3>Real-Time Analytics</h3>
                    <p>Make data-driven decisions with comprehensive dashboards showing sales, inventory, and performance metrics.</p>
                </div>
                
                <div class="benefit-card">
                    <span class="benefit-icon">🚚</span>
                    <h3>Smart Delivery Management</h3>
                    <p>QR code verification, route optimization, and real-time tracking ensure efficient and secure deliveries.</p>
                </div>
                
                <div class="benefit-card">
                    <span class="benefit-icon">📦</span>
                    <h3>Intelligent Inventory</h3>
                    <p>Automatic stock management prevents overselling and ensures optimal inventory levels with smart alerts.</p>
                </div>
            </div>
        </section>

        <!-- Key Features Section -->
        <section class="features-section">
            <h2 class="section-title">Powerful Features</h2>
            <p class="section-subtitle">Everything you need to run a modern gas delivery business</p>
            
            <div class="feature-showcase">
                <div class="feature-item">
                    <h4>🛍️ Customer Experience</h4>
                    <ul>
                        <li>Browse gas cylinders, spare parts, and packages</li>
                        <li>Easy ordering with quantity selection</li>
                        <li>Real-time order tracking</li>
                        <li>QR code delivery verification</li>
                        <li>Order history and receipts</li>
                    </ul>
                </div>
                
                <div class="feature-item">
                    <h4>🚚 Delivery Management</h4>
                    <ul>
                        <li>Agent dashboard with delivery assignments</li>
                        <li>QR code scanning for verification</li>
                        <li>Real-time status updates</li>
                        <li>Performance tracking and metrics</li>
                        <li>Route optimization capabilities</li>
                    </ul>
                </div>
                
                <div class="feature-item">
                    <h4>📊 Business Intelligence</h4>
                    <ul>
                        <li>Comprehensive sales analytics</li>
                        <li>Inventory management and alerts</li>
                        <li>Financial reporting and insights</li>
                        <li>Agent performance monitoring</li>
                        <li>Customer behavior analysis</li>
                    </ul>
                </div>
                
                <div class="feature-item">
                    <h4>⚙️ System Administration</h4>
                    <ul>
                        <li>User and role management</li>
                        <li>Product catalog management</li>
                        <li>Order processing and assignment</li>
                        <li>Payment gateway integration</li>
                        <li>System health monitoring</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- User Roles Section -->
        <section class="roles-section">
            <h2 class="section-title">Built for Every User</h2>
            <p class="section-subtitle">Tailored experiences for different roles in your organization</p>

            <div class="roles-grid">
                <div class="role-card customer">
                    <span class="role-icon">🛍️</span>
                    <h3>Customers</h3>
                    <p>Easy product browsing, seamless ordering, real-time tracking, and secure payments. Everything customers need for a great experience.</p>
                </div>

                <div class="role-card agent">
                    <span class="role-icon">🚚</span>
                    <h3>Delivery Agents</h3>
                    <p>Streamlined delivery management with QR verification, performance tracking, and easy-to-use mobile interface for efficient operations.</p>
                </div>

                <div class="role-card supervisor">
                    <span class="role-icon">👨‍💼</span>
                    <h3>Supervisors</h3>
                    <p>Daily operations oversight with order assignment capabilities, today's metrics, and agent management tools for smooth coordination.</p>
                </div>

                <div class="role-card admin">
                    <span class="role-icon">⚡</span>
                    <h3>Administrators</h3>
                    <p>Complete system control with inventory management, user administration, comprehensive analytics, and full business oversight.</p>
                </div>
            </div>
        </section>

        <!-- Statistics Section -->
        <section class="stats-section">
            <h2 class="section-title" style="color: white;">System Capabilities</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number">4</span>
                    <div class="stat-label">User Roles</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">3</span>
                    <div class="stat-label">Product Categories</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">6</span>
                    <div class="stat-label">Order Statuses</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">24/7</span>
                    <div class="stat-label">System Availability</div>
                </div>
            </div>
        </section>

        <!-- Process Flow Section -->
        <section class="content-section">
            <h2 class="section-title">How It Works</h2>
            <p class="section-subtitle">Simple, efficient process from order to delivery</p>

            <div class="feature-showcase">
                <div class="feature-item">
                    <h4>1️⃣ Customer Orders</h4>
                    <p>Customers browse products, select quantities, and place orders through the intuitive mobile app. Payment is processed securely through WaaFi mobile money.</p>
                </div>

                <div class="feature-item">
                    <h4>2️⃣ Order Processing</h4>
                    <p>System automatically reserves inventory, processes payment, and generates QR codes for delivery verification. Orders are ready for assignment.</p>
                </div>

                <div class="feature-item">
                    <h4>3️⃣ Agent Assignment</h4>
                    <p>Supervisors or admins assign confirmed orders to available delivery agents based on location, capacity, and availability.</p>
                </div>

                <div class="feature-item">
                    <h4>4️⃣ Delivery & Verification</h4>
                    <p>Agents deliver products and scan customer QR codes for verification. Order is completed automatically with inventory updates.</p>
                </div>
            </div>
        </section>

        <!-- Benefits Section -->
        <section class="features-section">
            <h2 class="section-title">Business Impact</h2>
            <p class="section-subtitle">Measurable improvements for your business</p>

            <div class="benefits-grid">
                <div class="benefit-card">
                    <span class="benefit-icon">📈</span>
                    <h3>Increased Sales</h3>
                    <p>Easy ordering process and mobile accessibility lead to more orders and higher customer satisfaction.</p>
                </div>

                <div class="benefit-card">
                    <span class="benefit-icon">⏱️</span>
                    <h3>Reduced Processing Time</h3>
                    <p>Automated workflows eliminate manual tasks, reducing order processing time by up to 70%.</p>
                </div>

                <div class="benefit-card">
                    <span class="benefit-icon">🎯</span>
                    <h3>Better Accuracy</h3>
                    <p>Automated inventory management and QR verification eliminate human errors and ensure accurate deliveries.</p>
                </div>

                <div class="benefit-card">
                    <span class="benefit-icon">💡</span>
                    <h3>Data-Driven Decisions</h3>
                    <p>Real-time analytics provide insights for better inventory planning, pricing strategies, and business growth.</p>
                </div>

                <div class="benefit-card">
                    <span class="benefit-icon">🔒</span>
                    <h3>Enhanced Security</h3>
                    <p>QR code verification and secure payment processing protect against fraud and ensure legitimate deliveries.</p>
                </div>

                <div class="benefit-card">
                    <span class="benefit-icon">😊</span>
                    <h3>Customer Satisfaction</h3>
                    <p>Real-time tracking, easy ordering, and reliable delivery create exceptional customer experiences.</p>
                </div>
            </div>
        </section>

        <!-- Call to Action Section -->
        <section class="cta-section">
            <h2>Ready to Transform Your Business?</h2>
            <p>Join the digital revolution in gas delivery services. Streamline operations, delight customers, and grow your business with our comprehensive platform.</p>
            <a href="#contact" class="cta-button">Get Started Today</a>
        </section>

        <!-- Footer -->
        <footer style="background: #2c3e50; color: white; text-align: center; padding: 40px;">
            <h3>🔥 Gas Delivery System</h3>
            <p>Revolutionary Digital Platform for Gas Cylinder Delivery</p>
            <p style="opacity: 0.7; margin-top: 20px;">
                <strong>Contact:</strong> Ready to discuss your digital transformation?
                <br>Let's schedule a demo and explore how this system can benefit your business.
            </p>
        </footer>
    </div>

    <script>
        // Add smooth scrolling and interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate benefit cards on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe all benefit cards
            document.querySelectorAll('.benefit-card, .feature-item, .role-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });

            // Add click handler for CTA button
            document.querySelector('.cta-button').addEventListener('click', function(e) {
                e.preventDefault();
                alert('Thank you for your interest! Please contact us to schedule a demo and discuss implementation details.');
            });
        });
    </script>
</body>
</html>
