<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gas Delivery System - Complete Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #007bff;
            font-size: 2.5em;
            margin: 0;
        }
        .header p {
            color: #666;
            font-size: 1.2em;
            margin: 10px 0 0 0;
        }
        h2 {
            color: #007bff;
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin-top: 40px;
        }
        h3 {
            color: #495057;
            margin-top: 30px;
        }
        .role-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .role-card.customer { border-left-color: #007bff; }
        .role-card.agent { border-left-color: #28a745; }
        .role-card.supervisor { border-left-color: #ffc107; }
        .role-card.admin { border-left-color: #dc3545; }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .feature-card h4 {
            color: #007bff;
            margin-top: 0;
        }
        .workflow-step {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 10px 0;
            border-radius: 0 8px 8px 0;
        }
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .tech-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .permissions-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .permissions-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .permissions-list li {
            margin: 5px 0;
        }
        .allowed { color: #28a745; }
        .restricted { color: #dc3545; }
        .toc {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
            padding-left: 20px;
        }
        .toc a {
            text-decoration: none;
            color: #007bff;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        @media print {
            body { background: white; }
            .container { box-shadow: none; }
            .page-break { page-break-before: always; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 Gas Delivery System</h1>
            <p>Complete System Documentation & User Guide</p>
            <p><strong>Version 1.0</strong> | <em>Comprehensive Role-Based Gas Cylinder Delivery Platform</em></p>
        </div>

        <div class="toc">
            <h2>📋 Table of Contents</h2>
            <ul>
                <li><a href="#overview">1. System Overview</a></li>
                <li><a href="#architecture">2. Technical Architecture</a></li>
                <li><a href="#user-roles">3. User Roles & Permissions</a></li>
                <li><a href="#core-features">4. Core Features</a></li>
                <li><a href="#workflows">5. Business Workflows</a></li>
                <li><a href="#interfaces">6. User Interfaces</a></li>
                <li><a href="#admin-features">7. Administrative Features</a></li>
                <li><a href="#technical-specs">8. Technical Specifications</a></li>
            </ul>
        </div>

        <div class="page-break"></div>

        <section id="overview">
            <h2>🎯 1. System Overview</h2>
            <p>The Gas Delivery System is a comprehensive full-stack application designed to streamline gas cylinder delivery operations. It provides a complete ecosystem for customers to place orders, agents to manage deliveries, supervisors to oversee operations, and administrators to manage the entire system.</p>
            
            <h3>Key Capabilities</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔐 Multi-Role Access Control</h4>
                    <p>Four distinct user roles (Customer, Agent, Supervisor, Admin) with specific permissions and tailored interfaces.</p>
                </div>
                <div class="feature-card">
                    <h4>📦 Real-time Inventory Management</h4>
                    <p>Live stock tracking with automatic reservation, release, and sales tracking across multiple product categories.</p>
                </div>
                <div class="feature-card">
                    <h4>🛒 Complete Order Lifecycle</h4>
                    <p>From order creation through payment processing to delivery confirmation with QR code verification.</p>
                </div>
                <div class="feature-card">
                    <h4>💳 Secure Payment Integration</h4>
                    <p>WaaFi payment gateway integration with preauthorization, capture, and failure handling.</p>
                </div>
                <div class="feature-card">
                    <h4>📱 Mobile-First Design</h4>
                    <p>Responsive Flutter frontend optimized for mobile devices with cross-platform compatibility.</p>
                </div>
                <div class="feature-card">
                    <h4>📊 Advanced Analytics</h4>
                    <p>Role-specific dashboards with sales metrics, inventory insights, and performance analytics.</p>
                </div>
            </div>
        </section>

        <div class="page-break"></div>

        <section id="architecture">
            <h2>🛠 2. Technical Architecture</h2>
            
            <h3>Technology Stack</h3>
            <div class="tech-stack">
                <div class="tech-item">
                    <h4>Frontend</h4>
                    <p><strong>Flutter</strong><br>Cross-platform mobile app with responsive design</p>
                </div>
                <div class="tech-item">
                    <h4>Backend</h4>
                    <p><strong>Node.js + Express</strong><br>RESTful API with TypeScript</p>
                </div>
                <div class="tech-item">
                    <h4>Database</h4>
                    <p><strong>MongoDB</strong><br>NoSQL database with transactions</p>
                </div>
                <div class="tech-item">
                    <h4>Authentication</h4>
                    <p><strong>JWT</strong><br>Secure token-based authentication</p>
                </div>
                <div class="tech-item">
                    <h4>Payments</h4>
                    <p><strong>WaaFi Gateway</strong><br>Mobile money integration</p>
                </div>
                <div class="tech-item">
                    <h4>Notifications</h4>
                    <p><strong>FCM + SMS</strong><br>Push notifications and SMS alerts</p>
                </div>
            </div>

            <h3>System Architecture</h3>
            <div class="workflow-step">
                <strong>Client Layer:</strong> Flutter mobile application with role-based UI components and state management using BLoC pattern.
            </div>
            <div class="workflow-step">
                <strong>API Layer:</strong> Express.js REST API with middleware for authentication, authorization, logging, and error handling.
            </div>
            <div class="workflow-step">
                <strong>Business Logic:</strong> Service layer handling order processing, inventory management, payment integration, and notification delivery.
            </div>
            <div class="workflow-step">
                <strong>Data Layer:</strong> MongoDB with Mongoose ODM, featuring transaction support and optimized indexing for performance.
            </div>
            <div class="workflow-step">
                <strong>External Services:</strong> WaaFi payment gateway, Hormuud SMS service, and Firebase Cloud Messaging for notifications.
            </div>
        </section>

        <div class="page-break"></div>

        <section id="user-roles">
            <h2>👥 3. User Roles & Permissions</h2>
            
            <div class="role-card customer">
                <h3>🛍️ Customer Role</h3>
                <p><strong>Primary Function:</strong> Browse products, place orders, and track deliveries</p>
                
                <div class="permissions-list">
                    <h4>✅ Allowed Actions:</h4>
                    <ul>
                        <li class="allowed">Browse gas cylinders, spare parts, and packages</li>
                        <li class="allowed">Add products to cart and place orders</li>
                        <li class="allowed">Make payments through WaaFi gateway</li>
                        <li class="allowed">Track order status in real-time</li>
                        <li class="allowed">View order history and details</li>
                        <li class="allowed">Generate and display QR codes for delivery verification</li>
                        <li class="allowed">Manage delivery addresses</li>
                        <li class="allowed">Update profile information</li>
                    </ul>
                    
                    <h4>❌ Restrictions:</h4>
                    <ul>
                        <li class="restricted">Cannot access inventory management</li>
                        <li class="restricted">Cannot view other customers' orders</li>
                        <li class="restricted">Cannot access admin or agent features</li>
                        <li class="restricted">Cannot modify product prices or inventory</li>
                    </ul>
                </div>
                
                <h4>Interface Features:</h4>
                <p>Three-tab navigation: <strong>Home</strong> (product catalog), <strong>Orders</strong> (order tracking), <strong>Profile</strong> (account management)</p>
            </div>

            <div class="role-card agent">
                <h3>🚚 Agent Role</h3>
                <p><strong>Primary Function:</strong> Manage assigned deliveries and complete order fulfillment</p>
                
                <div class="permissions-list">
                    <h4>✅ Allowed Actions:</h4>
                    <ul>
                        <li class="allowed">View assigned delivery orders</li>
                        <li class="allowed">Scan QR codes for delivery verification</li>
                        <li class="allowed">Update order status (In Transit, Delivered)</li>
                        <li class="allowed">Access delivery dashboard with performance metrics</li>
                        <li class="allowed">View today's delivery schedule</li>
                        <li class="allowed">Update location and on-duty status</li>
                        <li class="allowed">Access customer contact information for deliveries</li>
                        <li class="allowed">View delivery history and earnings</li>
                    </ul>
                    
                    <h4>❌ Restrictions:</h4>
                    <ul>
                        <li class="restricted">Cannot create or modify orders</li>
                        <li class="restricted">Cannot access inventory management</li>
                        <li class="restricted">Cannot view financial data (costs, profits)</li>
                        <li class="restricted">Cannot assign orders to other agents</li>
                    </ul>
                </div>
                
                <h4>Interface Features:</h4>
                <p>Three-tab navigation: <strong>Dashboard</strong> (metrics & today's summary), <strong>Deliveries</strong> (active orders), <strong>Profile</strong> (agent settings)</p>
            </div>

            <div class="role-card supervisor">
                <h3>👨‍💼 Supervisor Role</h3>
                <p><strong>Primary Function:</strong> Oversee daily operations with limited administrative access</p>

                <div class="permissions-list">
                    <h4>✅ Allowed Actions:</h4>
                    <ul>
                        <li class="allowed">View today's sales and orders only</li>
                        <li class="allowed">See selling prices (no cost/profit data)</li>
                        <li class="allowed">Assign orders to available agents</li>
                        <li class="allowed">View today's delivery status</li>
                        <li class="allowed">Access available agents list</li>
                        <li class="allowed">Generate today's reports</li>
                        <li class="allowed">Monitor order fulfillment progress</li>
                        <li class="allowed">View customer information for today's orders</li>
                    </ul>

                    <h4>❌ Restrictions:</h4>
                    <ul>
                        <li class="restricted">Cannot view historical data beyond today</li>
                        <li class="restricted">Cannot see cost, profit, or margin data</li>
                        <li class="restricted">Cannot edit inventory or product information</li>
                        <li class="restricted">Cannot access user management features</li>
                        <li class="restricted">Cannot modify system settings</li>
                    </ul>
                </div>

                <h4>Interface Features:</h4>
                <p>Three-tab navigation: <strong>Dashboard</strong> (today's metrics), <strong>Deliveries</strong> (order assignment), <strong>Profile</strong> (supervisor settings)</p>
            </div>

            <div class="role-card admin">
                <h3>⚡ Admin Role</h3>
                <p><strong>Primary Function:</strong> Complete system administration and management</p>

                <div class="permissions-list">
                    <h4>✅ Allowed Actions:</h4>
                    <ul>
                        <li class="allowed">Full inventory management (create, update, delete products)</li>
                        <li class="allowed">Complete order management and processing</li>
                        <li class="allowed">User management (create, modify, deactivate users)</li>
                        <li class="allowed">Access all financial data including costs and profits</li>
                        <li class="allowed">View comprehensive analytics and reports</li>
                        <li class="allowed">Manage system settings and configurations</li>
                        <li class="allowed">Handle soft-deleted items (restore/permanent delete)</li>
                        <li class="allowed">Bulk operations on inventory and orders</li>
                        <li class="allowed">Access system health and performance metrics</li>
                        <li class="allowed">Manage payment gateway settings</li>
                    </ul>

                    <h4>❌ Restrictions:</h4>
                    <ul>
                        <li class="restricted">None - Full system access</li>
                    </ul>
                </div>

                <h4>Interface Features:</h4>
                <p>Three-tab navigation: <strong>Dashboard</strong> (system overview), <strong>Inventory</strong> (product management), <strong>Orders</strong> (order administration)</p>
            </div>
        </section>

        <div class="page-break"></div>

        <section id="core-features">
            <h2>🚀 4. Core Features</h2>

            <h3>📦 Inventory Management</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>Gas Cylinders</h4>
                    <p>Multiple sizes: 6KG, 13KG, 17KG, 20KG, 25KG with material options (Metal/Plastic) and real-time stock tracking.</p>
                </div>
                <div class="feature-card">
                    <h4>Spare Parts</h4>
                    <p>Categories include regulators, valves, stoves, and accessories with detailed specifications and availability status.</p>
                </div>
                <div class="feature-card">
                    <h4>Complete Packages</h4>
                    <p>Bundled offerings combining cylinders with accessories for customer convenience and better value.</p>
                </div>
            </div>

            <h3>🛒 Order Processing</h3>
            <div class="workflow-step">
                <strong>Order Creation:</strong> Customers select products, specify quantities, and provide delivery address.
            </div>
            <div class="workflow-step">
                <strong>Inventory Reservation:</strong> System automatically reserves requested items to prevent overselling.
            </div>
            <div class="workflow-step">
                <strong>Payment Processing:</strong> WaaFi preauthorization followed by immediate capture for confirmed orders.
            </div>
            <div class="workflow-step">
                <strong>Order Assignment:</strong> Admins or supervisors assign confirmed orders to available agents.
            </div>
            <div class="workflow-step">
                <strong>Delivery & Verification:</strong> Agents scan QR codes to verify and complete deliveries.
            </div>

            <h3>💳 Payment Integration</h3>
            <div class="feature-card">
                <h4>WaaFi Mobile Money</h4>
                <p>Secure payment processing with preauthorization and capture mechanism. Supports automatic failure handling and inventory release on payment failures.</p>
                <ul>
                    <li>Preauthorization for order security</li>
                    <li>Immediate capture for confirmed orders</li>
                    <li>Automatic rollback on payment failures</li>
                    <li>Detailed transaction logging and audit trails</li>
                </ul>
            </div>

            <h3>📱 Communication Features</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>SMS Notifications</h4>
                    <p>Hormuud SMS integration for order confirmations, delivery updates, and important alerts.</p>
                </div>
                <div class="feature-card">
                    <h4>Push Notifications</h4>
                    <p>Firebase Cloud Messaging for real-time app notifications and status updates.</p>
                </div>
            </div>
        </section>

        <div class="page-break"></div>

        <section id="workflows">
            <h2>🔄 5. Business Workflows</h2>

            <h3>Complete Order Lifecycle</h3>
            <div class="workflow-step">
                <strong>1. Order Creation (PENDING):</strong> Customer selects products and creates order. System validates inventory availability and creates payment record.
            </div>
            <div class="workflow-step">
                <strong>2. Payment Processing:</strong> WaaFi preauthorization initiated, then immediately captured. Order status changes to CONFIRMED on successful payment.
            </div>
            <div class="workflow-step">
                <strong>3. Order Assignment:</strong> Admin/Supervisor assigns confirmed order to available agent. Status remains CONFIRMED until agent starts delivery.
            </div>
            <div class="workflow-step">
                <strong>4. Delivery Dispatch (IN_TRANSIT):</strong> Agent accepts order and begins delivery. Customer receives notification with estimated delivery time.
            </div>
            <div class="workflow-step">
                <strong>5. Delivery Verification:</strong> Agent scans customer's QR code to verify delivery location and customer identity.
            </div>
            <div class="workflow-step">
                <strong>6. Order Completion (DELIVERED):</strong> Successful QR scan completes order. Inventory is marked as sold and agent receives completion confirmation.
            </div>

            <h3>Inventory Management Workflow</h3>
            <div class="workflow-step">
                <strong>Automatic Reservation:</strong> When order is created, requested quantities are automatically reserved to prevent overselling.
            </div>
            <div class="workflow-step">
                <strong>Payment Failure Handling:</strong> If payment fails, reserved inventory is automatically released back to available stock.
            </div>
            <div class="workflow-step">
                <strong>Delivery Completion:</strong> When order is delivered, reserved items are marked as sold and removed from available inventory.
            </div>
            <div class="workflow-step">
                <strong>Stock Monitoring:</strong> System continuously monitors stock levels and updates product status (Available, Low Stock, Out of Stock).
            </div>

            <h3>User Authentication & Authorization</h3>
            <div class="workflow-step">
                <strong>Registration:</strong> Users register with phone number and receive OTP for verification.
            </div>
            <div class="workflow-step">
                <strong>Login:</strong> JWT token-based authentication with role-based access control.
            </div>
            <div class="workflow-step">
                <strong>Authorization:</strong> Each API endpoint validates user role and permissions before allowing access.
            </div>
            <div class="workflow-step">
                <strong>Session Management:</strong> Secure token refresh and logout functionality with proper session cleanup.
            </div>
        </section>

        <div class="page-break"></div>

        <section id="interfaces">
            <h2>📱 6. User Interfaces</h2>

            <h3>Customer Interface</h3>
            <div class="feature-card">
                <h4>Home Tab - Product Catalog</h4>
                <ul>
                    <li><strong>Gas/Cylinder Tab:</strong> Browse available gas cylinders with sizes, prices, and stock status</li>
                    <li><strong>Full Package Tab:</strong> Complete bundles with cylinders and accessories</li>
                    <li><strong>Spare Parts Tab:</strong> Individual components and replacement parts</li>
                    <li><strong>Product Details:</strong> Full-screen product views with quantity selection and real-time price calculation</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4>Orders Tab - Order Management</h4>
                <ul>
                    <li>Order history with status tracking</li>
                    <li>Real-time order status updates</li>
                    <li>QR code generation for delivery verification</li>
                    <li>Order details with itemized breakdown</li>
                </ul>
            </div>

            <h3>Agent Interface</h3>
            <div class="feature-card">
                <h4>Dashboard Tab - Performance Overview</h4>
                <ul>
                    <li>Today's delivery summary and earnings</li>
                    <li>Performance metrics and ratings</li>
                    <li>Quick access to pending deliveries</li>
                    <li>On-duty status toggle</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4>Deliveries Tab - Active Orders</h4>
                <ul>
                    <li>Assigned delivery orders with customer details</li>
                    <li>QR code scanner for delivery verification</li>
                    <li>Order status update capabilities</li>
                    <li>Customer contact information</li>
                    <li>Floating action button for quick QR scanning</li>
                </ul>
            </div>

            <h3>Supervisor Interface</h3>
            <div class="feature-card">
                <h4>Dashboard Tab - Today's Operations</h4>
                <ul>
                    <li>Today's sales overview (selling prices only)</li>
                    <li>Order fulfillment progress</li>
                    <li>Agent availability status</li>
                    <li>Quick action buttons for common tasks</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4>Deliveries Tab - Order Assignment</h4>
                <ul>
                    <li>Confirmed orders awaiting assignment</li>
                    <li>Available agents list with status</li>
                    <li>Order assignment interface</li>
                    <li>Today's delivery tracking</li>
                </ul>
            </div>

            <h3>Admin Interface</h3>
            <div class="feature-card">
                <h4>Dashboard Tab - System Overview</h4>
                <ul>
                    <li>Comprehensive sales and revenue analytics</li>
                    <li>Syncfusion charts with interactive features</li>
                    <li>System health metrics</li>
                    <li>Agent performance overview</li>
                    <li>Financial overview with profit margins</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4>Inventory Tab - Product Management</h4>
                <ul>
                    <li>Complete inventory listing with search and filters</li>
                    <li>Create new products (cylinders, packages, spare parts)</li>
                    <li>Bulk operations and status updates</li>
                    <li>Soft-deleted items management</li>
                    <li>Stock adjustment and restocking capabilities</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4>Orders Tab - Order Administration</h4>
                <ul>
                    <li>All orders with comprehensive filtering</li>
                    <li>Order creation for customers</li>
                    <li>Order details and modification</li>
                    <li>Agent assignment interface</li>
                    <li>Payment status and transaction details</li>
                </ul>
            </div>
        </section>

        <div class="page-break"></div>

        <section id="admin-features">
            <h2>⚙️ 7. Administrative Features</h2>

            <h3>User Management</h3>
            <div class="feature-card">
                <h4>User Administration</h4>
                <ul>
                    <li>Create and manage user accounts for all roles</li>
                    <li>Role assignment and permission management</li>
                    <li>User activation/deactivation controls</li>
                    <li>Agent on-duty status management</li>
                    <li>User profile and contact information updates</li>
                </ul>
            </div>

            <h3>Inventory Administration</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>Product Management</h4>
                    <ul>
                        <li>Create new cylinders, packages, and spare parts</li>
                        <li>Update product information and pricing</li>
                        <li>Manage product images and descriptions</li>
                        <li>Set minimum stock levels and alerts</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>Stock Operations</h4>
                    <ul>
                        <li>Restock inventory with quantity adjustments</li>
                        <li>Bulk status updates for multiple products</li>
                        <li>Low stock monitoring and alerts</li>
                        <li>Inventory statistics and analytics</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>Soft Delete Management</h4>
                    <ul>
                        <li>View and manage soft-deleted items</li>
                        <li>Restore accidentally deleted products</li>
                        <li>Permanent deletion for cleanup</li>
                        <li>Audit trail for deletion activities</li>
                    </ul>
                </div>
            </div>

            <h3>Order Administration</h3>
            <div class="feature-card">
                <h4>Order Management</h4>
                <ul>
                    <li>View all orders with advanced filtering options</li>
                    <li>Create orders on behalf of customers</li>
                    <li>Modify order details and status</li>
                    <li>Assign orders to available agents</li>
                    <li>Handle order cancellations and refunds</li>
                    <li>Access complete order history and analytics</li>
                </ul>
            </div>

            <h3>Financial Management</h3>
            <div class="feature-card">
                <h4>Revenue & Profit Analysis</h4>
                <ul>
                    <li>Complete financial overview with profit margins</li>
                    <li>Revenue tracking by product category</li>
                    <li>Cost analysis and profitability reports</li>
                    <li>Payment gateway transaction monitoring</li>
                    <li>Financial performance metrics and trends</li>
                </ul>
            </div>

            <h3>System Monitoring</h3>
            <div class="feature-card">
                <h4>System Health & Performance</h4>
                <ul>
                    <li>User activity and engagement metrics</li>
                    <li>Order processing performance</li>
                    <li>Payment success rates and failure analysis</li>
                    <li>Agent performance and delivery metrics</li>
                    <li>System uptime and error monitoring</li>
                </ul>
            </div>
        </section>

        <div class="page-break"></div>

        <section id="technical-specs">
            <h2>🔧 8. Technical Specifications</h2>

            <h3>API Architecture</h3>
            <div class="feature-card">
                <h4>RESTful API Design</h4>
                <ul>
                    <li><strong>Base URL:</strong> /api/v1/</li>
                    <li><strong>Authentication:</strong> Bearer token (JWT)</li>
                    <li><strong>Response Format:</strong> JSON with consistent structure</li>
                    <li><strong>Error Handling:</strong> Standardized error responses with codes</li>
                    <li><strong>Rate Limiting:</strong> Implemented for security and performance</li>
                </ul>
            </div>

            <h3>Database Schema</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>Core Collections</h4>
                    <ul>
                        <li><strong>Users:</strong> Authentication, roles, and profile data</li>
                        <li><strong>Orders:</strong> Order lifecycle and status tracking</li>
                        <li><strong>Payments:</strong> Transaction records and gateway responses</li>
                        <li><strong>Cylinders:</strong> Gas cylinder inventory and specifications</li>
                        <li><strong>SpareParts:</strong> Spare parts catalog and stock</li>
                        <li><strong>Packages:</strong> Bundled product offerings</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>Data Relationships</h4>
                    <ul>
                        <li>Orders reference Users (customers and agents)</li>
                        <li>Payments linked to Orders for transaction tracking</li>
                        <li>Inventory items connected to Orders through order items</li>
                        <li>Users have role-based permissions and metadata</li>
                    </ul>
                </div>
            </div>

            <h3>Security Features</h3>
            <div class="feature-card">
                <h4>Security Implementation</h4>
                <ul>
                    <li><strong>Authentication:</strong> JWT tokens with expiration and refresh</li>
                    <li><strong>Authorization:</strong> Role-based access control (RBAC)</li>
                    <li><strong>Data Validation:</strong> Input sanitization and validation</li>
                    <li><strong>CORS:</strong> Cross-origin resource sharing configuration</li>
                    <li><strong>Helmet:</strong> Security headers and protection middleware</li>
                    <li><strong>Rate Limiting:</strong> API request throttling</li>
                    <li><strong>QR Security:</strong> Signed QR codes with expiration</li>
                </ul>
            </div>

            <h3>Performance Optimizations</h3>
            <div class="feature-card">
                <h4>System Performance</h4>
                <ul>
                    <li><strong>Database Indexing:</strong> Optimized queries with proper indexes</li>
                    <li><strong>Caching:</strong> Strategic caching for frequently accessed data</li>
                    <li><strong>Pagination:</strong> Efficient data loading with pagination</li>
                    <li><strong>Image Optimization:</strong> Compressed images with multiple formats</li>
                    <li><strong>Connection Pooling:</strong> Database connection optimization</li>
                    <li><strong>Async Processing:</strong> Non-blocking operations for better performance</li>
                </ul>
            </div>

            <h3>Integration Points</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>Payment Gateway</h4>
                    <ul>
                        <li>WaaFi API integration for mobile money</li>
                        <li>Preauthorization and capture workflow</li>
                        <li>Webhook handling for payment status updates</li>
                        <li>Transaction logging and audit trails</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>Communication Services</h4>
                    <ul>
                        <li>Hormuud SMS API for text notifications</li>
                        <li>Firebase Cloud Messaging for push notifications</li>
                        <li>Email service integration (future enhancement)</li>
                        <li>Notification delivery tracking and analytics</li>
                    </ul>
                </div>
            </div>
        </section>

        <div class="page-break"></div>

        <section id="conclusion">
            <h2>📋 9. System Summary</h2>

            <div class="feature-card">
                <h4>🎯 Business Value</h4>
                <p>The Gas Delivery System provides a complete digital transformation solution for gas cylinder delivery operations. It streamlines the entire process from customer ordering to delivery completion, while providing comprehensive management tools for different organizational roles.</p>
            </div>

            <div class="feature-card">
                <h4>🔑 Key Strengths</h4>
                <ul>
                    <li><strong>Role-Based Design:</strong> Tailored interfaces and permissions for each user type</li>
                    <li><strong>Automated Operations:</strong> Inventory management and order processing with minimal manual intervention</li>
                    <li><strong>Secure Transactions:</strong> Robust payment processing with failure handling</li>
                    <li><strong>Real-Time Tracking:</strong> Live order status and inventory updates</li>
                    <li><strong>Mobile-First:</strong> Optimized for mobile devices with responsive design</li>
                    <li><strong>Scalable Architecture:</strong> Built to handle growing business demands</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4>📊 System Metrics</h4>
                <ul>
                    <li><strong>User Roles:</strong> 4 distinct roles with specific permissions</li>
                    <li><strong>Product Categories:</strong> 3 main categories (Cylinders, Spare Parts, Packages)</li>
                    <li><strong>Order Statuses:</strong> 6 status levels tracking complete lifecycle</li>
                    <li><strong>API Endpoints:</strong> 50+ endpoints covering all functionality</li>
                    <li><strong>Payment Methods:</strong> WaaFi mobile money integration</li>
                    <li><strong>Verification:</strong> QR code-based delivery confirmation</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4>🚀 Future Enhancements</h4>
                <ul>
                    <li>Advanced analytics and reporting dashboards</li>
                    <li>Customer loyalty and rewards program</li>
                    <li>Route optimization for delivery agents</li>
                    <li>Multi-language support</li>
                    <li>Integration with additional payment gateways</li>
                    <li>Advanced inventory forecasting</li>
                </ul>
            </div>
        </section>

        <footer style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #666;">
            <p><strong>Gas Delivery System Documentation</strong></p>
            <p>Version 1.0 | Generated on <span id="current-date"></span></p>
            <p><em>Comprehensive Role-Based Gas Cylinder Delivery Platform</em></p>
        </footer>
    </div>

    <script>
        // Set current date
        document.getElementById('current-date').textContent = new Date().toLocaleDateString();

        // Smooth scrolling for table of contents links
        document.querySelectorAll('.toc a').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
    </script>
</body>
</html>
