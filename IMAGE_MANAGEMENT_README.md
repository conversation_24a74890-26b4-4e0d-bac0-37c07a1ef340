# 🖼️ Image Management System

A simple, high-performance image handling system for the Gas Delivery System project. Each model (Cylinder, SparePart, Package) stores its own image paths directly, with files organized in a structured local filesystem.

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    Image Management System                  │
├─────────────────────────────────────────────────────────────┤
│  Frontend (Flutter)                                        │
│  ├── ImageUploadWidget (Reusable component)                │
│  ├── ImageManagementPage (Admin interface)                 │
│  └── ImageUploadService (API integration)                  │
├─────────────────────────────────────────────────────────────┤
│  Backend (Node.js + Express)                               │
│  ├── Multer Middleware (File Upload)                       │
│  ├── ImageController (API endpoints)                       │
│  ├── ImageUploadService (Business logic)                   │
│  └── ImagePathUtils (Path management)                      │
├─────────────────────────────────────────────────────────────┤
│  Storage Layer                                             │
│  ├── Local File System (/uploads/images/)                  │
│  │   ├── /cylinders/YYYY/MM/                               │
│  │   ├── /spare-parts/YYYY/MM/                             │
│  │   ├── /packages/YYYY/MM/                                │
│  │   └── /profiles/YYYY/MM/                                │
│  └── MongoDB (Image Paths in Product Models)               │
└─────────────────────────────────────────────────────────────┘
```

## 📁 File Organization

```
backend/
├── uploads/
│   └── images/
│       ├── cylinders/
│       │   ├── 2024/
│       │   │   ├── 01/
│       │   │   │   ├── 1704067200000-abc123.png
│       │   │   │   └── 1704067300000-def456.jpg
│       │   │   └── 02/
│       │   └── 2025/
│       ├── spare-parts/
│       │   ├── 2024/
│       │   └── 2025/
│       ├── packages/
│       │   ├── 2024/
│       │   └── 2025/
│       └── profiles/
└── public/
    └── images/ (legacy static images)
```

## 🔧 Backend Implementation

### Models Updated
- **Cylinder Model**: Added `imagePath` field alongside legacy `imageUrl`
- **SparePart Model**: Added `imagePath` field alongside legacy `imageUrl`  
- **Package Model**: Added `imagePath` field alongside legacy `imageUrl`

### Key Components

#### 1. ImagePathUtils (`backend/src/models/image.model.ts`)
```typescript
// Generate organized file path
ImagePathUtils.generateImagePath(category, originalName)
// Returns: uploads/images/cylinders/2024/01/timestamp-random.jpg

// Get serving URL
ImagePathUtils.getImageUrl(imagePath)
// Returns: /api/v1/images/images/cylinders/2024/01/timestamp-random.jpg
```

#### 2. ImageUploadService (`backend/src/services/image-upload.service.ts`)
- Multer configuration for file uploads
- File validation (type, size, extension)
- Automatic directory creation
- File cleanup on errors

#### 3. ImageController (`backend/src/controllers/image.controller.ts`)
- Upload endpoint: `POST /api/v1/images/upload`
- Replace endpoint: `PUT /api/v1/images/:entityType/:entityId`
- Delete endpoint: `DELETE /api/v1/images/:entityType/:entityId`
- Info endpoint: `GET /api/v1/images/info/:entityType/:entityId`

### API Endpoints

#### Upload Image
```http
POST /api/v1/images/upload
Content-Type: multipart/form-data
Authorization: Bearer <token>

Form Data:
- image: <file>
- category: "cylinders" | "spare-parts" | "packages" | "profiles"
- entityType: "cylinder" | "sparepart" | "package" (optional)
- entityId: <string> (optional)
```

#### Replace Image
```http
PUT /api/v1/images/:entityType/:entityId
Content-Type: multipart/form-data
Authorization: Bearer <token>

Form Data:
- image: <file>
- category: "cylinders" | "spare-parts" | "packages" | "profiles"
```

#### Delete Image
```http
DELETE /api/v1/images/:entityType/:entityId
Authorization: Bearer <token>
```

## 📱 Frontend Implementation

### Components

#### 1. ImageUploadWidget (`frontend/lib/features/shared/presentation/widgets/image_upload_widget.dart`)
- Reusable image upload component
- Camera and gallery selection
- Image preview with loading states
- Progress indicators
- Error handling

#### 2. ImageManagementPage (`frontend/lib/features/admin/presentation/pages/image_management_page.dart`)
- Admin interface for managing product images
- Upload, replace, and delete functionality
- Image guidelines and validation feedback

#### 3. ImageUploadService (`frontend/lib/features/shared/data/services/image_upload_service.dart`)
- API integration for image operations
- Multipart form data handling
- Response models for type safety

### Usage Example
```dart
ImageUploadWidget(
  initialImageUrl: product.currentImageUrl,
  onImageSelected: (file) {
    // Handle selected image file
  },
  category: 'cylinders',
  width: 200.w,
  height: 200.h,
)
```

## 🔄 Backward Compatibility

The system maintains full backward compatibility:

1. **Legacy `imageUrl` field**: Still supported for existing static images
2. **New `imagePath` field**: For uploaded images
3. **Virtual `currentImageUrl`**: Automatically prioritizes uploaded images over legacy URLs
4. **ImageUrlGenerator**: Enhanced with hybrid support

### Priority System
1. **Uploaded images** (`imagePath`) - Highest priority
2. **Legacy image URLs** (`imageUrl`) - Fallback
3. **Generated URLs** (convention-based) - Default fallback

## 🚀 Getting Started

### 1. Install Dependencies
```bash
cd backend
npm install multer @types/multer
```

### 2. Create Upload Directories
```bash
mkdir -p uploads/images/cylinders uploads/images/spare-parts uploads/images/packages uploads/images/profiles
```

### 3. Test the System
```bash
cd backend/src
node test-image-upload.js
```

### 4. Frontend Integration
Add the image upload widget to your admin forms:
```dart
import '../../../shared/presentation/widgets/image_upload_widget.dart';
```

## 📊 File Specifications

- **Supported formats**: JPG, JPEG, PNG, WebP, GIF
- **Maximum file size**: 5MB
- **Recommended dimensions**: 1024x1024 pixels
- **Automatic optimization**: Images are compressed during upload
- **Naming convention**: `timestamp-randomstring.extension`

## 🔒 Security Features

- **Authentication required**: All upload endpoints require valid JWT token
- **Role-based access**: Only admins can upload/manage images
- **File validation**: MIME type and extension checking
- **Size limits**: Prevents large file uploads
- **Path sanitization**: Prevents directory traversal attacks

## 🛠️ Maintenance

### Cleanup Old Images
```javascript
// TODO: Implement cleanup service for orphaned images
// - Remove files not referenced in database
// - Archive old images based on retention policy
```

### Monitoring
- Monitor `uploads/images/` directory size
- Track upload success/failure rates
- Log file operations for audit trail

## 🔧 Configuration

### Environment Variables
```env
# Maximum file size (default: 5MB)
MAX_IMAGE_SIZE=5242880

# Upload directory (default: uploads/images)
UPLOAD_DIR=uploads/images

# Allowed file types
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/webp,image/gif
```

## 📈 Performance Considerations

- **Local storage**: Fast file serving without external dependencies
- **Organized structure**: Year/month folders prevent directory bloat
- **Efficient serving**: Express static middleware for optimal performance
- **Caching**: Frontend uses CachedNetworkImage for better UX
- **Lazy loading**: Images loaded on demand

## 🎯 Future Enhancements

- [ ] Image resizing and thumbnail generation
- [ ] CDN integration for better performance
- [ ] Bulk image upload functionality
- [ ] Image compression optimization
- [ ] Automated cleanup service
- [ ] Image metadata extraction
- [ ] Advanced image editing features
