{"info": {"name": "Gas System API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "User Management", "item": [{"name": "Register Customer", "request": {"method": "POST", "url": "http://localhost:3010/api/users/register", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+252615719999\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123\",\n  \"address\": {\n    \"tag\": \"home\",\n    \"coordinates\": [45.3182, 2.0469],\n    \"details\": \"Near Bakara Market, Mogadishu\",\n    \"contactPhone\": \"+252615719999\"\n  }\n}"}}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "url": "http://localhost:3010/api/users/login", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+252615719999\",\n  \"password\": \"Password123\"\n}"}}}, {"name": "Register Admin", "request": {"method": "POST", "url": "http://localhost:3010/api/users/admin", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+252615719998\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"AdminPass123\",\n  \"role\": \"admin\"\n}"}}}, {"name": "Register Agent", "request": {"method": "POST", "url": "http://localhost:3010/api/users/admin", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+252615719997\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"AgentPass123\",\n  \"role\": \"agent\",\n  \"agentMetadata\": {\n    \"vehicle\": {\n      \"type\": \"motorcycle\",\n      \"number\": \"MOG-1234\"\n    }\n  }\n}"}}}, {"name": "Get Current User", "request": {"method": "GET", "url": "http://localhost:3010/api/users/me", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}]}}, {"name": "Get User by ID", "request": {"method": "GET", "url": "http://localhost:3010/api/users/{{user_id}}", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}]}}, {"name": "Get All Users", "request": {"method": "GET", "url": {"raw": "http://localhost:3010/api/users?role=customer&isActive=true&page=1&limit=10&sortBy=createdAt&sortOrder=desc", "host": ["localhost"], "port": "3010", "path": ["api", "users"], "query": [{"key": "role", "value": "customer"}, {"key": "isActive", "value": "true"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "sortBy", "value": "createdAt"}, {"key": "sortOrder", "value": "desc"}]}, "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}]}}, {"name": "Update User", "request": {"method": "PUT", "url": "http://localhost:3010/api/users/{{user_id}}", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"address\": {\n    \"tag\": \"work\",\n    \"coordinates\": [45.3182, 2.0469],\n    \"details\": \"Office building, Mogadishu\",\n    \"contactPhone\": \"+252615719999\"\n  }\n}"}}}, {"name": "Delete User", "request": {"method": "DELETE", "url": "http://localhost:3010/api/users/{{user_id}}", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}]}}, {"name": "Subscribe to Notifications", "request": {"method": "POST", "url": "http://localhost:3010/api/users/notifications/subscribe", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"{{user_id}}\",\n  \"topic\": \"order_updates\",\n  \"deviceToken\": \"fcm-device-token-example\"\n}"}}}]}, {"name": "Inventory Management", "item": [{"name": "Create Inventory Item", "request": {"method": "POST", "url": "http://localhost:3010/api/inventory", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"cylinderType\": \"small\",\n  \"cylinderMaterial\": \"steel\",\n  \"currentStock\": 50,\n  \"lowStockThreshold\": 10,\n  \"warehouse\": \"ceelasha_warehouse\",\n  \"lastRestocked\": \"2023-06-14T10:00:00.000Z\"\n}"}}}, {"name": "Get All Inventory", "request": {"method": "GET", "url": "http://localhost:3010/api/inventory", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}]}}]}, {"name": "Order Management", "item": [{"name": "Create Order", "request": {"method": "POST", "url": "http://localhost:3010/api/orders", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"items\": [\n    {\n      \"inventoryId\": \"{{inventory_id}}\",\n      \"quantity\": 2\n    }\n  ],\n  \"deliveryAddress\": {\n    \"coordinates\": [45.3182, 2.0469],\n    \"details\": \"Near Bakara Market, Mogadishu\"\n  },\n  \"paymentMethod\": \"cash\"\n}"}}}, {"name": "Get Order by ID", "request": {"method": "GET", "url": "http://localhost:3010/api/orders/{{order_id}}", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}]}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Store token from login response", "if (pm.response.code === 200 && pm.request.url.toString().includes('/login')) {", "    const jsonData = pm.response.json();", "    if (jsonData.data && jsonData.data.token) {", "        pm.environment.set('token', jsonData.data.token);", "        ", "        // Extract user ID from token", "        const tokenParts = jsonData.data.token.split('.');", "        if (tokenParts.length > 1) {", "            const payload = JSON.parse(atob(tokenParts[1]));", "            pm.environment.set('user_id', payload.userId);", "        }", "    }", "}", "", "// Store inventory ID from inventory creation response", "if (pm.response.code === 201 && pm.request.url.toString().includes('/inventory')) {", "    const jsonData = pm.response.json();", "    if (jsonData.data && jsonData.data._id) {", "        pm.environment.set('inventory_id', jsonData.data._id);", "    }", "}", "", "// Store order ID from order creation response", "if (pm.response.code === 201 && pm.request.url.toString().includes('/orders')) {", "    const jsonData = pm.response.json();", "    if (jsonData.data && jsonData.data._id) {", "        pm.environment.set('order_id', jsonData.data._id);", "    }", "}"]}}], "variable": [{"key": "token", "value": ""}, {"key": "admin_token", "value": ""}, {"key": "user_id", "value": ""}, {"key": "inventory_id", "value": ""}, {"key": "order_id", "value": ""}]}