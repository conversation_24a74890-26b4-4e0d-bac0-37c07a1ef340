# 📧 Email Service Testing Guide

## 🎯 Overview

This guide provides comprehensive instructions for testing the Ciribey Gas Delivery email service functionality. The email service supports HTML templates, bulk sending, multilingual content (Somali/English), and various notification types.

## 🚀 Quick Start

### Method 1: Using npm scripts (Recommended)

```bash
# Simple email test (sends one test email)
npm run test:email

# Comprehensive email test (tests all features)
npm run test:email:full

# Test image cleanup functionality
npm run test:image-cleanup
```

### Method 2: Direct execution

```bash
# Simple test
npx ts-node src/test/email_test_simple.ts

# Full test suite
npx ts-node src/test/email_service_test.ts
```

### Method 3: Using Node.js directly

```bash
# Compile and run
npm run build
node dist/test/email_test_simple.js
```

## 📧 Test Email Address

All tests will send emails to: **<EMAIL>**

Make sure to check this inbox after running the tests to verify email delivery.

## 🧪 Test Scenarios

### 1. Simple Email Test (`email_test_simple.ts`)

**What it tests:**
- Basic email sending functionality
- HTML template rendering
- Professional email formatting
- Somali/English bilingual content

**Expected outcome:**
- One beautifully formatted test email
- Success confirmation in console
- Message ID returned

### 2. Comprehensive Email Test (`email_service_test.ts`)

**What it tests:**
- Email service health check
- Simple email sending
- OTP verification emails
- Low stock alert emails
- Order confirmation emails
- Bulk email sending
- Error handling with invalid emails
- Email service metrics
- Message template generation

**Expected outcome:**
- Multiple test emails <NAME_EMAIL>
- Detailed console output with test results
- Performance metrics displayed

## 📊 Email Templates Tested

### 🔐 OTP Verification Email
- **Subject:** "🔐 Ciribey Gas Delivery - Koodka Xaqiijinta"
- **Content:** Somali OTP verification with security warnings
- **Features:** Large OTP display, expiration timer, security notice

### 📦 Order Confirmation Email
- **Subject:** "✅ Ciribey Gas Delivery - Dalabka la Xaqiijiyay"
- **Content:** Order details in Somali with delivery information
- **Features:** Order tracking, delivery timeline, contact info

### 📊 Low Stock Alert Email
- **Subject:** "⚠️ Ciribey Gas Delivery - Digniin Alaab Yar"
- **Content:** Professional admin notification about low inventory
- **Features:** Item details, stock levels, action items

### 🎉 Welcome Email
- **Subject:** "👋 Ciribey Gas Delivery - Soo dhawayn"
- **Content:** User onboarding with role-specific information
- **Features:** Personalized content, app download links

## 🔧 Configuration Requirements

### Environment Variables

Ensure these are set in your `.env` file:

```env
# Email Service Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM_NAME=Ciribey Gas Delivery
SMTP_FROM_EMAIL=<EMAIL>

# Test Configuration
TEST_EMAIL=<EMAIL>
```

### Gmail App Password Setup

If using Gmail SMTP:

1. Enable 2-Factor Authentication on your Google account
2. Generate an App Password for the application
3. Use the App Password (not your regular password) in `SMTP_PASS`

## 📈 Expected Test Results

### ✅ Success Indicators

```
📧 Simple Email Test Starting...
📤 Sending test email to: <EMAIL>
✅ Email sent successfully!
📧 Message ID: <unique-message-id>
📬 Check your inbox at: <EMAIL>
```

### ❌ Common Issues

**SMTP Authentication Failed:**
```
❌ Email failed to send
🚨 Error: Invalid login: 535-5.7.8 Username and Password not accepted
```
**Solution:** Check SMTP credentials and use App Password for Gmail

**Network Connection Issues:**
```
💥 Test failed with error: connect ECONNREFUSED
```
**Solution:** Check internet connection and SMTP server settings

**Invalid Email Address:**
```
✅ Error handling works correctly: Invalid email address format
```
**Expected behavior:** This shows error handling is working correctly

## 🎨 Email Design Features

### Professional Styling
- **Gradient Headers:** Modern blue-purple gradients
- **Responsive Design:** Works on desktop and mobile
- **Brand Colors:** Consistent with Ciribey Gas Delivery branding
- **Typography:** Clean, readable fonts with proper hierarchy

### Multilingual Support
- **Primary Language:** Somali (so)
- **Secondary Language:** English (en)
- **Professional Tone:** Business-appropriate messaging
- **Cultural Sensitivity:** Appropriate greetings and closings

### Content Structure
- **Header Section:** Branding and title
- **Main Content:** Key information with clear formatting
- **Action Items:** Clear next steps or instructions
- **Footer:** Contact information and branding

## 🔍 Troubleshooting

### Test Not Running

1. **Check Node.js installation:**
   ```bash
   node --version
   npm --version
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Check TypeScript compilation:**
   ```bash
   npm run build
   ```

### Emails Not Received

1. **Check spam folder** in <EMAIL>
2. **Verify SMTP configuration** in environment variables
3. **Check email service logs** for error messages
4. **Test with different email provider** if Gmail blocks emails

### Performance Issues

1. **Monitor email service metrics:**
   ```typescript
   const metrics = emailService.getMetrics();
   console.log('Success rate:', metrics.successRate);
   ```

2. **Adjust bulk email settings:**
   ```typescript
   // Reduce batch size for slower connections
   batchSize: 5,
   delayBetweenBatches: 2000
   ```

## 📞 Support

If you encounter issues with email testing:

1. **Check the console output** for detailed error messages
2. **Verify environment configuration** matches requirements
3. **Test with a simple email client** to isolate SMTP issues
4. **Review email service logs** for additional debugging information

## 🎉 Success Confirmation

After running the tests successfully, you should see:

1. **Console confirmation** with success messages and message IDs
2. **Test emails in inbox** at <EMAIL>
3. **Performance metrics** showing email service health
4. **Error handling demonstrations** for invalid scenarios

The email service is ready for production use when all tests pass! 🚀

---

**Note:** Always test email functionality in a development environment before deploying to production. The test emails are designed to be safe and informative for testing purposes.
