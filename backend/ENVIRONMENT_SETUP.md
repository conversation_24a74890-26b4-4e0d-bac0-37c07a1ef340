# Environment Setup Guide

This guide explains how to properly run the Gas System Project backend in different environments.

## 🔧 Environment Configuration

The application uses different environment files based on the `NODE_ENV` variable:

- **Development**: `.env.development` (default)
- **Production**: `.env.production`

## 📜 Available Scripts

### Development Scripts
```bash
# Run in development mode with hot reload (nodemon)
npm run dev

# Run built version in development mode
npm run start:dev
```

### Production Scripts
```bash
# Run in production mode (recommended for deployment)
npm start
# or
npm run start:prod

# Build for production
npm run build:prod
```

### Build Scripts
```bash
# Build the application
npm run build

# Build for production with NODE_ENV=production
npm run build:prod
```

## 🌐 Environment Differences

### Development Mode
- **Environment File**: `.env.development`
- **CORS**: Permissive (allows local network IPs)
- **Allowed Origins**: 21 origins including local development URLs
- **Logging**: More verbose
- **Features**: Hot reload with nodemon

### Production Mode
- **Environment File**: `.env.production`
- **CORS**: Strict origin validation
- **Allowed Origins**: 15 origins (production URLs only)
- **Logging**: Production-level logging
- **Features**: Optimized for performance

## 🔍 Environment Detection

The application automatically detects the environment and loads the appropriate configuration:

```
🔧 Loading environment: development
📄 Environment file: .env.development
🌐 CORS Configuration:
   Environment: development
   Allowed Origins: 21 origins
   🔓 Development mode: Local network origins allowed
```

## 📋 Environment Files

### `.env.development`
```env
NODE_ENV=development
PORT=3010
SERVER_URL="http://*************:3010"
FRONTEND_URL="http://*************:3000"
CORS_ORIGINS="http://localhost:3000,http://127.0.0.1:3000,..."
```

### `.env.production`
```env
NODE_ENV=production
PORT=3010
SERVER_URL="https://your-production-domain.com"
FRONTEND_URL="https://gas-booking-system-production.up.railway.app"
CORS_ORIGINS="https://gas-booking-system-production.up.railway.app,..."
```

## 🚀 Deployment

For production deployment:

1. **Build the application**:
   ```bash
   npm run build:prod
   ```

2. **Start in production mode**:
   ```bash
   npm start
   ```

3. **Verify environment**:
   Check the console output to ensure it shows:
   ```
   🔧 Loading environment: production
   📄 Environment file: .env.production
   🔒 Production mode: Strict origin validation
   ```

## 🔧 Troubleshooting

### Issue: Both dev and prod use development environment
**Solution**: Ensure `NODE_ENV` is properly set in package.json scripts:
```json
{
  "scripts": {
    "dev": "NODE_ENV=development nodemon",
    "start": "NODE_ENV=production node dist/server.js"
  }
}
```

### Issue: CORS errors in production
**Solution**: Check that your frontend URL is included in `.env.production` CORS_ORIGINS.

### Issue: Environment variables not loading
**Solution**: Ensure the correct `.env` file exists and contains all required variables.
