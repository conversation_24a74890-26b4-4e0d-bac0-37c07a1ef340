const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3010/api/v1';

// Test credentials (using admin credentials)
const TEST_ADMIN = {
  phone: '+252619597745',
  password: '123123',
};

let authToken = '';

async function login() {
  try {
    // Step 1: Send login request (phone-based)
    const loginResponse = await axios.post(`${BASE_URL}/users/login`, { phone: TEST_ADMIN.phone });
    console.log('📱 Login request sent, checking for OTP verification...');

    // Step 2: Verify OTP (using dummy OTP since DISABLE_OTP_VERIFICATION=true)
    const verifyResponse = await axios.post(`${BASE_URL}/users/verify-otp`, {
      phone: TEST_ADMIN.phone,
      otp: '000000',
    });

    console.log('🔍 Verify response:', JSON.stringify(verifyResponse.data, null, 2));
    authToken = verifyResponse.data.data.token;
    console.log('✅ Login successful');
    return true;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data?.message || error.message);
    if (error.response?.data) {
      console.error('🔍 Error response:', JSON.stringify(error.response.data, null, 2));
    }
    return false;
  }
}

async function testJsonRequest() {
  console.log('\n🧪 Testing JSON request (backward compatibility)...');

  try {
    const cylinderData = {
      type: '17KG',
      material: 'METAL',
      price: 50.0,
      cost: 30.0,
      description: 'Test cylinder via JSON',
      quantity: 10,
      minimumStockLevel: 5,
      status: 'ACTIVE',
    };

    const response = await axios.post(`${BASE_URL}/cylinders`, cylinderData, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        'Content-Type': 'application/json',
      },
    });

    console.log('✅ JSON request successful');
    console.log('📄 Response:', response.data);
    return response.data.data;
  } catch (error) {
    console.error('❌ JSON request failed:', error.response?.data?.message || error.message);
    return null;
  }
}

async function testMultipartRequest() {
  console.log('\n🧪 Testing Multipart form data request...');

  try {
    const form = new FormData();

    // Add form fields
    form.append('type', '13KG');
    form.append('material', 'METAL');
    form.append('price', '75.00');
    form.append('cost', '45.00');
    form.append('description', 'Test cylinder via multipart');
    form.append('quantity', '15');
    form.append('minimumStockLevel', '8');
    form.append('status', 'ACTIVE');

    // Create a simple test image file (1x1 pixel PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d, 0x49, 0x48, 0x44,
      0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x08, 0x02, 0x00, 0x00, 0x00, 0x90,
      0x77, 0x53, 0xde, 0x00, 0x00, 0x00, 0x0c, 0x49, 0x44, 0x41, 0x54, 0x08, 0x99, 0x01, 0x01,
      0x00, 0x00, 0x00, 0xff, 0xff, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0xe2, 0x21, 0xbc, 0x33,
      0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82,
    ]);

    form.append('image', testImageBuffer, {
      filename: 'test-cylinder.png',
      contentType: 'image/png',
    });

    const response = await axios.post(`${BASE_URL}/cylinders`, form, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        ...form.getHeaders(),
      },
    });

    console.log('✅ Multipart request successful');
    console.log('📄 Response:', response.data);
    return response.data.data;
  } catch (error) {
    console.error('❌ Multipart request failed:', error.response?.data?.message || error.message);
    if (error.response?.data) {
      console.error('🔍 Error response:', JSON.stringify(error.response.data, null, 2));
    }
    return null;
  }
}

async function testSparePartMultipart() {
  console.log('\n🧪 Testing Spare Part multipart request...');

  try {
    const form = new FormData();

    // Add form fields
    form.append('description', 'Test spare part via multipart');
    form.append('price', '25.00');
    form.append('cost', '15.00');
    form.append('category', 'REGULATOR_HIGH_PRESSURE');
    form.append('compatibleCylinderTypes', '6KG,13KG');
    form.append('barcode', 'TEST123456');
    form.append('minimumStockLevel', '10');
    form.append('initialStock', '50');

    // Create a simple test image file
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d, 0x49, 0x48, 0x44,
      0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x08, 0x02, 0x00, 0x00, 0x00, 0x90,
      0x77, 0x53, 0xde, 0x00, 0x00, 0x00, 0x0c, 0x49, 0x44, 0x41, 0x54, 0x08, 0x99, 0x01, 0x01,
      0x00, 0x00, 0x00, 0xff, 0xff, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0xe2, 0x21, 0xbc, 0x33,
      0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82,
    ]);

    form.append('image', testImageBuffer, {
      filename: 'test-spare-part.png',
      contentType: 'image/png',
    });

    const response = await axios.post(`${BASE_URL}/spare-parts`, form, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        ...form.getHeaders(),
      },
    });

    console.log('✅ Spare Part multipart request successful');
    console.log('📄 Response:', response.data);
    return response.data.data;
  } catch (error) {
    console.error(
      '❌ Spare Part multipart request failed:',
      error.response?.data?.message || error.message
    );
    if (error.response?.data) {
      console.error('🔍 Error response:', JSON.stringify(error.response.data, null, 2));
    }
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting Backend Integration Tests...\n');

  // Step 1: Login
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('❌ Cannot proceed without authentication');
    return;
  }

  // Step 2: Test JSON request (backward compatibility)
  const jsonResult = await testJsonRequest();

  // Step 3: Test multipart request (new functionality)
  const multipartResult = await testMultipartRequest();

  // Step 4: Test spare part multipart request
  const sparePartResult = await testSparePartMultipart();

  // Summary
  console.log('\n📊 Test Summary:');
  console.log(`JSON Request: ${jsonResult ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Multipart Request: ${multipartResult ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Spare Part Multipart: ${sparePartResult ? '✅ PASSED' : '❌ FAILED'}`);

  if (jsonResult && multipartResult && sparePartResult) {
    console.log(
      '\n🎉 All tests passed! Backend is handling both JSON and multipart requests correctly.'
    );
  } else {
    console.log('\n⚠️  Some tests failed. Please check the error messages above.');
  }
}

// Run the tests
runTests().catch(console.error);
