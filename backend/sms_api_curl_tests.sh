#!/bin/bash

# Hormuud SMS API Curl Test Suite
# Run this script to test the SMS API directly with curl commands

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# API Configuration
API_URL="https://smsapi.hormuud.com/api/SendSMS"
TOKEN="Y100Z9tep744u7numGF7CgYA4l7YRqH4pc8K6AqZ6YZIWVTY6JdNt0kUQWCty2cJcL8QSkVL1GZJzhKtY50-DXa1LzesoVVOSTmaMUGO7OFhlN-9ueGIWQQdRPbIUBKtAmq4ctudbFFq2lHBQo7GfBV8iDNeUIGI-AkQgEUkKlO7AotE3JSiEoPzmmBpDz1nknxEz5WZSI9vQUjEYu_909slFGRJ68DVDVDr3DZeute7r84VfCuQXaRx0iR-vKGG-vHF0y2blrpCREC18ciGcRCzy5kPsO5Fik2rY72bR5Og9JYp_NjfLujiR3i03JjJgkIPRkmSdO5vbdmUFxbi5YV7Bw_EDS8w8dlp2SOiaLYCbei28avsnLO_ot_WJnmxFvyhzkXZ_7zdLPytGmhsP5UYY24Ng6GUqSrQpVjcm5CatcCvN0EnGvlW1ZPsW7sIRbQxHVIeUpYzwQT-NNVQTyBb_7kNPvXQqLVm6VjyerwynGJLNosp409zBWfRkuEamJSTiLWAL8BBVnByFSXnKA"
PHONE="613656021"
SENDER_ID="HODAN HOSPITAL"

# Results file
RESULTS_FILE="sms_api_test_results_$(date +%Y%m%d_%H%M%S).txt"

# Function to log test results
log_test() {
    local test_name="$1"
    local message="$2"
    echo -e "${BLUE}=== $test_name ===${NC}"
    echo "Test: $test_name" >> "$RESULTS_FILE"
    echo "Message: $message" >> "$RESULTS_FILE"
    echo "Message Length: ${#message} characters" >> "$RESULTS_FILE"
    echo "Timestamp: $(date)" >> "$RESULTS_FILE"
    echo "---" >> "$RESULTS_FILE"
}

# Function to run curl test
run_test() {
    local test_name="$1"
    local json_data="$2"
    local message="$3"
    
    log_test "$test_name" "$message"
    
    echo -e "${YELLOW}Running: $test_name${NC}"
    echo "Message: $message"
    echo "Length: ${#message} characters"
    echo ""
    
    curl -X POST "$API_URL" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -w "\n\nResponse Time: %{time_total}s\nHTTP Status: %{http_code}\nContent Length: %{size_download} bytes\n" \
        -d "$json_data" \
        2>&1 | tee -a "$RESULTS_FILE"
    
    echo -e "\n${GREEN}Test completed${NC}\n"
    echo "========================================" >> "$RESULTS_FILE"
    echo ""
    
    # Wait between tests to respect rate limiting
    sleep 2
}

echo -e "${BLUE}🚀 Starting Hormuud SMS API Curl Test Suite${NC}"
echo "Results will be saved to: $RESULTS_FILE"
echo "Test phone number: $PHONE"
echo "Sender ID: $SENDER_ID"
echo ""

# Test 1: Basic functionality
echo -e "${GREEN}📱 BASIC FUNCTIONALITY TESTS${NC}"

run_test "Basic Short Message" '{
    "refid": "test-basic-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "Basic test message",
    "senderid": "'$SENDER_ID'"
}' "Basic test message"

run_test "Minimal Required Fields" '{
    "mobile": "'$PHONE'",
    "message": "Minimal test",
    "senderid": "'$SENDER_ID'"
}' "Minimal test"

# Test 2: Message length limits
echo -e "${GREEN}📏 MESSAGE LENGTH TESTS${NC}"

run_test "50 Characters" '{
    "refid": "test-50char-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "This is a 50 character test message for length.",
    "senderid": "'$SENDER_ID'"
}' "This is a 50 character test message for length."

run_test "100 Characters" '{
    "refid": "test-100char-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "This is a 100 character test message to check length limits and see how the API responds to it.",
    "senderid": "'$SENDER_ID'"
}' "This is a 100 character test message to check length limits and see how the API responds to it."

run_test "160 Characters (Standard SMS)" '{
    "refid": "test-160char-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "This is a 160 character test message to check the standard SMS length limit and see how the Hormuud API responds to messages of this exact length.",
    "senderid": "'$SENDER_ID'"
}' "This is a 160 character test message to check the standard SMS length limit and see how the Hormuud API responds to messages of this exact length."

run_test "320 Characters (2 SMS)" '{
    "refid": "test-320char-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "This is a 320 character test message to check how the API handles longer messages that would typically be split into multiple SMS parts. This message should be long enough to test the multipart SMS functionality and see if the API properly handles concatenated messages or if it has specific limitations.",
    "senderid": "'$SENDER_ID'"
}' "This is a 320 character test message to check how the API handles longer messages that would typically be split into multiple SMS parts. This message should be long enough to test the multipart SMS functionality and see if the API properly handles concatenated messages or if it has specific limitations."

run_test "459 Characters (Maximum)" '{
    "refid": "test-459char-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "This is a 459 character test message to check the maximum length that our system allows for non-OTP messages. This message is designed to test the upper limit of what the Hormuud SMS API can handle and whether it properly processes very long messages or if there are any restrictions or limitations that we need to be aware of when sending longer content to customers through their SMS gateway service.",
    "senderid": "'$SENDER_ID'"
}' "This is a 459 character test message to check the maximum length that our system allows for non-OTP messages. This message is designed to test the upper limit of what the Hormuud SMS API can handle and whether it properly processes very long messages or if there are any restrictions or limitations that we need to be aware of when sending longer content to customers through their SMS gateway service."

run_test "500+ Characters (Over Limit)" '{
    "refid": "test-500char-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "This is a 500+ character test message that exceeds our normal limits to see how the Hormuud SMS API responds to very long messages. This test is designed to understand if the API has its own length restrictions and how it handles messages that are longer than typical SMS limits. We want to see if it truncates, rejects, or processes the entire message and what kind of response we get back from the API when we send content that is significantly longer than standard SMS message lengths.",
    "senderid": "'$SENDER_ID'"
}' "This is a 500+ character test message that exceeds our normal limits to see how the Hormuud SMS API responds to very long messages. This test is designed to understand if the API has its own length restrictions and how it handles messages that are longer than typical SMS limits. We want to see if it truncates, rejects, or processes the entire message and what kind of response we get back from the API when we send content that is significantly longer than standard SMS message lengths."

# Test 3: Character encoding
echo -e "${GREEN}🔤 CHARACTER ENCODING TESTS${NC}"

run_test "ASCII Only" '{
    "refid": "test-ascii-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "ASCII only test message 123 ABC",
    "senderid": "'$SENDER_ID'"
}' "ASCII only test message 123 ABC"

run_test "Special Characters" '{
    "refid": "test-special-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "Test with numbers 123456 and symbols !@#$%^&*()_+-=[]{}|;:,.<>?",
    "senderid": "'$SENDER_ID'"
}' "Test with numbers 123456 and symbols !@#$%^&*()_+-=[]{}|;:,.<>?"

run_test "Unicode/Emojis (Problematic)" '{
    "refid": "test-unicode-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "Unicode test: 🔥 📱 ✅ ❌ 📊 🚀",
    "senderid": "'$SENDER_ID'"
}' "Unicode test: 🔥 📱 ✅ ❌ 📊 🚀"

run_test "Smart Quotes and Dashes" '{
    "refid": "test-quotes-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "Smart quotes \"test\" and dashes – — test",
    "senderid": "'$SENDER_ID'"
}' "Smart quotes \"test\" and dashes – — test"

# Test 4: Error handling
echo -e "${GREEN}❌ ERROR HANDLING TESTS${NC}"

run_test "Invalid Phone Number" '{
    "refid": "test-invalid-phone-'$(date +%s)'",
    "mobile": "123456",
    "message": "Test with invalid phone number",
    "senderid": "'$SENDER_ID'"
}' "Test with invalid phone number"

run_test "Missing Mobile Field" '{
    "refid": "test-missing-mobile-'$(date +%s)'",
    "message": "Test with missing mobile field",
    "senderid": "'$SENDER_ID'"
}' "Test with missing mobile field"

run_test "Empty Message" '{
    "refid": "test-empty-message-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "",
    "senderid": "'$SENDER_ID'"
}' ""

echo -e "${BLUE}🎯 All tests completed!${NC}"
echo "Results saved to: $RESULTS_FILE"
echo ""
echo -e "${YELLOW}📊 SUMMARY:${NC}"
echo "- Check the results file for detailed responses"
echo "- Look for HTTP status codes (200 = success, 4xx/5xx = error)"
echo "- Note response times and any error messages"
echo "- Pay attention to Unicode/emoji test results"
echo ""
echo -e "${GREEN}Next steps:${NC}"
echo "1. Review the results file: cat $RESULTS_FILE"
echo "2. Analyze which message types work/fail"
echo "3. Update your SMS service based on findings"
