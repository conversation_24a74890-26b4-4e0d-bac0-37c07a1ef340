// {
//   "compilerOptions": {
//     "module": "NodeNext",
//     "moduleResolution": "NodeNext",
//     "baseUrl": "src",
//     "outDir": "dist",
//     "sourceMap": true,
//     "noImplicitAny": true,
//     "paths": {
//       "@errors/*": ["errors/*"],
//       "@controllers/*": ["controller/*"],
//       "@models/*": ["models/*"],
//       "@routes/*": ["routes/*"],
//       "@utils/*": ["utils/*"],
//       "@types/*": ["types/*"],
//       "@config/*": ["config/*"],
//       "@middleware/*": ["middleware/*"],
//       "@services/*": ["services/*"]
//     }
//   },
//   "include": ["src/**/*"]
// }

{
  "compilerOptions": {
    "module": "NodeNext",
    "moduleResolution": "NodeNext",
    "baseUrl": "src",
    "outDir": "dist",
    "sourceMap": true,
    "noImplicitAny": true,
    "paths": {
      "@errors/*": ["errors/*"],
      "@controllers/*": ["controller/*"],
      "@models/*": ["models/*"],
      "@routes/*": ["routes/*"],
      "@utils/*": ["utils/*"],
      "@types/*": ["types/*"],
      "@config/*": ["config/*"],
      "@middleware/*": ["middleware/*"],
      "@services/*": ["services/*"]
    }
  },
  "include": ["src/**/*"]
}
