// import { TokenPayload } from '../types/interfaces';

// declare global {
//   namespace Express {
//     interface Request {
//       user?: {
//         userId: TokenPayload['userId'];
//         role: TokenPayload['role'];
//         [key: string]: any;
//       };
//     }
//   }
// }

import { TokenPayload } from './interfaces';

declare global {
  namespace Express {
    interface Request {
      user?: TokenPayload;
    }
  }
}
