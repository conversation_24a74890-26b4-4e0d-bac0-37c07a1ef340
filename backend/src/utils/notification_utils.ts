import * as admin from 'firebase-admin';
import { cert } from 'firebase-admin/app';
import { config } from '../config/env_config';

// Initialize Firebase Admin if not already done
if (!admin.apps.length) {
  admin.initializeApp({
    credential: cert({
      projectId: config.firebase.projectId,
      privateKey: config.firebase.privateKey,
      clientEmail: config.firebase.clientEmail,
    }),
  });
}

// Enum for notification topics
export enum NotificationTopic {
  AGENTS_NEW_ORDER = 'agents_new_order', // Notify all agents of a new unassigned order
  AGENTS_SCHEDULE_UPDATE = 'agents_schedule_update', // Schedule or shift change notices
  CUSTOMERS_PROMOTIONS = 'customers_promotions', // Discounts, offers, loyalty rewards
  CUSTOMERS_SERVICE_ALERTS = 'customers_service_alerts', // Delays, outages, or service issues
  SYSTEM_MAINTENANCE = 'system_maintenance', // Planned downtime for all users
  INVENTORY_ALERTS = 'inventory_alerts', // Low stock, restock info to admins or warehouse managers
  EMERGENCY_BROADCAST = 'emergency_broadcast', // Gas safety alerts, emergencies, etc.
  TEST_NOTIFICATION = 'test_notification', // Test notifications
  
}

interface NotificationPayload {
  title: string;
  body: string;
  data?: { [key: string]: string }; // Additional payload data
  imageUrl?: string;
  // Optional platform-specific overrides
  android?: admin.messaging.AndroidConfig;
  apns?: admin.messaging.ApnsConfig;
  webpush?: admin.messaging.WebpushConfig;
}

export interface NotificationResult {
  success: boolean;
  message?: string;
  count?: number;
  notificationId?: string;
  details?: any;
}

export async function sendPushNotification(
  deviceToken: string | string[],
  payload: NotificationPayload
): Promise<admin.messaging.MessagingPayload | admin.messaging.BatchResponse | string> {
  try {
    console.log('credentials', {
      projectId: config.firebase.projectId,
      clientEmail: config.firebase.clientEmail,
      privateKey: config.firebase.privateKey,
    });
    const message: admin.messaging.Message = {
      notification: {
        title: payload.title,
        body: payload.body,
        imageUrl: payload.imageUrl,
      },
      data: payload.data,
      android: payload.android,
      apns: payload.apns,
      webpush: payload.webpush,
      token: Array.isArray(deviceToken) ? deviceToken[0] : deviceToken,
    };

    if (Array.isArray(deviceToken)) {
      // For multiple devices (up to 500 per batch)
      const batchResponse = await admin.messaging().sendEachForMulticast({
        tokens: deviceToken,
        notification: message.notification,
        data: message.data,
        android: message.android,
        apns: message.apns,
        webpush: message.webpush,
      });
      console.log('Successfully sent multicast notification:', batchResponse);
      return batchResponse;
    } else {
      // For single device
      const response = await admin.messaging().send(message);
      console.log('Successfully sent notification:', response);
      return response;
    }
  } catch (error) {
    console.error('Error sending notification:', error);
    throw error;
  }
}

// Topic management functions
export async function subscribeToTopic(
  deviceTokens: string[],
  topic: NotificationTopic
): Promise<admin.messaging.MessagingTopicManagementResponse> {
  try {
    const response = await admin.messaging().subscribeToTopic(deviceTokens, topic);
    console.log('Successfully subscribed to topic:', response);
    return response;
  } catch (error) {
    console.error('Error subscribing to topic:', error);
    throw error;
  }
}

export async function unsubscribeFromTopic(
  deviceTokens: string[],
  topic: NotificationTopic
): Promise<admin.messaging.MessagingTopicManagementResponse> {
  try {
    const response = await admin.messaging().unsubscribeFromTopic(deviceTokens, topic);
    console.log('Successfully unsubscribed from topic:', response);
    return response;
  } catch (error) {
    console.error('Error unsubscribing from topic:', error);
    throw error;
  }
}

export async function sendToTopic(
  topic: NotificationTopic,
  payload: NotificationPayload
): Promise<string> {
  try {
    const message: admin.messaging.Message = {
      notification: {
        title: payload.title,
        body: payload.body,
        imageUrl: payload.imageUrl,
      },
      data: payload.data,
      android: payload.android,
      apns: payload.apns,
      webpush: payload.webpush,
      topic: topic,
    };

    const response = await admin.messaging().send(message);
    console.log('Successfully sent to topic:', response);
    return response;
  } catch (error) {
    console.error('Error sending to topic:', error);
    throw error;
  }
}
// Additional utility to check topic subscription status
// export async function checkTopicSubscription(
//   deviceToken: string,
//   topic: NotificationTopic
// ): Promise<boolean> {
//   try {
//     const response = await admin.messaging().getTopics(deviceToken);
//     return response.topics.includes(topic);
//   } catch (error) {
//     console.error('Error checking topic subscription:', error);
//     throw error;
//   }
// }

// export admin
export { admin };
