/**
 * Centralized Timezone Management System
 * Handles all date/time operations with proper timezone conversion
 * for Ciribey Gas Delivery System (Mogadishu, Somalia - EAT UTC+3)
 */

import { UserRole } from '../enums/enums';
import logger from '../config/logger';

// Application timezone configuration
export const APP_TIMEZONE = 'Africa/Mogadishu'; // EAT (UTC+3)
export const APP_TIMEZONE_OFFSET = '+03:00';

/**
 * User timezone context for operations
 */
export interface TimezoneContext {
  userId?: string;
  role?: UserRole;
  timezone?: string;
  clientTimezone?: string; // From client request headers
}

/**
 * Date range for filtering operations
 */
export interface DateRange {
  start: Date;
  end: Date;
  timezone: string;
}

/**
 * Timezone utility class for centralized date/time management
 */
export class TimezoneManager {
  private static instance: TimezoneManager;

  private constructor() {}

  public static getInstance(): TimezoneManager {
    if (!TimezoneManager.instance) {
      TimezoneManager.instance = new TimezoneManager();
    }
    return TimezoneManager.instance;
  }

  /**
   * Get the appropriate timezone for a user based on their role and context
   * @param context - User context with role and timezone info
   * @returns timezone string (IANA timezone identifier)
   */
  public getUserTimezone(context: TimezoneContext = {}): string {
    // Priority order:
    // 1. Explicitly provided client timezone
    // 2. User's stored timezone preference (if implemented)
    // 3. Application default timezone (Mogadishu)
    
    if (context.clientTimezone) {
      return this.validateTimezone(context.clientTimezone) ? context.clientTimezone : APP_TIMEZONE;
    }
    
    if (context.timezone) {
      return this.validateTimezone(context.timezone) ? context.timezone : APP_TIMEZONE;
    }
    
    // Default to Mogadishu timezone for all users
    return APP_TIMEZONE;
  }

  /**
   * Validate if a timezone string is valid
   * @param timezone - IANA timezone identifier
   * @returns true if valid, false otherwise
   */
  private validateTimezone(timezone: string): boolean {
    try {
      Intl.DateTimeFormat(undefined, { timeZone: timezone });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get start and end of day in user's timezone
   * @param date - Date to get day boundaries for (defaults to today)
   * @param context - User timezone context
   * @returns DateRange with start and end of day in UTC for MongoDB queries
   */
  public getDayBoundaries(date: Date = new Date(), context: TimezoneContext = {}): DateRange {
    const userTimezone = this.getUserTimezone(context);
    
    try {
      // Create date in user's timezone
      const userDate = new Date(date.toLocaleString('en-US', { timeZone: userTimezone }));
      
      // Get start of day in user's timezone
      const startOfDay = new Date(userDate);
      startOfDay.setHours(0, 0, 0, 0);
      
      // Get end of day in user's timezone
      const endOfDay = new Date(userDate);
      endOfDay.setHours(23, 59, 59, 999);
      
      // Convert back to UTC for MongoDB storage/queries
      const utcStart = this.convertToUTC(startOfDay, userTimezone);
      const utcEnd = this.convertToUTC(endOfDay, userTimezone);
      
      logger.debug('Generated day boundaries', {
        userTimezone,
        inputDate: date.toISOString(),
        localStart: startOfDay.toISOString(),
        localEnd: endOfDay.toISOString(),
        utcStart: utcStart.toISOString(),
        utcEnd: utcEnd.toISOString(),
      });
      
      return {
        start: utcStart,
        end: utcEnd,
        timezone: userTimezone,
      };
    } catch (error) {
      logger.error('Failed to generate day boundaries', {
        error: error.message,
        userTimezone,
        inputDate: date.toISOString(),
      });
      
      // Fallback to UTC boundaries
      const startOfDay = new Date(date);
      startOfDay.setUTCHours(0, 0, 0, 0);
      const endOfDay = new Date(date);
      endOfDay.setUTCHours(23, 59, 59, 999);
      
      return {
        start: startOfDay,
        end: endOfDay,
        timezone: 'UTC',
      };
    }
  }

  /**
   * Convert date range from user's timezone to UTC for MongoDB queries
   * @param startDate - Start date in user's timezone
   * @param endDate - End date in user's timezone
   * @param context - User timezone context
   * @returns DateRange with UTC dates for MongoDB queries
   */
  public convertDateRangeToUTC(
    startDate?: Date | string,
    endDate?: Date | string,
    context: TimezoneContext = {}
  ): DateRange | null {
    if (!startDate && !endDate) {
      return null;
    }
    
    const userTimezone = this.getUserTimezone(context);
    
    try {
      let utcStart: Date;
      let utcEnd: Date;
      
      if (startDate) {
        const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
        utcStart = this.convertToUTC(start, userTimezone);
      } else {
        // If no start date, use beginning of time
        utcStart = new Date('1970-01-01T00:00:00.000Z');
      }
      
      if (endDate) {
        const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
        // If end date doesn't include time, set to end of day
        if (this.isDateOnly(end)) {
          end.setHours(23, 59, 59, 999);
        }
        utcEnd = this.convertToUTC(end, userTimezone);
      } else {
        // If no end date, use current time
        utcEnd = new Date();
      }
      
      logger.debug('Converted date range to UTC', {
        userTimezone,
        originalStart: startDate?.toString(),
        originalEnd: endDate?.toString(),
        utcStart: utcStart.toISOString(),
        utcEnd: utcEnd.toISOString(),
      });
      
      return {
        start: utcStart,
        end: utcEnd,
        timezone: userTimezone,
      };
    } catch (error) {
      logger.error('Failed to convert date range to UTC', {
        error: error.message,
        startDate: startDate?.toString(),
        endDate: endDate?.toString(),
        userTimezone,
      });
      return null;
    }
  }

  /**
   * Convert a date from user's timezone to UTC
   * @param date - Date in user's timezone
   * @param timezone - User's timezone
   * @returns Date in UTC
   */
  private convertToUTC(date: Date, timezone: string): Date {
    // Get the timezone offset for the given date and timezone
    const utcTime = date.getTime();
    const localTime = new Date(date.toLocaleString('en-US', { timeZone: timezone })).getTime();
    const timezoneOffset = utcTime - localTime;
    
    return new Date(utcTime + timezoneOffset);
  }

  /**
   * Check if a date object represents date-only (no specific time)
   * @param date - Date to check
   * @returns true if date appears to be date-only
   */
  private isDateOnly(date: Date): boolean {
    return date.getHours() === 0 && date.getMinutes() === 0 && 
           date.getSeconds() === 0 && date.getMilliseconds() === 0;
  }

  /**
   * Convert UTC date to user's timezone for display
   * @param utcDate - Date in UTC (from MongoDB)
   * @param context - User timezone context
   * @returns Date object in user's timezone
   */
  public convertFromUTC(utcDate: Date, context: TimezoneContext = {}): Date {
    const userTimezone = this.getUserTimezone(context);
    
    try {
      // Convert UTC date to user's timezone
      const userDate = new Date(utcDate.toLocaleString('en-US', { timeZone: userTimezone }));
      
      logger.debug('Converted UTC to user timezone', {
        userTimezone,
        utcDate: utcDate.toISOString(),
        userDate: userDate.toISOString(),
      });
      
      return userDate;
    } catch (error) {
      logger.error('Failed to convert UTC to user timezone', {
        error: error.message,
        utcDate: utcDate.toISOString(),
        userTimezone,
      });
      
      // Fallback to original date
      return utcDate;
    }
  }

  /**
   * Get today's date boundaries for role-specific filtering
   * @param role - User role
   * @param context - User timezone context
   * @returns DateRange for today in user's timezone (converted to UTC)
   */
  public getTodayBoundariesForRole(role: UserRole, context: TimezoneContext = {}): DateRange {
    const today = new Date();
    const boundaries = this.getDayBoundaries(today, { ...context, role });
    
    logger.info('Generated today boundaries for role', {
      role,
      timezone: boundaries.timezone,
      start: boundaries.start.toISOString(),
      end: boundaries.end.toISOString(),
    });
    
    return boundaries;
  }

  /**
   * Build MongoDB date query with proper timezone handling
   * @param startDate - Start date filter
   * @param endDate - End date filter
   * @param context - User timezone context
   * @returns MongoDB date query object or null
   */
  public buildDateQuery(
    startDate?: Date | string,
    endDate?: Date | string,
    context: TimezoneContext = {}
  ): { createdAt?: any } | null {
    const dateRange = this.convertDateRangeToUTC(startDate, endDate, context);
    
    if (!dateRange) {
      return null;
    }
    
    const query: any = {};
    
    if (dateRange.start && dateRange.end) {
      query.createdAt = {
        $gte: dateRange.start,
        $lte: dateRange.end,
      };
    } else if (dateRange.start) {
      query.createdAt = { $gte: dateRange.start };
    } else if (dateRange.end) {
      query.createdAt = { $lte: dateRange.end };
    }
    
    logger.debug('Built MongoDB date query', {
      query,
      timezone: dateRange.timezone,
    });
    
    return Object.keys(query).length > 0 ? query : null;
  }

  /**
   * Format date for display in user's timezone
   * @param utcDate - Date in UTC
   * @param context - User timezone context
   * @param options - Intl.DateTimeFormat options
   * @returns Formatted date string
   */
  public formatDateForUser(
    utcDate: Date,
    context: TimezoneContext = {},
    options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short',
    }
  ): string {
    const userTimezone = this.getUserTimezone(context);
    
    try {
      return utcDate.toLocaleString('en-US', {
        ...options,
        timeZone: userTimezone,
      });
    } catch (error) {
      logger.error('Failed to format date for user', {
        error: error.message,
        utcDate: utcDate.toISOString(),
        userTimezone,
      });
      
      // Fallback to ISO string
      return utcDate.toISOString();
    }
  }
}

// Export singleton instance
export const timezoneManager = TimezoneManager.getInstance();

// Convenience functions for common operations
export const getUserTimezone = (context?: TimezoneContext) => timezoneManager.getUserTimezone(context);
export const getDayBoundaries = (date?: Date, context?: TimezoneContext) => timezoneManager.getDayBoundaries(date, context);
export const convertDateRangeToUTC = (startDate?: Date | string, endDate?: Date | string, context?: TimezoneContext) => 
  timezoneManager.convertDateRangeToUTC(startDate, endDate, context);
export const buildDateQuery = (startDate?: Date | string, endDate?: Date | string, context?: TimezoneContext) =>
  timezoneManager.buildDateQuery(startDate, endDate, context);
export const formatDateForUser = (utcDate: Date, context?: TimezoneContext, options?: Intl.DateTimeFormatOptions) =>
  timezoneManager.formatDateForUser(utcDate, context, options);
