import crypto from 'crypto';
import { config } from '../config/env_config';
import logger from '../config/logger';

export interface IOtp {
  code: string;
  expirationTime: Date;
}

/**
 * Check if OTP verification should be skipped
 * @returns true if OTP verification is disabled in configuration
 */
export function shouldSkipOtp(): boolean {
  return config.server.disableOtpVerification;
}

/**
 * Generate OTP code and expiration time
 * @returns IOtp object with code and expiration time
 */
export function generateOtp(): IOtp {
  if (shouldSkipOtp()) {
    logger.info('OTP verification disabled, generating dummy OTP');
    return {
      code: '000000',
      expirationTime: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
    };
  }

  const otp = crypto.randomInt(100000, 999999).toString();
  const expirationTime = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

  logger.info('Generated secure OTP', {
    expirationTime: expirationTime.toISOString(),
    expiresInMinutes: 5,
  });

  return { code: otp, expirationTime };
}

/**
 * Validate OTP code and expiration
 * @param storedOtp - The stored OTP object
 * @param providedCode - The code provided by user
 * @returns validation result with success status and error message
 */
export function validateOtp(
  storedOtp: IOtp | null | undefined,
  providedCode: string
): { isValid: boolean; error?: string } {
  if (!storedOtp) {
    return { isValid: false, error: 'No OTP found. Please request a new one.' };
  }

  if (!storedOtp.code) {
    return { isValid: false, error: 'Invalid OTP data. Please request a new one.' };
  }

  if (storedOtp.code !== providedCode) {
    return { isValid: false, error: 'Incorrect OTP. Please try again.' };
  }

  if (storedOtp.expirationTime <= new Date()) {
    return { isValid: false, error: 'OTP has expired. Please request a new one.' };
  }

  return { isValid: true };
}

/**
 * Check if OTP is still valid (not expired)
 * @param storedOtp - The stored OTP object
 * @returns true if OTP exists and is not expired
 */
export function isOtpValid(storedOtp: IOtp | null | undefined): boolean {
  if (!storedOtp || !storedOtp.expirationTime) {
    return false;
  }
  return storedOtp.expirationTime > new Date();
}

/**
 * Get remaining time for OTP in seconds
 * @param storedOtp - The stored OTP object
 * @returns remaining seconds or 0 if expired/invalid
 */
export function getOtpRemainingTime(storedOtp: IOtp | null | undefined): number {
  if (!storedOtp || !storedOtp.expirationTime) {
    return 0;
  }

  const now = new Date();
  const remaining = Math.ceil((storedOtp.expirationTime.getTime() - now.getTime()) / 1000);
  return Math.max(0, remaining);
}
