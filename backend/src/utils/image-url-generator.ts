import { CylinderType, CylinderMaterial, SparePartCategory } from '../enums/enums';
import { ImagePathUtils, ImageCategoryHelper } from './image_utils';
import { EntityType } from '../enums/enums';

export class ImageUrlGenerator {
  private static readonly BASE_IMAGE_PATH = '/api/v1/images';

  /**
   * Generate image URL for cylinders based on type
   */
  static getCylinderImageUrl(type: CylinderType, material?: CylinderMaterial): string {
    // Extract numeric value from cylinder type (e.g., "6KG" -> "6")
    const weight = type.replace(/[^\d]/g, '');
    const imageFileName = `${weight}kg.png`.toLowerCase();
    return `${ImageUrlGenerator.BASE_IMAGE_PATH}/cylinders/${imageFileName}`;
  }

  /**
   * Generate image URL for spare parts based on category
   */
  static getSparePartImageUrl(category: SparePartCategory): string {
    const imageMap: Record<SparePartCategory, string> = {
      [SparePartCategory.BRASS_CONTROL_VALVE_KIT]: 'brass_controll_valve_kit.png',
      [SparePartCategory.REGULATOR_HIGH_PRESSURE]: 'regulator_high_pressure.png',
      [SparePartCategory.REGULATOR_LOW_PRESSURE]: 'regulator_low_pressure.png',
      [SparePartCategory.SINGLE_BURNER_GAS_STOVE]: 'single_burner_gas_stove.png',
      [SparePartCategory.THREE_BURNER_LPG_STOVE]: 'three_burner_lpg_stove.png',
      [SparePartCategory.TUBO_2_METER]: 'tubo_2_meter.png',
      [SparePartCategory.VALUE_BURNER]: 'value_burner.png',
    };

    const fileName = imageMap[category] || 'default.png';
    return `${ImageUrlGenerator.BASE_IMAGE_PATH}/spare-parts/${fileName}`;
  }

  /**
   * Generate image URL for packages
   */
  static getPackageImageUrl(packageType: string): string {
    const packageName = packageType.toLowerCase().replace(/\s+/g, '-');
    const imageFileName = `${packageName}.png`;
    return `${ImageUrlGenerator.BASE_IMAGE_PATH}/packages/${imageFileName}`;
  }

  /**
   * Get default fallback image URL
   */
  static getDefaultImageUrl(type: 'cylinder' | 'spare-part' | 'package'): string {
    return `${ImageUrlGenerator.BASE_IMAGE_PATH}/${type}s/default.png`;
  }

  /**
   * Get image URL from uploaded file path (new system)
   * This method bridges the old and new image systems
   */
  static getUploadedImageUrl(imagePath: string): string {
    return ImagePathUtils.getImageUrl(imagePath);
  }

  /**
   * Get the best available image URL for an entity
   * Prioritizes uploaded images over legacy convention-based images
   */
  static getBestImageUrl(
    uploadedImagePath?: string,
    legacyImageUrl?: string,
    fallbackType?: 'cylinder' | 'spare-part' | 'package'
  ): string {
    // Priority 1: Uploaded image path
    if (uploadedImagePath) {
      return ImagePathUtils.getImageUrl(uploadedImagePath);
    }

    // Priority 2: Legacy image URL
    if (legacyImageUrl) {
      return legacyImageUrl;
    }

    // Priority 3: Default fallback
    if (fallbackType) {
      return ImageUrlGenerator.getDefaultImageUrl(fallbackType);
    }

    // Final fallback
    return `${ImageUrlGenerator.BASE_IMAGE_PATH}/default.png`;
  }
}

// Convenience functions for direct use
export const getCylinderImageUrl = ImageUrlGenerator.getCylinderImageUrl;
export const getSparePartImageUrl = ImageUrlGenerator.getSparePartImageUrl;
export const getPackageImageUrl = ImageUrlGenerator.getPackageImageUrl;
export const getDefaultImageUrl = ImageUrlGenerator.getDefaultImageUrl;
