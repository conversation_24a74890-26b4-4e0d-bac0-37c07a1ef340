import jwt, { SignOptions } from 'jsonwebtoken';

import { config } from '../config/env_config';
import { TokenPayload, QRTokenPayload } from '../types/interfaces';

export const generateToken = (
  userId: TokenPayload['userId'],
  role: TokenPayload['role']
): string => {
  return jwt.sign({ userId, role }, config.server.jwtSecret, { expiresIn: '360d' });
};

export const verifyToken = (token: string): TokenPayload => {
  const decoded = jwt.verify(token, config.server.jwtSecret);

  if (typeof decoded === 'string' || !('userId' in decoded) || !('role' in decoded)) {
    throw new Error('Invalid token payload');
  }

  return decoded as TokenPayload;
};

///---------------------- QR  SECTION ----------------

export const qrSecretKey = config.server.qrSecretKey;

// Generate a signed QR payload using JWT
export function generateQRPayload(payload: QRTokenPayload): string {
  const options: SignOptions = {
    expiresIn: (payload.expiresIn ?? '24h') as SignOptions['expiresIn'],
  };

  return jwt.sign(payload, qrSecretKey, options);
}

// Validate QR payload using JWT
export function validateQRPayload(token: string): {
  success: boolean;
  orderId: string | null;
  expiresAt: Date | null;
  message: string;
} {
  try {
    // const decoded = jwt.verify(token, qrSecretKey) as jwt.JwtPayload;
    const decoded = jwt.verify(token, qrSecretKey) as jwt.JwtPayload & QRTokenPayload;

    const orderId = decoded.orderId;
    const expiresAt = decoded.exp ? new Date(decoded.exp * 1000) : null;

    if (!orderId) {
      return {
        success: false,
        orderId: null,
        expiresAt,
        message: 'Missing orderId in token',
      };
    }

    return {
      success: true,
      orderId,
      expiresAt,
      message: 'QR code is valid',
    };
  } catch (err) {
    return {
      success: false,
      orderId: null,
      expiresAt: null,
      message: (err as Error).message,
    };
  }
}
