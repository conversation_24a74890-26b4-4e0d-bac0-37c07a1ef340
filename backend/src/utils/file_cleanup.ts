import fs from 'fs/promises';
import path from 'path';
import logger from '../config/logger';

/**
 * File cleanup utility for managing uploaded files
 * Handles deletion of image files when entities are permanently deleted
 */
export class FileCleanupService {
  /**
   * Delete an image file from the uploads directory
   * @param imagePath - The relative path to the image file (e.g., "uploads/images/cylinders/2025/07/filename.jpg")
   * @returns Promise<boolean> - true if file was deleted, false if file didn't exist
   */
  static async deleteImageFile(imagePath: string): Promise<boolean> {
    try {
      if (!imagePath || imagePath.trim() === '') {
        logger.warn('Empty image path provided for deletion');
        return false;
      }

      // Ensure the path is within the uploads directory for security
      const normalizedPath = path.normalize(imagePath);
      if (!normalizedPath.startsWith('uploads/')) {
        logger.warn('Attempted to delete file outside uploads directory', {
          imagePath,
          normalizedPath,
        });
        return false;
      }

      // Get the absolute path
      const absolutePath = path.resolve(normalizedPath);

      // Check if file exists
      try {
        await fs.access(absolutePath);
      } catch (error) {
        logger.info('Image file does not exist, skipping deletion', {
          imagePath,
          absolutePath,
        });
        return false;
      }

      // Delete the file
      await fs.unlink(absolutePath);

      logger.info('Image file deleted successfully', {
        imagePath,
        absolutePath,
      });

      return true;
    } catch (error) {
      logger.error('Failed to delete image file', {
        imagePath,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      });

      // Don't throw error - file cleanup shouldn't break the main operation
      return false;
    }
  }

  /**
   * Delete multiple image files
   * @param imagePaths - Array of image paths to delete
   * @returns Promise<{ deleted: number; failed: number }> - Summary of deletion results
   */
  static async deleteMultipleImageFiles(
    imagePaths: string[]
  ): Promise<{ deleted: number; failed: number }> {
    let deleted = 0;
    let failed = 0;

    for (const imagePath of imagePaths) {
      const success = await this.deleteImageFile(imagePath);
      if (success) {
        deleted++;
      } else {
        failed++;
      }
    }

    logger.info('Bulk image file deletion completed', {
      total: imagePaths.length,
      deleted,
      failed,
    });

    return { deleted, failed };
  }

  /**
   * Clean up empty directories after file deletion
   * @param imagePath - The path of the deleted file
   */
  static async cleanupEmptyDirectories(imagePath: string): Promise<void> {
    try {
      if (!imagePath || !imagePath.startsWith('uploads/')) {
        return;
      }

      const dirPath = path.dirname(path.resolve(imagePath));

      // Check if directory is empty
      const files = await fs.readdir(dirPath);
      if (files.length === 0) {
        await fs.rmdir(dirPath);
        logger.info('Removed empty directory', { dirPath });

        // Recursively check parent directories
        const parentPath = path.dirname(dirPath);
        if (parentPath !== path.resolve('uploads') && parentPath.includes('uploads')) {
          await this.cleanupEmptyDirectories(path.relative(process.cwd(), parentPath));
        }
      }
    } catch (error) {
      // Ignore errors - directory cleanup is optional
      logger.debug('Directory cleanup failed (non-critical)', {
        imagePath,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Validate that a file path is safe to delete
   * @param filePath - The file path to validate
   * @returns boolean - true if safe to delete
   */
  static isValidImagePath(filePath: string): boolean {
    if (!filePath || typeof filePath !== 'string') {
      return false;
    }

    const normalizedPath = path.normalize(filePath);

    // Must be within uploads directory
    if (!normalizedPath.startsWith('uploads/')) {
      return false;
    }

    // Must be an image file
    const ext = path.extname(normalizedPath).toLowerCase();
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];

    return allowedExtensions.includes(ext);
  }

  /**
   * Get file size before deletion (for logging purposes)
   * @param imagePath - The image path
   * @returns Promise<number> - File size in bytes, or 0 if file doesn't exist
   */
  static async getFileSize(imagePath: string): Promise<number> {
    try {
      const absolutePath = path.resolve(imagePath);
      const stats = await fs.stat(absolutePath);
      return stats.size;
    } catch (error) {
      return 0;
    }
  }
}
