import { notificationDispatcher } from '../services/notification-dispatcher.service';
import { UserRole } from '../enums/enums';
import { User as UserModel } from '../models/index';
import logger from '../config/logger';

/**
 * Notification Helper Utilities
 * Provides convenient methods for sending common notifications
 */

/**
 * Get admin and supervisor contact information for inventory alerts
 */
async function getAdminContacts(): Promise<{
  emails: string[];
  phones: string[];
  userIds: string[];
}> {
  try {
    const admins = await UserModel.find({
      role: { $in: [UserRole.ADMIN, UserRole.SUPERVISOR] },
      isActive: true,
    })
      .select('email phone _id')
      .lean();

    const emails = admins.filter((admin: any) => admin.email).map((admin: any) => admin.email!);
    const phones = admins.filter((admin: any) => admin.phone).map((admin: any) => admin.phone);
    const userIds = admins.map((admin: any) => admin._id.toString());

    return { emails, phones, userIds };
  } catch (error) {
    logger.error('Failed to get admin contacts', { error: error.message });
    return { emails: [], phones: [], userIds: [] };
  }
}

/**
 * Send OTP verification message
 */
async function sendOtpNotification(
  userId: string,
  phone: string,
  otp: string,
  expiresIn: number = 5,
  language: 'so' | 'en' = 'so'
) {
  try {
    return await notificationDispatcher.sendNotification({
      type: 'otp_verification',
      channels: ['sms'],
      recipients: [{ userId, phone, language, }],
      data: { otp, expiresIn },
      options: { priority: 'high', includePhoneInFooter: false },
    });
  } catch (error) {
    logger.error('Failed to send OTP notification', { userId, error: error.message });
    throw error;
  }
}

/**
 * Send order confirmation notification
 */
async function sendOrderConfirmationNotification(
  userId: string,
  phone: string,
  orderId: string,
  language: 'so' | 'en' = 'so'
) {
  try {
    return await notificationDispatcher.sendNotification({
      type: 'order_confirmed',
      channels: ['sms', 'push'],
      recipients: [{ userId, phone, language }],
      data: { orderId },
      options: { priority: 'high' },
    });
  } catch (error) {
    logger.error('Failed to send order confirmation notification', {
      userId,
      orderId,
      error: error.message,
    });
    throw error;
  }
}

/**
 * Send order delivered notification
 */
async function sendOrderDeliveredNotification(
  userId: string,
  phone: string,
  orderId: string,
  language: 'so' | 'en' = 'so'
) {
  try {
    return await notificationDispatcher.sendNotification({
      type: 'order_delivered',
      channels: ['sms', 'push'],
      recipients: [{ userId, phone, language }],
      data: { orderId },
      options: { priority: 'normal' },
    });
  } catch (error) {
    logger.error('Failed to send order delivered notification', {
      userId,
      orderId,
      error: error.message,
    });
    throw error;
  }
}

/**
 * Send order cancelled notification
 */
async function sendOrderCancelledNotification(
  userId: string,
  phone: string,
  orderId: string,
  reason?: string,
  language: 'so' | 'en' = 'so'
) {
  try {
    return await notificationDispatcher.sendNotification({
      type: 'order_cancelled',
      channels: ['sms', 'push'],
      recipients: [{ userId, phone, language }],
      data: { orderId, reason },
      options: { priority: 'high' },
    });
  } catch (error) {
    logger.error('Failed to send order cancelled notification', {
      userId,
      orderId,
      error: error.message,
    });
    throw error;
  }
}

/**
 * Send delivery assignment notification to agent
 */
async function sendDeliveryAssignmentNotification(
  agentId: string,
  agentPhone: string,
  orderId: string,
  customerName: string,
  address: string,
  language: 'so' | 'en' = 'so'
) {
  try {
    return await notificationDispatcher.sendNotification({
      type: 'delivery_assigned',
      channels: ['sms', 'push'],
      recipients: [{ userId: agentId, phone: agentPhone, role: UserRole.AGENT, language }],
      data: { orderId, customerName, address },
      options: { priority: 'high', includePhoneInFooter: false },
    });
  } catch (error) {
    logger.error('Failed to send delivery assignment notification', {
      agentId,
      orderId,
      error: error.message,
    });
    throw error;
  }
}

/**
 * Send delivery started notification to customer
 */
async function sendDeliveryStartedNotification(
  customerId: string,
  customerPhone: string,
  orderId: string,
  agentName: string,
  agentPhone: string,
  language: 'so' | 'en' = 'so'
) {
  try {
    return await notificationDispatcher.sendNotification({
      type: 'delivery_started',
      channels: ['sms', 'push'],
      recipients: [{ userId: customerId, phone: customerPhone, language }],
      data: { orderId, agentName, agentPhone },
      options: { priority: 'high' },
    });
  } catch (error) {
    logger.error('Failed to send delivery started notification', {
      customerId,
      orderId,
      error: error.message,
    });
    throw error;
  }
}

/**
 * Send payment confirmation notification
 */
async function sendPaymentConfirmationNotification(
  userId: string,
  phone: string,
  orderId: string,
  amount: number,
  language: 'so' | 'en' = 'so'
) {
  try {
    return await notificationDispatcher.sendNotification({
      type: 'payment_confirmed',
      channels: ['sms', 'push'],
      recipients: [{ userId, phone, language }],
      data: { orderId, amount },
      options: { priority: 'high' },
    });
  } catch (error) {
    logger.error('Failed to send payment confirmation notification', {
      userId,
      orderId,
      error: error.message,
    });
    throw error;
  }
}

/**
 * Send payment failed notification
 */
async function sendPaymentFailedNotification(
  userId: string,
  phone: string,
  orderId: string,
  reason: string,
  language: 'so' | 'en' = 'so'
) {
  try {
    return await notificationDispatcher.sendNotification({
      type: 'payment_failed',
      channels: ['sms', 'push'],
      recipients: [{ userId, phone, language }],
      data: { orderId, reason },
      options: { priority: 'high' },
    });
  } catch (error) {
    logger.error('Failed to send payment failed notification', {
      userId,
      orderId,
      error: error.message,
    });
    throw error;
  }
}

/**
 * Send low stock alert to admins and supervisors
 */
async function sendLowStockAlert(item: string, quantity: number, language: 'so' | 'en' = 'so') {
  try {
    const { emails, phones, userIds } = await getAdminContacts();

    if (emails.length === 0 && phones.length === 0) {
      logger.warn('No admin contacts found for low stock alert', { item, quantity });
      return {
        success: false,
        message: 'No admin contacts available',
        totalRecipients: 0,
        successfulDeliveries: 0,
        failedDeliveries: 0,
        errors: ['No admin contacts found'],
        channels: {},
      };
    }

    const recipients = [
      ...emails.map(email => ({ email, role: UserRole.ADMIN, language })),
      ...phones.map(phone => ({ phone, role: UserRole.ADMIN, language })),
    ];

    return await notificationDispatcher.sendNotification({
      type: 'low_stock_alert',
      channels: ['sms', 'email'],
      recipients,
      data: { item, quantity },
      options: { priority: 'high', includePhoneInFooter: false },
    });
  } catch (error) {
    logger.error('Failed to send low stock alert', { item, quantity, error: error.message });
    throw error;
  }
}

/**
 * Send out of stock alert to admins
 */
async function sendOutOfStockAlert(item: string, language: 'so' | 'en' = 'so') {
  try {
    const { emails, phones } = await getAdminContacts();

    if (emails.length === 0 && phones.length === 0) {
      logger.warn('No admin contacts found for out of stock alert', { item });
      return {
        success: false,
        message: 'No admin contacts available',
        totalRecipients: 0,
        successfulDeliveries: 0,
        failedDeliveries: 0,
        errors: ['No admin contacts found'],
        channels: {},
      };
    }

    const recipients = [
      ...emails.map(email => ({ email, role: UserRole.ADMIN, language })),
      ...phones.map(phone => ({ phone, role: UserRole.ADMIN, language })),
    ];

    return await notificationDispatcher.sendNotification({
      type: 'out_of_stock_alert',
      channels: ['sms', 'email'],
      recipients,
      data: { item },
      options: { priority: 'high', includePhoneInFooter: false },
    });
  } catch (error) {
    logger.error('Failed to send out of stock alert', { item, error: error.message });
    throw error;
  }
}

/**
 * Check stock levels and send appropriate alerts
 */
async function checkStockAndAlert(
  itemName: string,
  currentQuantity: number,
  minimumStockLevel: number,
  language: 'so' | 'en' = 'so'
) {
  try {
    if (currentQuantity === 0) {
      // Out of stock - urgent alert
      logger.warn('Item out of stock, sending alert', { itemName, currentQuantity });
      return await sendOutOfStockAlert(itemName, language);
    } else if (currentQuantity <= minimumStockLevel) {
      // Low stock - warning alert
      logger.warn('Item low stock, sending alert', {
        itemName,
        currentQuantity,
        minimumStockLevel,
      });
      return await sendLowStockAlert(itemName, currentQuantity, language);
    }

    // Stock levels are fine
    return null;
  } catch (error) {
    logger.error('Failed to check stock and send alert', {
      itemName,
      currentQuantity,
      minimumStockLevel,
      error: error.message,
    });
    throw error;
  }
}

/**
 * Send welcome message to new user
 */
async function sendWelcomeMessage(
  userId: string,
  phone: string,
  userName: string,
  userRole: UserRole,
  language: 'so' | 'en' = 'so'
) {
  try {
    return await notificationDispatcher.sendNotification({
      type: 'welcome_message',
      // channels: ['sms', 'push'],
      channels: ['sms'],
      recipients: [{ userId, phone, role: userRole, language }],
      data: { userName, userRole },
      options: { priority: 'normal' },
    });
  } catch (error) {
    logger.error('Failed to send welcome message', { userId, userName, error: error.message });
    throw error;
  }
}

/**
 * Send emergency alert to all users
 */
async function sendEmergencyAlert(
  message: string,
  targetRoles: UserRole[] = [
    UserRole.CUSTOMER,
    UserRole.AGENT,
    UserRole.ADMIN,
    UserRole.SUPERVISOR,
  ],
  language: 'so' | 'en' = 'so'
) {
  try {
    // This would typically fetch users from database based on roles
    // For now, we'll use topic-based notification
    return await notificationDispatcher.sendNotification({
      type: 'emergency_alert',
      channels: ['sms', 'push'],
      recipients: [{ phone: 'broadcast', role: UserRole.CUSTOMER, language }], // Placeholder
      data: { message },
      options: { priority: 'high', includePhoneInFooter: true },
    });
  } catch (error) {
    logger.error('Failed to send emergency alert', { message, error: error.message });
    throw error;
  }
}

/**
 * Send system maintenance notification
 */
async function sendMaintenanceNotification(
  startTime: string,
  duration: string,
  language: 'so' | 'en' = 'so'
) {
  try {
    return await notificationDispatcher.sendNotification({
      type: 'maintenance_notification',
      channels: ['sms', 'push'],
      recipients: [{ phone: 'broadcast', language }], // Placeholder for broadcast
      data: { startTime, duration },
      options: { priority: 'normal' },
    });
  } catch (error) {
    logger.error('Failed to send maintenance notification', {
      startTime,
      duration,
      error: error.message,
    });
    throw error;
  }
}

/*

Namespace export by group related notifications eg(order..)

*/
const Order = {
  sendOrderConfirmationNotification,
  sendOrderDeliveredNotification,
  sendOrderCancelledNotification,
  sendDeliveryAssignmentNotification,
  sendDeliveryStartedNotification,
  sendPaymentConfirmationNotification,
  sendPaymentFailedNotification,
};

const Inventory = {
  sendLowStockAlert,
  sendOutOfStockAlert,
  checkStockAndAlert,
};

const User = {
  sendWelcomeMessage,
  sendOtpNotification,
};

const System = {
  sendEmergencyAlert,
  sendMaintenanceNotification,
};

export const Notifications = {
  Order,
  Inventory,
  User,
  System,
} as const;
