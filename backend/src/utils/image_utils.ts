/**
 * Simple Image Path Management
 * Each model stores its own image paths directly for high performance
 */

import { EntityType } from '../enums/enums';

/**
 * Image category mapping from EntityType to directory names
 */
export const IMAGE_CATEGORY_MAP = {
  [EntityType.Cylinder]: 'cylinders',
  [EntityType.SparePart]: 'spare-parts',
  [EntityType.Package]: 'packages',
} as const;

/**
 * Additional image categories for non-product entities
 */
export enum AdditionalImageCategory {
  USER_PROFILE = 'profiles',
}

/**
 * Combined type for all image categories
 */
export type ImageCategory = (typeof IMAGE_CATEGORY_MAP)[EntityType] | AdditionalImageCategory;

/**
 * All valid image category values
 */
export const ALL_IMAGE_CATEGORIES = [
  ...Object.values(IMAGE_CATEGORY_MAP),
  ...Object.values(AdditionalImageCategory),
] as const;

/**
 * Helper functions for EntityType to category conversion
 */
export const ImageCategoryHelper = {
  fromEntityType: (entityType: EntityType): string => IMAGE_CATEGORY_MAP[entityType],
  isValidCategory: (category: string): category is ImageCategory =>
    ALL_IMAGE_CATEGORIES.includes(category as any),
  getAllCategories: () => ALL_IMAGE_CATEGORIES,
};

/**
 * Image path utilities for consistent file organization
 */
export class ImagePathUtils {
  private static readonly UPLOADS_BASE = 'uploads/images';

  /**
   * Generate organized file path for uploaded images
   * Format: uploads/images/{category}/{year}/{month}/{filename}
   */
  static generateImagePath(category: string, originalName: string): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const timestamp = Date.now();
    const extension = originalName.split('.').pop()?.toLowerCase() || 'jpg';
    const filename = `${timestamp}-${Math.random().toString(36).substring(2)}.${extension}`;

    return `${this.UPLOADS_BASE}/${category}/${year}/${month}/${filename}`;
  }

  /**
   * Generate URL for serving images
   */
  static getImageUrl(imagePath: string): string {
    if (!imagePath) return '';
    // Remove 'uploads/' prefix for URL serving
    const urlPath = imagePath.replace(/^uploads\//, '');
    return `/api/v1/images/${urlPath}`;
  }

  /**
   * Get full file system path
   */
  static getFullPath(imagePath: string): string {
    return imagePath; // Already includes uploads/images/...
  }

  /**
   * Extract category from image path
   */
  static getCategoryFromPath(imagePath: string): string | null {
    const match = imagePath.match(/uploads\/images\/([^\/]+)/);
    if (!match) return null;

    const categoryPath = match[1];
    return ImageCategoryHelper.isValidCategory(categoryPath) ? categoryPath : null;
  }
}
