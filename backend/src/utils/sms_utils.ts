/**
 * SMS Utility Functions
 * 
 * This module provides utility functions for SMS handling including:
 * - Unicode character detection and removal
 * - Message sanitization for SMS providers
 * - Character encoding validation
 * - Message length optimization
 */

/**
 * Common Unicode characters that cause SMS issues
 */
const PROBLEMATIC_UNICODE = {
  // Emojis
  '🔥': 'FIRE',
  '📱': 'PHONE',
  '✅': 'CHECK',
  '❌': 'X',
  '📊': 'CHART',
  '📞': 'PHONE',
  '🌐': 'WEB',
  '📧': 'EMAIL',
  '🚀': 'ROCKET',
  '💥': 'BOOM',
  '⚠️': 'WARNING',
  '🎯': 'TARGET',
  '🔍': 'SEARCH',
  '📋': 'CLIPBOARD',
  '📅': 'CALENDAR',
  '⏰': 'CLOCK',
  '📈': 'TRENDING_UP',
  '💡': 'BULB',
  '🛠️': 'TOOLS',
  '🎉': 'PARTY',
  '👥': 'PEOPLE',
  '📦': 'PACKAGE',
  '🔧': 'WRENCH',
  '📝': 'MEMO',
  '🔐': 'LOCK',
  '📤': 'OUTBOX',
  '📡': 'SATELLITE',
  '🐛': 'BUG',
  
  // Special symbols
  '•': '-',
  '→': '->',
  '←': '<-',
  '↑': '^',
  '↓': 'v',
  '✓': 'CHECK',
  '×': 'X',
  '±': '+/-',
  '≤': '<=',
  '≥': '>=',
  '≠': '!=',
  '∞': 'INFINITY',
  
  // Currency and math
  '€': 'EUR',
  '£': 'GBP',
  '¥': 'YEN',
  '₹': 'INR',
  '°': 'deg',
  '²': '2',
  '³': '3',
  '½': '1/2',
  '¼': '1/4',
  '¾': '3/4',
  
  // Quotes and dashes
  '"': '"',
  "'": "'",
  '–': '-',
  '—': '-',
  '…': '...',
};

/**
 * Detects if a string contains Unicode characters that might cause SMS issues
 */
export function containsProblematicUnicode(text: string): boolean {
  // Check for emojis (most common issue)
  const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u;
  
  // Check for other problematic Unicode
  const problematicChars = Object.keys(PROBLEMATIC_UNICODE);
  
  return emojiRegex.test(text) || problematicChars.some(char => text.includes(char));
}

/**
 * Sanitizes SMS text by replacing problematic Unicode characters
 */
export function sanitizeSmsText(text: string, options: {
  removeEmojis?: boolean;
  replaceUnicode?: boolean;
  preserveFormatting?: boolean;
} = {}): string {
  const {
    removeEmojis = true,
    replaceUnicode = true,
    preserveFormatting = true,
  } = options;

  let sanitized = text;

  if (replaceUnicode) {
    // Replace known problematic Unicode characters
    Object.entries(PROBLEMATIC_UNICODE).forEach(([unicode, replacement]) => {
      sanitized = sanitized.replace(new RegExp(unicode, 'g'), replacement);
    });
  }

  if (removeEmojis) {
    // Remove emojis that weren't in our replacement map
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
    sanitized = sanitized.replace(emojiRegex, '');
  }

  if (preserveFormatting) {
    // Clean up extra spaces that might result from emoji removal
    sanitized = sanitized.replace(/\s+/g, ' ').trim();
  }

  return sanitized;
}

/**
 * Validates if text is safe for SMS sending
 */
export function validateSmsText(text: string): {
  isValid: boolean;
  issues: string[];
  sanitizedText?: string;
} {
  const issues: string[] = [];
  
  // Check for problematic Unicode
  if (containsProblematicUnicode(text)) {
    issues.push('Contains Unicode characters that may cause SMS delivery issues');
  }
  
  // Check length (basic check - actual limits depend on encoding)
  if (text.length > 459) {
    issues.push('Message too long for SMS (max 459 characters for non-OTP)');
  }
  
  // Check for control characters
  if (/[\x00-\x1F\x7F-\x9F]/.test(text)) {
    issues.push('Contains control characters that may cause issues');
  }
  
  const isValid = issues.length === 0;
  const sanitizedText = isValid ? text : sanitizeSmsText(text);
  
  return {
    isValid,
    issues,
    sanitizedText,
  };
}

/**
 * Prepares SMS text for sending by automatically sanitizing if needed
 */
export function prepareSmsText(text: string, options: {
  autoSanitize?: boolean;
  throwOnInvalid?: boolean;
} = {}): string {
  const { autoSanitize = true, throwOnInvalid = false } = options;
  
  const validation = validateSmsText(text);
  
  if (!validation.isValid) {
    if (throwOnInvalid) {
      throw new Error(`Invalid SMS text: ${validation.issues.join(', ')}`);
    }
    
    if (autoSanitize && validation.sanitizedText) {
      console.warn('SMS text contains problematic characters, auto-sanitizing:', {
        original: text.substring(0, 50) + (text.length > 50 ? '...' : ''),
        sanitized: validation.sanitizedText.substring(0, 50) + (validation.sanitizedText.length > 50 ? '...' : ''),
        issues: validation.issues,
      });
      return validation.sanitizedText;
    }
  }
  
  return text;
}

/**
 * Creates SMS-safe versions of common message templates
 */
export const SmsSafeTemplates = {
  /**
   * Welcome message template
   */
  welcome: (userName: string, companyName: string = 'Ciribey Gas Delivery') => 
    `Welcome to ${companyName}, ${userName}! Your account is now active. Contact us at +************ for support.`,
  
  /**
   * OTP message template
   */
  otp: (code: string, expiryMinutes: number = 5) =>
    `Your verification code is: ${code}. This code expires in ${expiryMinutes} minutes. Do not share this code.`,
  
  /**
   * Order confirmation template
   */
  orderConfirmation: (orderId: string, amount: number) =>
    `Order ${orderId} confirmed! Amount: $${amount}. We'll notify you when your order is ready for delivery.`,
  
  /**
   * Delivery notification template
   */
  deliveryNotification: (orderId: string, estimatedTime: string) =>
    `Your order ${orderId} is out for delivery! Estimated arrival: ${estimatedTime}. Have your QR code ready.`,
  
  /**
   * Order delivered template
   */
  orderDelivered: (orderId: string) =>
    `Order ${orderId} has been delivered successfully! Thank you for choosing Ciribey Gas Delivery.`,
  
  /**
   * Low stock alert template
   */
  lowStockAlert: (itemName: string, currentStock: number) =>
    `LOW STOCK ALERT: ${itemName} - Only ${currentStock} units remaining. Please restock soon.`,
  
  /**
   * Generic notification template
   */
  notification: (title: string, message: string) =>
    `${title}: ${message}. For more info, contact +************.`,
};

/**
 * Test function to validate SMS utilities
 */
export function testSmsUtils(): void {
  console.log('Testing SMS Utilities...');
  
  const testCases = [
    '🔥 Ciribey Gas Delivery - Test',
    'Normal text message',
    'Message with "smart quotes" and –dashes–',
    'Emoji test: 📱✅❌🚀',
    'Mixed: Hello 🔥 World! ✅ Done.',
  ];
  
  testCases.forEach((testCase, index) => {
    console.log(`\nTest ${index + 1}: "${testCase}"`);
    const validation = validateSmsText(testCase);
    console.log(`Valid: ${validation.isValid}`);
    if (!validation.isValid) {
      console.log(`Issues: ${validation.issues.join(', ')}`);
      console.log(`Sanitized: "${validation.sanitizedText}"`);
    }
  });
}
