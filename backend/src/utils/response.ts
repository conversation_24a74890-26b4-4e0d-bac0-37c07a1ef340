import { Response } from 'express';

interface ResponseOptions {
  data?: any;
  meta?: any;
  errors?: any;
  stack?: string;
}

export const sendResponse = (
  res: Response,
  statusCode: number,
  status: 'success' | 'fail' | 'error',
  message: string,
  options: ResponseOptions = {}
) => {
  const response: {
    status: string;
    message: string;
    data?: any;
    meta?: any;
    errors?: any;
    stack?: string;
  } = {
    status,
    message,
  };

  if (options.data !== undefined) response.data = options.data;
  if (options.meta !== undefined) response.meta = options.meta;
  if (options.errors !== undefined) response.errors = options.errors;
  if (options.stack !== undefined) response.stack = options.stack;

  return res.status(statusCode).json(response);
};