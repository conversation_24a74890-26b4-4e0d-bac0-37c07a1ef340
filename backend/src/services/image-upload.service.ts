import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import { Request } from 'express';
import { ImagePathUtils, ImageCategoryHelper } from '../utils/image_utils';
import { EntityType } from '../enums/enums';
import { BadRequestError, InternalServerError } from '../errors/app_errors';

/**
 * File upload configuration and validation
 */
export class ImageUploadService {
  private static readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  private static readonly ALLOWED_MIME_TYPES = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp',
    'image/gif',
  ];
  private static readonly ALLOWED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];

  /**
   * Configure multer storage for organized file uploads
   */
  private static createStorage() {
    return multer.diskStorage({
      destination: async (req: Request, file: Express.Multer.File, cb) => {
        try {
          // Determine category based on the route/entity type
          let category: string;

          // Check if this is a spare part request (has category field)
          if (req.body.category) {
            // For spare parts, we use the 'spare-parts' directory regardless of the specific category
            category = 'spare-parts'; // Use directory name for spare parts
          }
          // Check if this is a cylinder request (has type field)
          else if (req.body.type) {
            category = 'cylinders'; // Use directory name for cylinders
          }
          // Check if this is a package request (could have name or other identifier)
          else if (req.originalUrl.includes('/packages')) {
            category = 'packages'; // Use directory name for packages
          } else {
            return cb(new BadRequestError('Unable to determine entity type for file upload'), '');
          }

          // Generate directory path
          const now = new Date();
          const year = now.getFullYear();
          const month = String(now.getMonth() + 1).padStart(2, '0');
          const dirPath = path.join('uploads', 'images', category, String(year), month);

          // Ensure directory exists
          await fs.mkdir(dirPath, { recursive: true });
          cb(null, dirPath);
        } catch (error) {
          cb(new InternalServerError('Failed to create upload directory'), '');
        }
      },

      filename: (req: Request, file: Express.Multer.File, cb) => {
        try {
          // Generate unique filename with timestamp and random string
          const timestamp = Date.now();
          const randomString = Math.random().toString(36).substring(2);
          const extension = path.extname(file.originalname).toLowerCase();
          const filename = `${timestamp}-${randomString}${extension}`;
          cb(null, filename);
        } catch (error) {
          cb(new InternalServerError('Failed to generate filename'), '');
        }
      },
    });
  }

  /**
   * File filter for validation
   */
  private static fileFilter(
    req: Request,
    file: Express.Multer.File,
    cb: multer.FileFilterCallback
  ) {
    // Check MIME type
    if (!ImageUploadService.ALLOWED_MIME_TYPES.includes(file.mimetype)) {
      return cb(
        new BadRequestError(
          `Invalid file type. Allowed types: ${ImageUploadService.ALLOWED_MIME_TYPES.join(', ')}`
        )
      );
    }

    // Check file extension
    const extension = path.extname(file.originalname).toLowerCase();
    if (!ImageUploadService.ALLOWED_EXTENSIONS.includes(extension)) {
      return cb(
        new BadRequestError(
          `Invalid file extension. Allowed extensions: ${ImageUploadService.ALLOWED_EXTENSIONS.join(', ')}`
        )
      );
    }

    cb(null, true);
  }

  /**
   * Create multer upload middleware
   */
  static createUploadMiddleware() {
    return multer({
      storage: ImageUploadService.createStorage(),
      fileFilter: ImageUploadService.fileFilter,
      limits: {
        fileSize: ImageUploadService.MAX_FILE_SIZE,
        files: 1, // Single file upload
      },
    });
  }

  /**
   * Process uploaded file and return image path
   */
  static async processUploadedFile(file: Express.Multer.File, category: string): Promise<string> {
    if (!file) {
      throw new BadRequestError('No file uploaded');
    }

    // Validate category
    if (!ImageCategoryHelper.isValidCategory(category)) {
      throw new BadRequestError('Invalid image category');
    }

    // Return the full path (multer already saved the file)
    return file.path;
  }

  /**
   * Delete image file from filesystem
   */
  static async deleteImageFile(imagePath: string): Promise<void> {
    try {
      if (!imagePath) return;

      // Check if file exists before attempting to delete
      await fs.access(imagePath);
      await fs.unlink(imagePath);
    } catch (error) {
      // Log error but don't throw - file might already be deleted
      console.warn(`Failed to delete image file: ${imagePath}`, error);
    }
  }

  /**
   * Validate image file before processing
   */
  static validateImageFile(file: Express.Multer.File): void {
    if (!file) {
      throw new BadRequestError('No file provided');
    }

    if (file.size > ImageUploadService.MAX_FILE_SIZE) {
      throw new BadRequestError(
        `File size exceeds limit of ${ImageUploadService.MAX_FILE_SIZE / (1024 * 1024)}MB`
      );
    }

    if (!ImageUploadService.ALLOWED_MIME_TYPES.includes(file.mimetype)) {
      throw new BadRequestError(`Invalid file type: ${file.mimetype}`);
    }
  }

  /**
   * Get image URL from file path
   */
  static getImageUrl(imagePath: string): string {
    return ImagePathUtils.getImageUrl(imagePath);
  }

  /**
   * Check if file exists
   */
  static async fileExists(imagePath: string): Promise<boolean> {
    try {
      await fs.access(imagePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get file info
   */
  static async getFileInfo(imagePath: string): Promise<{
    size: number;
    mimeType: string;
    exists: boolean;
  }> {
    try {
      const stats = await fs.stat(imagePath);
      const extension = path.extname(imagePath).toLowerCase();

      // Map extension to MIME type
      const mimeTypeMap: Record<string, string> = {
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.webp': 'image/webp',
        '.gif': 'image/gif',
      };

      return {
        size: stats.size,
        mimeType: mimeTypeMap[extension] || 'application/octet-stream',
        exists: true,
      };
    } catch {
      return {
        size: 0,
        mimeType: '',
        exists: false,
      };
    }
  }
}

/**
 * Express middleware for handling single image upload
 */
export const uploadSingleImage = ImageUploadService.createUploadMiddleware().single('image');

/**
 * Express middleware for handling multiple image uploads (max 5)
 */
export const uploadMultipleImages = ImageUploadService.createUploadMiddleware().array('images', 5);
