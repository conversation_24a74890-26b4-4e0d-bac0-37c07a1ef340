import { <PERSON><PERSON><PERSON> } from '../models/cylinder.model';
import { SparePart } from '../models/spareParts.model';
import { Package } from '../models/package.model';
import { Notifications } from '../utils/notification_helper';
import { CylinderStatus, SparePartStatus } from '../enums/enums';
import logger from '../config/logger';

/**
 * Inventory Monitoring Service
 * Provides comprehensive inventory monitoring and alerting capabilities
 */
class InventoryMonitorService {
  private static instance: InventoryMonitorService;

  private constructor() {}

  public static getInstance(): InventoryMonitorService {
    if (!InventoryMonitorService.instance) {
      InventoryMonitorService.instance = new InventoryMonitorService();
    }
    return InventoryMonitorService.instance;
  }

  /**
   * Check all inventory items and send alerts for low/out of stock items
   */
  async checkAllInventoryLevels(): Promise<{
    cylinders: { checked: number; alerts: number };
    spareParts: { checked: number; alerts: number };
    packages: { checked: number; alerts: number };
    totalAlerts: number;
  }> {
    logger.info('Starting comprehensive inventory level check');

    const results = {
      cylinders: { checked: 0, alerts: 0 },
      spareParts: { checked: 0, alerts: 0 },
      packages: { checked: 0, alerts: 0 },
      totalAlerts: 0,
    };

    try {
      // Check cylinders
      const cylinderResults = await this.checkCylinderLevels();
      results.cylinders = cylinderResults;

      // Check spare parts
      const sparePartResults = await this.checkSparePartLevels();
      results.spareParts = sparePartResults;

      // Check packages
      const packageResults = await this.checkPackageLevels();
      results.packages = packageResults;

      results.totalAlerts =
        cylinderResults.alerts + sparePartResults.alerts + packageResults.alerts;

      logger.info('Inventory level check completed', results);
      return results;
    } catch (error) {
      logger.error('Failed to check inventory levels', { error: error.message });
      throw error;
    }
  }

  /**
   * Check cylinder inventory levels
   */
  private async checkCylinderLevels(): Promise<{ checked: number; alerts: number }> {
    try {
      const cylinders = await Cylinder.find({
        status: { $in: [CylinderStatus.Active, CylinderStatus.OutOfStock] },
      }).select('type availableQuantity minimumStockLevel');

      let alertsSent = 0;

      for (const cylinder of cylinders) {
        try {
          const itemName = `${cylinder.type} Gas Cylinder`;
          const alertResult = await Notifications.Inventory.checkStockAndAlert(
            itemName,
            cylinder.availableQuantity,
            cylinder.minimumStockLevel || 5,
            'so'
          );

          if (alertResult) {
            alertsSent++;
          }
        } catch (alertError) {
          logger.error('Failed to send cylinder stock alert', {
            cylinderId: cylinder._id,
            type: cylinder.type,
            error: alertError.message,
          });
        }
      }

      return { checked: cylinders.length, alerts: alertsSent };
    } catch (error) {
      logger.error('Failed to check cylinder levels', { error: error.message });
      throw error;
    }
  }

  /**
   * Check spare part inventory levels
   */
  private async checkSparePartLevels(): Promise<{ checked: number; alerts: number }> {
    try {
      const spareParts = await SparePart.find({
        status: { $in: [SparePartStatus.AVAILABLE, SparePartStatus.OUT_OF_STOCK] },
      }).select('category availableQuantity minimumStockLevel');

      let alertsSent = 0;

      for (const sparePart of spareParts) {
        try {
          const itemName = `${sparePart.category} Spare Part`;
          const alertResult = await Notifications.Inventory.checkStockAndAlert(
            itemName,
            sparePart.availableQuantity,
            sparePart.minimumStockLevel || 5,
            'so'
          );

          if (alertResult) {
            alertsSent++;
          }
        } catch (alertError) {
          logger.error('Failed to send spare part stock alert', {
            sparePartId: sparePart._id,
            category: sparePart.category,
            error: alertError.message,
          });
        }
      }

      return { checked: spareParts.length, alerts: alertsSent };
    } catch (error) {
      logger.error('Failed to check spare part levels', { error: error.message });
      throw error;
    }
  }

  /**
   * Check package inventory levels
   */
  private async checkPackageLevels(): Promise<{ checked: number; alerts: number }> {
    try {
      const packages = await Package.find({
        isActive: true,
      }).select('name availableQuantity minimumStockLevel');

      let alertsSent = 0;

      for (const packageItem of packages) {
        try {
          const itemName = `${packageItem.name} Package`;
          const alertResult = await Notifications.Inventory.checkStockAndAlert(
            itemName,
            packageItem.availableQuantity,
            packageItem.minimumStockLevel || 5,
            'so'
          );

          if (alertResult) {
            alertsSent++;
          }
        } catch (alertError) {
          logger.error('Failed to send package stock alert', {
            packageId: packageItem._id,
            name: packageItem.name,
            error: alertError.message,
          });
        }
      }

      return { checked: packages.length, alerts: alertsSent };
    } catch (error) {
      logger.error('Failed to check package levels', { error: error.message });
      throw error;
    }
  }

  /**
   * Get low stock items across all inventory types
   */
  async getLowStockItems(): Promise<{
    cylinders: Array<{ id: string; name: string; available: number; minimum: number }>;
    spareParts: Array<{ id: string; name: string; available: number; minimum: number }>;
    packages: Array<{ id: string; name: string; available: number; minimum: number }>;
  }> {
    try {
      // Get low stock cylinders
      const lowStockCylinders = await Cylinder.find({
        status: CylinderStatus.Active,
        $expr: { $lte: ['$availableQuantity', '$minimumStockLevel'] },
      }).select('type availableQuantity minimumStockLevel');

      // Get low stock spare parts
      const lowStockSpareParts = await SparePart.find({
        status: SparePartStatus.AVAILABLE,
        $expr: { $lte: ['$availableQuantity', '$minimumStockLevel'] },
      }).select('category availableQuantity minimumStockLevel');

      // Get low stock packages
      const lowStockPackages = await Package.find({
        isActive: true,
        $expr: { $lte: ['$availableQuantity', '$minimumStockLevel'] },
      }).select('name availableQuantity minimumStockLevel');

      return {
        cylinders: lowStockCylinders.map(c => ({
          id: c._id.toString(),
          name: `${c.type} Gas Cylinder`,
          available: c.availableQuantity,
          minimum: c.minimumStockLevel || 5,
        })),
        spareParts: lowStockSpareParts.map(sp => ({
          id: sp._id.toString(),
          name: `${sp.category} Spare Part`,
          available: sp.availableQuantity,
          minimum: sp.minimumStockLevel || 5,
        })),
        packages: lowStockPackages.map(p => ({
          id: p._id.toString(),
          name: `${p.name} Package`,
          available: p.availableQuantity,
          minimum: p.minimumStockLevel || 5,
        })),
      };
    } catch (error) {
      logger.error('Failed to get low stock items', { error: error.message });
      throw error;
    }
  }

  /**
   * Get out of stock items across all inventory types
   */
  async getOutOfStockItems(): Promise<{
    cylinders: Array<{ id: string; name: string }>;
    spareParts: Array<{ id: string; name: string }>;
    packages: Array<{ id: string; name: string }>;
  }> {
    try {
      // Get out of stock cylinders
      const outOfStockCylinders = await Cylinder.find({
        availableQuantity: 0,
      }).select('type');

      // Get out of stock spare parts
      const outOfStockSpareParts = await SparePart.find({
        availableQuantity: 0,
      }).select('category');

      // Get out of stock packages
      const outOfStockPackages = await Package.find({
        availableQuantity: 0,
      }).select('name');

      return {
        cylinders: outOfStockCylinders.map(c => ({
          id: c._id.toString(),
          name: `${c.type} Gas Cylinder`,
        })),
        spareParts: outOfStockSpareParts.map(sp => ({
          id: sp._id.toString(),
          name: `${sp.category} Spare Part`,
        })),
        packages: outOfStockPackages.map(p => ({
          id: p._id.toString(),
          name: `${p.name} Package`,
        })),
      };
    } catch (error) {
      logger.error('Failed to get out of stock items', { error: error.message });
      throw error;
    }
  }

  /**
   * Send daily inventory summary to admins
   */
  async sendDailyInventorySummary(): Promise<void> {
    try {
      const lowStockItems = await this.getLowStockItems();
      const outOfStockItems = await this.getOutOfStockItems();

      const totalLowStock =
        lowStockItems.cylinders.length +
        lowStockItems.spareParts.length +
        lowStockItems.packages.length;

      const totalOutOfStock =
        outOfStockItems.cylinders.length +
        outOfStockItems.spareParts.length +
        outOfStockItems.packages.length;

      if (totalLowStock > 0 || totalOutOfStock > 0) {
        // Send summary notification
        const summaryMessage = `Inventory Summary: ${totalOutOfStock} items out of stock, ${totalLowStock} items low stock. Please check admin dashboard for details.`;

        await Notifications.Inventory.sendLowStockAlert(
          'Daily Inventory Summary',
          totalLowStock + totalOutOfStock,
          'so'
        );

        logger.info('Daily inventory summary sent', {
          totalLowStock,
          totalOutOfStock,
        });
      } else {
        logger.info('Daily inventory summary: All items adequately stocked');
      }
    } catch (error) {
      logger.error('Failed to send daily inventory summary', { error: error.message });
      throw error;
    }
  }
}

export const inventoryMonitor = InventoryMonitorService.getInstance();
