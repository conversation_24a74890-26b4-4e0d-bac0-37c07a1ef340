import nodemailer, { Transporter, SendMailOptions, createTransport } from 'nodemailer';
import { config } from '../config/env_config';
import { InternalServerError, BadRequestError } from '../errors/app_errors';
import logger from '../config/logger';

/**
 * Email delivery result interface
 */
interface EmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
  details?: any;
}

/**
 * Email template interface
 */
interface EmailTemplate {
  subject: string;
  body: string;
}

/**
 * Email service configuration
 */
interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
  from: string;
}

/**
 * Professional Email Service for Gas Delivery System
 * Supports HTML templates, attachments, and delivery tracking
 */
class EmailService {
  private static instance: EmailService;
  private transporter: Transporter | null = null;
  private metrics = {
    sentCount: 0,
    failedCount: 0,
    lastError: null as Error | null,
  };

  private constructor() {
    this.initializeTransporter();
  }

  public static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService();
    }
    return EmailService.instance;
  }

  /**
   * Initialize email transporter based on configuration
   */
  private initializeTransporter(): void {
    try {
      // Use Gmail SMTP configuration for both development and production
      const emailConfig: EmailConfig = {
        host: config.email?.host || 'smtp.gmail.com',
        port: config.email?.port || 587,
        secure: config.email?.secure || false,
        auth: {
          user: config.email?.user || process.env.EMAIL_USER || '',
          pass: config.email?.pass || process.env.EMAIL_PASS || '',
        },
        from: config.email?.from || process.env.EMAIL_FROM || '<EMAIL>',
      };

      logger.info('Initializing email service with configuration', {
        host: emailConfig.host,
        port: emailConfig.port,
        secure: emailConfig.secure,
        user: emailConfig.auth.user,
        from: emailConfig.from,
        environment: config.server.env,
      });

      this.transporter = createTransport(emailConfig);

      // Verify connection
      this.verifyConnection();
    } catch (error) {
      logger.error('Failed to initialize email transporter', { error: error.message });
      throw new InternalServerError('Email service initialization failed');
    }
  }

  /**
   * Verify email service connection
   */
  private async verifyConnection(): Promise<void> {
    try {
      if (this.transporter) {
        await this.transporter.verify();
        logger.info('Email service connection verified successfully');
      }
    } catch (error) {
      logger.error('Email service connection verification failed', { error: error.message });
      // Don't throw error here - allow service to continue with degraded functionality
    }
  }

  /**
   * Send a single email
   */
  public async sendEmail(
    to: string | string[],
    template: EmailTemplate,
    options?: {
      cc?: string | string[];
      bcc?: string | string[];
      attachments?: any[];
      priority?: 'high' | 'normal' | 'low';
      replyTo?: string;
    }
  ): Promise<EmailResult> {
    try {
      if (!this.transporter) {
        throw new InternalServerError('Email transporter not initialized');
      }

      // Validate inputs
      if (!to || (Array.isArray(to) && to.length === 0)) {
        throw new BadRequestError('Email recipient is required');
      }

      if (!template.subject || !template.body) {
        throw new BadRequestError('Email subject and body are required');
      }

      const mailOptions: SendMailOptions = {
        from: config.email?.from || 'Ciribey Gas Delivery <<EMAIL>>',
        to: Array.isArray(to) ? to.join(', ') : to,
        subject: template.subject,
        html: template.body,
        priority: options?.priority || 'normal',
        replyTo: options?.replyTo,
        cc: options?.cc,
        bcc: options?.bcc,
        attachments: options?.attachments,
      };

      const result = await this.transporter.sendMail(mailOptions);

      this.metrics.sentCount++;
      logger.info('Email sent successfully', {
        to: Array.isArray(to) ? to.length + ' recipients' : to,
        subject: template.subject,
        messageId: result.messageId,
      });

      return {
        success: true,
        messageId: result.messageId,
        details: result,
      };
    } catch (error) {
      this.metrics.failedCount++;
      this.metrics.lastError = error instanceof Error ? error : new Error(String(error));

      logger.error('Failed to send email', {
        to: Array.isArray(to) ? to.length + ' recipients' : to,
        subject: template.subject,
        error: error.message,
      });

      return {
        success: false,
        error: error.message,
        details: error,
      };
    }
  }

  /**
   * Send emails to multiple recipients (bulk email)
   */
  public async sendBulkEmail(
    recipients: string[],
    template: EmailTemplate,
    options?: {
      batchSize?: number;
      delayBetweenBatches?: number;
      onProgress?: (sent: number, failed: number, total: number) => void;
    }
  ): Promise<{
    status: 'success' | 'partial_success' | 'failed';
    message: string;
    data: {
      successful: string[];
      failed: { email: string; error: string }[];
      counts: {
        total: number;
        successful: number;
        failed: number;
      };
      sentAt: string;
    };
  }> {
    const batchSize = options?.batchSize || 50;
    const delay = options?.delayBetweenBatches || 1000;
    const results = {
      successful: [] as string[],
      failed: [] as { email: string; error: string }[],
    };

    // Process in batches to avoid overwhelming the SMTP server
    for (let i = 0; i < recipients.length; i += batchSize) {
      const batch = recipients.slice(i, i + batchSize);

      await Promise.all(
        batch.map(async email => {
          try {
            const result = await this.sendEmail(email, template);
            if (result.success) {
              results.successful.push(email);
            } else {
              results.failed.push({ email, error: result.error || 'Unknown error' });
            }
          } catch (error) {
            results.failed.push({
              email,
              error: error instanceof Error ? error.message : String(error),
            });
          }

          // Update progress
          options?.onProgress?.(
            results.successful.length,
            results.failed.length,
            recipients.length
          );
        })
      );

      // Delay between batches (except for the last batch)
      if (i + batchSize < recipients.length) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // Determine status
    let status: 'success' | 'partial_success' | 'failed' = 'success';
    let message = 'All emails sent successfully';

    if (results.failed.length > 0) {
      status = results.successful.length > 0 ? 'partial_success' : 'failed';
      message =
        results.successful.length > 0
          ? 'Some emails sent successfully'
          : 'All emails failed to send';
    }

    return {
      status,
      message,
      data: {
        successful: results.successful,
        failed: results.failed,
        counts: {
          total: recipients.length,
          successful: results.successful.length,
          failed: results.failed.length,
        },
        sentAt: new Date().toISOString(),
      },
    };
  }

  /**
   * Get email service metrics
   */
  public getMetrics() {
    return {
      ...this.metrics,
      successRate:
        this.metrics.sentCount > 0
          ? (this.metrics.sentCount - this.metrics.failedCount) / this.metrics.sentCount
          : 1,
    };
  }

  /**
   * Check email service health
   */
  public async checkHealth(): Promise<boolean> {
    try {
      if (!this.transporter) {
        return false;
      }
      await this.transporter.verify();
      return true;
    } catch (error) {
      logger.error('Email service health check failed', { error: error.message });
      return false;
    }
  }

  /**
   * Create a test email account (for development)
   */
  public static async createTestAccount(): Promise<any> {
    try {
      const testAccount = await nodemailer.createTestAccount();
      logger.info('Test email account created', {
        user: testAccount.user,
        pass: testAccount.pass,
        smtp: testAccount.smtp,
        imap: testAccount.imap,
        pop3: testAccount.pop3,
        web: testAccount.web,
      });
      return testAccount;
    } catch (error) {
      logger.error('Failed to create test email account', { error: error.message });
      throw error;
    }
  }
}

export const emailService = EmailService.getInstance();
