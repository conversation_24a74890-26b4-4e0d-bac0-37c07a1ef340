import mongoose, { Types } from 'mongoose';
import { User, TempOtp } from '../models';
import { IUser, TokenPayload } from '../types/interfaces';
import { UserRole, VehicleType } from '../enums/enums';
import { hashPassword } from '../utils/hash_utils';
import { generateToken } from '../utils/jwt_utils';
import {
  NotFoundError,
  ValidationError,
  UnauthorizedError,
  DuplicateResourceError,
  BadRequestError,
  InternalServerError,
} from '../errors/app_errors';
import {
  subscribeToTopic,
  unsubscribeFromTopic,
  NotificationTopic,
} from '../utils/notification_utils';
import { smsService } from './sms.services';
import logger from '../config/logger';
import {
  generateOtp,
  shouldSkipOtp,
  validateOtp,
  isOtpValid,
  getOtpRemainingTime,
} from '../utils/otp_utils';
import { Notifications } from '../utils/notification_helper';
import { AppMessageService } from '../constants/app_message.service';
import { timezoneManager, TimezoneContext } from '../utils/timezone_utils';

class UserServices {
  /**
   * Send OTP using unified notification system
   * @param phone - User's phone number
   * @param otp - OTP code to send
   * @param userId - Optional user ID for better tracking
   */
  private async sendOtp(phone: string, otp: string, userId?: string): Promise<void> {
    // Skip sending OTP if verification is disabled
    if (shouldSkipOtp()) {
      logger.info('OTP verification disabled, skipping OTP send', { phone, userId });
      return;
    }

    try {
      if (userId) {
        // Use the unified notification dispatcher for better delivery
        await Notifications.User.sendOtpNotification(userId, phone, otp, 5, 'so');
        logger.info('OTP sent via unified notification system', { phone, userId });
      } else {
        // Fallback to direct SMS for cases where userId is not available
        const messageService = new AppMessageService({ lang: 'so', includePhoneInFooter: false, });
        const otpMessage = messageService.otpMessage(otp, 5);
        await smsService.sendSms(phone, otpMessage, { isOtp: true });
        logger.info('OTP sent via direct SMS', { phone });
      }
    } catch (error) {
      logger.error('Failed to send OTP notification', {
        error: error.message,
        phone,
        userId,
        timestamp: new Date().toISOString(),
      });
      // Don't throw error to prevent blocking user registration/login
      // The user can still proceed if OTP verification is disabled
    }
  }

  async login(phone: string) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      logger.info(`Login attempt for phone: ${phone}`);

      // Generate OTP using centralized utility (handles skip logic internally)
      const { code: otp, expirationTime } = generateOtp();

      logger.info(`Generated OTP: ${otp}, Expires at: ${expirationTime}`);

      // Check if user exists first
      const user = await User.findOne({ phone }).session(session);
      logger.info(`Existing user found: ${user ? 'Yes' : 'No'}`);

      if (!user) {
        // Clean up any existing TempOtp for this phone
        await TempOtp.deleteMany({ phone }).session(session);

        // Store OTP in temporary collection
        const newTempUser = await TempOtp.create(
          [
            {
              phone,
              code: otp,
              expirationTime,
              createdAt: new Date(),
            },
          ],
          { session }
        );

        logger.info(`TempOtp created with ID: ${newTempUser[0]._id}`);

        // Attempt to send SMS
        await this.sendOtp(phone, otp);

        await session.commitTransaction();
        logger.info('Transaction committed for new user');

        return {
          isNewUser: true,
          _id: newTempUser[0]._id,
          phone: newTempUser[0].phone,
        };
      }

      if (!user.isActive) {
        throw new UnauthorizedError('Your account has been deactivated');
      }

      logger.info(`Updating existing user OTP...`);
      await User.updateOne(
        { phone },
        { $set: { otp: { code: otp, expirationTime } } },
        { session }
      );

      // Send OTP notification (handles skip logic internally via generateOtp)
      await this.sendOtp(phone, otp, user._id.toString());

      await session.commitTransaction();
      logger.info('Transaction committed for existing user');

      return {
        isNewUser: false,
        _id: user._id,
        phone: user.phone,
        email: user.email,
        role: user.role,
        addresses: user.addresses,
        isActive: user.isActive,
        agentMetadata: user.agentMetadata,
      };
    } catch (error) {
      try {
        if (session.inTransaction()) {
          await session.abortTransaction();
          logger.info('Login transaction aborted successfully', {
            phone,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (abortError) {
        logger.error('Critical: Failed to abort login transaction', {
          error: abortError.message,
          originalError: error.message,
          phone,
          timestamp: new Date().toISOString(),
        });
      }

      logger.error('Login transaction aborted', {
        phone,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });

      throw error;
    } finally {
      session.endSession();
    }
  }

  async verifyOtp(phone: string, otp: string) {
    logger.info(`Verifying OTP for phone: ${phone}`);
    const now = new Date();

    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // STEP 1: Check for new user in TempOtp
      const tempOtp = await TempOtp.findOne({
        phone,
        code: otp,
        expirationTime: { $gt: now },
      }).session(session);

      if (tempOtp) {
        logger.info(`Valid TempOtp found. Creating new user.`);

        const newUser = await User.create(
          [
            {
              phone,
              role: UserRole.CUSTOMER,
              isActive: true,
              addresses: [],
            },
          ],
          { session }
        );

        await TempOtp.deleteOne({ _id: tempOtp._id }).session(session);

        await session.commitTransaction();

        const token = generateToken(newUser[0]._id.toString(), newUser[0].role);
        logger.info(`New user created: ${newUser[0]._id}`);

        // Send welcome message to new user
        try {
          await Notifications.User.sendWelcomeMessage(
            newUser[0]._id.toString(),
            newUser[0].phone,
            newUser[0].username || newUser[0].phone,
            newUser[0].role,
            'so'
          );
        } catch (notificationError) {
          logger.error('Failed to send welcome message', {
            error: notificationError.message,
            userId: newUser[0]._id.toString(),
            timestamp: new Date().toISOString(),
          });
        }

        return { token, user: newUser[0] };
      }

      // STEP 2: Check existing user
      const user = await User.findOne({ phone }).session(session);

      if (!user) {
        const expiredTempOtp = await TempOtp.findOne({ phone }).session(session);
        if (expiredTempOtp) {
          logger.warn(`OTP expired for unregistered user`);
          throw new UnauthorizedError('OTP has expired. Please request a new one.');
        }

        logger.warn(`No user found with phone: ${phone}`);
        throw new NotFoundError('User not found. Please register first.');
      }

      // STEP 3: Validate OTP on existing user using utility function
      const validation = validateOtp(user.otp, otp);
      if (!validation.isValid) {
        logger.warn(`OTP validation failed for user: ${user._id}`, { error: validation.error });
        throw new UnauthorizedError(validation.error || 'OTP validation failed');
      }

      await User.updateOne({ phone }, { $set: { otp: null } }).session(session);

      await session.commitTransaction();

      const token = generateToken(user._id.toString(), user.role);
      logger.info(`User verified successfully: ${user._id}`);
      return { token, user };
    } catch (error) {
      try {
        if (session.inTransaction()) {
          await session.abortTransaction();
          logger.info('Transaction aborted due to OTP verification failure', {
            phone,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (abortError) {
        logger.error('Failed to abort transaction after OTP verification failure', {
          error: abortError.message,
          originalError: error.message,
          phone,
          timestamp: new Date().toISOString(),
        });
      }
      logger.error('OTP verification failed', {
        phone,
        otp,
        error: error.message,
        timestamp: new Date().toISOString(),
      });

      throw error;
    } finally {
      if (!session.hasEnded) {
        await session.endSession();
      }
    }
  }

  async resendOtp(phone: string) {
    logger.info(`Resend OTP requested for phone: ${phone}`);

    const user = await User.findOne({ phone }).select('otp phone');
    if (!user) {
      logger.warn(`No user found with phone: ${phone}`);
      throw new NotFoundError('User not found with this phone number');
    }

    // Check if existing OTP is still valid using utility function
    if (isOtpValid(user.otp)) {
      const secondsLeft = getOtpRemainingTime(user.otp);
      logger.warn(`OTP already sent for ${phone}, expires in ${secondsLeft}s`);
      throw new BadRequestError(
        `OTP already sent. Please wait ${secondsLeft} seconds before requesting a new one.`
      );
    }

    // Generate OTP using centralized utility (handles skip logic internally)
    const { code: otp, expirationTime } = generateOtp();

    // Send OTP notification
    await this.sendOtp(phone, otp, user._id.toString());

    // Update user's OTP in one atomic operation
    await User.updateOne(
      { phone },
      { $set: { 'otp.code': otp, 'otp.expirationTime': expirationTime } }
    );

    // In production, don't return the OTP — only for testing or debugging
    return { message: 'OTP sent successfully' };
  }

  /**
   * Register a new admin or agent user (admin only)
   */
  async registerAdminOrAgent(
    userData: {
      phone: string;
      email?: string;
      password: string;
      role: UserRole.ADMIN | UserRole.AGENT;
      agentMetadata?: {
        vehicle: {
          type: VehicleType;
          number: string;
        };
      };
    },
    currentUser: TokenPayload
  ) {
    // Verify current user is admin
    if (currentUser.role !== UserRole.ADMIN) {
      throw new UnauthorizedError('Only admins can register new admins or agents');
    }

    // Check if user already exists
    const existingUser = await User.findOne({ phone: userData.phone });
    if (existingUser) {
      throw new DuplicateResourceError('User with this phone number already exists');
    }

    // Hash password
    const passwordHash = await hashPassword(userData.password);

    // Prepare user data
    const newUserData: Partial<IUser> = {
      phone: userData.phone,
      email: userData.email,
      passwordHash,
      role: userData.role,
      isActive: true,
    };

    // Add agent metadata if role is agent
    if (userData.role === UserRole.AGENT && userData.agentMetadata) {
      newUserData.agentMetadata = {
        vehicle: {
          type: userData.agentMetadata.vehicle.type,
          number: userData.agentMetadata.vehicle.number,
        },
        isOnDuty: false,
        lastKnownLocation: {
          type: 'Point',
          coordinates: [0, 0], // Default coordinates
        },
        rating: 0,
      } as any;
    }

    // Create new user
    const newUser = await User.create(newUserData);

    return {
      _id: newUser._id,
      phone: newUser.phone,
      email: newUser.email,
      role: newUser.role,
      isActive: newUser.isActive,
      agentMetadata: newUser.agentMetadata,
    };
  }

  /**
   * Register a new customer user (admin/supervisor only)
   */
  async registerCustomer(
    userData: {
      phone: string;
      email?: string;
      username?: string;
      addresses?: Array<{
        street: string;
        city: string;
        state: string;
        zipCode: string;
        country: string;
        isDefault?: boolean;
      }>;
    },
    currentUser: TokenPayload
  ) {
    // Verify current user is admin or supervisor
    if (![UserRole.ADMIN, UserRole.SUPERVISOR].includes(currentUser.role)) {
      throw new UnauthorizedError('Only admins and supervisors can register new customers');
    }

    // Check if user already exists
    const existingUser = await User.findOne({ phone: userData.phone });
    if (existingUser) {
      throw new DuplicateResourceError('User with this phone number already exists');
    }

    // Prepare user data
    const newUserData: Partial<IUser> = {
      phone: userData.phone,
      email: userData.email,
      username: userData.username,
      role: UserRole.CUSTOMER,
      isActive: true,
      // addresses: userData.addresses || [],
    };

    // Create new user
    const newUser = await User.create(newUserData);

    // Send welcome message to new customer
    try {
      await Notifications.User.sendWelcomeMessage(
        newUser._id.toString(),
        newUser.phone,
        newUser.username || newUser.phone,
        newUser.role,
        'so'
      );
    } catch (notificationError) {
      logger.error('Failed to send welcome message to new customer', {
        error: notificationError.message,
        userId: newUser._id.toString(),
        timestamp: new Date().toISOString(),
      });
    }

    return {
      _id: newUser._id,
      phone: newUser.phone,
      email: newUser.email,
      username: newUser.username,
      role: newUser.role,
      isActive: newUser.isActive,
      addresses: newUser.addresses,
    };
  }

  /**
   * Get single user data
   */
  async getSingleUser(userId: string | Types.ObjectId, currentUser: TokenPayload) {
    // Convert string ID to ObjectId if needed
    const userObjectId = typeof userId === 'string' ? new Types.ObjectId(userId) : userId;

    // Check permissions - users can only access their own data unless they're admins
    if (currentUser.role !== UserRole.ADMIN && currentUser.userId !== userId.toString()) {
      throw new UnauthorizedError('You can only access your own user data');
    }

    // Find user
    const user = await User.findById(userObjectId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    return {
      _id: user._id,
      phone: user.phone,
      email: user.email,
      role: user.role,
      addresses: user.addresses,
      isActive: user.isActive,
      agentMetadata: user.agentMetadata,
    };
  }

  /**
   * 🔍 Get all users with advanced search and filtering capabilities
   */
  async getAllUsers(
    currentUser: TokenPayload,
    options: {
      role?: UserRole;
      isActive?: boolean;
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      search?: string;
      searchFields?: string[];
    } = {}
  ) {
    // Verify current user is admin or supervisor
    // if (![UserRole.ADMIN, UserRole.SUPERVISOR].includes(currentUser.role)) {
    //   throw new UnauthorizedError('Only admins and supervisors can access users data');
    // }

    // Build base query
    const query: any = {};
    if (options.role) query.role = options.role;
    if (options.isActive !== undefined) query.isActive = options.isActive;

    // 🚀 Advanced search implementation
    if (options.search && options.search.trim()) {
      const searchTerm = options.search.trim();

      // Escape special regex characters to prevent errors
      const escapeRegex = (str: string) => {
        return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      };

      const escapedSearchTerm = escapeRegex(searchTerm);

      // Use regex search for phone numbers (more reliable for partial matches)
      // MongoDB text search doesn't work well with phone numbers containing special characters
      const searchRegex = new RegExp(escapedSearchTerm, 'i');

      query.$or = [{ phone: searchRegex }, { email: searchRegex }, { username: searchRegex }];

      logger.debug('🔍 Search query built', {
        originalTerm: searchTerm,
        escapedTerm: escapedSearchTerm,
        regexPattern: searchRegex.toString(),
      });
    }

    // Pagination with validation
    const page = Math.max(1, options.page || 1);
    const limit = Math.min(100, Math.max(1, options.limit || 20)); // Max 100 items per page
    const skip = (page - 1) * limit;

    // 📊 Smart sorting
    const sortOptions: any = {};

    // Apply custom sorting
    if (options.sortBy) {
      sortOptions[options.sortBy] = options.sortOrder === 'desc' ? -1 : 1;
    } else {
      sortOptions.createdAt = -1; // Default sort by creation date, newest first
    }

    logger.debug('🔍 User search query', {
      query,
      sortOptions,
      pagination: { page, limit, skip },
      searchTerm: options.search,
      currentUser: currentUser.userId,
    });

    // 🚀 Execute optimized query for better performance
    const [users, totalUsers] = await Promise.all([
      User.find(query).sort(sortOptions).skip(skip).limit(limit).lean(), // Use lean() for better performance
      User.countDocuments(query),
    ]);

    // 📈 Enhanced response with search metadata
    const response = {
      users,
      pagination: {
        total: totalUsers,
        page,
        limit,
        pages: Math.ceil(totalUsers / limit),
        hasNextPage: page < Math.ceil(totalUsers / limit),
        hasPrevPage: page > 1,
      },
      searchMetadata: options.search
        ? {
            query: options.search,
            resultsCount: users.length,
            totalMatches: totalUsers,
            searchTime: Date.now(), // Can be enhanced with actual timing
          }
        : undefined,
    };

    logger.info('🎯 User search completed', {
      searchTerm: options.search,
      resultsCount: users.length,
      totalUsers,
      page,
      limit,
      currentUser: currentUser.userId,
    });

    return response;
  }

  /**
   * 🔍 Advanced user search with fuzzy matching and autocomplete
   */
  async searchUsers(
    currentUser: TokenPayload,
    searchQuery: string,
    options: {
      role?: UserRole;
      isActive?: boolean;
      limit?: number;
      fuzzySearch?: boolean;
      autocomplete?: boolean;
    } = {}
  ) {
    if (!searchQuery || searchQuery.trim().length < 1) {
      throw new ValidationError('Search query must be at least 1 character long');
    }

    const searchTerm = searchQuery.trim();
    const limit = Math.min(50, Math.max(1, options.limit || 10));

    // Build base query
    const baseQuery: any = {};
    if (options.role) baseQuery.role = options.role;
    if (options.isActive !== undefined) baseQuery.isActive = options.isActive;

    let searchResults: any[] = [];

    try {
      // 🎯 Primary search: Regex search for reliable phone number matching
      const escapeRegex = (str: string) => {
        return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      };

      const escapedSearchTerm = escapeRegex(searchTerm);

      const regexSearchQuery = {
        ...baseQuery,
        $or: [
          { phone: { $regex: escapedSearchTerm, $options: 'i' } },
          { email: { $regex: escapedSearchTerm, $options: 'i' } },
          { username: { $regex: escapedSearchTerm, $options: 'i' } },
        ],
      };

      searchResults = await User.find(regexSearchQuery).sort({ createdAt: -1 }).limit(limit).lean();

      // 🔍 Fallback search: Regex search for partial matches
      if (searchResults.length === 0 || options.fuzzySearch) {
        // Escape special regex characters to prevent errors
        const escapeRegex = (str: string) => {
          return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        };

        const escapedSearchTerm = escapeRegex(searchTerm);

        const regexQuery = {
          ...baseQuery,
          $or: [
            { phone: { $regex: escapedSearchTerm, $options: 'i' } },
            { email: { $regex: escapedSearchTerm, $options: 'i' } },
            { username: { $regex: escapedSearchTerm, $options: 'i' } },
          ],
        };

        const regexResults = await User.find(regexQuery)
          .sort({ createdAt: -1 })
          .limit(limit)
          .lean();

        // Merge results, avoiding duplicates
        const existingIds = new Set(searchResults.map(user => user._id.toString()));
        const newResults = regexResults.filter(user => !existingIds.has(user._id.toString()));

        searchResults = [...searchResults, ...newResults].slice(0, limit);
      }

      // 🚀 Autocomplete suggestions for partial phone numbers
      if (options.autocomplete && searchTerm.length >= 3) {
        // Escape special regex characters for autocomplete
        const escapeRegex = (str: string) => {
          return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        };

        const escapedSearchTerm = escapeRegex(searchTerm);

        const autocompleteQuery = {
          ...baseQuery,
          phone: { $regex: `^.*${escapedSearchTerm}`, $options: 'i' },
        };

        const autocompleteResults = await User.find(autocompleteQuery)
          .select('phone email username')
          .limit(5)
          .lean();

        return {
          results: searchResults,
          autocomplete: autocompleteResults,
          metadata: {
            query: searchTerm,
            resultsCount: searchResults.length,
            autocompleteCount: autocompleteResults.length,
            searchType: 'autocomplete',
          },
        };
      }

      logger.info('🔍 User search completed', {
        searchTerm,
        resultsCount: searchResults.length,
        searchType: 'regex_search',
        currentUser: currentUser.userId,
      });

      return {
        results: searchResults,
        metadata: {
          query: searchTerm,
          resultsCount: searchResults.length,
          searchType: 'regex_search',
        },
      };
    } catch (error) {
      logger.error('❌ User search failed', {
        searchTerm,
        error: error.message,
        currentUser: currentUser.userId,
      });
      throw new InternalServerError('Search operation failed');
    }
  }

  /**
   * Update user data
   */
  async updateUser(
    userId: string | Types.ObjectId,
    updateData: {
      email?: string;
      password?: string;
      addresses?: Array<{
        tag?: string;
        coordinates: [number, number];
        details: string;
        contactPhone?: string;
      }>;
      isActive?: boolean;
      agentMetadata?: {
        vehicle?: {
          type?: string;
          number?: string;
        };
        isOnDuty?: boolean;
        lastKnownLocation?: {
          coordinates: [number, number];
        };
      };
    },
    currentUser: TokenPayload
  ) {
    // Convert string ID to ObjectId if needed
    const userObjectId = typeof userId === 'string' ? new Types.ObjectId(userId) : userId;

    // Check permissions - users can only update their own data unless they're admins
    const isOwnAccount = currentUser.userId === userId.toString();
    const isAdmin = currentUser.role === UserRole.ADMIN;

    if (!isAdmin && !isOwnAccount) {
      throw new UnauthorizedError('You can only update your own user data');
    }

    // Find user
    const user = await User.findById(userObjectId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Prepare update data
    const updateFields: any = {};

    // Basic fields
    if (updateData.email) updateFields.email = updateData.email;

    // Password update
    if (updateData.password) {
      updateFields.passwordHash = await hashPassword(updateData.password);
    }

    // Addresses update
    if (updateData.addresses && updateData.addresses.length > 0) {
      updateFields.addresses = updateData.addresses.map(addr => ({
        tag: addr.tag || 'home',
        location: {
          type: 'Point',
          coordinates: addr.coordinates,
        },
        details: addr.details,
        contactPhone: addr.contactPhone || user.phone,
      }));
    }

    // Active status (admin only)
    if (isAdmin && updateData.isActive !== undefined) {
      updateFields.isActive = updateData.isActive;
    }

    // Agent metadata updates
    if (user.role === UserRole.AGENT && updateData.agentMetadata) {
      // Vehicle updates (admin only)
      if (isAdmin && updateData.agentMetadata.vehicle) {
        updateFields['agentMetadata.vehicle'] = {
          ...user.agentMetadata?.vehicle,
          ...updateData.agentMetadata.vehicle,
        };
      }

      // Duty status (can be updated by the agent or admin)
      if (updateData.agentMetadata.isOnDuty !== undefined) {
        updateFields['agentMetadata.isOnDuty'] = updateData.agentMetadata.isOnDuty;
      }

      // Location updates (can be updated by the agent or admin)
      if (updateData.agentMetadata.lastKnownLocation) {
        updateFields['agentMetadata.lastKnownLocation'] = {
          type: 'Point',
          coordinates: updateData.agentMetadata.lastKnownLocation.coordinates,
        };
      }
    }

    // Update user
    const updatedUser = await User.findByIdAndUpdate(
      userObjectId,
      { $set: updateFields },
      { new: true }
    ).select('-passwordHash');

    if (!updatedUser) {
      throw new NotFoundError('User not found after update');
    }

    return updatedUser;
  }

  /**
   * Delete user (admin only or self-delete)
   */
  async deleteUser(userId: string | Types.ObjectId, currentUser: TokenPayload) {
    // Convert string ID to ObjectId if needed
    const userObjectId = typeof userId === 'string' ? new Types.ObjectId(userId) : userId;

    // Check permissions - users can only delete their own account unless they're admins
    const isOwnAccount = currentUser.userId === userId.toString();
    const isAdmin = currentUser.role === UserRole.ADMIN;

    if (!isAdmin && !isOwnAccount) {
      throw new UnauthorizedError('You can only delete your own account');
    }

    // Find user
    const user = await User.findById(userObjectId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Prevent deleting the last admin
    if (user.role === UserRole.ADMIN) {
      const adminCount = await User.countDocuments({ role: UserRole.ADMIN });
      if (adminCount <= 1) {
        throw new ValidationError('Cannot delete the last admin account');
      }
    }

    // Delete user
    await User.findByIdAndDelete(userObjectId);

    return { success: true, message: 'User deleted successfully' };
  }

  /**
   * Subscribe user to notification topic
   */
  async subscribeToTopic(
    userId: string | Types.ObjectId,
    topic: NotificationTopic,
    deviceToken: string,
    currentUser: TokenPayload
  ) {
    // Convert string ID to ObjectId if needed
    const userObjectId = typeof userId === 'string' ? new Types.ObjectId(userId) : userId;

    // Check permissions - users can only subscribe themselves unless they're admins
    const isOwnAccount = currentUser.userId === userId.toString();
    const isAdmin = currentUser.role === UserRole.ADMIN;

    if (!isAdmin && !isOwnAccount) {
      throw new UnauthorizedError('You can only manage your own subscriptions');
    }

    // Find user
    const user = await User.findById(userObjectId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Subscribe to topic
    const result = await subscribeToTopic([deviceToken], topic);

    return {
      success: result.successCount > 0,
      topic,
      failureCount: result.failureCount,
      successCount: result.successCount,
    };
  }

  /**
   * Unsubscribe user from notification topic
   */
  async unsubscribeFromTopic(
    userId: string | Types.ObjectId,
    topic: NotificationTopic,
    deviceToken: string,
    currentUser: TokenPayload
  ) {
    // Convert string ID to ObjectId if needed
    const userObjectId = typeof userId === 'string' ? new Types.ObjectId(userId) : userId;

    // Check permissions - users can only unsubscribe themselves unless they're admins
    const isOwnAccount = currentUser.userId === userId.toString();
    const isAdmin = currentUser.role === UserRole.ADMIN;

    if (!isAdmin && !isOwnAccount) {
      throw new UnauthorizedError('You can only manage your own subscriptions');
    }

    // Find user
    const user = await User.findById(userObjectId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Unsubscribe from topic
    const result = await unsubscribeFromTopic([deviceToken], topic);

    return {
      success: result.successCount > 0,
      topic,
      failureCount: result.failureCount,
      successCount: result.successCount,
    };
  }

  /**
   * Toggle user active status (admin only)
   */
  async toggleUserActiveStatus(
    userId: string | Types.ObjectId,
    isActive: boolean,
    currentUser: TokenPayload
  ) {
    // Verify current user is admin
    if (currentUser.role !== UserRole.ADMIN) {
      throw new UnauthorizedError('Only admins can toggle user active status');
    }

    // Convert string ID to ObjectId if needed
    const userObjectId = typeof userId === 'string' ? new Types.ObjectId(userId) : userId;

    // Find user
    const user = await User.findById(userObjectId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Prevent deactivating the last admin
    if (user.role === UserRole.ADMIN && !isActive) {
      const adminCount = await User.countDocuments({ role: UserRole.ADMIN, isActive: true });
      if (adminCount <= 1) {
        throw new ValidationError('Cannot deactivate the last admin account');
      }
    }

    // Update user
    const updatedUser = await User.findByIdAndUpdate(
      userObjectId,
      { $set: { isActive } },
      { new: true }
    );

    if (!updatedUser) {
      throw new NotFoundError('User not found after update');
    }

    return updatedUser;
  }

  /**
   * Toggle agent on duty status (admin or agent only)
   */
  async toggleAgentOnDutyStatus(
    userId: string | Types.ObjectId,
    isOnDuty: boolean,
    currentUser: TokenPayload
  ) {
    // Convert string ID to ObjectId if needed
    const userObjectId = typeof userId === 'string' ? new Types.ObjectId(userId) : userId;

    // Check permissions - users can only toggle their own status unless they're admins
    const isOwnAccount = currentUser.userId === userId.toString();
    const isAdmin = currentUser.role === UserRole.ADMIN;

    if (!isAdmin && !isOwnAccount) {
      throw new UnauthorizedError('You can only toggle your own on duty status');
    }

    // Find user
    const user = await User.findById(userObjectId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Update user
    const updatedUser = await User.findByIdAndUpdate(
      userObjectId,
      { $set: { 'agentMetadata.isOnDuty': isOnDuty } },
      { new: true }
    );

    if (!updatedUser) {
      throw new NotFoundError('User not found after update');
    }

    return updatedUser;
  }

  /**
   * Change user role (admin only)
   */
  async changeUserRole(
    userId: string | Types.ObjectId,
    newRole: UserRole,
    currentUser: TokenPayload
  ) {
    // Verify current user is admin
    if (currentUser.role !== UserRole.ADMIN) {
      throw new UnauthorizedError('Only admins can change user roles');
    }

    // Convert string ID to ObjectId if needed
    const userObjectId = typeof userId === 'string' ? new Types.ObjectId(userId) : userId;

    // Find user
    const user = await User.findById(userObjectId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Prevent changing the role of the last admin
    if (user.role === UserRole.ADMIN && newRole !== UserRole.ADMIN) {
      const adminCount = await User.countDocuments({ role: UserRole.ADMIN, isActive: true });
      if (adminCount <= 1) {
        throw new ValidationError('Cannot change the role of the last admin account');
      }
    }

    // Prevent changing own role
    if (currentUser.userId === userId.toString()) {
      throw new ValidationError('You cannot change your own role');
    }

    // Prepare update data
    const updateData: any = { role: newRole };

    // Handle role-specific metadata
    if (newRole === UserRole.AGENT && user.role !== UserRole.AGENT) {
      // Initialize agent metadata if changing to agent role
      updateData.agentMetadata = {
        vehicle: {
          type: VehicleType.MOTORCYCLE, // Default vehicle type
          number: '', // Will need to be updated later
        },
        isOnDuty: false,
        lastKnownLocation: {
          type: 'Point',
          coordinates: [0, 0], // Default coordinates
        },
        rating: 0,
      };
    } else if (newRole !== UserRole.AGENT && user.role === UserRole.AGENT) {
      // Remove agent metadata if changing from agent role
      updateData.$unset = { agentMetadata: 1 };
    }

    // Update user
    const updatedUser = await User.findByIdAndUpdate(userObjectId, updateData, { new: true });

    if (!updatedUser) {
      throw new NotFoundError('User not found after update');
    }

    logger.info(
      `User role changed: ${user.phone} from ${user.role} to ${newRole} by admin ${currentUser.userId}`
    );

    return updatedUser;
  }

  /**
   * Format user registration date for display
   * @param registrationDate - User registration date in UTC
   * @param userRole - User role for timezone context
   * @returns Formatted registration date string in user's timezone
   */
  formatUserRegistrationDateForUser(
    registrationDate: Date,
    userRole: UserRole = UserRole.CUSTOMER
  ): string {
    const timezoneContext: TimezoneContext = {
      role: userRole,
    };

    return timezoneManager.formatDateForUser(registrationDate, timezoneContext, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short',
    });
  }

  /**
   * Get users with timezone-formatted registration dates
   * @param currentUser - Current user context
   * @param options - Query options
   * @returns Users with formatted registration dates
   */
  async getUsersWithFormattedDates(
    currentUser: TokenPayload,
    options: {
      role?: UserRole;
      isActive?: boolean;
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      dateRange?: { startDate?: Date | string; endDate?: Date | string };
    } = {}
  ) {
    // Build query
    const query: any = {};
    if (options.role) query.role = options.role;
    if (options.isActive !== undefined) query.isActive = options.isActive;

    // Add timezone-aware date filtering if provided
    if (options.dateRange?.startDate || options.dateRange?.endDate) {
      const timezoneContext: TimezoneContext = {
        userId: currentUser.userId.toString(),
        role: currentUser.role,
      };

      const dateQuery = timezoneManager.buildDateQuery(
        options.dateRange.startDate,
        options.dateRange.endDate,
        timezoneContext
      );

      if (dateQuery) {
        Object.assign(query, dateQuery);
      }
    }

    // Pagination
    const page = options.page || 1;
    const limit = options.limit || 10;
    const skip = (page - 1) * limit;

    // Sorting
    const sortOptions: any = {};
    if (options.sortBy) {
      sortOptions[options.sortBy] = options.sortOrder === 'desc' ? -1 : 1;
    } else {
      sortOptions.createdAt = -1; // Default sort by creation date, newest first
    }

    // Execute query
    const [users, totalUsers] = await Promise.all([
      User.find(query).sort(sortOptions).skip(skip).limit(limit).lean(),
      User.countDocuments(query),
    ]);

    // Format registration dates for display
    const usersWithFormattedDates = users.map((user: any) => ({
      ...user,
      formattedRegistrationDate: this.formatUserRegistrationDateForUser(
        new Date(user.createdAt),
        user.role
      ),
    }));

    logger.debug('Retrieved users with formatted registration dates', {
      currentUser: currentUser.userId,
      totalUsers,
      page,
      limit,
      options,
    });

    return {
      users: usersWithFormattedDates,
      pagination: {
        total: totalUsers,
        page,
        limit,
        pages: Math.ceil(totalUsers / limit),
      },
    };
  }

  /**
   * Get user registration statistics with timezone-aware filtering
   * @param currentUser - Current user context
   * @param dateRange - Optional date range filter
   * @returns User registration statistics
   */
  async getUserRegistrationStatsWithTimezone(
    currentUser: TokenPayload,
    dateRange?: { startDate?: Date | string; endDate?: Date | string }
  ): Promise<{
    total: number;
    active: number;
    inactive: number;
    todayRegistrations: number;
    weekRegistrations: number;
    monthRegistrations: number;
    byRole: Record<UserRole, number>;
  }> {
    try {
      const timezoneContext: TimezoneContext = {
        userId: currentUser.userId.toString(),
        role: currentUser.role,
      };

      // Get today's boundaries in user timezone
      const todayBoundaries = timezoneManager.getDayBoundaries(new Date(), timezoneContext);

      // Get week boundaries
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const weekBoundaries = timezoneManager.getDayBoundaries(weekAgo, timezoneContext);

      // Get month boundaries
      const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const monthBoundaries = timezoneManager.getDayBoundaries(monthAgo, timezoneContext);

      // Build base query
      let baseQuery: any = {};

      // Add date range filter if provided
      if (dateRange?.startDate || dateRange?.endDate) {
        const dateQuery = timezoneManager.buildDateQuery(
          dateRange.startDate,
          dateRange.endDate,
          timezoneContext
        );
        if (dateQuery) {
          Object.assign(baseQuery, dateQuery);
        }
      }

      // Get statistics
      const [total, active, todayRegistrations, weekRegistrations, monthRegistrations, roleStats] =
        await Promise.all([
          User.countDocuments(baseQuery),
          User.countDocuments({ ...baseQuery, isActive: true }),
          User.countDocuments({
            createdAt: { $gte: todayBoundaries.start, $lte: todayBoundaries.end },
          }),
          User.countDocuments({
            createdAt: { $gte: weekBoundaries.start },
          }),
          User.countDocuments({
            createdAt: { $gte: monthBoundaries.start },
          }),
          User.aggregate([{ $match: baseQuery }, { $group: { _id: '$role', count: { $sum: 1 } } }]),
        ]);

      const inactive = total - active;

      // Format role statistics
      const byRole: Record<UserRole, number> = {
        [UserRole.ADMIN]: 0,
        [UserRole.SUPERVISOR]: 0,
        [UserRole.AGENT]: 0,
        [UserRole.CUSTOMER]: 0,
      };

      roleStats.forEach((stat: any) => {
        if (stat._id && Object.values(UserRole).includes(stat._id)) {
          byRole[stat._id as UserRole] = stat.count;
        }
      });

      logger.debug('Generated user registration statistics with timezone', {
        currentUser: currentUser.userId,
        total,
        active,
        inactive,
        todayRegistrations,
        weekRegistrations,
        monthRegistrations,
        byRole,
        dateRange,
        timezone: todayBoundaries.timezone,
      });

      return {
        total,
        active,
        inactive,
        todayRegistrations,
        weekRegistrations,
        monthRegistrations,
        byRole,
      };
    } catch (error) {
      logger.error('Failed to get user registration statistics with timezone', {
        currentUser: currentUser.userId,
        dateRange,
        error: error.message,
      });
      throw error;
    }
  }
}

// singleton user services export
export const userService = new UserServices();
