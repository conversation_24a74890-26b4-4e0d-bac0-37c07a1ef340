import { config } from '../config/env_config';
import mongoose, { ClientSession, Schema, Types } from 'mongoose';
import axios, { AxiosInstance, AxiosError } from 'axios';
import { Order, Payment, User } from '../models/index';
import { IPayment, IGatewayRawResponse } from '../models/payment.model';
import { OrderStatus, PaymentMethod, PaymentStatus, UserRole } from '../enums/enums';
import logger from '../config/logger';
import {
  PaymentError,
  WaaFiPaymentFailed,
  WaaFiPreauthExpired,
  WaaFiServiceUnavailable,
  PaymentAlreadyCompleted,
  InsufficientFundsError,
  InternalServerError,
  NotFoundError,
  BadRequestError,
  ValidationError,
  AppError,
} from '../errors/app_errors';
import { timezoneManager, TimezoneContext } from '../utils/timezone_utils';
import { generateUUID } from '../utils/cypto-utils';

/**
 * WaaFi API Response interfaces
 */
interface WaaFiPayPurchaseResponse {
  responseCode: string;
  responseMsg: string;
  params: {
    transactionId: string;
    cashierURL: string;
    amount: string;
    currency: string;
    timestamp: string;
    referenceId: string;
    invoiceId: string;
  };
}

/**
 * Service method types and interfaces
 */
interface DeliveryDetails {
  estimatedDeliveryTime?: string;
  deliveryAddress?: string;
}

interface PaymentInitiationResult {
  payment: IPayment;
  cashierUrl?: string; // Optional as some wallets may not return URL
  transactionId: string;
}

interface PaymentValidationOptions {
  checkExpiry?: boolean;
  allowedStatuses?: PaymentStatus[];
}

/**
 * WaaFi API service names
 */
enum PaymentServiceName { PURCHASE='API_PURCHASE', CANCELPURCHASE='API_CANCELPURCHASE' }

/**
 * Payment configuration constants
 */
const PAYMENT_CONSTANTS = {
  MAX_AMOUNT: 0, // No max amount 
  MIN_AMOUNT: 0.01,
  API_TIMEOUT: 30000,
  SUCCESS_RESPONSE_CODE: '2001',
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const; 

/**
 * PaymentService class for handling WaaFi payment operations
 * Implements singleton pattern for consistent configuration
 */
class PaymentService {
  private static instance: PaymentService;
  private readonly axiosInstance: AxiosInstance;

  private readonly paymentApiUrl = config.payment.rasiin.apiUrl;
  private readonly paymentApiKey = config.payment.rasiin.apiKey;
  private readonly paymentApiUserId = config.payment.rasiin.apiUserId;
  private readonly paymentMerchantUid = config.payment.rasiin.merchantUid;

  private constructor() {
    this.validateConfig();
    this.axiosInstance = this.createAxiosInstance();
  }

  /**
   * Creates and configures axios instance with proper error handling
   */
  private createAxiosInstance(): AxiosInstance {
    const instance = axios.create({
      baseURL: this.paymentApiUrl,
      timeout: PAYMENT_CONSTANTS.API_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'GasSystem/1.0',
      },
    });

    // Add request interceptor for logging
    instance.interceptors.request.use(
      config => {
        logger.info('WaaFi API Request', {
          url: config.url,
          method: config.method,
          timestamp: new Date().toISOString(),
        });
        return config;
      },
      error => {
        logger.error('WaaFi API Request Error', { error: error.message });
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging and error handling
    instance.interceptors.response.use(
      response => {
        logger.info('WaaFi API Response', {
          status: response.status,
          responseCode: response.data?.responseCode,
          timestamp: new Date().toISOString(),
        });
        return response;
      },
      error => {
        logger.error('WaaFi API Response Error', {
          status: error.response?.status,
          message: error.message,
          timestamp: new Date().toISOString(),
        });
        return Promise.reject(this.handleAxiosError(error));
      }
    );

    return instance;
  }

  /**
   * Validates payment service configuration
   * @throws {InternalServerError} When configuration is missing or invalid
   */
  private validateConfig(): void {
    const missingConfigs: string[] = [];

    if (!this.paymentApiUrl) missingConfigs.push('apiUrl');
    if (!this.paymentApiKey) missingConfigs.push('apiKey');
    if (!this.paymentApiUserId) missingConfigs.push('apiUserId');
    if (!this.paymentMerchantUid) missingConfigs.push('merchantUid');

    if (missingConfigs.length > 0) {
      const errorMessage = `Missing WaaFi payment configuration: ${missingConfigs.join(', ')}`;
      logger.error('Payment service configuration error', {
        missingConfigs,
        timestamp: new Date().toISOString(),
      });
      throw new InternalServerError(errorMessage, {
        code: 'PAYMENT_CONFIG_MISSING',
        details: { missingConfigs },
      });
    }
  }

  /**
   * Validates payment amount
   * @param amount - Payment amount to validate
   * @throws {BadRequestError} When amount is invalid
   */
  private validateAmount(amount: number): void {
    if (typeof amount !== 'number' || isNaN(amount)) {
      throw new BadRequestError('Amount must be a valid number', {
        code: 'INVALID_AMOUNT_TYPE',
        details: { providedAmount: amount, type: typeof amount },
      });
    }

    if (amount < PAYMENT_CONSTANTS.MIN_AMOUNT) {
      throw new BadRequestError(`Amount must be at least ${PAYMENT_CONSTANTS.MIN_AMOUNT}`, {
        code: 'AMOUNT_TOO_LOW',
        details: { amount, minimum: PAYMENT_CONSTANTS.MIN_AMOUNT },
      });
    }

    /// if MAX_AMOUNT is 0 means no limit skip this else check 
    if (PAYMENT_CONSTANTS.MAX_AMOUNT > 0 && amount > PAYMENT_CONSTANTS.MAX_AMOUNT) {
      logger.debug('Amount exceeds maximum limit', {
        amount,
        maximum: PAYMENT_CONSTANTS.MAX_AMOUNT,
      }); 
      throw new BadRequestError(`Amount exceeds maximum limit of ${PAYMENT_CONSTANTS.MAX_AMOUNT}`, {
        code: 'AMOUNT_TOO_HIGH',
        details: { amount, maximum: PAYMENT_CONSTANTS.MAX_AMOUNT },
      });
    }
  }

  /**
   * Validates and formats Somalia mobile number
   * @param mobile - Mobile number to validate
   * @returns Formatted mobile number with country code
   * @throws {ValidationError} When mobile number format is invalid
   */
  private validateMobileNumber(mobile: string): string {
    if (!mobile || typeof mobile !== 'string') {
      throw new ValidationError('Mobile number is required and must be a string', {
        code: 'MOBILE_REQUIRED',
        details: { providedMobile: mobile, type: typeof mobile },
      });
    }

    const cleaned = mobile.replace(/\D/g, '');
    const regex = /^(252)?(61|62|65|66|67|68|69|71|77|79|88|89|90)\d{7}$/;

    if (!regex.test(cleaned)) {
      throw new ValidationError('Invalid Somalia phone number format', {
        code: 'INVALID_MOBILE_FORMAT',
        details: {
          providedMobile: mobile,
          cleanedMobile: cleaned,
          expectedFormat: '252XXXXXXXXX or XXXXXXXXX',
        },
      });
    }

    const formattedNumber = cleaned.startsWith('252') ? cleaned : `252${cleaned}`;

    logger.debug('Mobile number validated and formatted', {
      original: mobile,
      formatted: formattedNumber,
    });

    return formattedNumber;
  }

  /**
   * Handles axios errors and converts them to appropriate custom errors
   * @param error - Axios error to handle
   * @returns Appropriate custom error
   */
  private handleAxiosError(error: AxiosError): Error {
    if (error.code === 'ECONNABORTED') {
      return new WaaFiServiceUnavailable('Payment service request timeout');
    }

    if (error.response?.status === 503) {
      return new WaaFiServiceUnavailable('Payment service temporarily unavailable');
    }

    if (error.response?.status >= 500) {
      return new InternalServerError('Payment service internal error', {
        code: 'PAYMENT_SERVICE_ERROR',
        details: {
          status: error.response.status,
          message: error.message,
        },
      });
    }

    return new PaymentError(
      'Payment service communication error',
      400,
      'PAYMENT_COMMUNICATION_ERROR',
      {
        details: {
          message: error.message,
          status: error.response?.status,
        },
      }
    );
  }

  /**
   * Validates ObjectId format
   * @param id - ID to validate
   * @param fieldName - Name of the field for error messages
   * @throws {BadRequestError} When ID format is invalid
   */
  private validateObjectId(id: string | Schema.Types.ObjectId, fieldName: string): void {
    if (!id) {
      throw new BadRequestError(`${fieldName} is required`, {
        code: 'MISSING_REQUIRED_FIELD',
        details: { fieldName },
      });
    }

    const idString = typeof id === 'string' ? id : id.toString();
    if (!Types.ObjectId.isValid(idString)) {
      throw new BadRequestError(`Invalid ${fieldName} format`, {
        code: 'INVALID_OBJECT_ID',
        details: { fieldName, providedId: idString },
      });
    }
  }

  /**
   * Validates WaaFi API response
   * @param response - API response to validate
   * @param context - Context information for error reporting
   * @throws {WaaFiPaymentFailed} When API response indicates failure
   */
  private validateWaaFiResponse(
    response: WaaFiPayPurchaseResponse,
    context: { paymentId: string; orderId: string; amount?: number, transactionId?: string }
  ): void {
    if (response.responseCode !== PAYMENT_CONSTANTS.SUCCESS_RESPONSE_CODE) {
      logger.error('WaaFi API returned error response', {
        responseCode: response.responseCode,
        responseMsg: response.responseMsg,
        context,
        timestamp: new Date().toISOString(),
      });

      throw new WaaFiPaymentFailed(response.responseMsg, {
        paymentId: context.paymentId,
        orderId: context.orderId,
        amount: context.amount,
        details: {
          responseCode: response.responseCode,
          responseMsg: response.responseMsg,
          timestamp: new Date().toISOString(),
        },
      });
    }
  }

  /**
   * Sanitizes request payload by removing sensitive information
   * @param payload - Original request payload
   * @returns Sanitized payload safe for storage
   */
  private sanitizeRequestPayload(payload: any): Record<string, any> {
    const sanitized = JSON.parse(JSON.stringify(payload));

    // Remove sensitive fields
    if (sanitized.serviceParams) {
      // Mask API key (keep first 4 and last 4 characters)
      if (sanitized.serviceParams.apiKey) {
        const apiKey = sanitized.serviceParams.apiKey;
        if (apiKey.length > 8) {
          sanitized.serviceParams.apiKey = `${apiKey.substring(0, 4)}****${apiKey.substring(apiKey.length - 4)}`;
        } else {
          sanitized.serviceParams.apiKey = '****';
        }
      }

      // Mask merchant UID (keep first 4 characters)
      if (sanitized.serviceParams.merchantUid) {
        const merchantUid = sanitized.serviceParams.merchantUid;
        sanitized.serviceParams.merchantUid = `${merchantUid.substring(0, 4)}****`;
      }

      // Mask account number (keep first 3 and last 3 digits)
      if (sanitized.serviceParams.payerInfo?.accountNo) {
        const accountNo = sanitized.serviceParams.payerInfo.accountNo;
        if (accountNo.length > 6) {
          sanitized.serviceParams.payerInfo.accountNo = `${accountNo.substring(0, 3)}****${accountNo.substring(accountNo.length - 3)}`;
        } else {
          sanitized.serviceParams.payerInfo.accountNo = '****';
        }
      }
    }

    return sanitized;
  }

  /**
   * Sanitizes response payload by removing sensitive information
   * @param response - Original response payload
   * @returns Sanitized response safe for storage
   */
  private sanitizeResponsePayload(response: any): Record<string, any> {
    const sanitized = JSON.parse(JSON.stringify(response));

    // Remove or mask sensitive fields if any
    // For WaaFi responses, most fields are safe to store
    // But we can add additional sanitization if needed

    return sanitized;
  }

  /**
   * Stores raw gateway response for audit and debugging
   * @param paymentId - Payment ID
   * @param operation - Type of operation performed
   * @param requestPayload - Original request payload
   * @param response - Gateway response
   * @param httpStatus - HTTP status code
   * @param processingTime - Response time in milliseconds
   * @param error - Error details if operation failed
   */
  private async storeGatewayResponse(
    paymentId: string,
    operation: IGatewayRawResponse['operation'],
    requestPayload: any,
    response?: any,
    httpStatus?: number,
    processingTime?: number,
    error?: {
      errorCode?: string;
      errorMessage?: string;
      stackTrace?: string;
    },
    options?: { session: ClientSession }
  ): Promise<void> {
    let session = options?.session ?? null;
    try {
      const gatewayResponse: IGatewayRawResponse = {
        provider: 'WAAFI',
        operation,
        timestamp: new Date(),
        requestId: requestPayload?.requestId,
        responseCode: response?.responseCode,
        responseMessage: response?.responseMsg,
        rawRequest: this.sanitizeRequestPayload(requestPayload),
        rawResponse: response ? this.sanitizeResponsePayload(response) : undefined,
        httpStatus,
        processingTime,
        errorDetails: error,
        metadata: {
          apiVersion: requestPayload?.schemaVersion,
          channelName: requestPayload?.channelName,
          serviceName: requestPayload?.serviceName,
        },
      };

      await Payment.findByIdAndUpdate(
        paymentId,
        {
          $push: {
            gatewayResponses: gatewayResponse,
          },
        },
        { new: true, session }
      );

      logger.info('Gateway response stored successfully', {
        paymentId,
        operation,
        provider: 'WAAFI',
        responseCode: response?.responseCode,
        httpStatus,
        processingTime,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('Failed to store gateway response', {
        paymentId,
        operation,
        error: error.message,
        timestamp: new Date().toISOString(),
      });
      // Don't throw error here to avoid breaking the main payment flow
    }
  }

  /**
   * Initiates a purchase payment with WaaFi
   * @param mobile - Customer's mobile number
   * @param amount - Payment amount
   * @param paymentId - Payment ID
   * @param deliveryDetails - Optional delivery information
   * @returns Payment initiation result with cashier URL and transaction ID
   * @throws {ValidationError} When input validation fails
   * @throws {WaaFiPaymentFailed} When WaaFi API returns error
   * @throws {InternalServerError} When payment record operations fail
   */
  public async initiatePurchase(
    paymentId: string | Types.ObjectId,
    mobile: string,
    amount: number,
    deliveryDetails?: DeliveryDetails
  ): Promise<PaymentInitiationResult> {
    // Validate payment exists (outside transaction)
    const payment = await Payment.findById(paymentId);
    if (!payment) {
      throw new NotFoundError('Payment not found', {
        code: 'PAYMENT_NOT_FOUND',
        details: { paymentId },
      });
    }

    // Validate payment status
    if (payment.status !== PaymentStatus.PENDING) {
      throw new BadRequestError('Payment must be in PENDING state', {
        code: 'INVALID_PAYMENT_STATUS',
        details: {
          currentStatus: payment.status,
          requiredStatus: PaymentStatus.PENDING,
        },
      });
    }

    // Input validation
    const cleanedMobile = this.validateMobileNumber(mobile);
    this.validateAmount(amount);

    logger.info('Initiating payment purchase', {
      paymentId: payment._id.toString(),
      amount,
      mobile: cleanedMobile,
      timestamp: new Date().toISOString(),
    });

    // Prepare WaaFi API payload with proper formatting and prefixes
    const paymentPayload = {
      schemaVersion: '1.0',
      requestId: generateUUID(),
      timestamp: new Date().toISOString(),
      channelName: 'WEB',
      serviceName: PaymentServiceName.PURCHASE,
      serviceParams: {
        merchantUid: this.paymentMerchantUid,
        apiUserId: this.paymentApiUserId,
        apiKey: this.paymentApiKey,
        paymentMethod: 'MWALLET_ACCOUNT',
        payerInfo: {
          accountNo: cleanedMobile,
        },
        transactionInfo: {
          referenceId: `REF-${payment._id.toString()}`,
          invoiceId: payment.orderId ? `INV-${payment.orderId.toString()}` : `PAY-${payment._id.toString()}`,
          amount: amount.toFixed(2),
          currency: payment.currency ?? 'USD',
          description: payment.metadata?.loanId
            ? `Loan payment: ${payment.metadata.loanId}`
            : `Gas delivery order: ${payment.orderId || payment._id}`,
        },
      },
    };

    // Call WaaFi API outside transaction to avoid holding locks during network calls
    const startTime = Date.now();
    let response: any;
    let httpStatus: number;
    let gatewayError: any = null;

    try {
      response = await this.axiosInstance.post<WaaFiPayPurchaseResponse>(
        '', // Use empty string since baseURL is already set
        paymentPayload
      );
      httpStatus = response.status;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      httpStatus = error.response?.status || 0;
      gatewayError = {
        errorCode: error.code || 'UNKNOWN_ERROR',
        errorMessage: error.message,
        stackTrace: error.stack,
      };

      // Store failed gateway response (no transaction needed for failed calls)
      await this.storeGatewayResponse(
        payment._id.toString(),
        'PURCHASE',
        paymentPayload,
        error.response?.data,
        httpStatus,
        processingTime,
        gatewayError
      );

      // Handle axios errors
      if (error.response) {
        const axiosError = error as AxiosError;
        logger.error('WaaFi API error response', {
          status: axiosError.response?.status,
          data: axiosError.response?.data,
          headers: axiosError.response?.headers,
          timestamp: new Date().toISOString(),
        });

        throw new WaaFiServiceUnavailable('WaaFi service returned an error', {
          // details: axiosError.response?.data,
          // httpStatus: axiosError.response?.status,
        });
      }

      if (error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT') {
        throw new WaaFiServiceUnavailable('WaaFi service timeout', {
          // details: { timeout: PAYMENT_CONSTANTS.API_TIMEOUT },
        });
      }

      if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        logger.error('WaaFi service connection error', {
          error: error.message,
          code: error.code,
          timestamp: new Date().toISOString(),
        });
      }

      throw error;
    }

    const processingTime = Date.now() - startTime;

    // Validate API response before persisting
    this.validateWaaFiResponse(response.data, {
      paymentId: payment._id.toString(),
      orderId: payment.orderId ? payment.orderId.toString() : payment._id.toString(),
      amount,
      transactionId: response.data.params.transactionId,
    });

    // Now open a short transaction to persist results
    const session = await mongoose.startSession();
    try {
      session.startTransaction();

      // Store successful gateway response
      await this.storeGatewayResponse(
        payment._id.toString(),
        'PURCHASE',
        paymentPayload,
        response.data,
        httpStatus,
        processingTime,
        undefined,
        { session }
      );

      // Update payment with transaction details - Purchase is immediately captured
      const updateData: Partial<IPayment> = {
        transactionId: response.data.params.transactionId,
        status: PaymentStatus.CAPTURED,
        currency: response.data.params.currency,
        metadata: {
          ...payment.metadata,
          deliveryDetails,
          finalCapture: true,
          initiatedAt: new Date(),
        },
      };

      const updatedPayment = await Payment.findByIdAndUpdate(payment._id, updateData, {
        new: true,
        runValidators: true,
        session,
      });

      if (!updatedPayment) {
        throw new InternalServerError('Failed to update payment record');
      }

      await session.commitTransaction();

      return {
        payment: updatedPayment,
        cashierUrl: response.data.params.cashierURL || undefined, // Guard against missing URL
        transactionId: response.data.params.transactionId,
      };
    } catch (error) {
      logger.error('Payment purchase database update failed', {
        paymentId,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });

      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Cancel a payment by payment ID (public interface for controllers)
   * @param paymentId - Payment ID
   * @param reason - Cancellation reason (defaults to "Cancelled")
   * @returns Updated payment record
   * @throws {ValidationError} When paymentId is invalid
   * @throws {NotFoundError} When payment not found
   * @throws {BadRequestError} When payment cannot be cancelled
   */
  public async cancelPayment(paymentId: string, reason = 'Cancelled'): Promise<IPayment> {
    // Delegate to the specific purchase cancellation method
    return await this.cancelPurchaseByPaymentId(paymentId, reason);
  }

  /**
   * Cancel a purchase payment by payment ID
   * @param paymentId - Payment ID
   * @param reason - Cancellation reason (defaults to "Cancelled")
   * @returns Updated payment record
   * @throws {ValidationError} When paymentId is invalid
   * @throws {NotFoundError} When payment not found
   * @throws {BadRequestError} When payment cannot be cancelled
   */
  public async cancelPurchaseByPaymentId(paymentId: string, reason = 'Cancelled'): Promise<IPayment> {
    // Validate paymentId is a valid ObjectId
    this.validateObjectId(paymentId, 'paymentId');

    // Find the Payment by ID
    const payment = await Payment.findById(paymentId);
    if (!payment) {
      throw new NotFoundError('Payment not found', {
        code: 'PAYMENT_NOT_FOUND',
        details: { paymentId },
      });
    }

    // Check payment status - allow CANCELLED (return as is) or require CAPTURED
    if (payment.status === PaymentStatus.CANCELLED) {
      // Maintain idempotency: if already cancelled, return the payment without calling WaaFi
      logger.info('Payment already cancelled, returning existing record', {
        paymentId,
        status: payment.status,
        timestamp: new Date().toISOString(),
      });
      return payment;
    }

    if (payment.status !== PaymentStatus.CAPTURED) {
      throw new BadRequestError('Payment must be in CAPTURED state to be cancelled', {
        code: 'INVALID_PAYMENT_STATUS',
        details: {
          paymentId,
          currentStatus: payment.status,
          requiredStatus: PaymentStatus.CAPTURED,
        },
      });
    }

    // Ensure transactionId exists
    if (!payment.transactionId) {
      throw new BadRequestError('Payment does not have a transaction ID', {
        code: 'MISSING_TRANSACTION_ID',
        details: { paymentId },
      });
    }

    // Call internal cancellation method
    return await this._cancelPurchaseInternal(payment, payment.transactionId, reason);
  }

  /**
   * Cancel a purchase payment by transaction ID
   * @param transactionId - WaaFi transaction ID
   * @param reason - Cancellation reason (defaults to "Cancelled")
   * @returns Updated payment record
   * @throws {ValidationError} When transactionId is invalid
   * @throws {NotFoundError} When payment not found
   * @throws {BadRequestError} When payment cannot be cancelled
   */
  public async cancelPurchaseByTransactionId(transactionId: string, reason = 'Cancelled'): Promise<IPayment> {
    // Validate transactionId is non-empty
    if (!transactionId || transactionId.trim() === '') {
      throw new ValidationError('Transaction ID is required', {
        code: 'INVALID_TRANSACTION_ID',
        details: { transactionId },
      });
    }

    // Find the Payment by transactionId
    const payment = await Payment.findOne({ transactionId });
    if (!payment) {
      throw new NotFoundError('Payment not found for transaction', {
        code: 'PAYMENT_NOT_FOUND',
        details: { transactionId },
      });
    }

    // Apply the same status checks
    if (payment.status === PaymentStatus.CANCELLED) {
      // Maintain idempotency: if already cancelled, return the payment without calling WaaFi
      logger.info('Payment already cancelled, returning existing record', {
        paymentId: payment._id.toString(),
        transactionId,
        status: payment.status,
        timestamp: new Date().toISOString(),
      });
      return payment;
    }

    if (payment.status !== PaymentStatus.CAPTURED) {
      throw new BadRequestError('Payment must be in CAPTURED state to be cancelled', {
        code: 'INVALID_PAYMENT_STATUS',
        details: {
          paymentId: payment._id.toString(),
          transactionId,
          currentStatus: payment.status,
          requiredStatus: PaymentStatus.CAPTURED,
        },
      });
    }

    // Call internal cancellation method
    return await this._cancelPurchaseInternal(payment, transactionId, reason);
  }

  /**
   * Internal method to cancel a purchase payment via WaaFi API
   * @param payment - Payment document
   * @param transactionId - WaaFi transaction ID
   * @param reason - Cancellation reason
   * @returns Updated payment record
   * @private
   */
  private async _cancelPurchaseInternal(payment: IPayment, transactionId: string, reason: string): Promise<IPayment> {
    // Build WaaFi payload for API_CANCELPURCHASE
    const payload = {
      schemaVersion: '1.0',
      requestId: generateUUID(),
      timestamp: new Date().toISOString(),
      channelName: 'WEB',
      serviceName: PaymentServiceName.CANCELPURCHASE,
      serviceParams: {
        merchantUid: this.paymentMerchantUid,
        apiUserId: this.paymentApiUserId,
        apiKey: this.paymentApiKey,
        transactionId,
        description: reason
      }
    };

    // Send POST request to WaaFi outside transaction to avoid holding locks
    const startTime = Date.now();
    let response: any;
    let httpStatus: number;
    let gatewayError: any = null;

    try {
      response = await this.axiosInstance.post('', payload); // Use empty string since baseURL is set
      httpStatus = response.status;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      httpStatus = error.response?.status || 0;
      gatewayError = {
        errorCode: error.code || 'UNKNOWN_ERROR',
        errorMessage: error.message,
        stackTrace: error.stack,
      };

      // Store failed gateway response (no transaction needed for failed calls)
      await this.storeGatewayResponse(
        payment._id.toString(),
        'CANCELPURCHASE',
        payload,
        error.response?.data,
        httpStatus,
        processingTime,
        gatewayError
      );

      throw error;
    }

    const processingTime = Date.now() - startTime;

    // Validate API response before persisting
    this.validateWaaFiResponse(response.data, {
      paymentId: payment._id.toString(),
      orderId: payment.orderId ? payment.orderId.toString() : payment._id.toString(),
      transactionId,
    });

    // In a Mongoose transaction: update Payment status and store gateway response
    const session = await mongoose.startSession();
    try {
      session.startTransaction();

      // Store successful gateway response with operation: 'CANCELPURCHASE'
      await this.storeGatewayResponse(
        payment._id.toString(),
        'CANCELPURCHASE',
        payload,
        response.data,
        httpStatus,
        processingTime,
        undefined,
        { session }
      );

      // Update Payment status to CANCELLED, set cancelledAt, and store cancellation reason in metadata
      const updateData: Partial<IPayment> = {
        status: PaymentStatus.CANCELLED,
        cancelledAt: new Date(),
        metadata: {
          ...payment.metadata,
          cancellationReason: reason,
        },
      };

      const updatedPayment = await Payment.findByIdAndUpdate(payment._id, updateData, {
        new: true,
        runValidators: true,
        session,
      });

      if (!updatedPayment) {
        throw new InternalServerError('Failed to update payment record');
      }

      await session.commitTransaction();

      logger.info('Payment cancelled successfully via WaaFi', {
        paymentId: payment._id.toString(),
        transactionId,
        reason,
        timestamp: new Date().toISOString(),
      });

      return updatedPayment;
    } catch (error) {
      logger.error('Payment cancellation database update failed', {
        paymentId: payment._id.toString(),
        transactionId,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });

      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Retrieves gateway responses for a specific payment
   * @param paymentId - Payment ID
   * @param operation - Optional filter by operation type
   * @param provider - Optional filter by provider
   * @returns Array of gateway responses
   */
  public async getGatewayResponses(
    paymentId: string,
    operation?: IGatewayRawResponse['operation'],
    provider?: IGatewayRawResponse['provider']
  ): Promise<IGatewayRawResponse[]> {
    try {
      this.validateObjectId(paymentId, 'paymentId');

      const payment = await Payment.findById(paymentId).select('gatewayResponses');
      if (!payment) {
        throw new NotFoundError('Payment not found', {
          code: 'PAYMENT_NOT_FOUND',
          details: { paymentId },
        });
      }

      let responses = payment.gatewayResponses || [];

      // Apply filters
      if (operation) {
        responses = responses.filter(response => response.operation === operation);
      }
      if (provider) {
        responses = responses.filter(response => response.provider === provider);
      }

      return responses;
    } catch (error) {
      logger.error('Failed to retrieve gateway responses', {
        paymentId,
        operation,
        provider,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Gets payment analytics including gateway response statistics
   * @param paymentId - Payment ID
   * @returns Payment analytics with gateway response data
   */
  public async getPaymentAnalytics(paymentId: string): Promise<{
    payment: IPayment;
    analytics: {
      totalGatewayInteractions: number;
      operationCounts: Record<string, number>;
      averageResponseTime: number;
      successRate: number;
      lastInteraction?: Date;
      errorCount: number;
    };
  }> {
    try {
      this.validateObjectId(paymentId, 'paymentId');

      const payment = await Payment.findById(paymentId);
      if (!payment) {
        throw new NotFoundError('Payment not found', {
          code: 'PAYMENT_NOT_FOUND',
          details: { paymentId },
        });
      }

      const responses = payment.gatewayResponses || [];
      const totalInteractions = responses.length;

      // Calculate operation counts
      const operationCounts: Record<string, number> = {};
      responses.forEach(response => {
        operationCounts[response.operation] = (operationCounts[response.operation] || 0) + 1;
      });

      // Calculate average response time
      const responseTimes = responses
        .filter(response => response.processingTime)
        .map(response => response.processingTime!);
      const averageResponseTime =
        responseTimes.length > 0
          ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
          : 0;

      // Calculate success rate
      const successfulResponses = responses.filter(
        response => response.responseCode === PAYMENT_CONSTANTS.SUCCESS_RESPONSE_CODE
      );
      const successRate =
        totalInteractions > 0 ? (successfulResponses.length / totalInteractions) * 100 : 0;

      // Get last interaction
      const lastInteraction =
        responses.length > 0 ? responses[responses.length - 1].timestamp : undefined;

      // Count errors
      const errorCount = responses.filter(response => response.errorDetails).length;

      return {
        payment,
        analytics: {
          totalGatewayInteractions: totalInteractions,
          operationCounts,
          averageResponseTime,
          successRate,
          lastInteraction,
          errorCount,
        },
      };
    } catch (error) {
      logger.error('Failed to get payment analytics', {
        paymentId,
        error: error.message,
      });
      throw error;
    }
  }


  /**
   * Format payment expiry date for user display
   * @param expiryDate - Payment expiry date in UTC
   * @param userId - User ID for timezone context
   * @returns Formatted expiry date string in user's timezone
   */
  public formatPaymentExpiryForUser(expiryDate: Date, userId?: string): string {
    const timezoneContext: TimezoneContext = {
      userId: userId,
      role: UserRole.CUSTOMER, // Default to customer for payment context
    };

    return timezoneManager.formatDateForUser(expiryDate, timezoneContext, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short',
    });
  }

  /**
   * Format payment timestamp for user display
   * @param timestamp - Payment timestamp in UTC
   * @param userId - User ID for timezone context
   * @returns Formatted timestamp string in user's timezone
   */
  public formatPaymentTimestampForUser(timestamp: Date, userId?: string): string {
    const timezoneContext: TimezoneContext = {
      userId: userId,
      role: UserRole.CUSTOMER, // Default to customer for payment context
    };

    return timezoneManager.formatDateForUser(timestamp, timezoneContext, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short',
    });
  }

  /**
   * Get payment with timezone-formatted dates
   * @param paymentId - Payment ID
   * @param userId - User ID for timezone context
   * @returns Payment with formatted dates
   */
  public async getPaymentWithFormattedDates(paymentId: string, userId?: string): Promise<any> {
    try {
      this.validateObjectId(paymentId, 'paymentId');

      const payment = await Payment.findById(paymentId).lean();
      if (!payment) {
        throw new NotFoundError('Payment not found', {
          code: 'PAYMENT_NOT_FOUND',
          details: { paymentId },
        });
      }

      // Format dates for user timezone
      const formattedPayment = {
        ...payment,
        createdAt: this.formatPaymentTimestampForUser(payment.createdAt, userId),
        updatedAt: this.formatPaymentTimestampForUser(payment.updatedAt, userId),
      };

      logger.debug('Payment retrieved with formatted dates', {
        paymentId,
        userId,
        originalCreatedAt: payment.createdAt.toISOString(),
        formattedCreatedAt: formattedPayment.createdAt,
      });

      return formattedPayment;
    } catch (error) {
      logger.error('Failed to get payment with formatted dates', {
        paymentId,
        userId,
        error: error.message,
      });
      throw error;
    }
  }

  public static getInstance(): PaymentService {
    if (!PaymentService.instance) {
      PaymentService.instance = new PaymentService();
    }
    return PaymentService.instance;
  }
}

export const paymentService = PaymentService.getInstance();
