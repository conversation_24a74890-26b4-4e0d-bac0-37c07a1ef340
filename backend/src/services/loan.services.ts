import mongoose, { Types } from 'mongoose';
import { Loan, Installment, LoanPayment, Payment, User } from '../models';
import { LoanStatus, InstallmentStatus, PaymentStatus, PaymentMethod } from '../enums/enums';
import logger from '../config/logger';
import {
  NotFoundError,
  BadRequestError,
  ValidationError,
} from '../errors/app_errors';


import { round2 } from '../utils/money_utils';
import { addMonthsAnchored, daysBetween } from '../utils/date_utils';
import { paymentService } from './payment.services';

interface CreateLoanParams {
  customerId: string | Types.ObjectId;
  principal: number;
  termMonths: number;
  orderId?: string | Types.ObjectId;
}

interface LoanPaymentParams {
  loanId: string | Types.ObjectId;
  paymentAmount: number;
  paymentMethod: PaymentMethod;
  installmentId?: string | Types.ObjectId;
}

interface InitiateLoanPaymentParams {
  loanId: string | Types.ObjectId;
  paymentAmount: number;
  mobile: string;
  installmentId?: string | Types.ObjectId;
}

class LoanService {
  constructor() {}

  /**
   * Create a new loan with equal principal installments
   */
  public async createLoan(params: CreateLoanParams) {
    const session = await mongoose.startSession();

    try {
      session.startTransaction();

      // Validate customer exists
      const customer = await User.findById(params.customerId).session(session);
      if (!customer) {
        throw new NotFoundError('Customer not found');
      }

      // Validate principal amount
      if (params.principal <= 0) {
        throw new ValidationError('Principal amount must be greater than 0');
      }

      if (params.termMonths <= 0) {
        throw new ValidationError('Term months must be greater than 0');
      }

      // Create the loan
      const loan = new Loan({
        customerId: params.customerId,
        orderId: params.orderId,
        principal: params.principal,
        termMonths: params.termMonths,
        startDate: new Date(),
        outstandingPrincipal: params.principal,
        status: LoanStatus.ACTIVE,
      });

      const savedLoan = await loan.save({ session });

      // Generate installments with equal principal amounts (rounded to 2 decimals)
      const slice = round2(params.principal / params.termMonths);
      const installments: any[] = [];
      let allocated = 0;

      for (let i = 0; i < params.termMonths; i++) {
        const isLast = i === params.termMonths - 1;
        const amountDue = isLast ? round2(params.principal - allocated) : slice;
        allocated = round2(allocated + amountDue);

        // Anchor due dates to loan.startDate to avoid drift
        const dueDate = addMonthsAnchored(savedLoan.startDate, i);

        const installment = new Installment({
          loanId: savedLoan._id,
          seq: i + 1,
          dueDate,
          amountDue,
          amountPaid: 0,
          status: InstallmentStatus.DUE,
          isOverdue: false,
        });

        installments.push(installment);
      }

      await Installment.insertMany(installments, { session });

      await session.commitTransaction();

      logger.info('Loan created successfully', {
        loanId: savedLoan._id,
        customerId: params.customerId,
        principal: params.principal,
        termMonths: params.termMonths,
      });

      return savedLoan;
    } catch (error) {
      await session.abortTransaction();
      logger.error('Error creating loan:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Get loans for a customer or all loans (admin)
   */
  public async getLoans(filters: {
    customerId?: string;
    status?: LoanStatus;
    limit?: number;
    skip?: number;
  }) {
    const query: any = {};

    if (filters.customerId) {
      query.customerId = filters.customerId;
    }

    if (filters.status) {
      query.status = filters.status;
    }

    const loans = await Loan.find(query)
      .populate('customerId', 'phone email username')
      .populate('orderId')
      .sort({ createdAt: -1 })
      .limit(filters.limit || 50)
      .skip(filters.skip || 0);

    return loans;
  }

  /**
   * Get loan details with installments
   */
  public async getLoanDetails(loanId: string | Types.ObjectId) {
    const loan = await Loan.findById(loanId)
      .populate('customerId', 'phone email username')
      .populate('orderId');

    if (!loan) {
      throw new NotFoundError('Loan not found');
    }

    const installments = await Installment.find({ loanId })
      .sort({ seq: 1 });

    const payments = await LoanPayment.find({ loan: loanId })
      .populate('payment')
      .sort({ paymentDate: -1 });

    return {
      loan,
      installments,
      payments,
    };
  }

  /**
   * Initiate WaaFi preauthorization for loan payment
   */
  public async initiateLoanPayment(params: InitiateLoanPaymentParams) {
    const session = await mongoose.startSession();

    try {
      session.startTransaction();

      // Validate loan
      const loan = await Loan.findById(params.loanId).session(session);
      if (!loan) {
        throw new NotFoundError('Loan not found');
      }

      if (loan.status !== LoanStatus.ACTIVE) {
        throw new BadRequestError('Loan is not active');
      }

      const paymentAmount = round2(params.paymentAmount);
      if (paymentAmount <= 0) {
        throw new ValidationError('Payment amount must be greater than 0');
      }

      if (paymentAmount > loan.outstandingPrincipal) {
        throw new ValidationError('Payment amount cannot exceed outstanding principal');
      }

      // Validate installment if provided
      if (params.installmentId) {
        const installment = await Installment.findById(params.installmentId).session(session);
        if (!installment) {
          throw new NotFoundError('Installment not found');
        }
        if (String(installment.loanId) !== String(loan._id)) {
          throw new BadRequestError('Installment does not belong to this loan');
        }
      }

      // Create Payment record with loan metadata
      const payment = new Payment({
        userId: loan.customerId,
        method: PaymentMethod.WAAFI_PURCHASE,
        amount: paymentAmount,
        status: PaymentStatus.PENDING,
        metadata: {
          loanId: params.loanId,
          installmentId: params.installmentId,
          allocationStrategy: params.installmentId ? 'TARGETED' : 'AUTO',
        },
      });

      const savedPayment = await payment.save({ session });

      await session.commitTransaction();

      // Initiate WaaFi purchase (immediate payment)
      const result = await paymentService.initiatePurchase(
        savedPayment._id.toString(),
        params.mobile,
        paymentAmount
      );

      logger.info('Loan payment purchase initiated', {
        loanId: params.loanId,
        paymentId: savedPayment._id,
        amount: paymentAmount,
      });

      return {
        payment: result.payment,
        cashierUrl: result.cashierUrl,
        transactionId: result.transactionId,
      };
    } catch (error) {
      await session.abortTransaction();
      logger.error('Error initiating loan payment:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Apply captured payment to loan installments
   */
  public async applyCapturedPayment(paymentId: string | Types.ObjectId) {
    const session = await mongoose.startSession();

    try {
      session.startTransaction();

      // Get payment with loan metadata
      const payment = await Payment.findById(paymentId).session(session);
      if (!payment) {
        throw new NotFoundError('Payment not found');
      }

      if (payment.status !== PaymentStatus.CAPTURED) {
        throw new BadRequestError('Payment is not captured');
      }

      const loanId = payment.metadata?.loanId;
      if (!loanId) {
        throw new BadRequestError('Payment does not contain loan information');
      }

      // Get loan
      const loan = await Loan.findById(loanId).session(session);
      if (!loan) {
        throw new NotFoundError('Loan not found');
      }

      if (loan.status !== LoanStatus.ACTIVE) {
        throw new BadRequestError('Loan is not active');
      }

      const paymentAmount = round2(payment.amount);
      const installmentId = payment.metadata?.installmentId;
      const allocationStrategy = payment.metadata?.allocationStrategy || 'AUTO';

      // Apply payment using existing allocation logic
      let remainingToApply = paymentAmount;

      const applyToInstallments = async (installments: any[]) => {
        for (const inst of installments) {
          if (remainingToApply <= 0) break;

          const remainingInst = round2(inst.amountDue - inst.amountPaid);
          if (remainingInst <= 0) continue;

          const apply = Math.min(remainingToApply, remainingInst);
          inst.amountPaid = round2(inst.amountPaid + apply);
          inst.status = inst.amountPaid >= inst.amountDue ? InstallmentStatus.PAID : InstallmentStatus.PARTIAL;
          if (inst.amountPaid > 0) inst.isOverdue = false;

          const today = new Date();
          const daysLate = daysBetween(inst.dueDate, today);

          await new LoanPayment({
            loan: loan._id,
            installmentId: inst._id,
            payment: payment._id,
            paymentAmount: apply,
            paymentDate: today,
            paymentMethod: PaymentMethod.WAAFI_PURCHASE,
            status: PaymentStatus.CAPTURED,
            dueDate: inst.dueDate,
            daysLate,
          }).save({ session });

          remainingToApply = round2(remainingToApply - apply);
          await inst.save({ session });
        }
      };

      if (allocationStrategy === 'TARGETED' && installmentId) {
        const target = await Installment.findById(installmentId).session(session);
        if (!target) throw new NotFoundError('Target installment not found');
        if (String(target.loanId) !== String(loan._id)) throw new BadRequestError('Installment does not belong to this loan');
        await applyToInstallments([target]);

        if (remainingToApply > 0) {
          const nextInsts = await Installment.find({
            loanId: loan._id,
            status: { $in: [InstallmentStatus.DUE, InstallmentStatus.PARTIAL] },
          })
            .sort({ seq: 1 })
            .session(session);
          await applyToInstallments(nextInsts);
        }
      } else {
        const insts = await Installment.find({
          loanId: loan._id,
          status: { $in: [InstallmentStatus.DUE, InstallmentStatus.PARTIAL, InstallmentStatus.OVERDUE] },
        })
          .sort({ seq: 1 })
          .session(session);
        await applyToInstallments(insts);
      }

      const applied = round2(paymentAmount - remainingToApply);
      if (applied <= 0) throw new BadRequestError('Nothing applied');

      loan.outstandingPrincipal = round2(loan.outstandingPrincipal - applied);
      if (loan.outstandingPrincipal === 0) loan.status = LoanStatus.PAID;

      await loan.save({ session });

      await session.commitTransaction();

      logger.info('Captured payment applied to loan', {
        loanId,
        paymentId,
        applied,
        remainingBalance: loan.outstandingPrincipal,
      });

      return this.getLoanDetails(loanId.toString());
    } catch (error) {
      await session.abortTransaction();
      logger.error('Error applying captured payment to loan:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Process a loan payment (CASH payments only - immediate allocation)
   */
  public async processLoanPayment(params: LoanPaymentParams) {
    const session = await mongoose.startSession();

    try {
      session.startTransaction();

      // Get loan
      const loan = await Loan.findById(params.loanId).session(session);
      if (!loan) {
        throw new NotFoundError('Loan not found');
      }

      if (loan.status !== LoanStatus.ACTIVE) {
        throw new BadRequestError('Loan is not active');
      }

      let remainingToApply = round2(params.paymentAmount);
      if (remainingToApply <= 0) {
        throw new ValidationError('Payment amount must be greater than 0');
      }

      if (remainingToApply > loan.outstandingPrincipal) {
        throw new ValidationError('Payment amount cannot exceed outstanding principal');
      }

      // Create payment record (captured)
      const payment = new Payment({
        userId: loan.customerId,
        method: params.paymentMethod,
        amount: remainingToApply,
        status: PaymentStatus.CAPTURED,
      });

      const savedPayment = await payment.save({ session });

      const applyToInstallments = async (installments: any[]) => {
        for (const inst of installments) {
          if (remainingToApply <= 0) break;

          const remainingInst = round2(inst.amountDue - inst.amountPaid);
          if (remainingInst <= 0) continue;

          const apply = Math.min(remainingToApply, remainingInst);
          inst.amountPaid = round2(inst.amountPaid + apply);
          inst.status = inst.amountPaid >= inst.amountDue ? InstallmentStatus.PAID : InstallmentStatus.PARTIAL;
          if (inst.amountPaid > 0) inst.isOverdue = false;

          const today = new Date();
          const daysLate = daysBetween(inst.dueDate, today);

          await new LoanPayment({
            loan: loan._id,
            installmentId: inst._id,
            payment: savedPayment._id,
            paymentAmount: apply,
            paymentDate: today,
            paymentMethod: params.paymentMethod,
            status: PaymentStatus.CAPTURED,
            dueDate: inst.dueDate,
            daysLate,
          }).save({ session });

          remainingToApply = round2(remainingToApply - apply);
          await inst.save({ session });
        }
      };

      if (params.installmentId) {
        const target = await Installment.findById(params.installmentId).session(session);
        if (!target) throw new NotFoundError('Installment not found');
        if (String(target.loanId) !== String(loan._id)) throw new BadRequestError('Installment does not belong to this loan');
        await applyToInstallments([target]);

        if (remainingToApply > 0) {
          const nextInsts = await Installment.find({
            loanId: loan._id,
            status: { $in: [InstallmentStatus.DUE, InstallmentStatus.PARTIAL] },
          })
            .sort({ seq: 1 })
            .session(session);
          await applyToInstallments(nextInsts);
        }
      } else {
        const insts = await Installment.find({
          loanId: loan._id,
          status: { $in: [InstallmentStatus.DUE, InstallmentStatus.PARTIAL, InstallmentStatus.OVERDUE] },
        })
          .sort({ seq: 1 })
          .session(session);
        await applyToInstallments(insts);
      }

      const applied = round2(params.paymentAmount - remainingToApply);
      if (applied <= 0) throw new BadRequestError('Nothing applied');

      loan.outstandingPrincipal = round2(loan.outstandingPrincipal - applied);
      if (loan.outstandingPrincipal === 0) loan.status = LoanStatus.PAID;

      await loan.save({ session });

      await session.commitTransaction();

      logger.info('Loan payment processed successfully', {
        loanId: params.loanId,
        paymentAmount: params.paymentAmount,
      });

      return this.getLoanDetails(params.loanId);
    } catch (error) {
      await session.abortTransaction();
      logger.error('Error processing loan payment:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Update installment payment status
   */


  /**
   * Get payment schedule for a loan
   */
  public async getPaymentSchedule(loanId: string | Types.ObjectId) {
    const loan = await Loan.findById(loanId);
    if (!loan) {
      throw new NotFoundError('Loan not found');
    }

    const installments = await Installment.find({ loanId })
      .sort({ seq: 1 });

    return {
      loan: {
        id: loan._id,
        principal: loan.principal,
        termMonths: loan.termMonths,
        outstandingPrincipal: loan.outstandingPrincipal,
        status: loan.status,
      },
      schedule: installments.map(installment => ({
        seq: installment.seq,
        dueDate: installment.dueDate,
        amountDue: installment.amountDue,
        amountPaid: installment.amountPaid,
        status: installment.status,
        remainingAmount: installment.amountDue - installment.amountPaid,
      })),
    };
  }
}

export const loanService = new LoanService();
