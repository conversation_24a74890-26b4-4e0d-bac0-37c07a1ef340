import { AppMessageService } from '../constants/app_message.service';
import { smsService } from './sms.services';
import { emailService } from './email.services';
import { notificationService } from './notification.services';
import { User } from '../models';
import { UserRole } from '../enums/enums';
import { BadRequestError, InternalServerError } from '../errors/app_errors';
import logger from '../config/logger';

/**
 * Notification channels supported by the dispatcher
 */
type NotificationChannel = 'sms' | 'email' | 'push' | 'all';

/**
 * Notification types with predefined templates
 */
type NotificationType =
  | 'otp_verification'
  | 'order_confirmed'
  | 'order_delivered'
  | 'order_cancelled'
  | 'delivery_assigned'
  | 'delivery_started'
  | 'payment_confirmed'
  | 'payment_failed'
  | 'low_stock_alert'
  | 'out_of_stock_alert'
  | 'welcome_message'
  | 'generic_notification'
  | 'emergency_alert'
  | 'maintenance_notification';

/**
 * Notification recipient interface
 */
interface NotificationRecipient {
  userId?: string;
  phone?: string;
  email?: string;
  fcmToken?: string;
  role?: UserRole;
  language?: 'so' | 'en';
}

/**
 * Notification payload interface
 */
interface NotificationPayload {
  type: NotificationType;
  channels: NotificationChannel[];
  recipients: NotificationRecipient[];
  data: Record<string, any>;
  options?: {
    priority?: 'high' | 'normal' | 'low';
    retryOnFailure?: boolean;
    scheduleAt?: Date;
    includePhoneInFooter?: boolean;
  };
}

/**
 * Notification result interface
 */
interface NotificationResult {
  success: boolean;
  channels: {
    sms?: { success: boolean; messageId?: string; error?: string };
    email?: { success: boolean; messageId?: string; error?: string };
    push?: { success: boolean; messageId?: string; error?: string };
  };
  totalRecipients: number;
  successfulDeliveries: number;
  failedDeliveries: number;
  errors: string[];
}

/**
 * Unified Notification Dispatcher Service
 * Handles SMS, Email, and Push notifications with template-based messaging
 */
class NotificationDispatcherService {
  private static instance: NotificationDispatcherService;
  private metrics = {
    totalSent: 0,
    totalFailed: 0,
    channelMetrics: {
      sms: { sent: 0, failed: 0 },
      email: { sent: 0, failed: 0 },
      push: { sent: 0, failed: 0 },
    },
  };

  private constructor() {}

  public static getInstance(): NotificationDispatcherService {
    if (!NotificationDispatcherService.instance) {
      NotificationDispatcherService.instance = new NotificationDispatcherService();
    }
    return NotificationDispatcherService.instance;
  }

  /**
   * Send notification using the unified dispatcher
   */
  public async sendNotification(payload: NotificationPayload): Promise<NotificationResult> {
    try {
      this.validatePayload(payload);

      const results: NotificationResult = {
        success: true,
        channels: {},
        totalRecipients: payload.recipients.length,
        successfulDeliveries: 0,
        failedDeliveries: 0,
        errors: [],
      };

      // Process each recipient
      for (const recipient of payload.recipients) {
        const recipientResults = await this.processRecipient(recipient, payload);

        // Merge results
        this.mergeResults(results, recipientResults);
      }

      // Update metrics
      this.updateMetrics(results);

      // Determine overall success
      results.success = results.failedDeliveries === 0;

      logger.info('Notification dispatch completed', {
        type: payload.type,
        channels: payload.channels,
        totalRecipients: results.totalRecipients,
        successful: results.successfulDeliveries,
        failed: results.failedDeliveries,
      });

      return results;
    } catch (error) {
      logger.error('Notification dispatch failed', {
        type: payload.type,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Process notification for a single recipient
   */
  private async processRecipient(
    recipient: NotificationRecipient,
    payload: NotificationPayload
  ): Promise<Partial<NotificationResult>> {
    const results: Partial<NotificationResult> = {
      channels: {},
      successfulDeliveries: 0,
      failedDeliveries: 0,
      errors: [],
    };

    // Get user data if userId is provided
    let userData = null;
    if (recipient.userId) {
      userData = await User.findById(recipient.userId).lean();
      if (userData) {
        recipient.phone = recipient.phone || userData.phone;
        recipient.email = recipient.email || userData.email;
        recipient.role = recipient.role || userData.role;
        recipient.fcmToken = recipient.fcmToken || userData.notification?.fcmToken;
      }
    }

    // Create message service instance
    const messageService = new AppMessageService({
      lang: recipient.language || 'so',
      recipientRole: recipient.role || 'customer',
      includePhoneInFooter: payload.options?.includePhoneInFooter ?? true,
    });

    // Generate messages for each channel
    const messages = this.generateMessages(messageService, payload.type, payload.data);

    // Send to each requested channel
    for (const channel of payload.channels) {
      if (channel === 'all') {
        // Send to all available channels
        await this.sendToAllChannels(recipient, messages, results);
      } else {
        await this.sendToChannel(channel, recipient, messages, results);
      }
    }

    return results;
  }

  /**
   * Generate messages for different channels based on notification type
   */
  private generateMessages(
    messageService: AppMessageService,
    type: NotificationType,
    data: Record<string, any>
  ): {
    sms: string;
    email?: { subject: string; body: string };
    push: { title: string; body: string };
  } {
    let smsMessage = '';
    let emailTemplate = null;
    let pushMessage = { title: '', body: '' };

    switch (type) {
      case 'otp_verification':
        smsMessage = messageService.otpMessage(data.otp, data.expiresIn);
        pushMessage = {
          title: `🔐 Verification Code`,
          body: `Your code: ${data.otp}`,
        };
        break;

      case 'order_confirmed':
        smsMessage = messageService.orderConfirmed(data.orderId);
        pushMessage = {
          title: '✅ Order Confirmed',
          body: `Order ${data.orderId} has been confirmed`,
        };
        break;

      case 'order_delivered':
        smsMessage = messageService.orderDelivered(data.orderId);
        pushMessage = {
          title: '🚚 Order Delivered',
          body: `Order ${data.orderId} has been delivered`,
        };
        break;

      case 'order_cancelled':
        smsMessage = messageService.orderCancelled(data.orderId, data.reason);
        pushMessage = {
          title: '❌ Order Cancelled',
          body: `Order ${data.orderId} has been cancelled`,
        };
        break;

      case 'delivery_assigned':
        smsMessage = messageService.deliveryAssigned(data.orderId, data.customerName, data.address);
        pushMessage = {
          title: '📦 New Delivery Assignment',
          body: `Order ${data.orderId} assigned to you`,
        };
        break;

      case 'delivery_started':
        smsMessage = messageService.deliveryStarted(data.orderId, data.agentName, data.agentPhone);
        pushMessage = {
          title: '🚛 Delivery Started',
          body: `Your order ${data.orderId} is on the way`,
        };
        break;

      case 'payment_confirmed':
        smsMessage = messageService.paymentConfirmed(data.orderId, data.amount);
        pushMessage = {
          title: '💳 Payment Confirmed',
          body: `Payment for order ${data.orderId} confirmed`,
        };
        break;

      case 'payment_failed':
        smsMessage = messageService.paymentFailed(data.orderId, data.reason);
        pushMessage = {
          title: '❌ Payment Failed',
          body: `Payment for order ${data.orderId} failed`,
        };
        break;

      case 'low_stock_alert':
        smsMessage = messageService.lowStockAlert(data.item, data.quantity);
        emailTemplate = messageService.lowStockEmail(data.item, data.quantity);
        pushMessage = {
          title: '⚠️ Low Stock Alert',
          body: `${data.item}: Only ${data.quantity} remaining`,
        };
        break;

      case 'out_of_stock_alert':
        smsMessage = messageService.outOfStockAlert(data.item);
        pushMessage = {
          title: '🚨 Out of Stock',
          body: `${data.item} is out of stock`,
        };
        break;

      case 'welcome_message':
        smsMessage = messageService.welcomeMessage(data.userName, data.userRole);
        pushMessage = {
          title: '🎉 Welcome!',
          body: `Welcome to Ciribey Gas Delivery, ${data.userName}`,
        };
        break;

      case 'generic_notification':
        smsMessage = messageService.genericNotification(data.title, data.message);
        pushMessage = {
          title: data.title,
          body: data.message,
        };
        break;

      case 'emergency_alert':
        smsMessage = messageService.emergencyAlert(data.message);
        pushMessage = {
          title: '🚨 URGENT',
          body: data.message,
        };
        break;

      case 'maintenance_notification':
        smsMessage = messageService.maintenanceNotification(data.startTime, data.duration);
        pushMessage = {
          title: '🔧 System Maintenance',
          body: `Maintenance scheduled: ${data.startTime}`,
        };
        break;

      default:
        throw new BadRequestError(`Unsupported notification type: ${type}`);
    }

    return {
      sms: smsMessage,
      email: emailTemplate,
      push: pushMessage,
    };
  }

  /**
   * Send notification to all available channels for a recipient
   */
  private async sendToAllChannels(
    recipient: NotificationRecipient,
    messages: any,
    results: Partial<NotificationResult>
  ): Promise<void> {
    const channels: NotificationChannel[] = [];

    if (recipient.phone) channels.push('sms');
    if (recipient.email) channels.push('email');
    if (recipient.fcmToken) channels.push('push');

    for (const channel of channels) {
      await this.sendToChannel(channel, recipient, messages, results);
    }
  }

  /**
   * Send notification to a specific channel
   */
  private async sendToChannel(
    channel: NotificationChannel,
    recipient: NotificationRecipient,
    messages: any,
    results: Partial<NotificationResult>
  ): Promise<void> {
    try {
      switch (channel) {
        case 'sms':
          if (recipient.phone && messages.sms) {
            const result = await smsService.sendSms(recipient.phone, messages.sms);
            results.channels!.sms = {
              success: true,
              messageId: result.messageId,
            };
            results.successfulDeliveries!++;
          } else {
            results.channels!.sms = {
              success: false,
              error: 'Phone number not available',
            };
            results.failedDeliveries!++;
          }
          break;

        case 'email':
          if (recipient.email && messages.email) {
            const result = await emailService.sendEmail(recipient.email, messages.email);
            results.channels!.email = {
              success: result.success,
              messageId: result.messageId,
              error: result.error,
            };
            if (result.success) {
              results.successfulDeliveries!++;
            } else {
              results.failedDeliveries!++;
              results.errors!.push(result.error || 'Email delivery failed');
            }
          } else {
            results.channels!.email = {
              success: false,
              error: 'Email address not available or no email template',
            };
            results.failedDeliveries!++;
          }
          break;

        case 'push':
          if (recipient.userId && recipient.fcmToken && messages.push) {
            const result = await notificationService.sendToUser(recipient.userId, {
              title: messages.push.title,
              body: messages.push.body,
            });
            results.channels!.push = {
              success: result.success,
              messageId: result.details?.messageId,
            };
            if (result.success) {
              results.successfulDeliveries!++;
            } else {
              results.failedDeliveries!++;
              results.errors!.push('Push notification delivery failed');
            }
          } else {
            results.channels!.push = {
              success: false,
              error: 'FCM token or user ID not available',
            };
            results.failedDeliveries!++;
          }
          break;

        default:
          throw new BadRequestError(`Unsupported channel: ${channel}`);
      }
    } catch (error) {
      logger.error(`Failed to send notification via ${channel}`, {
        channel,
        recipient: recipient.userId || recipient.phone || recipient.email,
        error: error.message,
      });

      if (channel === 'sms' || channel === 'email' || channel === 'push') {
        results.channels![channel] = {
          success: false,
          error: error.message,
        };
      }
      results.failedDeliveries!++;
      results.errors!.push(`${channel}: ${error.message}`);
    }
  }

  /**
   * Merge results from individual recipient processing
   */
  private mergeResults(
    mainResults: NotificationResult,
    recipientResults: Partial<NotificationResult>
  ): void {
    mainResults.successfulDeliveries += recipientResults.successfulDeliveries || 0;
    mainResults.failedDeliveries += recipientResults.failedDeliveries || 0;
    mainResults.errors.push(...(recipientResults.errors || []));

    // Merge channel results (this is simplified - in production you might want more detailed aggregation)
    Object.keys(recipientResults.channels || {}).forEach(channel => {
      if (!mainResults.channels[channel as keyof typeof mainResults.channels]) {
        mainResults.channels[channel as keyof typeof mainResults.channels] =
          recipientResults.channels![channel as keyof typeof recipientResults.channels];
      }
    });
  }

  /**
   * Update service metrics
   */
  private updateMetrics(results: NotificationResult): void {
    this.metrics.totalSent += results.successfulDeliveries;
    this.metrics.totalFailed += results.failedDeliveries;

    // Update channel-specific metrics
    Object.keys(results.channels).forEach(channel => {
      const channelResult = results.channels[channel as keyof typeof results.channels];
      if (channelResult) {
        if (channelResult.success) {
          this.metrics.channelMetrics[channel as keyof typeof this.metrics.channelMetrics].sent++;
        } else {
          this.metrics.channelMetrics[channel as keyof typeof this.metrics.channelMetrics].failed++;
        }
      }
    });
  }

  /**
   * Validate notification payload
   */
  private validatePayload(payload: NotificationPayload): void {
    if (!payload.type) {
      throw new BadRequestError('Notification type is required');
    }

    if (!payload.channels || payload.channels.length === 0) {
      throw new BadRequestError('At least one notification channel is required');
    }

    if (!payload.recipients || payload.recipients.length === 0) {
      throw new BadRequestError('At least one recipient is required');
    }

    if (!payload.data) {
      throw new BadRequestError('Notification data is required');
    }

    // Validate recipients have required contact information
    for (const recipient of payload.recipients) {
      if (!recipient.userId && !recipient.phone && !recipient.email && !recipient.fcmToken) {
        throw new BadRequestError('Each recipient must have at least one contact method');
      }
    }
  }

  /**
   * Get service metrics
   */
  public getMetrics() {
    return {
      ...this.metrics,
      successRate:
        this.metrics.totalSent > 0
          ? (this.metrics.totalSent - this.metrics.totalFailed) / this.metrics.totalSent
          : 1,
      channelSuccessRates: {
        sms:
          this.metrics.channelMetrics.sms.sent > 0
            ? (this.metrics.channelMetrics.sms.sent - this.metrics.channelMetrics.sms.failed) /
              this.metrics.channelMetrics.sms.sent
            : 1,
        email:
          this.metrics.channelMetrics.email.sent > 0
            ? (this.metrics.channelMetrics.email.sent - this.metrics.channelMetrics.email.failed) /
              this.metrics.channelMetrics.email.sent
            : 1,
        push:
          this.metrics.channelMetrics.push.sent > 0
            ? (this.metrics.channelMetrics.push.sent - this.metrics.channelMetrics.push.failed) /
              this.metrics.channelMetrics.push.sent
            : 1,
      },
    };
  }

  /**
   * Check health of all notification channels
   */
  public async checkHealth(): Promise<{
    overall: boolean;
    channels: {
      sms: boolean;
      email: boolean;
      push: boolean;
    };
  }> {
    const health = {
      overall: true,
      channels: {
        sms: false,
        email: false,
        push: false,
      },
    };

    try {
      health.channels.sms = await smsService.checkHealth();
      health.channels.email = await emailService.checkHealth();
      health.channels.push = true; // Push service doesn't have a health check method

      health.overall = health.channels.sms && health.channels.email && health.channels.push;
    } catch (error) {
      logger.error('Health check failed', { error: error.message });
      health.overall = false;
    }

    return health;
  }
}

export const notificationDispatcher = NotificationDispatcherService.getInstance();
