import { NotFoundError, BadRequestError, InternalServerError } from '../errors/app_errors';
import { User, Notification } from '../models';
import * as notificationUtil from '../utils/notification_utils';
import { NotificationTopic } from '../utils/notification_utils';
import { UserRole } from '../enums/enums';
import logger from '../config/logger';
import { timezoneManager, TimezoneContext } from '../utils/timezone_utils';

class NotificationService {
  /**
   * Send notification to a specific user
   */
  async sendToUser(
    userId: string,
    payload: {
      title: string;
      body: string;
      data?: Record<string, string>;
      imageUrl?: string;
    }
  ): Promise<notificationUtil.NotificationResult> {
    try {
      // Validate input
      if (!userId) throw new BadRequestError('User ID is required');
      if (!payload?.title || !payload?.body) {
        throw new BadRequestError('Title and body are required');
      }

      const user = await User.findById(userId).select('notification');
      if (!user) throw new NotFoundError('User not found');

      if (!user.notification) {
        throw new NotFoundError('Notification settings not configured');
      }

      if (!user.notification.isEnabled) {
        throw new BadRequestError('Notifications are disabled for this user');
      }

      if (!user.notification.fcmToken) {
        throw new BadRequestError('FCM token not registered for this user');
      }

      // Create notification record
      const notificationRecord = await Notification.create({
        userId,
        title: payload.title,
        body: payload.body,
        data: payload.data,
        imageUrl: payload.imageUrl,
        status: 'pending',
      });

      try {
        // Send the actual push notification
        const result = await notificationUtil.sendPushNotification(
          user.notification.fcmToken,
          payload
        );

        // Update notification status
        await Notification.findByIdAndUpdate(notificationRecord._id, {
          status: 'delivered',
          deliveredAt: new Date(),
          result,
        });

        logger.info(`Notification sent to user ${userId}`, {
          notificationId: notificationRecord._id,
          title: payload.title,
        });

        return {
          success: true,
          //   notificationId: notificationRecord._id,
          details: result,
        };
      } catch (error) {
        await Notification.findByIdAndUpdate(notificationRecord._id, {
          status: 'failed',
          error: error.message,
        });

        logger.error(`Failed to send notification to user ${userId}`, {
          error: error.message,
          notificationId: notificationRecord._id,
        });

        throw new InternalServerError('Failed to send notification');
      }
    } catch (error) {
      logger.error('NotificationService.sendToUser error', {
        userId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Send notification to all users subscribed to a topic
   */
  async sendToTopic(
    topic: NotificationTopic,
    payload: {
      title: string;
      body: string;
      data?: Record<string, string>;
      imageUrl?: string;
    },
    options?: {
      onlyActiveUsers?: boolean;
    }
  ): Promise<notificationUtil.NotificationResult> {
    try {
      // Validate input
      if (!Object.values(NotificationTopic).includes(topic)) {
        throw new BadRequestError('Invalid notification topic');
      }

      if (!payload?.title || !payload?.body) {
        throw new BadRequestError('Title and body are required');
      }

      // Get all users subscribed to this topic
      const query: any = {
        'notification.topics': topic,
        'notification.isEnabled': true,
      };

      if (options?.onlyActiveUsers) {
        query.isActive = true;
      }

      const users = await User.find(query).select('notification isActive').lean();

      if (!users.length) {
        logger.warn(`No users found subscribed to topic ${topic}`);
        return {
          success: true,
          message: 'No subscribed users found',
          count: 0,
        };
      }

      // Filter users with valid FCM tokens
      const validUsers = users.filter(u => u.notification?.fcmToken);
      const tokens = validUsers.map(u => u.notification.fcmToken);

      if (!tokens.length) {
        logger.warn(`No valid FCM tokens found for topic ${topic}`);
        return {
          success: true,
          message: 'No valid FCM tokens found',
          count: 0,
        };
      }

      // Create bulk notification records
      const notificationRecords = await Notification.insertMany(
        validUsers.map(user => ({
          userId: user._id,
          title: payload.title,
          body: payload.body,
          data: payload.data,
          imageUrl: payload.imageUrl,
          topic,
          status: 'pending',
        }))
      );

      try {
        // Send to topic
        const result = await notificationUtil.sendToTopic(topic, payload);

        // Update all notification records
        await Notification.updateMany(
          { _id: { $in: notificationRecords.map(n => n._id) } },
          {
            status: 'delivered',
            deliveredAt: new Date(),
            result,
          }
        );

        logger.info(`Notification sent to topic ${topic}`, {
          count: validUsers.length,
          title: payload.title,
        });

        return {
          success: true,
          count: validUsers.length,
          details: result,
        };
      } catch (error) {
        await Notification.updateMany(
          { _id: { $in: notificationRecords.map(n => n._id) } },
          {
            status: 'failed',
            error: error.message,
          }
        );

        logger.error(`Failed to send notification to topic ${topic}`, {
          error: error.message,
          count: validUsers.length,
        });

        throw new InternalServerError('Failed to send topic notification');
      }
    } catch (error) {
      logger.error('NotificationService.sendToTopic error', {
        topic,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Update user's FCM token
   */
  async updateFcmToken(userId: string, token: string): Promise<{ success: boolean }> {
    try {
      if (!userId || !token) {
        throw new BadRequestError('User ID and token are required');
      }

      const user = await User.findByIdAndUpdate(
        userId,
        {
          $set: {
            'notification.fcmToken': token,
            'notification.isEnabled': true,
          },
        },
        { new: true }
      );

      if (!user) throw new NotFoundError('User not found');

      logger.info(`Updated FCM token for user ${userId}`);

      return { success: true };
    } catch (error) {
      logger.error('Failed to update FCM token', {
        userId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Subscribe user to notification topic
   */
  async subscribeToTopic(userId: string, topic: NotificationTopic): Promise<{ success: boolean }> {
    try {
      if (!Object.values(NotificationTopic).includes(topic)) {
        throw new BadRequestError('Invalid notification topic');
      }

      const user = await User.findById(userId).select('notification');
      if (!user) throw new NotFoundError('User not found');

      if (!user.notification) {
        throw new BadRequestError('Notification settings not initialized');
      }

      // Check if already subscribed
      if (user.notification.topics?.includes(topic)) {
        return { success: true };
      }

      // Update in database
      await User.findByIdAndUpdate(userId, { $addToSet: { 'notification.topics': topic } });

      // Subscribe with FCM if token exists
      if (user.notification.fcmToken) {
        try {
          await notificationUtil.subscribeToTopic([user.notification.fcmToken], topic);
        } catch (error) {
          logger.error('FCM topic subscription failed', {
            userId,
            topic,
            error: error.message,
          });
          // Continue even if FCM fails - we'll sync later
        }
      }

      logger.info(`User ${userId} subscribed to topic ${topic}`);

      return { success: true };
    } catch (error) {
      logger.error('Failed to subscribe to topic', {
        userId,
        topic,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Toggle notification enable/disable
   */
  async toggleNotifications(userId: string, enabled: boolean): Promise<{ success: boolean }> {
    try {
      const user = await User.findByIdAndUpdate(
        userId,
        { $set: { 'notification.isEnabled': enabled } },
        { new: true }
      );

      if (!user) throw new NotFoundError('User not found');

      logger.info(`Notifications ${enabled ? 'enabled' : 'disabled'} for user ${userId}`);

      return { success: true };
    } catch (error) {
      logger.error('Failed to toggle notifications', {
        userId,
        enabled,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Format notification timestamp for user display
   * @param timestamp - Notification timestamp in UTC
   * @param userId - User ID for timezone context
   * @returns Formatted timestamp string in user's timezone
   */
  formatNotificationTimestampForUser(timestamp: Date, userId?: string): string {
    const timezoneContext: TimezoneContext = {
      userId: userId,
      role: UserRole.CUSTOMER, // Default role for notification context
    };

    return timezoneManager.formatDateForUser(timestamp, timezoneContext, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short',
    });
  }

  /**
   * Get user notifications with timezone-formatted timestamps
   * @param userId - User ID
   * @param options - Query options
   * @returns Notifications with formatted timestamps
   */
  async getUserNotificationsWithFormattedDates(
    userId: string,
    options: {
      page?: number;
      limit?: number;
      status?: 'read' | 'unread';
      topic?: NotificationTopic;
    } = {}
  ): Promise<{
    notifications: any[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      if (!userId) throw new BadRequestError('User ID is required');

      const { page = 1, limit = 20, status, topic } = options;
      const skip = (page - 1) * limit;

      // Build query
      const query: any = { userId };
      if (status) {
        query.status = status === 'read' ? 'read' : { $ne: 'read' };
      }
      if (topic) {
        query.topic = topic;
      }

      // Get notifications and total count
      const [notifications, total] = await Promise.all([
        Notification.find(query).sort({ createdAt: -1 }).skip(skip).limit(limit).lean(),
        Notification.countDocuments(query),
      ]);

      // Format timestamps for user timezone
      const formattedNotifications = notifications.map((notification: any) => ({
        ...notification,
        formattedCreatedAt: this.formatNotificationTimestampForUser(
          new Date(notification.createdAt),
          userId
        ),
        formattedUpdatedAt: this.formatNotificationTimestampForUser(
          new Date(notification.updatedAt),
          userId
        ),
      }));

      const totalPages = Math.ceil(total / limit);

      logger.debug('Retrieved user notifications with formatted dates', {
        userId,
        total,
        page,
        limit,
        totalPages,
        status,
        topic,
      });

      return {
        notifications: formattedNotifications,
        total,
        page,
        totalPages,
      };
    } catch (error) {
      logger.error('Failed to get user notifications with formatted dates', {
        userId,
        options,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Get notification statistics with timezone-aware date filtering
   * @param userId - User ID
   * @param dateRange - Optional date range filter
   * @returns Notification statistics
   */
  async getNotificationStatsWithTimezone(
    userId: string,
    dateRange?: { startDate?: Date | string; endDate?: Date | string }
  ): Promise<{
    total: number;
    unread: number;
    read: number;
    todayCount: number;
    weekCount: number;
  }> {
    try {
      if (!userId) throw new BadRequestError('User ID is required');

      const timezoneContext: TimezoneContext = {
        userId: userId,
        role: UserRole.CUSTOMER,
      };

      // Get today's boundaries in user timezone
      const todayBoundaries = timezoneManager.getDayBoundaries(new Date(), timezoneContext);

      // Get week boundaries
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const weekBoundaries = timezoneManager.getDayBoundaries(weekAgo, timezoneContext);

      // Build base query
      let baseQuery: any = { userId };

      // Add date range filter if provided
      if (dateRange?.startDate || dateRange?.endDate) {
        const dateQuery = timezoneManager.buildDateQuery(
          dateRange.startDate,
          dateRange.endDate,
          timezoneContext
        );
        if (dateQuery) {
          Object.assign(baseQuery, dateQuery);
        }
      }

      // Get statistics
      const [total, unread, todayCount, weekCount] = await Promise.all([
        Notification.countDocuments(baseQuery),
        Notification.countDocuments({ ...baseQuery, status: { $ne: 'read' } }),
        Notification.countDocuments({
          userId,
          createdAt: { $gte: todayBoundaries.start, $lte: todayBoundaries.end },
        }),
        Notification.countDocuments({
          userId,
          createdAt: { $gte: weekBoundaries.start },
        }),
      ]);

      const read = total - unread;

      logger.debug('Generated notification statistics with timezone', {
        userId,
        total,
        unread,
        read,
        todayCount,
        weekCount,
        dateRange,
        timezone: todayBoundaries.timezone,
      });

      return {
        total,
        unread,
        read,
        todayCount,
        weekCount,
      };
    } catch (error) {
      logger.error('Failed to get notification statistics with timezone', {
        userId,
        dateRange,
        error: error.message,
      });
      throw error;
    }
  }
}

export const notificationService = new NotificationService();
