// User Roles
export enum UserRole {
  CUSTOMER = 'customer',
  AGENT = 'agent',
  ADMIN = 'admin',
  SUPERVISOR = 'supervisor',
}

// Order Lifecycle
export enum OrderStatus {
  // Core Flow
  PENDING = 'PENDING', // Order created, awaiting payment
  CONFIRMED = 'CONFIRMED', // Payment successful
  IN_TRANSIT = 'IN_TRANSIT', // Driver dispatched
  DELIVERED = 'DELIVERED', // Successful completion

  // Termination States
  CANCELLED = 'CANCELLED', // User-initiated cancellation
  FAILED = 'FAILED', // System/payment failures
}

export enum EntityType {
  SparePart = 'SPARE_PART',
  Cylinder = 'CYLINDER',
  Package = 'PACKAGE',
}


export enum SparePartStatus {
  AVAILABLE = 'AVAILABLE',
  LOW_STOCK = 'LOW_STOCK',
  OUT_OF_STOCK = 'OUT_OF_STOCK',
  DISCONTINUED = 'DISCONTINUED',
}

export enum SparePartCategory {
  BRASS_CONTROL_VALVE_KIT = 'BRASS_CONTROL_VALVE_KIT',
  REGULATOR_HIGH_PRESSURE = 'REGULATOR_HIGH_PRESSURE',
  REGULATOR_LOW_PRESSURE = 'REGULATOR_LOW_PRESSURE',
  SINGLE_BURNER_GAS_STOVE = 'SINGLE_BURNER_GAS_STOVE',
  THREE_BURNER_LPG_STOVE = 'THREE_BURNER_LPG_STOVE',
  TUBO_2_METER = 'TUBO_2_METER',
  VALUE_BURNER = 'VALUE_BURNER',
}

// Cylinder Types
export enum CylinderType {
  SixKg = '6KG',
  ThirteenKg = '13KG',
  SeventeenKg = '17KG',
  TwentyKg = '20KG',
  TwentyFiveKg = '25KG',
}

export enum CylinderStatus {
  Active = 'ACTIVE',
  Discontinued = 'DISCONTINUED',
  OutOfStock = 'OUT_OF_STOCK',
  
}

export enum CylinderMaterial {
  Metal = 'METAL',
  Plastic = 'PLASTIC',
}

export enum VehicleType {
  BIKE = 'bike',
  CAR = 'car',
  MOTORCYCLE = 'motorcycle',
}

// Payment Methods
export enum PaymentMethod {
  CASH = 'cash',
  WAAFI_PURCHASE = 'waafi_purchase',
  // Legacy: WAAFI_PREAUTH = 'waafi_preauth', (deprecated - use WAAFI_PURCHASE)
}

export enum PaymentStatus {
  PENDING = 'PENDING',
  CAPTURED = 'CAPTURED',
  CANCELLED = 'CANCELLED',
  FAILED = 'FAILED',
  // Legacy: PREAUTHORIZED = 'PREAUTHORIZED', (deprecated - Purchase API goes directly to CAPTURED)
}

// Delivery Verification
export enum DeliveryVerificationMethod {
  QR_CODE = 'qr_code',
  OTP = 'otp',
  BIOMETRIC = 'biometric', // Future-proofing
}

// Loan Management
export enum LoanType {
  PRINCIPAL_ONLY = 'PRINCIPAL_ONLY',
}

export enum LoanStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  PAID = 'PAID',
  CANCELLED = 'CANCELLED',
  DEFAULTED = 'DEFAULTED',
}

export enum InstallmentStatus {
  DUE = 'DUE',
  PARTIAL = 'PARTIAL',
  PAID = 'PAID',
  OVERDUE = 'OVERDUE', // can be computed or flagged by a cron
}

export enum NotificationStatus {
  PENDING = 'pending',
  DELIVERED = 'delivered',
  FAILED = 'failed',
}
