import { Router } from 'express';
import { packageController } from '../controllers/package.controller';
import { authenticate } from '../middleware/auth.middleware';
import { validateRole } from '../middleware/role.middleware';
import { UserRole } from '../enums/enums';
import { uploadSingleImage } from '../services/image-upload.service';

const packageRouter = Router();

// Apply authentication middleware to all package routes
packageRouter.use(authenticate);

// ==================== CRUD OPERATIONS ====================

/**
 * @route   POST /api/v1/packages
 * @desc    Create a new package
 * @access  Private (Admin only)
 * @body    { name, description?, cylinder, includedSpareParts, totalPrice?, costPrice?, discount?, imageUrl?, quantity?, minimumStockLevel? }
 * @file    image (optional multipart/form-data)
 */
packageRouter.post(
  '/',
  validateRole([UserRole.ADMIN]),
  uploadSingleImage,
  packageController.createPackage
);

/**
 * @route   GET /api/v1/packages
 * @desc    Get all packages with filtering and pagination
 * @access  Private (All authenticated users)
 * @query   { search?, cylinder?, isActive?, minPrice?, maxPrice?, page?, limit?, populate? }
 */
packageRouter.get('/', packageController.getPackages);

/**
 * @route   GET /api/v1/packages/analytics
 * @desc    Get package analytics
 * @access  Private (Admin only)
 */
packageRouter.get(
  '/analytics',
  validateRole([UserRole.ADMIN]),
  packageController.getPackageAnalytics
);

/**
 * @route   GET /api/v1/packages/low-stock
 * @desc    Get packages with low stock
 * @access  Private (Admin, Agent)
 */
packageRouter.get(
  '/low-stock',
  validateRole([UserRole.ADMIN, UserRole.AGENT]),
  packageController.getLowStockPackages
);

/**
 * @route   GET /api/v1/packages/sales-statistics
 * @desc    Get package sales statistics
 * @access  Private (Admin only)
 */
packageRouter.get(
  '/sales-statistics',
  validateRole([UserRole.ADMIN]),
  packageController.getPackageSalesStatistics
);

/**
 * @route   GET /api/v1/packages/deleted
 * @desc    Get soft-deleted packages
 * @access  Private (Admin only)
 * @query   { page?, limit? }
 */
packageRouter.get('/deleted', validateRole([UserRole.ADMIN]), packageController.getDeletedPackages);

/**
 * @route   POST /api/v1/packages/cleanup-deleted-parts
 * @desc    Clean up packages that reference deleted spare parts
 * @access  Private (Admin only)
 */
packageRouter.post(
  '/cleanup-deleted-parts',
  validateRole([UserRole.ADMIN]),
  packageController.cleanupPackagesWithDeletedParts
);

/**
 * @route   GET /api/v1/packages/:id
 * @desc    Get package by ID
 * @access  Private (All authenticated users)
 * @query   { populate? }
 */
packageRouter.get('/:id', packageController.getPackageById);

/**
 * @route   DELETE /api/v1/packages/:id
 * @desc    Soft delete a package
 * @access  Private (Admin only)
 */
packageRouter.delete('/:id', validateRole([UserRole.ADMIN]), packageController.deletePackage);

/**
 * @route   POST /api/v1/packages/:id/restore
 * @desc    Restore a soft-deleted package
 * @access  Private (Admin only)
 */
packageRouter.post(
  '/:id/restore',
  validateRole([UserRole.ADMIN]),
  packageController.restorePackage
);

/**
 * @route   DELETE /api/v1/packages/:id/permanent
 * @desc    Permanently delete a package (hard delete)
 * @access  Private (Admin only)
 */
packageRouter.delete(
  '/:id/permanent',
  validateRole([UserRole.ADMIN]),
  packageController.permanentlyDeletePackage
);

/**
 * @route   PUT /api/v1/packages/:id
 * @desc    Update package details
 * @access  Private (Admin only)
 * @body    { name?, description?, includedSpareParts?, discount?, imageUrl? }
 * @file    image (optional multipart/form-data)
 */
packageRouter.put(
  '/:id',
  validateRole([UserRole.ADMIN]),
  uploadSingleImage,
  packageController.updatePackage
);

/**
 * @route   PUT /api/v1/packages/:id/toggle-status
 * @desc    Toggle package active status
 * @access  Private (Admin only)
 */
packageRouter.put(
  '/:id/toggle-status',
  validateRole([UserRole.ADMIN]),
  packageController.togglePackageStatus
);

/**
 * @route   GET /api/v1/packages/:id/availability
 * @desc    Check package availability
 * @access  Private (All authenticated users)
 * @query   { quantity? }
 */
packageRouter.get('/:id/availability', packageController.checkPackageAvailability);

/**
 * @route   GET /api/v1/packages/:id/available-quantity
 * @desc    Get available package quantity
 * @access  Private (All authenticated users)
 */
packageRouter.get('/:id/available-quantity', packageController.getAvailableQuantity);

// ==================== ADMINISTRATIVE INVENTORY MANAGEMENT ====================
// Note: Reservation, release, and sales operations are now handled automatically
// through the order lifecycle. Only administrative operations remain.

/**
 * @route   POST /api/v1/packages/:id/restock
 * @desc    Restock package
 * @access  Private (Admin only)
 * @body    { quantity }
 */
packageRouter.post(
  '/:id/restock',
  validateRole([UserRole.ADMIN]),
  packageController.restockPackage
);

/**
 * @route   POST /api/v1/packages/:id/adjust-stock
 * @desc    Adjust package stock
 * @access  Private (Admin only)
 * @body    { adjustment, reason }
 */
packageRouter.post(
  '/:id/adjust-stock',
  validateRole([UserRole.ADMIN]),
  packageController.adjustPackageStock
);

/**
 * @route   PUT /api/v1/packages/bulk-status
 * @desc    Bulk update package statuses
 * @access  Private (Admin only)
 * @body    { packageIds, isActive }
 */
packageRouter.put(
  '/bulk-status',
  validateRole([UserRole.ADMIN]),
  packageController.bulkUpdatePackageStatus
);

export default packageRouter;
