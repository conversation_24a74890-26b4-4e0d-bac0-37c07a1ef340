import { Router } from 'express';
import { ImageController } from '../controllers/image.controller';
import { uploadSingleImage } from '../services/image-upload.service';
import { authenticate } from '../middleware/auth.middleware';
import { authorizeRoles } from '../middleware/role.middleware';
import { UserRole } from '../enums/enums';
import asyncHandler from '../utils/async-handler';

const router = Router();

/**
 * Image upload and management routes
 * All routes require authentication
 */

/**
 * @route POST /api/v1/images/upload
 * @desc Upload a new image
 * @access Admin only
 * @body {category: ImageCategory, entityId?: string, entityType?: string}
 * @file image (multipart/form-data)
 */
router.post(
  '/upload',
  authenticate,
  authorizeRoles([UserRole.ADMIN]),
  uploadSingleImage,
  asyncHandler(ImageController.uploadImage)
);

/**
 * @route GET /api/v1/images/info/:entityType/:entityId
 * @desc Get image information for a specific entity
 * @access Admin, Agent (read-only)
 * @params {entityType: 'cylinder'|'sparepart'|'package', entityId: string}
 */
router.get(
  '/info/:entityType/:entityId',
  authenticate,
  authorizeRoles([UserRole.ADMIN, UserRole.AGENT]),
  asyncHandler(ImageController.getImageInfo)
);

/**
 * @route PUT /api/v1/images/:entityType/:entityId
 * @desc Replace image for a specific entity
 * @access Admin only
 * @params {entityType: 'cylinder'|'sparepart'|'package', entityId: string}
 * @body {category: ImageCategory}
 * @file image (multipart/form-data)
 */
router.put(
  '/:entityType/:entityId',
  authenticate,
  authorizeRoles([UserRole.ADMIN]),
  uploadSingleImage,
  asyncHandler(ImageController.replaceImage)
);

/**
 * @route DELETE /api/v1/images/:entityType/:entityId
 * @desc Delete image for a specific entity
 * @access Admin only
 * @params {entityType: 'cylinder'|'sparepart'|'package', entityId: string}
 */
router.delete(
  '/:entityType/:entityId',
  authenticate,
  authorizeRoles([UserRole.ADMIN]),
  asyncHandler(ImageController.deleteImage)
);

export default router;
