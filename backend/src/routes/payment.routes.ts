import { Router } from 'express';
import { paymentController } from '../controllers/payment.controller';
import { authenticate } from '../middleware/auth.middleware';
import { validateRole } from '../middleware/role.middleware';
import { UserRole } from '../enums/enums';

const paymentRouter = Router();

// ==================== PAYMENT OPERATIONS ====================

/**
 * @route   POST /api/v1/payments/:id/purchase
 * @desc    Initiate payment purchase (immediate)
 * @access  Private (Customer, Admin)
 * @body    { mobile, amount, deliveryDetails? }
 */
paymentRouter.post(
  '/:id/purchase',
  authenticate,
  validateRole([UserRole.CUSTOMER, UserRole.ADMIN]),
  paymentController.initiatePurchase
);

/**
 * @route   POST /api/v1/payments/:id/cancel
 * @desc    Cancel purchase payment
 * @access  Private (Customer, Admin, Agent)
 * @body    { reason }
 */
paymentRouter.post(
  '/:id/cancel',
  authenticate,
  validateRole([UserRole.CUSTOMER, UserRole.ADMIN, UserRole.AGENT, UserRole.SUPERVISOR]),
  paymentController.cancelPurchase
);


// ==================== GATEWAY RESPONSE ANALYTICS ====================

/**
 * @route   GET /api/v1/payments/:id/gateway-responses
 * @desc    Get raw gateway responses for debugging and audit
 * @access  Private (Admin only)
 * @query   { operation?, provider? }
 */
paymentRouter.get(
  '/:id/gateway-responses',
  authenticate,
  validateRole([UserRole.ADMIN]),
  paymentController.getGatewayResponses
);

/**
 * @route   GET /api/v1/payments/:id/analytics
 * @desc    Get payment analytics including gateway statistics
 * @access  Private (Admin only)
 */
paymentRouter.get(
  '/:id/analytics',
  authenticate,
  validateRole([UserRole.ADMIN]),
  paymentController.getPaymentAnalytics
);


// ==================== SYSTEM ANALYTICS ====================

/**
 * @route   GET /api/v1/payments/system/analytics
 * @desc    Get system-wide payment analytics
 * @access  Private (Admin only)
 * @query   { startDate?, endDate?, provider?, operation? }
 */
paymentRouter.get(
  '/system/analytics',
  authenticate,
  validateRole([UserRole.ADMIN]),
  paymentController.getSystemAnalytics
);

export default paymentRouter;
