import { Router } from 'express';
import { notificationController } from '../controllers/notification.controller';
import { authenticate } from '../middleware/auth.middleware';

const notificationRouter = Router();

// Apply authentication middleware to all notification routes
notificationRouter.use(authenticate);

/**
 * @route   POST /api/v1/notifications/send-to-user
 * @desc    Send notification to a specific user
 * @access  Private (Admin, Agent)
 * @body    { userId, title, body, data?, imageUrl? }
 */
notificationRouter.post('/send-to-user', notificationController.sendToUser);

/**
 * @route   POST /api/v1/notifications/send-to-topic
 * @desc    Send notification to all users subscribed to a topic
 * @access  Private (Admin only)
 * @body    { topic, title, body, data?, imageUrl?, onlyActiveUsers? }
 */
notificationRouter.post('/send-to-topic', notificationController.sendToTopic);

/**
 * @route   PUT /api/v1/notifications/fcm-token
 * @desc    Update user's FCM token
 * @access  Private (All authenticated users)
 * @body    { token }
 */
notificationRouter.put('/fcm-token', notificationController.updateFcmToken);

/**
 * @route   POST /api/v1/notifications/subscribe
 * @desc    Subscribe user to notification topic
 * @access  Private (All authenticated users)
 * @body    { topic }
 */
notificationRouter.post('/subscribe', notificationController.subscribeToTopic);

/**
 * @route   PUT /api/v1/notifications/toggle
 * @desc    Toggle user's notification settings
 * @access  Private (All authenticated users)
 * @body    { enabled }
 */
notificationRouter.put('/toggle', notificationController.toggleNotifications);

/**
 * @route   GET /api/v1/notifications/history
 * @desc    Get user's notification history
 * @access  Private (All authenticated users)
 * @query   { page?, limit?, status? }
 */
notificationRouter.get('/history', notificationController.getNotificationHistory);

/**
 * @route   GET /api/v1/notifications/stats
 * @desc    Get notification statistics
 * @access  Private (Admin only)
 * @query   { startDate?, endDate? }
 */
notificationRouter.get('/stats', notificationController.getNotificationStats);

/**
 * @route   GET /api/v1/notifications/topics
 * @desc    Get available notification topics
 * @access  Private (All authenticated users)
 */
notificationRouter.get('/topics', notificationController.getTopics);

export default notificationRouter;
