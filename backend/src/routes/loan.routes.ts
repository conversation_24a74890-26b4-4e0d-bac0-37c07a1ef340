import { Router } from 'express';
import { loanController } from '../controllers/loan.controller';
import { authenticate } from '../middleware/auth.middleware';
import { validateRole } from '../middleware/role.middleware';
import { UserRole } from '../enums/enums';

const loanRouter = Router();

// Create loan (Admin/Supervisor)
loanRouter.post(
  '/',
  authenticate,
  validateRole([UserRole.ADMIN, UserRole.SUPERVISOR]),
  loanController.createLoan
);

// List loans (Admin/Supervisor), customers can filter by self
loanRouter.get(
  '/',
  authenticate,
  validateRole([UserRole.ADMIN, UserRole.SUPERVISOR, UserRole.CUSTOMER]),
  loanController.getLoans
);

// Get loan details
loanRouter.get(
  '/:id',
  authenticate,
  validateRole([UserRole.ADMIN, UserRole.SUPERVISOR, UserRole.CUSTOMER]),
  loanController.getLoanDetails
);

// Get schedule
loanRouter.get(
  '/:id/schedule',
  authenticate,
  validateRole([UserRole.ADMIN, UserRole.SUPERVISOR, UserRole.CUSTOMER]),
  loanController.getSchedule
);

// Initiate WaaFi preauthorization for loan payment (Customer/Admin)
loanRouter.post(
  '/:id/preauthorize',
  authenticate,
  validateRole([UserRole.CUSTOMER, UserRole.ADMIN]),
  loanController.initiateLoanPayment
);

// Pay loan with CASH (Customer/Admin)
loanRouter.post(
  '/:id/payments',
  authenticate,
  validateRole([UserRole.CUSTOMER, UserRole.ADMIN]),
  loanController.payLoan
);

export default loanRouter;
