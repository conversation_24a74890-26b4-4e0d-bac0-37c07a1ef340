import { Router } from 'express';
import { smsController } from '../controllers/sms.controller';
import { authenticate } from '../middleware/auth.middleware';

const smsRouter = Router();

// Apply authentication middleware to all SMS routes
smsRouter.use(authenticate);

/**
 * @route   POST /api/v1/sms/send
 * @desc    Send SMS to a single recipient
 * @access  Private (Admin, Agent)
 * @body    { phoneNumber, message, senderId?, refId?, isOtp? }
 */
smsRouter.post('/send', smsController.sendSms);

/**
 * @route   POST /api/v1/sms/send-bulk
 * @desc    Send SMS to multiple recipients
 * @access  Private (Admin only)
 * @body    { phoneNumbers[], message, senderId?, refId?, isOtp? }
 */
smsRouter.post('/send-bulk', smsController.sendBulkSms);

/**
 * @route   POST /api/v1/sms/send-to-role
 * @desc    Send SMS to all users with a specific role
 * @access  Private (Admin only)
 * @body    { role, message, senderId?, refId?, isOtp?, onlyActiveUsers? }
 */
smsRouter.post('/send-to-role', smsController.sendSmsToRole);

/**
 * @route   POST /api/v1/sms/send-otp
 * @desc    Send OTP SMS to a user
 * @access  Private (Admin, Agent)
 * @body    { phoneNumber, otp, customMessage? }
 */
smsRouter.post('/send-otp', smsController.sendOtp);

/**
 * @route   GET /api/v1/sms/health
 * @desc    Get SMS service health status
 * @access  Private (Admin only)
 */
smsRouter.get('/health', smsController.getHealthStatus);

/**
 * @route   GET /api/v1/sms/metrics
 * @desc    Get SMS service metrics
 * @access  Private (Admin only)
 */
smsRouter.get('/metrics', smsController.getMetrics);

export default smsRouter;
