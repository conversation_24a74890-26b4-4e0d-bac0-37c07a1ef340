import { Router } from 'express';
import { dashboardController } from '../controllers/dashboard.controller';
import { authenticate } from '../middleware/auth.middleware';
import { validateRole } from '../middleware/role.middleware';
import { UserRole } from '../enums/enums';

const dashboardRouter = Router();

/**
 * Dashboard Routes
 * All routes require authentication
 */

// Legacy endpoint - can be kept for backward compatibility
dashboardRouter.get('/summary', authenticate, dashboardController.getDashboard);

// Role-specific dashboard endpoints
dashboardRouter.get(
  '/admin',
  authenticate,
  validateRole([UserRole.ADMIN]),
  dashboardController.getAdminDashboard
);

dashboardRouter.get(
  '/supervisor',
  authenticate,
  validateRole([UserRole.SUPERVISOR]),
  dashboardController.getSupervisorDashboard
);

dashboardRouter.get(
  '/agent',
  authenticate,
  validateRole([UserRole.AGENT]),
  dashboardController.getAgentDashboard
);

dashboardRouter.get(
  '/customer',
  authenticate,
  validateRole([UserRole.CUSTOMER]),
  dashboardController.getCustomerDashboard
);

// Admin reports endpoint
dashboardRouter.get(
  '/reports/:type',
  authenticate,
  validateRole([UserRole.ADMIN]),
  dashboardController.getReports
);

export default dashboardRouter;
