import { Router } from 'express';
import { orderController } from '../controllers/order.controller';
import { authenticate } from '../middleware/auth.middleware';
import { validateRole } from '../middleware/role.middleware';
import { UserRole } from '../enums/enums';

const orderRouter = Router();

// Apply authentication middleware to all order routes
orderRouter.use(authenticate);

/**
 * @route   POST /api/v1/orders
 * @desc    Create a new order
 * @access  Private (Customers and admins only)
 * @body    { items[], deliveryAddress, paymentMethod? }
 */
orderRouter.post(
  '/',
  validateRole([UserRole.CUSTOMER, UserRole.ADMIN, UserRole.SUPERVISOR]),
  orderController.createOrder
);

/**
 * @route   GET /api/v1/orders
 * @desc    Get all orders
 * @access  Private (Customers only)
 * @query   { customer?, status?, paymentMethod?, startDate?, endDate? }
 */
orderRouter.get('/', orderController.getOrders);

/**
 * @route   PUT /api/v1/orders/:id/assign-agent
 * @desc    Assign agent to order
 * @access  Private (Admin, Supervisor)
 * @body    { agentId }
 */
orderRouter.put(
  '/:id/assign-agent',
  validateRole([
    UserRole.ADMIN,
    UserRole.SUPERVISOR,
    //  UserRole.AGENT,
  ]),
  orderController.assignAgentToOrder
);

/**
 * @route   PUT /api/v1/orders/:id/cancel
 * @desc    Cancel order
 * @access  Private (Admin, Supervisor )
 */
orderRouter.put(
  '/:id/cancel',
  validateRole([
    UserRole.ADMIN,
    UserRole.SUPERVISOR,
    // UserRole.AGENT,
  ]),
  orderController.cancelOrder
);

/**
 * @route   PUT /api/v1/orders/:id/complete
 * @desc    Complete order
 * @access  Private (Admin, Agent, Supervisor)
 */
orderRouter.put(
  '/:id/complete',
  validateRole([UserRole.ADMIN, UserRole.AGENT, UserRole.SUPERVISOR]),
  orderController.completeOrder
);

/**
 * @route   PUT /api/v1/orders/:id/regenerate-qr
 * @desc    Regenerate QR code for an order
 * @access  Private (Admin, Supervisor)
 */
orderRouter.put(
  '/:id/regenerate-qr',
  validateRole([UserRole.ADMIN, UserRole.SUPERVISOR]),
  orderController.regenerateQRCode
);

/**
 * @route   POST /api/v1/orders/validate
 * @desc    Validate order in QR code
 * @access  Private (Admin, Agent,Supervisor)
 * @body    { qrCode }
 */
orderRouter.post(
  '/validate-qr',
  validateRole([UserRole.ADMIN, UserRole.AGENT, UserRole.SUPERVISOR]),
  orderController.validateOrderInQRCode
);

/**
 * @route   PUT /api/v1/orders/:id
 * @desc    Update order
 * @access  Private (Admin)
 * @body    { customerId?, items?, deliveryAddress?, paymentMethod? }
 */
orderRouter.put('/:id', validateRole([UserRole.ADMIN]), orderController.updateOrder);

/**
 * @route   DELETE /api/v1/orders/:id
 * @desc    Delete order
 * @access  Private (Admin)
 */
orderRouter.delete('/:id', validateRole([UserRole.ADMIN]), orderController.deleteOrder);

export default orderRouter;
