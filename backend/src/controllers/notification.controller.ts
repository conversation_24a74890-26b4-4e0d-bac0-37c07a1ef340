import { Request, Response, NextFunction } from 'express';
import { notificationService } from '../services/notification.services';
import { User, Notification } from '../models';
import { BadRequestError, NotFoundError } from '../errors/app_errors';
import { NotificationTopic } from '../utils/notification_utils';
import logger from '../config/logger';
import { UserRole } from '../enums/enums';
import { sendResponse } from '../utils/response';

class NotificationController {
  /**
   * Send notification to a specific user
   */
  async sendToUser(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId, title, body, data, imageUrl } = req.body;

      // Validate required fields
      if (!userId || !title || !body) {
        throw new BadRequestError('User ID, title, and body are required');
      }

      // Check if user has permission to send notifications
      if (req.user?.role !== UserRole.ADMIN && req.user?.role !== UserRole.AGENT) {
        throw new BadRequestError('Insufficient permissions to send notifications');
      }

      const result = await notificationService.sendToUser(userId, {
        title,
        body,
        data,
        imageUrl,
      });

      logger.info('Notification sent to user successfully', {
        senderId: req.user?.id,
        recipientId: userId,
        title,
      });

      sendResponse(res, 200, 'success', 'Notification sent successfully', {
        data: {
          success: result.success,
          sentAt: new Date().toISOString(),
          recipient: userId,
        },
      });
      return;
    } catch (error) {
      logger.error('Failed to send notification to user', {
        senderId: req.user?.id,
        error: error.message,
        recipientId: req.body.userId,
      });
      next(error);
    }
  }

  /**
   * Send notification to all users subscribed to a topic
   */
  async sendToTopic(req: Request, res: Response, next: NextFunction) {
    try {
      const { topic, title, body, data, imageUrl, onlyActiveUsers } = req.body;

      // Validate required fields
      if (!topic || !title || !body) {
        throw new BadRequestError('Topic, title, and body are required');
      }

      // Check if user has permission
      if (req.user?.role !== UserRole.ADMIN) {
        throw new BadRequestError('Only admins can send topic notifications');
      }

      // Validate topic
      if (!Object.values(NotificationTopic).includes(topic)) {
        throw new BadRequestError('Invalid notification topic');
      }

      const result = await notificationService.sendToTopic(
        topic,
        { title, body, data, imageUrl },
        { onlyActiveUsers }
      );

      logger.info('Topic notification sent successfully', {
        senderId: req.user?.id,
        topic,
        title,
        recipientCount: result.count,
      });

      sendResponse(res, 200, 'success', 'Topic notification sent successfully', {
        data: {
          success: result.success,
          topic,
          recipientCount: result.count || 0,
          sentAt: new Date().toISOString(),
        },
      });
      return;
    } catch (error) {
      logger.error('Failed to send topic notification', {
        senderId: req.user?.id,
        error: error.message,
        topic: req.body.topic,
      });
      next(error);
    }
  }

  /**
   * Update user's FCM token
   */
  async updateFcmToken(req: Request, res: Response, next: NextFunction) {
    try {
      const { token } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        throw new BadRequestError('User authentication required');
      }

      if (!token) {
        throw new BadRequestError('FCM token is required');
      }

      const result = await notificationService.updateFcmToken(userId, token);

      logger.info('FCM token updated successfully', {
        userId,
      });

      sendResponse(res, 200, 'success', 'FCM token updated successfully', {
        data: {
          success: result.success,
          updatedAt: new Date().toISOString(),
        },
      });
      return;
    } catch (error) {
      logger.error('Failed to update FCM token', {
        userId: req.user?.id,
        error: error.message,
      });
      next(error);
    }
  }

  /**
   * Subscribe user to notification topic
   */
  async subscribeToTopic(req: Request, res: Response, next: NextFunction) {
    try {
      const { topic } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        throw new BadRequestError('User authentication required');
      }

      if (!topic) {
        throw new BadRequestError('Topic is required');
      }

      // Validate topic
      if (!Object.values(NotificationTopic).includes(topic)) {
        throw new BadRequestError('Invalid notification topic');
      }

      const result = await notificationService.subscribeToTopic(userId, topic);

      logger.info('User subscribed to topic successfully', {
        userId,
        topic,
      });

      sendResponse(res, 200, 'success', 'Successfully subscribed to topic', {
        data: {
          success: result.success,
          topic,
          subscribedAt: new Date().toISOString(),
        },
      });
      return;
    } catch (error) {
      logger.error('Failed to subscribe to topic', {
        userId: req.user?.id,
        error: error.message,
        topic: req.body.topic,
      });
      next(error);
    }
  }

  /**
   * Toggle user's notification settings
   */
  async toggleNotifications(req: Request, res: Response, next: NextFunction) {
    try {
      const { enabled } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        throw new BadRequestError('User authentication required');
      }

      if (typeof enabled !== 'boolean') {
        throw new BadRequestError('Enabled field must be a boolean');
      }

      const result = await notificationService.toggleNotifications(userId, enabled);

      logger.info('Notification settings updated', {
        userId,
        enabled,
      });

      sendResponse(
        res,
        200,
        'success',
        `Notifications ${enabled ? 'enabled' : 'disabled'} successfully`,
        {
          data: {
            success: result.success,
            enabled,
            updatedAt: new Date().toISOString(),
          },
        }
      );
      return;
    } catch (error) {
      logger.error('Failed to toggle notifications', {
        userId: req.user?.id,
        error: error.message,
      });
      next(error);
    }
  }

  /**
   * Get user's notification history
   */
  async getNotificationHistory(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = req.user?.id;
      const { page = 1, limit = 20, status } = req.query;

      if (!userId) {
        throw new BadRequestError('User authentication required');
      }

      const query: any = { userId };
      if (status) {
        query.status = status;
      }

      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;

      const [notifications, total] = await Promise.all([
        Notification.find(query).sort({ createdAt: -1 }).skip(skip).limit(limitNum).lean(),
        Notification.countDocuments(query),
      ]);

      sendResponse(res, 200, 'success', 'Notification history retrieved successfully', {
        data: {
          notifications,
          pagination: {
            page: pageNum,
            limit: limitNum,
            total,
            pages: Math.ceil(total / limitNum),
          },
        },
      });
      return;
    } catch (error) {
      logger.error('Failed to get notification history', {
        userId: req.user?.id,
        error: error.message,
      });
      next(error);
    }
  }

  /**
   * Get notification statistics (Admin only)
   */
  async getNotificationStats(req: Request, res: Response, next: NextFunction) {
    try {
      // Check if user has permission
      if (req.user?.role !== UserRole.ADMIN) {
        throw new BadRequestError('Only admins can view notification statistics');
      }

      const { startDate, endDate } = req.query;

      const dateFilter: any = {};
      if (startDate) {
        dateFilter.$gte = new Date(startDate as string);
      }
      if (endDate) {
        dateFilter.$lte = new Date(endDate as string);
      }

      const matchStage = Object.keys(dateFilter).length > 0 ? { createdAt: dateFilter } : {};

      const stats = await Notification.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: null,
            total: { $sum: 1 },
            delivered: { $sum: { $cond: [{ $eq: ['$status', 'delivered'] }, 1, 0] } },
            failed: { $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] } },
            pending: { $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] } },
          },
        },
      ]);

      const topicStats = await Notification.aggregate([
        { $match: { topic: { $exists: true }, ...matchStage } },
        {
          $group: {
            _id: '$topic',
            count: { $sum: 1 },
            delivered: { $sum: { $cond: [{ $eq: ['$status', 'delivered'] }, 1, 0] } },
          },
        },
        { $sort: { count: -1 } },
      ]);

      const result = stats[0] || { total: 0, delivered: 0, failed: 0, pending: 0 };
      const successRate = result.total > 0 ? (result.delivered / result.total) * 100 : 0;

      // res.status(200).json({
      //   status: 'success',
      //   message: 'Notification statistics retrieved successfully',
      //   data: {
      //     overview: {
      //       ...result,
      //       successRate: Math.round(successRate),
      //     },
      //     topicBreakdown: topicStats,
      //     period: {
      //       startDate: startDate || null,
      //       endDate: endDate || null,
      //     },
      //     generatedAt: new Date().toISOString(),
      //   },
      // });
      sendResponse(res, 200, 'success', 'Notification statistics retrieved successfully', {
        data: {
          overview: {
            ...result,
            successRate: Math.round(successRate),
          },
          topicBreakdown: topicStats,
          period: {
            startDate: startDate || null,
            endDate: endDate || null,
          },
          generatedAt: new Date().toISOString(),
        },
      });
    } catch (error) {
      logger.error('Failed to get notification statistics', {
        userId: req.user?.id,
        error: error.message,
      });
      next(error);
    }
  }

  /**
   * Get available notification topics
   */
  async getTopics(req: Request, res: Response, next: NextFunction) {
    try {
      const topics = Object.values(NotificationTopic).map(topic => ({
        value: topic,
        label: topic.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      }));

      sendResponse(res, 200, 'success', 'Notification topics retrieved successfully', {
        data: {
          topics,
        },
      });
      return;
    } catch (error) {
      logger.error('Failed to get notification topics', {
        userId: req.user?.id,
        error: error.message,
      });
      next(error);
    }
  }
}

export const notificationController = new NotificationController();
