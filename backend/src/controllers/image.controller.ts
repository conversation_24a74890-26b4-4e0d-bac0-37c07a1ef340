import { Request, Response } from 'express';
import { ImageUploadService } from '../services/image-upload.service';
import { ImagePathUtils, ImageCategoryHelper } from '../utils/image_utils';
import { Cylinder, SparePart, Package } from '../models';
import { BadRequestError, NotFoundError } from '../errors/app_errors';
import { UserRole } from '../enums/enums';

/**
 * Image management controller for handling uploads and serving images
 */
export class ImageController {
  /**
   * Upload image for a specific product
   * POST /api/v1/images/upload
   */
  static async uploadImage(req: Request, res: Response): Promise<void> {
    try {
      const { category, entityId, entityType } = req.body;
      const file = req.file;

      if (!file) {
        throw new BadRequestError('No image file provided');
      }

      if (!category || !ImageCategoryHelper.isValidCategory(category)) {
        throw new BadRequestError('Valid category is required');
      }

      // Validate and process the uploaded file
      ImageUploadService.validateImageFile(file);
      const imagePath = await ImageUploadService.processUploadedFile(file, category);

      // If entityId and entityType provided, update the corresponding model
      if (entityId && entityType) {
        await ImageController.updateEntityImage(entityType, entityId, imagePath);
      }

      const imageUrl = ImageUploadService.getImageUrl(imagePath);

      res.status(201).json({
        message: {
          status: 'success',
          msg: 'Image uploaded successfully',
          data: {
            imagePath,
            imageUrl,
            originalName: file.originalname,
            size: file.size,
            mimeType: file.mimetype,
          },
        },
      });
    } catch (error) {
      // Clean up uploaded file if there was an error
      if (req.file?.path) {
        await ImageUploadService.deleteImageFile(req.file.path);
      }
      throw error;
    }
  }

  /**
   * Update entity with new image path
   */
  private static async updateEntityImage(
    entityType: string,
    entityId: string,
    imagePath: string
  ): Promise<void> {
    switch (entityType.toLowerCase()) {
      case 'cylinder':
        const cylinder = await Cylinder.findById(entityId);
        if (!cylinder) throw new NotFoundError('Cylinder not found');

        // Delete old image if exists
        if (cylinder.imagePath) {
          await ImageUploadService.deleteImageFile(cylinder.imagePath);
        }

        cylinder.imagePath = imagePath;
        await cylinder.save();
        break;

      case 'sparepart':
        const sparePart = await SparePart.findById(entityId);
        if (!sparePart) throw new NotFoundError('Spare part not found');

        if (sparePart.imagePath) {
          await ImageUploadService.deleteImageFile(sparePart.imagePath);
        }

        sparePart.imagePath = imagePath;
        await sparePart.save();
        break;

      case 'package':
        const packageItem = await Package.findById(entityId);
        if (!packageItem) throw new NotFoundError('Package not found');

        if (packageItem.imagePath) {
          await ImageUploadService.deleteImageFile(packageItem.imagePath);
        }

        packageItem.imagePath = imagePath;
        await packageItem.save();
        break;

      default:
        throw new BadRequestError('Invalid entity type');
    }
  }

  /**
   * Get image info
   * GET /api/v1/images/info/:entityType/:entityId
   */
  static async getImageInfo(req: Request, res: Response): Promise<void> {
    const { entityType, entityId } = req.params;

    let entity: any;
    switch (entityType.toLowerCase()) {
      case 'cylinder':
        entity = await Cylinder.findById(entityId);
        break;
      case 'sparepart':
        entity = await SparePart.findById(entityId);
        break;
      case 'package':
        entity = await Package.findById(entityId);
        break;
      default:
        throw new BadRequestError('Invalid entity type');
    }

    if (!entity) {
      throw new NotFoundError(`${entityType} not found`);
    }

    const imagePath = entity.imagePath;
    const imageUrl = entity.currentImageUrl || entity.imageUrl;

    let fileInfo = null;
    if (imagePath) {
      fileInfo = await ImageUploadService.getFileInfo(imagePath);
    }

    res.json({
      message: {
        status: 'success',
        msg: 'Image info retrieved successfully',
        data: {
          imagePath,
          imageUrl,
          legacyImageUrl: entity.imageUrl,
          fileInfo,
        },
      },
    });
  }

  /**
   * Delete image
   * DELETE /api/v1/images/:entityType/:entityId
   */
  static async deleteImage(req: Request, res: Response): Promise<void> {
    const { entityType, entityId } = req.params;

    let entity: any;
    switch (entityType.toLowerCase()) {
      case 'cylinder':
        entity = await Cylinder.findById(entityId);
        break;
      case 'sparepart':
        entity = await SparePart.findById(entityId);
        break;
      case 'package':
        entity = await Package.findById(entityId);
        break;
      default:
        throw new BadRequestError('Invalid entity type');
    }

    if (!entity) {
      throw new NotFoundError(`${entityType} not found`);
    }

    // Delete the file from filesystem
    if (entity.imagePath) {
      await ImageUploadService.deleteImageFile(entity.imagePath);
      entity.imagePath = undefined;
      await entity.save();
    }

    res.json({
      message: {
        status: 'success',
        msg: 'Image deleted successfully',
        data: null,
      },
    });
  }

  /**
   * Replace image
   * PUT /api/v1/images/:entityType/:entityId
   */
  static async replaceImage(req: Request, res: Response): Promise<void> {
    const { entityType, entityId } = req.params;
    const { category } = req.body;
    const file = req.file;

    if (!file) {
      throw new BadRequestError('No image file provided');
    }

    if (!category || !ImageCategoryHelper.isValidCategory(category)) {
      throw new BadRequestError('Valid category is required');
    }

    try {
      // Validate and process the uploaded file
      ImageUploadService.validateImageFile(file);
      const newImagePath = await ImageUploadService.processUploadedFile(file, category);

      // Update the entity and delete old image
      await ImageController.updateEntityImage(entityType, entityId, newImagePath);

      const imageUrl = ImageUploadService.getImageUrl(newImagePath);

      res.json({
        message: {
          status: 'success',
          msg: 'Image replaced successfully',
          data: {
            imagePath: newImagePath,
            imageUrl,
            originalName: file.originalname,
            size: file.size,
            mimeType: file.mimetype,
          },
        },
      });
    } catch (error) {
      // Clean up uploaded file if there was an error
      if (req.file?.path) {
        await ImageUploadService.deleteImageFile(req.file.path);
      }
      throw error;
    }
  }
}
