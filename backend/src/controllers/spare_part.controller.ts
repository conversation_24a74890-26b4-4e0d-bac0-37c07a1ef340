import { Request, Response, NextFunction } from 'express';
import { sparePartService } from '../services/spare_part.services';
import { sendResponse } from '../utils/response';
import { BadRequestError, ValidationError } from '../errors/app_errors';
import { SparePartCategory, SparePartStatus, CylinderType, EntityType } from '../enums/enums';
import { Types } from 'mongoose';
import logger from '../config/logger';
import { ImageUploadService } from '../services/image-upload.service';

class SparePartController {
  /**
   * @route   POST /api/v1/spare-parts
   * @desc    Create a new spare part
   * @access  Private (Admin only)
   */
  async createSparePart(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        description,
        price,
        cost,
        category,
        compatibleCylinderTypes,
        barcode,
        minimumStockLevel,
        imageUrl,
        initialStock,
        initialReserved,
        initialSold,
      } = req.body;

      // Handle uploaded image file (if present)
      const imageFile = (req as any).file;
      let imagePath: string | undefined;

      if (imageFile) {
        // Process uploaded image
        imagePath = await ImageUploadService.processUploadedFile(imageFile, 'spare-parts');
        logger.info(`Image uploaded for spare part: ${imagePath}`);
      }

      // Validation
      if (!price || !cost || !category) {
        throw new ValidationError('Price, cost, and category are required');
      }

      if (price <= 0 || cost <= 0) {
        throw new ValidationError('Price and cost must be positive numbers');
      }

      if (!Object.values(SparePartCategory).includes(category)) {
        throw new ValidationError('Invalid category');
      }

      // Handle compatibleCylinderTypes conversion from string to array (for multipart form data)
      let processedCompatibleCylinderTypes = compatibleCylinderTypes;
      if (compatibleCylinderTypes && typeof compatibleCylinderTypes === 'string') {
        processedCompatibleCylinderTypes = compatibleCylinderTypes
          .split(',')
          .map(type => type.trim());
      }

      if (processedCompatibleCylinderTypes && !Array.isArray(processedCompatibleCylinderTypes)) {
        throw new ValidationError('Compatible cylinder types must be an array');
      }

      const sparePart = await sparePartService.createSparePart({
        description: description?.trim(),
        price: Number(price),
        cost: Number(cost),
        category,
        compatibleCylinderTypes: processedCompatibleCylinderTypes,
        barcode: barcode?.trim(),
        minimumStockLevel: minimumStockLevel ? Number(minimumStockLevel) : undefined,
        imageUrl: imageUrl?.trim(), // Legacy field for backward compatibility
        imagePath, // New field for uploaded images
        initialStock: initialStock ? Number(initialStock) : undefined,
        initialReserved: initialReserved ? Number(initialReserved) : undefined,
        initialSold: initialSold ? Number(initialSold) : undefined,
      });

      logger.info('Spare part created successfully', {
        sparePartId: sparePart._id,
        userId: req.user?.userId,
        category: sparePart.category,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 201, 'success', 'Spare part created successfully', {
        data: sparePart,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/spare-parts
   * @desc    Get all spare parts with filtering and pagination
   * @access  Private (All authenticated users)
   */
  async getSpareParts(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        search,
        category,
        status,
        compatibleWith,
        lowStock,
        page = 1,
        limit = 10,
      } = req.query;

      // Validation
      const pageNum = Math.max(1, parseInt(page as string) || 1);
      const limitNum = Math.min(50, Math.max(1, parseInt(limit as string) || 10));

      const filters: any = {};
      if (search) filters.search = search as string;
      if (category) filters.category = category as SparePartCategory;
      if (status) filters.status = status as SparePartStatus;
      if (compatibleWith) filters.compatibleWith = compatibleWith as CylinderType;
      if (lowStock === 'true') filters.lowStock = true;

      const result = await sparePartService.listSpareParts(
        filters,
        {
          page: pageNum,
          limit: limitNum,
        },
        {
          userId: req.user?.userId.toString(),
          role: req.user?.role,
        }
      );

      sendResponse(res, 200, 'success', 'Spare parts retrieved successfully', {
        data: result.data,
        meta: {
          pagination: {
            page: pageNum,
            limit: limitNum,
            total: result.total,
            pages: Math.ceil(result.total / limitNum),
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/spare-parts/:id
   * @desc    Get spare part by ID
   * @access  Private (All authenticated users)
   */
  async getSparePartById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid spare part ID');
      }

      const sparePart = await sparePartService.getSparePartById(id);

      sendResponse(res, 200, 'success', 'Spare part retrieved successfully', {
        data: sparePart,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   PUT /api/v1/spare-parts/:id
   * @desc    Update spare part details
   * @access  Private (Admin only)
   */
  async updateSparePart(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      // Handle uploaded image file (if present)
      const imageFile = (req as any).file;
      let imagePath: string | undefined;

      if (imageFile) {
        // Process uploaded image
        imagePath = await ImageUploadService.processUploadedFile(imageFile, 'spare-parts');
        logger.info(`Image uploaded for spare part update: ${imagePath}`);
        updateData.imagePath = imagePath;
      }

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid spare part ID');
      }

      // Handle compatibleCylinderTypes conversion from string to array (for multipart form data)
      if (
        updateData.compatibleCylinderTypes &&
        typeof updateData.compatibleCylinderTypes === 'string'
      ) {
        updateData.compatibleCylinderTypes = updateData.compatibleCylinderTypes
          .split(',')
          .map((type: string) => type.trim());
      }

      // Validate numeric fields if provided
      if (updateData.price !== undefined && updateData.price <= 0) {
        throw new ValidationError('Price must be positive');
      }
      if (updateData.cost !== undefined && updateData.cost <= 0) {
        throw new ValidationError('Cost must be positive');
      }

      const sparePart = await sparePartService.updateSparePart(id, updateData);

      logger.info('Spare part updated successfully', {
        sparePartId: id,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Spare part updated successfully', {
        data: sparePart,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   POST /api/v1/spare-parts/:id/restock
   * @desc    Restock spare part
   * @access  Private (Admin only)
   */
  async restockSparePart(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { quantity } = req.body;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid spare part ID');
      }

      if (!quantity || quantity <= 0) {
        throw new ValidationError('Quantity must be a positive number');
      }

      const sparePart = await sparePartService.restock(id, Number(quantity));

      logger.info('Spare part restocked successfully', {
        sparePartId: id,
        quantity: Number(quantity),
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Spare part restocked successfully', {
        data: sparePart,
      });
    } catch (error) {
      next(error);
    }
  }

  // Note: Reservation, release, and sales operations are now handled automatically
  // through the order lifecycle. These methods have been removed to prevent
  // data inconsistencies and ensure proper audit trails through order tracking.

  /**
   * @route   PUT /api/v1/spare-parts/:id/discontinue
   * @desc    Discontinue a spare part
   * @access  Private (Admin only)
   */
  async discontinueSparePart(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid spare part ID');
      }

      const sparePart = await sparePartService.discontinue(id);

      sendResponse(res, 200, 'success', 'Spare part discontinued successfully', {
        data: sparePart,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/spare-parts/low-stock
   * @desc    Get spare parts with low stock
   * @access  Private (Admin, Agent)
   */
  async getLowStockSpareParts(_req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const spareParts = await sparePartService.getLowStockAlerts();

      sendResponse(res, 200, 'success', 'Low stock spare parts retrieved successfully', {
        data: spareParts,
        meta: { count: spareParts.length },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/spare-parts/statistics
   * @desc    Get spare parts sales statistics
   * @access  Private (Admin only)
   */
  async getSparePartStatistics(_req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const statistics = await sparePartService.getSalesStatistics();

      sendResponse(res, 200, 'success', 'Spare part statistics retrieved successfully', {
        data: statistics,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/spare-parts/search
   * @desc    Search spare parts
   * @access  Private (All authenticated users)
   */
  async searchSpareParts(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { q: query, limit = 10 } = req.query;

      if (!query || typeof query !== 'string') {
        throw new ValidationError('Search query is required');
      }

      const limitNum = Math.min(50, Math.max(1, parseInt(limit as string) || 10));
      const spareParts = await sparePartService.searchSpareParts(query, limitNum);

      sendResponse(res, 200, 'success', 'Search results retrieved successfully', {
        data: spareParts,
        meta: { count: spareParts.length },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   DELETE /api/v1/spare-parts/:id
   * @desc    Soft delete a spare part
   * @access  Private (Admin only)
   */
  async deleteSparePart(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid spare part ID');
      }

      const sparePart = await sparePartService.deleteSparePart(id, req.user?.userId.toString());

      logger.info('Spare part soft deleted successfully', {
        sparePartId: id,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Spare part deleted successfully', {
        data: sparePart,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   POST /api/v1/spare-parts/:id/restore
   * @desc    Restore a soft-deleted spare part
   * @access  Private (Admin only)
   */
  async restoreSparePart(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid spare part ID');
      }

      const sparePart = await sparePartService.restoreSparePart(id);

      logger.info('Spare part restored successfully', {
        sparePartId: id,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Spare part restored successfully', {
        data: sparePart,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   DELETE /api/v1/spare-parts/:id/permanent
   * @desc    Permanently delete a spare part (hard delete)
   * @access  Private (Admin only)
   */
  async permanentlyDeleteSparePart(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid spare part ID');
      }

      const sparePart = await sparePartService.permanentlyDeleteSparePart(id);

      logger.info('Spare part permanently deleted', {
        sparePartId: id,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Spare part permanently deleted', {
        data: sparePart,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/spare-parts/deleted
   * @desc    Get soft-deleted spare parts
   * @access  Private (Admin only)
   */
  async getDeletedSpareParts(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;

      const result = await sparePartService.listDeletedSpareParts({ page, limit });

      sendResponse(res, 200, 'success', 'Deleted spare parts retrieved successfully', {
        data: result.data,
        meta: {
          pagination: {
            page,
            limit,
            total: result.total,
            pages: Math.ceil(result.total / limit),
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/spare-parts/:id/packages
   * @desc    Get packages that use a specific spare part
   * @access  Private (Admin only)
   */
  async getPackagesUsingPart(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { includeDeleted = 'false' } = req.query;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid spare part ID');
      }

      const packages = await sparePartService.getPackagesUsingPart(id, includeDeleted === 'true');

      sendResponse(res, 200, 'success', 'Packages using spare part retrieved successfully', {
        data: packages,
        meta: { count: packages.length },
      });
    } catch (error) {
      next(error);
    }
  }
}

export const sparePartController = new SparePartController();
