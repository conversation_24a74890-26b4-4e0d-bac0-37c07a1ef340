import { Request, Response, NextFunction } from 'express';
import { paymentService } from '../services/payment.services';
import { loanService } from '../services/loan.services';
import { sendResponse } from '../utils/response';
import { BadRequestError, ValidationError } from '../errors/app_errors';
import { Types } from 'mongoose';
import logger from '../config/logger';
import { IGatewayRawResponse } from '../models/payment.model';

/**
 * PaymentController class for handling payment-related HTTP requests
 * Includes enhanced functionality for gateway response analytics and debugging
 */
class PaymentController {
  /**
   * @route   POST /api/v1/payments/:id/purchase
   * @desc    Initiate payment purchase (immediate)
   * @access  Private (Customer, Admin)
   */
  async initiatePurchase(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id: paymentId } = req.params;
      const { mobile, amount, deliveryDetails } = req.body;

      if (!Types.ObjectId.isValid(paymentId)) {
        throw new BadRequestError('Invalid payment ID format');
      }

      if (!mobile) {
        throw new ValidationError('Mobile number is required');
      }

      if (!amount || typeof amount !== 'number' || amount <= 0) {
        throw new ValidationError('Valid amount is required');
      }

      const result = await paymentService.initiatePurchase(
        paymentId,
        mobile,
        amount,
        deliveryDetails
      );

      logger.info('Payment purchase initiated successfully', {
        paymentId,
        mobile: mobile.replace(/\d(?=\d{4})/g, '*'), // Mask mobile number
        amount,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Payment purchase completed successfully', {
        data: {
          payment: result.payment,
          cashierUrl: result.cashierUrl,
          transactionId: result.transactionId,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   POST /api/v1/payments/:id/cancel
   * @desc    Cancel payment (not supported with Purchase API)
   * @access  Private (Customer, Admin, Agent)
   */
  async cancelPayment(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id: paymentId } = req.params;

      if (!Types.ObjectId.isValid(paymentId)) {
        throw new BadRequestError('Invalid payment ID format');
      }

      const cancelledPayment = await paymentService.cancelPayment(paymentId);

      logger.info('Payment cancellation attempted', {
        paymentId,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Payment cancelled successfully', {
        data: cancelledPayment,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   POST /api/v1/payments/:id/capture
   * @desc    Capture preauthorized payment
   * @access  Private (Admin, Agent)
   */
  async capturePreauthorizedPayment(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id: paymentId } = req.params;

      if (!Types.ObjectId.isValid(paymentId)) {
        throw new BadRequestError('Invalid payment ID format');
      }

      const capturedPayment = await paymentService.capturePreauthorizedPayment(paymentId);

      // Check if this is a loan payment and apply it to loan installments
      let loanDetails = null;
      if (capturedPayment.metadata?.loanId) {
        try {
          loanDetails = await loanService.applyCapturedPayment(paymentId);
          logger.info('Captured payment applied to loan', {
            paymentId,
            loanId: capturedPayment.metadata.loanId,
            userId: req.user?.userId,
          });
        } catch (loanError) {
          logger.error('Error applying captured payment to loan:', loanError);
          // Don't fail the capture if loan application fails
        }
      }

      logger.info('Payment captured successfully', {
        paymentId,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Payment captured successfully', {
        data: {
          payment: capturedPayment,
          loanDetails,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/payments/:id/gateway-responses
   * @desc    Get gateway responses for a payment (for debugging and audit)
   * @access  Private (Admin only)
   */
  async getGatewayResponses(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id: paymentId } = req.params;
      const { operation, provider } = req.query;

      if (!Types.ObjectId.isValid(paymentId)) {
        throw new BadRequestError('Invalid payment ID format');
      }

      const gatewayResponses = await paymentService.getGatewayResponses(
        paymentId,
        operation as IGatewayRawResponse['operation'],
        provider as IGatewayRawResponse['provider']
      );

      logger.info('Gateway responses retrieved successfully', {
        paymentId,
        responseCount: gatewayResponses.length,
        operation,
        provider,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Gateway responses retrieved successfully', {
        data: gatewayResponses,
        meta: {
          paymentId,
          totalResponses: gatewayResponses.length,
          filters: { operation, provider },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/payments/:id/analytics
   * @desc    Get payment analytics including gateway response statistics
   * @access  Private (Admin only)
   */
  async getPaymentAnalytics(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id: paymentId } = req.params;

      if (!Types.ObjectId.isValid(paymentId)) {
        throw new BadRequestError('Invalid payment ID format');
      }

      const analytics = await paymentService.getPaymentAnalytics(paymentId);

      logger.info('Payment analytics retrieved successfully', {
        paymentId,
        totalInteractions: analytics.analytics.totalGatewayInteractions,
        successRate: analytics.analytics.successRate,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Payment analytics retrieved successfully', {
        data: analytics,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   POST /api/v1/payments/:id/webhook
   * @desc    Handle webhook/callback from payment gateway
   * @access  Public (webhook endpoint)
   */
  async handleWebhook(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id: paymentId } = req.params;
      const { provider = 'WAAFI' } = req.query;
      const webhookData = req.body;

      if (!Types.ObjectId.isValid(paymentId)) {
        throw new BadRequestError('Invalid payment ID format');
      }

      const payment = await paymentService.handleWebhookResponse(
        paymentId,
        webhookData,
        provider as IGatewayRawResponse['provider']
      );

      logger.info('Webhook processed successfully', {
        paymentId,
        provider,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Webhook processed successfully', {
        data: payment,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/payments/system/analytics
   * @desc    Get system-wide payment analytics
   * @access  Private (Admin only)
   */
  async getSystemAnalytics(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { startDate, endDate, provider, operation } = req.query;

      // This would be implemented to provide system-wide analytics
      // For now, return a placeholder response
      const analytics = {
        totalPayments: 0,
        totalGatewayInteractions: 0,
        averageResponseTime: 0,
        successRate: 0,
        errorRate: 0,
        providerBreakdown: {},
        operationBreakdown: {},
        timeRange: {
          startDate: startDate || 'Not specified',
          endDate: endDate || 'Not specified',
        },
      };

      logger.info('System payment analytics retrieved', {
        filters: { startDate, endDate, provider, operation },
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'System payment analytics retrieved successfully', {
        data: analytics,
        meta: {
          note: 'System-wide analytics implementation pending',
        },
      });
    } catch (error) {
      next(error);
    }
  }
}

export const paymentController = new PaymentController();
