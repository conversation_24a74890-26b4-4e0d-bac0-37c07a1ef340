import { Request, Response, NextFunction } from 'express';
import { loanService } from '../services/loan.services';
import { sendResponse } from '../utils/response';
import { LoanStatus, PaymentMethod } from '../enums/enums';
import { BadRequestError, ValidationError } from '../errors/app_errors';
import { Types } from 'mongoose';

class LoanController {
  /**
   * Create a new loan
   * @route POST /api/v1/loans
   */
  async createLoan(req: Request, res: Response, next: NextFunction) {
    try {
      const { customerId, principal, termMonths, orderId } = req.body;

      if (!customerId) throw new ValidationError('customerId is required');
      if (!principal || principal <= 0) throw new ValidationError('principal must be > 0');
      if (!termMonths || termMonths <= 0) throw new ValidationError('termMonths must be > 0');

      const loan = await loanService.createLoan({
        customerId,
        principal,
        termMonths,
        orderId,
      });

      sendResponse(res, 201, 'success', 'Loan created successfully', { data: loan });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get loans (customer can see their loans; admin/supervisor can filter)
   * @route GET /api/v1/loans
   */
  async getLoans(req: Request, res: Response, next: NextFunction) {
    try {
      const { status, limit, skip } = req.query;

      // Scope customer access to own loans only
      const role = req.user?.role as string | undefined;
      const userId = req .user?.userId?.toString();
      // const isCustomer = role === 'customer' || role === 'CUSTOMER';
      const isCustomer = role.toLowerCase() === 'customer';

      const customerId = isCustomer ? userId : (req.query.customerId?.toString() || undefined);

      const loans = await loanService.getLoans({
        customerId,
        status: status as LoanStatus,
        limit: limit ? parseInt(limit as string) : undefined,
        skip: skip ? parseInt(skip as string) : undefined,
      });

      sendResponse(res, 200, 'success', 'Loans retrieved successfully', { data: loans });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get loan details including installments and payments
   * @route GET /api/v1/loans/:id
   */
  async getLoanDetails(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      if (!Types.ObjectId.isValid(id)) throw new BadRequestError('Invalid loan ID');

      const details = await loanService.getLoanDetails(id);
      sendResponse(res, 200, 'success', 'Loan details retrieved successfully', { data: details });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get payment schedule for a loan
   * @route GET /api/v1/loans/:id/schedule
   */
  async getSchedule(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      if (!Types.ObjectId.isValid(id)) throw new BadRequestError('Invalid loan ID');

      const schedule = await loanService.getPaymentSchedule(id);
      sendResponse(res, 200, 'success', 'Payment schedule retrieved successfully', { data: schedule });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Initiate WaaFi preauthorization for loan payment
   * @route POST /api/v1/loans/:id/preauthorize
   */
  async initiateLoanPayment(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const { paymentAmount, mobile, installmentId } = req.body as {
        paymentAmount: number;
        mobile: string;
        installmentId?: string;
      };

      if (!Types.ObjectId.isValid(id)) throw new BadRequestError('Invalid loan ID');
      if (!paymentAmount || paymentAmount <= 0) throw new ValidationError('paymentAmount must be > 0');
      if (!mobile) throw new ValidationError('mobile is required');

      const result = await loanService.initiateLoanPayment({
        loanId: id,
        paymentAmount,
        mobile,
        installmentId,
      });

      sendResponse(res, 200, 'success', 'Loan payment purchase completed successfully', { data: result });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Process a principal-only loan payment (CASH only)
   * @route POST /api/v1/loans/:id/payments
   */
  async payLoan(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const { paymentAmount, paymentMethod, installmentId } = req.body as {
        paymentAmount: number;
        paymentMethod: PaymentMethod;
        installmentId?: string;
      };

      if (!Types.ObjectId.isValid(id)) throw new BadRequestError('Invalid loan ID');
      if (!paymentAmount || paymentAmount <= 0) throw new ValidationError('paymentAmount must be > 0');
      if (!paymentMethod) throw new ValidationError('paymentMethod is required');

      // Only allow CASH payments through this endpoint
      if (paymentMethod !== PaymentMethod.CASH) {
        throw new BadRequestError('Only CASH payments allowed. Use /preauthorize for WaaFi payments');
      }

      const result = await loanService.processLoanPayment({
        loanId: id,
        paymentAmount,
        paymentMethod,
        installmentId,
      });

      sendResponse(res, 200, 'success', 'Loan payment processed successfully', { data: result });
    } catch (error) {
      next(error);
    }
  }
}

export const loanController = new LoanController();
