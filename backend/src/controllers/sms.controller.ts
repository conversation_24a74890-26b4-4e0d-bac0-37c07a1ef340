import { Request, Response, NextFunction } from 'express';
import { smsService } from '../services/sms.services';
import { User } from '../models';
import { BadRequestError } from '../errors/app_errors';
import logger from '../config/logger';
import { UserRole } from '../enums/enums';
import { sendResponse } from '../utils/response';

class SmsController {
  /**
   * Send SMS to a single recipient
   */
  async sendSms(req: Request, res: Response, next: NextFunction) {
    try {
      const { phoneNumber, message, senderId, refId, isOtp } = req.body;

      // Validate required fields
      if (!phoneNumber || !message) {
        throw new BadRequestError('Phone number and message are required');
      }

      // Check if user has permission to send SMS
      if (req.user?.role !== UserRole.ADMIN && req.user?.role !== UserRole.AGENT) {
        throw new BadRequestError('Insufficient permissions to send SMS');
      }

      const result = await smsService.sendSms(phoneNumber, message, {
        senderId,
        refId,
        isOtp: isOtp || false,
      });

      logger.info('SMS sent successfully', {
        userId: req.user?.id,
        recipient: phoneNumber,
        messageId: result.messageId,
        isOtp: isOtp || false,
      });

      sendResponse(res, 200, 'success', 'SMS sent successfully', {
        data: {
          messageId: result.messageId,
          recipient: phoneNumber,
          sentAt: new Date().toISOString(),
        },
      });
    } catch (error) {
      logger.error('SMS sending failed', {
        userId: req.user?.id,
        error: error.message,
        phoneNumber: req.body.phoneNumber,
      });
      next(error);
    }
  }

  /**
   * Send SMS to multiple recipients
   */
  async sendBulkSms(req: Request, res: Response, next: NextFunction) {
    try {
      const { phoneNumbers, message, senderId, refId, isOtp } = req.body;

      // Validate required fields
      if (!phoneNumbers || !Array.isArray(phoneNumbers) || phoneNumbers.length === 0) {
        throw new BadRequestError('Phone numbers array is required and cannot be empty');
      }

      if (!message) {
        throw new BadRequestError('Message is required');
      }

      // Check if user has permission to send bulk SMS
      if (req.user?.role !== UserRole.ADMIN) {
        throw new BadRequestError('Only admins can send bulk SMS');
      }

      // Limit bulk SMS to prevent abuse
      // if (phoneNumbers.length > 100) {
      //   throw new BadRequestError('Maximum 100 recipients allowed per bulk SMS');
      // }

      const results = await smsService.sendSmsToMany(phoneNumbers, message, {
        senderId,
        refId,
        isOtp: isOtp || false,
      });

      logger.info('Bulk SMS sent successfully', {
        userId: req.user?.id,
        recipientCount: phoneNumbers.length,
        messageIds: results,
        isOtp: isOtp || false,
      });

      sendResponse(res, 200, 'success', 'Bulk SMS sent successfully', {
        data: {
          messageIds: results,
          recipientCount: phoneNumbers.length,
          sentAt: new Date().toISOString(),
        },
      });
    } catch (error) {
      logger.error('Bulk SMS sending failed', {
        userId: req.user?.id,
        error: error.message,
        recipientCount: req.body.phoneNumbers?.length || 0,
      });
      next(error);
    }
  }

  /**
   * Send SMS to users by role
   */
  async sendSmsToRole(req: Request, res: Response, next: NextFunction) {
    try {
      const { role, message, refId, isOtp, onlyActiveUsers } = req.body;

      // Validate required fields
      if (!role || !message) {
        throw new BadRequestError('Role and message are required');
      }

      // Check if user has permission
      if (req.user?.role !== UserRole.ADMIN) {
        throw new BadRequestError('Only admins can send SMS to user roles');
      }

      // Validate role
      if (!Object.values(UserRole).includes(role)) {
        throw new BadRequestError('Invalid user role');
      }

      // Get users by role
      const query: any = { role };
      if (onlyActiveUsers) {
        query.isActive = true;
      }

      const users = await User.find(query).select('phone').lean();

      if (!users.length) {
        sendResponse(res, 200, 'success', 'No users found for the specified role', {
          data: {
            recipientCount: 0,
            sentAt: new Date().toISOString(),
          },
        });
        return;
      }

      const phoneNumbers = users.map(user => user.phone).filter(Boolean);

      if (!phoneNumbers.length) {
        sendResponse(res, 200, 'success', 'No valid phone numbers found for the specified role', {
          data: {
            recipientCount: 0,
            sentAt: new Date().toISOString(),
          },
        });
        return;
      }

      const results = await smsService.sendSmsToMany(phoneNumbers, message, {
        senderId: req.user?.id,
        refId,
        isOtp: isOtp || false,
      });

      logger.info('Role-based SMS sent successfully', {
        userId: req.user?.id,
        targetRole: role,
        recipientCount: phoneNumbers.length,
        messageIds: results,
        isOtp: isOtp || false,
      });

      sendResponse(res, 200, 'success', `SMS sent to all ${role} users successfully`, {
        data: {
          messageIds: results,
          recipientCount: phoneNumbers.length,
          targetRole: role,
          sentAt: new Date().toISOString(),
        },
      });
      return;
    } catch (error) {
      logger.error('Role-based SMS sending failed', {
        userId: req.user?.id,
        error: error.message,
        targetRole: req.body.role,
      });
      next(error);
    }
  }

  /**
   * Get SMS service health status
   */
  async getHealthStatus(req: Request, res: Response, next: NextFunction) {
    try {
      // Check if user has permission
      if (req.user?.role !== UserRole.ADMIN) {
        throw new BadRequestError('Only admins can check SMS service health');
      }

      const isHealthy = await smsService.checkHealth();
      const metrics = smsService.getMetrics();

      sendResponse(res, 200, 'success', 'SMS service health status retrieved', {
        data: {
          isHealthy,
          metrics: {
            sentCount: metrics.sentCount,
            failedCount: metrics.failedCount,
            successRate: Math.round(metrics.successRate * 100),
            lastError: metrics.lastError?.message || null,
          },
          timestamp: new Date().toISOString(),
        },
      });
      return;
    } catch (error) {
      logger.error('Failed to get SMS health status', {
        userId: req.user?.id,
        error: error.message,
      });
      next(error);
    }
  }

  /**
   * Get SMS service metrics
   */
  async getMetrics(req: Request, res: Response, next: NextFunction) {
    try {
      // Check if user has permission
      if (req.user?.role !== UserRole.ADMIN) {
        throw new BadRequestError('Only admins can view SMS metrics');
      }

      const metrics = smsService.getMetrics();

      sendResponse(res, 200, 'success', 'SMS service metrics retrieved', {
        data: {
          totalSent: metrics.sentCount,
          totalFailed: metrics.failedCount,
          successRate: Math.round(metrics.successRate * 100),
          lastError: metrics.lastError
            ? {
                message: metrics.lastError.message,
                timestamp: new Date().toISOString(),
              }
            : null,
          retrievedAt: new Date().toISOString(),
        },
      });
      return;
    } catch (error) {
      logger.error('Failed to get SMS metrics', {
        userId: req.user?.id,
        error: error.message,
      });
      next(error);
    }
  }

  /**
   * Send OTP to user
   */
  async sendOtp(req: Request, res: Response, next: NextFunction) {
    try {
      const { phoneNumber, otp, customMessage } = req.body;

      // Validate required fields
      if (!phoneNumber || !otp) {
        throw new BadRequestError('Phone number and OTP are required');
      }

      // Check if user has permission
      if (req.user?.role !== UserRole.ADMIN && req.user?.role !== UserRole.AGENT) {
        throw new BadRequestError('Insufficient permissions to send OTP');
      }

      const message =
        customMessage || `Your verification code is ${otp}. Do not share this code with anyone.`;

      const result = await smsService.sendSms(phoneNumber, message, {
        isOtp: true,
        refId: `otp_${Date.now()}`,
      });

      logger.info('OTP sent successfully', {
        userId: req.user?.id,
        recipient: phoneNumber,
        messageId: result.messageId,
      });

      sendResponse(res, 200, 'success', 'OTP sent successfully', {
        data: {
          messageId: result.messageId,
          recipient: phoneNumber,
          sentAt: new Date().toISOString(),
        },
      });
      return;
    } catch (error) {
      logger.error('OTP sending failed', {
        userId: req.user?.id,
        error: error.message,
        phoneNumber: req.body.phoneNumber,
      });
      next(error);
    }
  }
}

export const smsController = new SmsController();
