import { Request, Response, NextFunction } from 'express';
import { dashboardService } from '../services/dashboard.services';
import { UserRole } from '../enums/enums';
import { BadRequestError } from '../errors/app_errors';
import logger from '../config/logger';
import { sendResponse } from '../utils/response';

export class DashboardController {
  /**
   * Get dashboard data based on user role
   */
  async getDashboard(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = req.user.userId.toString();
      const userRole = req.user.role;

      // Get dashboard data based on user role
      let dashboardData;
      switch (userRole) {
        case UserRole.ADMIN:
          dashboardData = await dashboardService.getAdminDashboard();
          break;
        case UserRole.SUPERVISOR:
          dashboardData = await dashboardService.getSupervisorDashboard();
          break;
        case UserRole.AGENT:
          dashboardData = await dashboardService.getAgentDashboard(userId);
          break;
        case UserRole.CUSTOMER:
          dashboardData = await dashboardService.getUserDashboard(userId);
          break;
        default:
          throw new BadRequestError('Invalid user role');
      }

      logger.info('Dashboard summary retrieved successfully', {
        userId,
        userRole,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Dashboard data retrieved successfully', {
        data: dashboardData,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get admin dashboard data
   */
  async getAdminDashboard(req: Request, res: Response, next: NextFunction) {
    try {
      const dashboardData = await dashboardService.getAdminDashboard();

      logger.info('Admin dashboard retrieved successfully', {
        userId: req.user.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Admin dashboard data retrieved successfully', {
        data: dashboardData,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get supervisor dashboard data
   */
  async getSupervisorDashboard(req: Request, res: Response, next: NextFunction) {
    try {
      const dashboardData = await dashboardService.getSupervisorDashboard();

      logger.info('Supervisor dashboard retrieved successfully', {
        userId: req.user.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Supervisor dashboard data retrieved successfully', {
        data: dashboardData,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get agent dashboard data
   */
  async getAgentDashboard(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = req.user.userId.toString();
      const dashboardData = await dashboardService.getAgentDashboard(userId);

      logger.info('Agent dashboard retrieved successfully', {
        userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Agent dashboard data retrieved successfully', {
        data: dashboardData,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get customer dashboard data
   */
  async getCustomerDashboard(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = req.user.userId.toString();
      const dashboardData = await dashboardService.getUserDashboard(userId);

      logger.info('Customer dashboard retrieved successfully', {
        userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Customer dashboard data retrieved successfully', {
        data: dashboardData,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Generate reports based on date range
   */
  async getReports(req: Request, res: Response, next: NextFunction) {
    try {
      const { startDate, endDate } = req.query;
      const reportType = req.params.type;

      // Validate dates
      if (!startDate || !endDate) {
        throw new BadRequestError('Start date and end date are required');
      }

      const start = new Date(startDate as string);
      const end = new Date(endDate as string);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        throw new BadRequestError('Invalid date format');
      }

      if (start > end) {
        throw new BadRequestError('Start date must be before end date');
      }

      const dateRange = { startDate: start, endDate: end };

      // Generate comprehensive report data
      const comprehensiveData = await dashboardService.getReports(start, end, req.user.role);

      // Extract specific report data based on type
      let reportData;
      switch (reportType) {
        case 'orders':
          reportData = {
            salesOverview: comprehensiveData.salesOverview,
          };
          break;
        case 'revenue':
          reportData = {
            financialOverview: comprehensiveData.financialOverview,
          };
          break;
        case 'inventory':
          reportData = {
            inventoryStatus: comprehensiveData.inventoryStatus,
          };
          break;
        case 'agents':
          reportData = {
            agentPerformance: comprehensiveData.agentPerformance.agentPerformance,
            teamStats: comprehensiveData.agentPerformance.teamStats,
            topPerformers: comprehensiveData.agentPerformance.topPerformers,
          };
          break;
        default:
          throw new BadRequestError(
            'Invalid report type. Available types: orders, revenue, inventory, agents'
          );
      }

      logger.info('Dashboard report generated successfully', {
        adminId: req.user.userId,
        reportType,
        dateRange,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', `${reportType} report generated successfully`, {
        data: {
          reportType,
          dateRange,
          report: reportData,
        },
      });
    } catch (error) {
      next(error);
    }
  }
}

export const dashboardController = new DashboardController();
