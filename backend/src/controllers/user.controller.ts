import { Request, Response, NextFunction } from 'express';
import { userService } from '../services/user.service';
import { sendResponse } from '../utils/response';
import { NotificationTopic } from '../utils/notification_utils';
import { UserRole } from '../enums/enums';
import { ValidationError } from '../errors/app_errors';

export const login = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { phone } = req.body;
    if (!phone || phone.trim() === '') {
      throw new ValidationError('Phone number is required');
    }
    const result = await userService.login(phone);
    sendResponse(res, 200, 'success', 'Login successful', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const registerAdminOrAgent = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userData = req.body;
    const result = await userService.registerAdminOrAgent(userData, req.user);
    sendResponse(res, 201, 'success', 'Admin/Agent registered successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const registerCustomer = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userData = req.body;
    const result = await userService.registerCustomer(userData, req.user);
    sendResponse(res, 201, 'success', 'Customer registered successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const resendOtp = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { phone } = req.body;
    if (!phone || phone.trim() === '') {
      throw new ValidationError('Phone number is required');
    }
    const result = await userService.resendOtp(phone);
    sendResponse(
      res,
      200,
      'success',
      'OTP resent successfully'
      //   {
      //   data: result,
      // }
    );
  } catch (error) {
    next(error);
  }
};

export const verifyOtp = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { phone, otp } = req.body;
    if (!phone || phone.trim() === '') {
      throw new ValidationError('Phone number is required');
    }
    if (!otp || otp.trim() === '') {
      throw new ValidationError('OTP is required');
    }
    const result = await userService.verifyOtp(phone, otp);
    sendResponse(res, 200, 'success', 'OTP verified successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const getSingleUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.params.id;
    const result = await userService.getSingleUser(userId, req.user);
    sendResponse(res, 200, 'success', 'User retrieved successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const getAllUsers = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // 🔍 Enhanced options with search support
    const options: any = {};

    // Basic filters
    if (req.query.role) {
      options.role = req.query.role as UserRole;
    }
    if (req.query.isActive !== undefined) {
      options.isActive = req.query.isActive === 'true';
    }

    // Pagination with validation
    if (req.query.page) {
      const page = parseInt(req.query.page as string);
      options.page = Math.max(1, page || 1);
    }
    if (req.query.limit) {
      const limit = parseInt(req.query.limit as string);
      options.limit = Math.min(100, Math.max(1, limit || 20));
    }

    // Sorting
    if (req.query.sortBy) {
      options.sortBy = req.query.sortBy as string;
    }
    if (req.query.sortOrder) {
      options.sortOrder = req.query.sortOrder as 'asc' | 'desc';
    }

    // 🚀 Search functionality
    if (req.query.search) {
      options.search = (req.query.search as string).trim();
    }
    if (req.query.searchFields) {
      options.searchFields = (req.query.searchFields as string).split(',');
    }
    if (req.query.limit) {
      options.limit = parseInt(req.query.limit as string);
    }
    if (req.query.sortBy) {
      options.sortBy = req.query.sortBy as string;
    }
    if (req.query.sortOrder) {
      options.sortOrder = req.query.sortOrder as 'asc' | 'desc';
    }

    const result = await userService.getAllUsers(req.user, options);

    // 📊 Enhanced response with search metadata
    const responseData: any = {
      data: result.users,
      meta: {
        pagination: result.pagination,
        ...(result.searchMetadata && { search: result.searchMetadata }),
      },
    };

    const message = options.search
      ? `Found ${result.users.length} users matching "${options.search}"`
      : 'Users retrieved successfully';

    sendResponse(res, 200, 'success', message, responseData);
  } catch (error) {
    next(error);
  }
};

export const updateUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.params.id;
    const updateData = req.body;
    const result = await userService.updateUser(userId, updateData, req.user);
    sendResponse(res, 200, 'success', 'User updated successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const deleteUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.params.id;
    const result = await userService.deleteUser(userId, req.user);
    sendResponse(res, 200, 'success', 'User deleted successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const subscribeToNotifications = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { userId, topic, deviceToken } = req.body;
    const result = await userService.subscribeToTopic(
      userId,
      topic as NotificationTopic,
      deviceToken,
      req.user
    );
    sendResponse(res, 200, 'success', 'Subscribed to notifications successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const getCurrentUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const result = await userService.getSingleUser(req.user.userId.toString(), req.user);
    sendResponse(res, 200, 'success', 'Current user retrieved successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * 🔍 Advanced user search endpoint
 * @route   GET /api/v1/users/search
 * @desc    Search users with advanced filtering and autocomplete
 * @access  Private (Admin, Supervisor)
 */
export const searchUsers = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { q: query, limit, role, isActive, fuzzy, autocomplete } = req.query;

    // Validation
    if (!query || typeof query !== 'string' || query.trim().length < 1) {
      throw new ValidationError('Search query (q) is required and must be at least 1 character');
    }

    const options: any = {};

    // Parse options
    if (limit) {
      const limitNum = parseInt(limit as string);
      options.limit = Math.min(50, Math.max(1, limitNum || 10));
    }

    if (role) {
      options.role = role as UserRole;
    }

    if (isActive !== undefined) {
      options.isActive = isActive === 'true';
    }

    if (fuzzy === 'true') {
      options.fuzzySearch = true;
    }

    if (autocomplete === 'true') {
      options.autocomplete = true;
    }

    const result = await userService.searchUsers(req.user, query as string, options);

    const message = options.autocomplete
      ? `Found ${result.results.length} users and ${result.autocomplete?.length || 0} suggestions for "${query}"`
      : `Found ${result.results.length} users matching "${query}"`;

    sendResponse(res, 200, 'success', message, {
      data: result.results,
      meta: {
        search: result.metadata,
        ...(result.autocomplete && { autocomplete: result.autocomplete }),
      },
    });
  } catch (error) {
    next(error);
  }
};

export const toggleUserActiveStatus = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.params.id;
    const isActive = req.body.isActive;
    const result = await userService.toggleUserActiveStatus(userId, isActive, req.user);
    sendResponse(res, 200, 'success', 'User active status toggled successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const toggleAgentOnDutyStatus = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.params.id;
    const isOnDuty = req.body.isOnDuty;
    const result = await userService.toggleAgentOnDutyStatus(userId, isOnDuty, req.user);
    sendResponse(res, 200, 'success', 'Agent on duty status toggled successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const changeUserRole = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.params.id;
    const { newRole } = req.body;

    if (!newRole || !Object.values(UserRole).includes(newRole)) {
      throw new ValidationError('Valid role is required');
    }

    const result = await userService.changeUserRole(userId, newRole, req.user);
    sendResponse(res, 200, 'success', 'User role changed successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};
