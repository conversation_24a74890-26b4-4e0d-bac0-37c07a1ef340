/**
 * Simple test script to verify image upload functionality
 * Run with: node test-image-upload.js
 */

const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000/api/v1';
const TEST_IMAGE_PATH = path.join(__dirname, '..', 'public', 'images', 'cylinders', '6kg.png');

// Test credentials (replace with actual admin credentials)
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123'
};

async function testImageUpload() {
  try {
    console.log('🚀 Starting Image Upload Test...\n');

    // Step 1: Login to get auth token
    console.log('1. Logging in...');
    const loginResponse = await axios.post(`${BASE_URL}/users/login`, TEST_CREDENTIALS);
    const token = loginResponse.data.message.data.token;
    console.log('✅ Login successful\n');

    // Step 2: Check if test image exists
    console.log('2. Checking test image...');
    if (!fs.existsSync(TEST_IMAGE_PATH)) {
      console.log('❌ Test image not found at:', TEST_IMAGE_PATH);
      console.log('Please ensure you have a test image at the specified path');
      return;
    }
    console.log('✅ Test image found\n');

    // Step 3: Upload image
    console.log('3. Uploading image...');
    const formData = new FormData();
    formData.append('image', fs.createReadStream(TEST_IMAGE_PATH));
    formData.append('category', 'cylinders');

    const uploadResponse = await axios.post(
      `${BASE_URL}/images/upload`,
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${token}`
        }
      }
    );

    console.log('✅ Image uploaded successfully!');
    console.log('📄 Upload Response:', JSON.stringify(uploadResponse.data, null, 2));
    console.log('🖼️  Image URL:', uploadResponse.data.message.data.imageUrl);
    console.log('📁 Image Path:', uploadResponse.data.message.data.imagePath);

    // Step 4: Test image serving
    console.log('\n4. Testing image serving...');
    const imageUrl = uploadResponse.data.message.data.imageUrl;
    const fullImageUrl = `${BASE_URL.replace('/api/v1', '')}${imageUrl}`;
    
    try {
      const imageResponse = await axios.get(fullImageUrl, { 
        responseType: 'arraybuffer',
        timeout: 5000 
      });
      console.log('✅ Image serving works!');
      console.log('📊 Image size:', imageResponse.data.length, 'bytes');
      console.log('🔗 Full URL:', fullImageUrl);
    } catch (imageError) {
      console.log('❌ Image serving failed:', imageError.message);
      console.log('🔗 Attempted URL:', fullImageUrl);
    }

    console.log('\n🎉 Image upload test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.response) {
      console.error('📄 Error Response:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Run the test
testImageUpload();
