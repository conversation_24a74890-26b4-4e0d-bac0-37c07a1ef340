import { Schema, model, Document } from 'mongoose';
import { PaymentMethod, PaymentStatus } from '../enums/enums';

export interface ILoanPayment extends Document {
  loan: Schema.Types.ObjectId | string;             // ref Loan
  installmentId?: Schema.Types.ObjectId | string;   // ref Installment (recommended)
  payment: Schema.Types.ObjectId | string;          // ref existing Payment
  paymentAmount: number;                            // principal applied (single field)
  paymentDate: Date;
  paymentMethod: PaymentMethod;
  status: PaymentStatus;                            // mirror Payment status (e.g., CAPTURED)
  dueDate: Date;                                    // snapshot from installment
  daysLate?: number;                                // info only, no fee logic
  createdAt: Date;
  updatedAt: Date;
}

const LoanPaymentSchema = new Schema<ILoanPayment>(
  {
    loan: { type: Schema.Types.ObjectId, ref: 'Loan', required: true },
    installmentId: { type: Schema.Types.ObjectId, ref: 'Installment' },
    payment: { type: Schema.Types.ObjectId, ref: 'Payment', required: true },
    paymentAmount: { type: Number, required: true, min: 0 },
    paymentDate: { type: Date, required: true },
    paymentMethod: { type: String, enum: Object.values(PaymentMethod), required: true },
    status: { type: String, enum: Object.values(PaymentStatus), required: true },
    dueDate: { type: Date, required: true },
    daysLate: { type: Number, min: 0, default: 0 },
  },
  { timestamps: true }
);

// Indexes
LoanPaymentSchema.index({ loan: 1, paymentDate: 1 });
LoanPaymentSchema.index({ installmentId: 1 });
LoanPaymentSchema.index({ payment: 1 });

export const LoanPayment = model<ILoanPayment>('LoanPayment', LoanPaymentSchema);
