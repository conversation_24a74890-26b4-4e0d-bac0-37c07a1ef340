import { CylinderStatus, CylinderType, CylinderMaterial } from '../enums/enums';
import { BadRequestError } from '../errors/app_errors';
import { Schema, model, Document } from 'mongoose';

/**
 * Represents a gas cylinder in inventory.
 */
export interface ICylinder extends Document {
  type: CylinderType;
  material: CylinderMaterial;
  price: number;
  cost: number; // Purchase cost for profit calculations
  createdAt: Date;
  updatedAt: Date;
  status: CylinderStatus;

  // Image Management - Simple path storage
  imageUrl?: string; // Legacy field for backward compatibility
  imagePath?: string; // New field for uploaded images (full path: uploads/images/cylinders/...)
  description?: string;
  quantity: number; // Total physical count
  reserved: number; // Reserved for orders but not yet sold
  sold: number; // Total sold count (for reporting)
  lastRestockedAt?: Date; // Last restock date
  minimumStockLevel: number; // Reorder threshold
  availableQuantity: number; // Derived field for available quantity

  // Soft delete fields
  isDeleted: boolean; // Soft delete flag
  deletedAt?: Date; // When it was deleted
  deletedBy?: string; // Who deleted it (user ID)
}

const CylinderSchema = new Schema<ICylinder>(
  {
    type: {
      type: String,
      enum: Object.values(CylinderType),
      required: true,
    },
    material: {
      type: String,
      enum: Object.values(CylinderMaterial),
      required: true,
    },
    status: {
      type: String,
      enum: Object.values(CylinderStatus),
      default: CylinderStatus.Active,
    },
    price: { type: Number, required: true, min: 0 },
    cost: { type: Number, required: true, min: 0 },
    imageUrl: { type: String }, // Legacy field
    imagePath: { type: String }, // New field for uploaded images
    description: { type: String },
    quantity: { type: Number, default: 0, min: 0 },
    reserved: { type: Number, default: 0, min: 0 },
    sold: { type: Number, default: 0, min: 0 },
    lastRestockedAt: Date,
    minimumStockLevel: { type: Number, default: 10, min: 0 },

    // Soft delete fields
    isDeleted: { type: Boolean, default: false },
    deletedAt: { type: Date },
    deletedBy: { type: String },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Compound index
CylinderSchema.index({ type: 1, material: 1, status: 1 });
CylinderSchema.index({ quantity: 1, minimumStockLevel: 1 });
CylinderSchema.index({ type: 1, status: 1 }); // Fast type/status queries
CylinderSchema.index({ quantity: 1 }); // Low stock alerts
CylinderSchema.index({ quantity: 1, reserved: 1 }); // Helps with availableQuantity calculations

// Soft delete indexes
CylinderSchema.index({ isDeleted: 1 }); // Filter deleted items
CylinderSchema.index({ isDeleted: 1, type: 1, status: 1 }); // Combined queries
CylinderSchema.index({ deletedAt: 1 }); // Sort by deletion date

// Virtuals
CylinderSchema.virtual('availableQuantity').get(function (this: ICylinder) {
  return Math.max(0, this.quantity - this.reserved);
});

// Virtual for getting the correct image URL (prioritize uploaded images)
CylinderSchema.virtual('currentImageUrl').get(function (this: ICylinder) {
  if (this.imagePath) {
    // Remove 'uploads/' prefix for URL serving
    const urlPath = this.imagePath.replace(/^uploads\//, '');
    return `/api/v1/images/${urlPath}`;
  }
  return this.imageUrl; // Fallback to legacy imageUrl
});

// Hook for ensuring data consisitency
CylinderSchema.pre('save', function (next) {
  if (this.reserved > this.quantity) {
    // this.reserved = this.quantity;
    throw new BadRequestError('Reserved quantity cannot exceed total quantity');
  }
  next();
});

export const Cylinder = model<ICylinder>('Cylinder', CylinderSchema);
