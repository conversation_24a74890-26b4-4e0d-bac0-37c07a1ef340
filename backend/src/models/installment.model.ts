import { Schema, model, Document } from 'mongoose';
import { InstallmentStatus } from '../enums/enums';

export interface IInstallment extends Document {
  loanId: Schema.Types.ObjectId | string;
  seq: number;                 // 1..termMonths
  dueDate: Date;
  amountDue: number;           // principal slice only
  amountPaid: number;          // applied so far
  status: InstallmentStatus;   // DUE/PARTIAL/PAID/OVERDUE
  isOverdue?: boolean;         // optional flag (cron or computed)
  createdAt: Date;
  updatedAt: Date;
}

const InstallmentSchema = new Schema<IInstallment>(
  {
    loanId: { type: Schema.Types.ObjectId, ref: 'Loan', required: true },
    seq: { type: Number, required: true, min: 1 },
    dueDate: { type: Date, required: true },
    amountDue: { type: Number, required: true, min: 0 },
    amountPaid: { type: Number, required: true, min: 0, default: 0 },
    status: { type: String, enum: Object.values(InstallmentStatus), default: InstallmentStatus.DUE },
    isOverdue: { type: Boolean, default: false },
  },
  { timestamps: true }
);

// Uniqueness + performance
InstallmentSchema.index({ loanId: 1, seq: 1 }, { unique: true });
InstallmentSchema.index({ loanId: 1, status: 1 });
InstallmentSchema.index({ dueDate: 1, status: 1 });
InstallmentSchema.index({ isOverdue: 1, dueDate: 1 });

export const Installment = model<IInstallment>('Installment', InstallmentSchema);
