import { Schema, model, Document } from 'mongoose';
import { LoanType, LoanStatus } from '../enums/enums';

export type ScheduleType = 'EQUAL_PRINCIPAL' | 'CUSTOM';

export interface ILoan extends Document {
  customerId: Schema.Types.ObjectId | string;
  orderId?: Schema.Types.ObjectId | string;     // optional: link to financed order
  type: LoanType;                                // PRINCIPAL_ONLY
  principal: number;                             // original financed amount
  termMonths: number;                            // total months to repay
  startDate: Date;                               // contract start
  scheduleType: ScheduleType;                    // default EQUAL_PRINCIPAL
  outstandingPrincipal: number;                  // decreases with payments
  status: LoanStatus;                            // ACTIVE/PAID/etc.
  createdAt: Date;
  updatedAt: Date;
}

const LoanSchema = new Schema<ILoan>(
  {
    customerId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    orderId: { type: Schema.Types.ObjectId, ref: 'Order' },
    type: { type: String, enum: Object.values(LoanType), default: LoanType.PRINCIPAL_ONLY },
    principal: { type: Number, required: true, min: 0 },
    termMonths: { type: Number, required: true, min: 1 },
    startDate: { type: Date, required: true },
    scheduleType: { type: String, enum: ['EQUAL_PRINCIPAL', 'CUSTOM'], default: 'EQUAL_PRINCIPAL' },
    outstandingPrincipal: { type: Number, required: true, min: 0 },
    status: { type: String, enum: Object.values(LoanStatus), default: LoanStatus.ACTIVE },
  },
  { timestamps: true }
);

// Indexes
LoanSchema.index({ customerId: 1, status: 1 });
LoanSchema.index({ orderId: 1 }, { sparse: true });
LoanSchema.index({ createdAt: -1 });

export const Loan = model<ILoan>('Loan', LoanSchema);
