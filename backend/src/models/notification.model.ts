import { Schema, model } from 'mongoose';
import { NotificationTopic } from '../utils/notification_utils';
import { NotificationStatus } from '../enums/enums';
import { INotification } from '../types/interfaces';

const NotificationSchema = new Schema<INotification>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    body: {
      type: String,
      required: true,
    },
    data: {
      type: Map,
      of: String,
    },
    imageUrl: String,
    topic: {
      type: String,
      enum: Object.values(NotificationTopic),
    },
    status: {
      type: String,
      enum: Object.values(NotificationStatus),
      default: NotificationStatus.PENDING,
    },
    deliveredAt: Date,
    readAt: Date,
    error: String,
    result: Schema.Types.Mixed,
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
      transform: (doc, ret) => {
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Indexes for faster queries
NotificationSchema.index({ userId: 1 });
NotificationSchema.index({ status: 1 });
NotificationSchema.index({ createdAt: -1 });
NotificationSchema.index({ topic: 1 });

export const Notification = model<INotification>('Notification', NotificationSchema);
