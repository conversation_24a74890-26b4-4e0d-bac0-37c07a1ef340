import { Schema, model, Document } from 'mongoose';
import { PaymentMethod, PaymentStatus } from '../enums/enums';

/**
 * Interface for raw payment gateway responses
 * Supports multiple payment providers (WaaFi, Zaad, etc.)
 */
export interface IGatewayRawResponse {
  provider: 'WAAFI' | 'ZAAD' | 'EDAHAB' | 'OTHER';
  operation: 'PURCHASE' | 'CAPTURE' | 'CANCEL' | 'REFUND' | 'WEBHOOK' | 'ERROR';
  timestamp: Date;
  requestId?: string;
  responseCode?: string;
  responseMessage?: string;
  rawRequest?: Record<string, any>; // Sanitized request payload
  rawResponse?: Record<string, any>; // Complete gateway response
  httpStatus?: number;
  processingTime?: number; // Response time in milliseconds
  errorDetails?: {
    errorCode?: string;
    errorMessage?: string;
    stackTrace?: string;
  };
  metadata?: Record<string, any>; // Additional provider-specific data
}

export interface IPayment extends Document {
  orderId: Schema.Types.ObjectId | string;
  userId: Schema.Types.ObjectId | string;
  method: PaymentMethod;
  amount: number;
  currency?: string;
  transactionId?: string;
  status: PaymentStatus;
  createdAt: Date;
  updatedAt: Date;
  paidAt?: Date;
  failedAt?: Date;
  cancelledAt?: Date;
  gatewayResponses?: IGatewayRawResponse[]; // Array to store all gateway interactions
  metadata?: {
    deliveryDetails?: {
      estimatedDeliveryTime?: string;
      deliveryAddress?: string;
    };
    finalCapture?: boolean;
    cancellationReason?: string;
    initiatedAt?: Date;
    // Loan payment metadata
    loanId?: Schema.Types.ObjectId | string;
    installmentId?: Schema.Types.ObjectId | string;
    allocationStrategy?: 'AUTO' | 'TARGETED';
  };
}

const PaymentSchema = new Schema<IPayment>(
  {
    orderId: {
      type: Schema.Types.ObjectId,
      ref: 'Order',
      required: false,
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    method: {
      type: String,
      enum: Object.values(PaymentMethod),
      default: PaymentMethod.CASH,
    },
    amount: { type: Number, required: true, min: 0 },
    currency: {
      type: String,
      required: false,
      enum: ['SOS', 'USD'],
      default: 'USD',
    },
    transactionId: String,
    status: {
      type: String,
      enum: Object.values(PaymentStatus),
      default: PaymentStatus.PENDING,
    },
    paidAt: Date,
    failedAt: Date,
    cancelledAt: Date,
    gatewayResponses: {
      type: [
        {
          provider: {
            type: String,
            enum: ['WAAFI', 'ZAAD', 'EDAHAB', 'OTHER'],
            default: 'WAAFI',
            required: true,
          },
          operation: {
            type: String,
            enum: ['PURCHASE', 'CAPTURE', 'CANCEL', 'REFUND', 'WEBHOOK', 'ERROR'],
            required: true,
          },
          timestamp: {
            type: Date,
            default: Date.now,
            required: true,
          },
          requestId: String,
          responseCode: String,
          responseMessage: String,
          rawRequest: {
            type: Schema.Types.Mixed,
            default: {},
          },
          rawResponse: {
            type: Schema.Types.Mixed,
            default: {},
          },
          httpStatus: Number,
          processingTime: Number,
          errorDetails: {
            errorCode: String,
            errorMessage: String,
            stackTrace: String,
          },
          metadata: {
            type: Schema.Types.Mixed,
            default: {},
          },
        },
      ],
      default: [],
    },
    metadata: {
      type: Schema.Types.Mixed,
      default: {},
    },
  },
  { timestamps: true }
);

// Indexes for performance
PaymentSchema.index({ orderId: 1 });
PaymentSchema.index({ userId: 1 });
PaymentSchema.index({ transactionId: 1 });
PaymentSchema.index({ status: 1 });
PaymentSchema.index({ method: 1 });
PaymentSchema.index({ createdAt: -1 });
PaymentSchema.index({ 'gatewayResponses.provider': 1 });
PaymentSchema.index({ 'gatewayResponses.operation': 1 });
PaymentSchema.index({ 'gatewayResponses.timestamp': -1 });

// Compound indexes for common queries
PaymentSchema.index({ userId: 1, status: 1 });
PaymentSchema.index({ orderId: 1, status: 1 });
PaymentSchema.index({ transactionId: 1, status: 1 });

export const Payment = model<IPayment>('Payment', PaymentSchema);
