// app_errors.ts
export class AppError extends Error {
  statusCode: number;
  status: 'fail' | 'error';
  isOperational: boolean;
  details?: unknown;
  code?: string;
  parentCode: string;

  constructor(
    message: string,
    statusCode: number,
    parentCode: string,
    options?: {
      details?: unknown;
      code?: string;
    }
  ) {
    super(message);

    this.statusCode = statusCode;
    this.status = String(statusCode).startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;
    this.parentCode = parentCode;

    if (options?.details) this.details = options.details;
    if (options?.code) this.code = options.code;

    Error.captureStackTrace(this, this.constructor);
  }
}

// 🔍 404 Not Found
export class NotFoundError extends AppError {
  constructor(message = 'Resource not found', options?: { details?: unknown; code?: string }) {
    super(message, 404, 'NOT_FOUND', options);
  }
}

// 🛑 400 Bad Request
export class BadRequestError extends AppError {
  constructor(message = 'Bad request', options?: { details?: unknown; code?: string }) {
    super(message, 400, 'BAD_REQUEST', options);
  }
}

// 🧪 400 Validation Error
export class ValidationError extends AppError {
  constructor(message = 'Validation failed', options?: { details?: unknown; code?: string }) {
    super(message, 400, 'VALIDATION_ERROR', options);
  }
}

// 🔒 401 Unauthorized
export class UnauthorizedError extends AppError {
  constructor(message = 'Unauthorized', options?: { details?: unknown; code?: string }) {
    super(message, 401, 'UNAUTHORIZED', options);
  }
}

// ⛔ 403 Forbidden
export class ForbiddenError extends AppError {
  constructor(message = 'Forbidden', options?: { details?: unknown; code?: string }) {
    super(message, 403, 'FORBIDDEN', options);
  }
}

// ♻️ 409 Conflict
export class DuplicateResourceError extends AppError {
  constructor(message = 'Resource already exists', options?: { details?: unknown; code?: string }) {
    super(message, 409, 'DUPLICATE_RESOURCE', options);
  }
}

// 💥 500 Internal Server Error
export class InternalServerError extends AppError {
  constructor(message = 'Internal server error', options?: { details?: unknown; code?: string }) {
    super(message, 500, 'INTERNAL_SERVER_ERROR', options);
  }
}

// ======================
// 📱 Hormuud SMS/OTP Errors
// ======================

export class HormuudSmsError extends AppError {
  constructor(
    hormuudResponseCode: string,
    responseData: any,
    options?: {
      recipient?: string;
      messageContent?: string;
      isOtp?: boolean;
      details?: unknown;
    }
  ) {
    const errorConfig = {
      '201': { message: 'Hormuud authentication failed', statusCode: 401 },
      '203': { message: 'Invalid sender ID', statusCode: 400 },
      '204': { message: 'Zero SMS balance', statusCode: 402 },
      '205': { message: 'Insufficient SMS balance', statusCode: 402 },
      '206': { message: options?.isOtp ? 'OTP too long' : 'Message too long', statusCode: 400 },
      '207': { message: 'Invalid mobile number', statusCode: 400 },
      '500': { message: 'Hormuud internal error', statusCode: 500 },
    }[hormuudResponseCode] || { message: 'Unknown Hormuud error', statusCode: 500 };

    super(errorConfig.message, errorConfig.statusCode, `HORMUUD_${hormuudResponseCode}`, {
      details: {
        ...responseData,
        ...(options?.recipient && { recipient: options.recipient }),
        ...(options?.messageContent && { messageContent: options.messageContent }),
        isOtp: options?.isOtp || false,
      },
      code: hormuudResponseCode,
    });
  }
}

export class HormuudServiceError extends AppError {
  constructor(
    message = 'Hormuud service unavailable',
    options?: {
      retryAfter?: number;
      isOtp?: boolean;
      details?: unknown;
    }
  ) {
    super(message, 503, 'HORMUUD_UNAVAILABLE', {
      ...options,
      details: {
        service: 'sms',
        ...(options?.isOtp && { service: 'otp' }),
        ...(options?.retryAfter && { retryAfter: options.retryAfter }),
      },
    });
  }
}

// ======================
// 💳 WaaFi Payment Errors
// ======================

export class PaymentError extends AppError {
  constructor(
    message: string,
    statusCode: number,
    parentCode: string,
    options?: {
      paymentId?: string;
      orderId?: string;
      amount?: number;
      details?: unknown;
    }
  ) {
    super(message, statusCode, parentCode, {
      ...options,
      details: {
        ...(options?.paymentId && { paymentId: options.paymentId }),
        ...(options?.orderId && { orderId: options.orderId }),
        ...(options?.amount && { amount: options.amount }),
        ...(typeof options?.details === 'object' && options?.details !== null
          ? options.details
          : {}),
      },
    });
  }
}

// Core WaaFi Errors
export class WaaFiPaymentFailed extends PaymentError {
  constructor(
    message = 'WaaFi payment failed',
    options?: {
      paymentId?: string;
      orderId?: string;
      amount?: number;
      details?: unknown;
    }
  ) {
    super(message, 400, 'WAAFI_PAYMENT_FAILED', options);
  }
}

export class WaaFiPreauthExpired extends PaymentError {
  constructor(
    message = 'WaaFi preauthorization expired',
    options?: {
      paymentId?: string;
      orderId?: string;
      expiryDate?: Date;
    }
  ) {
    super(message, 400, 'WAAFI_PREAUTH_EXPIRED', {
      ...options,
      details: {
        ...(options?.expiryDate && { expiryDate: options.expiryDate }),
      },
    });
  }
}

export class WaaFiServiceUnavailable extends PaymentError {
  constructor(
    message = 'WaaFi payment service unavailable',
    options?: {
      retryAfter?: number;
    }
  ) {
    super(message, 503, 'WAAFI_UNAVAILABLE', {
      details: {
        ...(options?.retryAfter && { retryAfter: options.retryAfter }),
      },
    });
  }
}

// Utility Errors
export class InsufficientFundsError extends PaymentError {
  constructor(
    amount: number,
    options?: {
      paymentId?: string;
      balance?: number;
    }
  ) {
    super(`Insufficient funds for payment of ${amount}`, 400, 'INSUFFICIENT_FUNDS', {
      ...options,
      amount,
      details: {
        ...(options?.balance && { availableBalance: options.balance }),
      },
    });
  }
}

export class PaymentAlreadyCompleted extends PaymentError {
  constructor(
    paymentId: string,
    options?: {
      orderId?: string;
      completedAt?: Date;
    }
  ) {
    super(`Payment ${paymentId} already completed`, 409, 'PAYMENT_COMPLETED', {
      paymentId,
      ...options,
      details: {
        ...(options?.completedAt && { completedAt: options.completedAt }),
      },
    });
  }
}
