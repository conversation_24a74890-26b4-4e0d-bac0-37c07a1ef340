process.on('unhandledRejection', err => {
  console.error('💥 Unhandled Promise Rejection:', err);
});

process.on('uncaughtException', err => {
  console.error('💥 Uncaught Exception:', err);
});

import { TokenPayload } from './types/interfaces';

// Type augmentation for Request
declare global {
  namespace Express {
    interface Request {
      user?: TokenPayload;
    }
  }
}

import app from './app';
import { connectDb as _db } from './config/db';
import { config } from './config/env_config';
import logger from './config/logger';

// Initialize database connection when the module is loaded
const startServer = async () => {
  console.log('📅 DEPLOYMENT TIME:', new Date().toISOString());
  try {
    let port = config.server.port;
    let serverUrl = config.server.url;
    await _db();

    app.listen(port, '0.0.0.0', () => {
      // console.log(`Server is running on http://localhost:${port}`);
      // console.log(`Server is running on ${serverUrl}`);
      logger.info(`Server is running on ${serverUrl}`);

      //
    });
  } catch (error) {
    // console.error(`Failed to connect to the database: ${error.message}`);
    logger.error(`Failed to connect to the database: ${error.message}`);
    process.exit(1); // Exit the process if the database connection fails
  }
};

startServer();

/*


               ? INVENTORY DOCUMENTATION
 🧩 Example Lifecycle
| Step            | Method Used    | Inventory Impact                                    |
| --------------- | -------------- | --------------------------------------------------- |
| Order placed    | `reserve()`    | `reserved += qty`                                   |
| Order cancelled | `release()`    | `reserved -= qty`                                   |
| Order delivered | `markAsSold()` | `quantity -= qty`, `reserved -= qty`, `sold += qty` |

🚦 Summary
| Method       | Purpose                             | Affects Fields                             |
| ------------ | ----------------------------------- | ------------------------------------------ |
| `reserve`    | Temporarily hold items for an order | `reserved` (+)                             |
| `release`    | Undo a reservation                  | `reserved` (-)                             |
| `markAsSold` | Finalize sale after delivery        | `quantity` (-), `reserved` (-), `sold` (+) |


*/
