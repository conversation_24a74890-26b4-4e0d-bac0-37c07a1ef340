import dotenv from 'dotenv';
import path from 'path';

// Determine environment and load appropriate .env file
const nodeEnv = process.env.NODE_ENV || 'development';
const envFile = nodeEnv === 'production' ? '.env.production' : '.env.development';

// console.log(`🔧 Loading environment: ${nodeEnv}`);
// console.log(`📄 Environment file: ${envFile}`);

dotenv.config({ path: path.resolve(process.cwd(), envFile) });

function requireEnv(varName: any) {
  const value = process.env[varName];
  if (!value) {
    throw new Error(`❌ Missing required environment variable: ${varName}`);
  }
  return value;
}

const server = {
  env: nodeEnv,
  port: parseInt(process.env.PORT || '3000'),
  url: requireEnv('SERVER_URL'),
  saltRounds: parseInt(process.env.SALT_ROUND || '10'),
  jwtSecret: requireEnv('JWT_SECRET'),
  qrSecretKey: requireEnv('QR_SECRET_KEY'),
  databaseUrl: requireEnv('DATABASE_URL'),
  disableOtpVerification: process.env.DISABLE_OTP_VERIFICATION === 'true',
  // CORS configuration
  corsOrigins: process.env.CORS_ORIGINS?.split(',') || [
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    'http://localhost:8080',
    'http://127.0.0.1:8080',
    'http://*************:3000',
    'http://*************:8080',
  ],
  frontendUrl: process.env.FRONTEND_URL || 'http://*************:3000',
};

// 🔥 Firebase
const firebase = {
  projectId: requireEnv('FIREBASE_PROJECT_ID'),
  privateKey: requireEnv('FIREBASE_PRIVATE_KEY').replace(/\\n/g, '\n'),
  clientEmail: requireEnv('FIREBASE_CLIENT_EMAIL'),
};

// 💳 Payment - Hodan
//  const hodan = {
//   merchantUid: requireEnv('HODAN_MERCHANT_UID'),
//   apiUserId: requireEnv('HODAN_MERCHANT_API_USER_ID'),
//   apiKey: requireEnv('HODAN_MERCHANT_API_KEY'),
//   apiUrl: requireEnv('HODAN_MERCHANT_API_URL'),
// };

// 💳 Payment - Rasiin
const payment = {
  rasiin: {
    merchantUid: requireEnv('RASIIN_MERCHANT_UID'),
    apiUserId: requireEnv('RASIIN_MERCHANT_API_USER_ID'),
    apiKey: requireEnv('RASIIN_MERCHANT_API_KEY'),
    apiUrl: requireEnv('RASIIN_MERCHANT_API_URL'),
  },
};

// 📩 SMS Provider
const sms = {
  username: requireEnv('SMS_USERNAME'),
  password: requireEnv('SMS_PASSWORD'),
  providerUrl: requireEnv('SMS_PROVIDER_URL'),
  senderId: requireEnv('SMS_SENDER_ID'),
  timeout: parseInt(process.env.SMS_TIMEOUT || '30000'),
};

const email = {
  user: requireEnv('EMAIL_USER'),
  pass: requireEnv('EMAIL_PASS'),
  from: requireEnv('EMAIL_FROM'),
  host: requireEnv('EMAIL_HOST'),
  port: parseInt(requireEnv('EMAIL_PORT')),
  secure: requireEnv('EMAIL_SECURE') === 'true',
};

export const config = {
  server,
  firebase,
  // hodan, // uncomment when needed
  payment,
  sms,
  email,
} as const; // Validate QR payload using JWT
