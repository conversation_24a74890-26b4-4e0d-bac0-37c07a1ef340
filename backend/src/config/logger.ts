// import winston from 'winston';

// const { combine, timestamp, printf, colorize, errors } = winston.format;

// const logFormat = printf(({ level, message, timestamp, stack }) => {
//   return `${timestamp} [${level}]: ${stack || message}`;
// });

// const logger = winston.createLogger({
//   level: 'info',
//   format: combine(
//     colorize(),
//     timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
//     errors({ stack: true }),
//     logFormat
//   ),
//   transports: [
//     new winston.transports.Console(),
//     // new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
//     //   new winston.transports.File({ filename: 'logs/combined.log' }),
//   ],
//   // exceptionHandlers: [new winston.transports.File({ filename: 'logs/exceptions.log' })],
// });

// export default logger;
import winston from 'winston';

const { combine, timestamp, printf, colorize, errors } = winston.format;

const logFormat = printf(({ level, message, timestamp, stack, ...metadata }) => {
  let logMessage = `${timestamp} [${level}]: ${stack || message}`;

  if (Object.keys(metadata).length > 0) {
    logMessage += `\n${JSON.stringify(metadata, null, 2)}`;
  }

  return logMessage;
});

const logger = winston.createLogger({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  format: combine(
    colorize(),
    timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    errors({ stack: true }),
    logFormat
  ),
  transports: [new winston.transports.Console()],
  exitOnError: false, // Continue on logging errors
});

// Export for use in other files
export default logger;

// Optional: Export individual log levels as functions
export const log = {
  info: (message: string, meta?: any) => logger.info(message, meta),
  error: (message: string, meta?: any) => logger.error(message, meta),
  warn: (message: string, meta?: any) => logger.warn(message, meta),
  debug: (message: string, meta?: any) => logger.debug(message, meta),
  verbose: (message: string, meta?: any) => logger.verbose(message, meta),
};
