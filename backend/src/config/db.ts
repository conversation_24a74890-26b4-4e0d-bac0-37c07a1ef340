import mongoose from 'mongoose';
import { config } from './env_config';
import logger from './logger';

const { connect: _connect, connection: dbConnection } = mongoose;

export const connectDb = async (retries = 5, delay = 5000) => {
  while (retries) {
    try {
      const database_url = config.server.databaseUrl;
      await _connect(database_url);
      // logger.info(
      // `Database connected successfully to : ${database_url.split('@')[1] || database_url}`
      // );
      logger.info('Database connected successfully');
      return;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown database disconnection error ';
      logger.error(
        `Failed to connect to the database. Retries left: ${retries}. Error: ${errorMessage}`
      );
      retries -= 1;
      await new Promise(res => setTimeout(res, delay));
    }
  }
  throw new Error('Database connection failed after multiple retries');
};

// Function to gracefully close the database connection
export const closeDb = async () => {
  try {
    await dbConnection.close();
    logger.info('Database connection closed gracefully');
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown database close error ';
    logger.error(`Error while closing the database connection: ${errorMessage}`);
  }
};
