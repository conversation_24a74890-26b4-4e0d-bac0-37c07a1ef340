import { AppMessageService } from '../constants/app_message.service';

/**
 * Enhanced Messaging Service Test Suite
 * Tests the new per-method branding control features
 */

class EnhancedMessagingTester {
  private messageService: AppMessageService;
  private testResults: { passed: number; failed: number; total: number } = {
    passed: 0,
    failed: 0,
    total: 0
  };

  constructor() {
    this.messageService = new AppMessageService({ lang: 'en' });
  }

  private assertEqual(actual: string, expected: string, testName: string): void {
    this.testResults.total++;
    if (actual === expected) {
      this.testResults.passed++;
      console.log(`✅ ${testName}`);
    } else {
      this.testResults.failed++;
      console.log(`❌ ${testName}`);
      console.log(`   Expected: "${expected}"`);
      console.log(`   Actual:   "${actual}"`);
    }
  }

  private assertContains(actual: string, expected: string, testName: string): void {
    this.testResults.total++;
    if (actual.includes(expected)) {
      this.testResults.passed++;
      console.log(`✅ ${testName}`);
    } else {
      this.testResults.failed++;
      console.log(`❌ ${testName}`);
      console.log(`   Expected to contain: "${expected}"`);
      console.log(`   Actual: "${actual}"`);
    }
  }

  private assertNotContains(actual: string, unexpected: string, testName: string): void {
    this.testResults.total++;
    if (!actual.includes(unexpected)) {
      this.testResults.passed++;
      console.log(`✅ ${testName}`);
    } else {
      this.testResults.failed++;
      console.log(`❌ ${testName}`);
      console.log(`   Expected NOT to contain: "${unexpected}"`);
      console.log(`   Actual: "${actual}"`);
    }
  }

  runTests(): void {
    console.log('🧪 Running Enhanced Messaging Service Tests...\n');

    // Test OTP messages (no branding by default)
    this.testOtpMessageDefaults();
    this.testOtpMessageWithBranding();

    // Test order messages (full branding by default)
    this.testOrderConfirmedDefaults();
    this.testOrderConfirmedWithoutBranding();

    // Test delivery messages (mixed branding)
    this.testDeliveryAssignedDefaults();
    this.testDeliveryStartedDefaults();

    // Test alert messages (appropriate branding)
    this.testLowStockAlertDefaults();
    this.testEmergencyAlertDefaults();

    // Test payment messages (strategic branding)
    this.testPaymentConfirmedDefaults();
    this.testPaymentFailedDefaults();

    // Test welcome messages (role-based branding)
    this.testWelcomeMessageDefaults();

    // Test utility methods
    this.testUtilityMethods();

    // Test multilingual support
    this.testMultilingualSupport();

    this.printResults();
  }

  private testOtpMessageDefaults(): void {
    console.log('\n📱 Testing OTP Message Defaults...');
    
    const otp = this.messageService.otpMessage('123456');
    
    // Should NOT contain app name prefix by default
    this.assertNotContains(otp, 'GasDelivery -', 'OTP should not include app name prefix by default');
    
    // Should NOT contain company signature by default
    this.assertNotContains(otp, 'Thank you,', 'OTP should not include company signature by default');
    
    // Should NOT contain app footer by default
    this.assertNotContains(otp, 'Download GasDelivery:', 'OTP should not include app footer by default');
    
    // Should contain the OTP code
    this.assertContains(otp, '123456', 'OTP should contain the verification code');
    
    // Should contain expiration info
    this.assertContains(otp, 'Expires in', 'OTP should contain expiration information');
  }

  private testOtpMessageWithBranding(): void {
    console.log('\n📱 Testing OTP Message with Branding...');
    
    const otp = this.messageService.otpMessage('123456', 5, {
      includeAppNamePrefix: true,
      includeCompanySignature: true,
    });
    
    // Should contain app name prefix when requested
    this.assertContains(otp, 'GasDelivery - Verification Code', 'OTP should include app name prefix when requested');
    
    // Should contain company signature when requested
    this.assertContains(otp, 'Thank you,', 'OTP should include company signature when requested');
    
    // Should contain the OTP code
    this.assertContains(otp, '123456', 'OTP should contain the verification code');
  }

  private testOrderConfirmedDefaults(): void {
    console.log('\n📦 Testing Order Confirmed Defaults...');
    
    const order = this.messageService.orderConfirmed('ORDER_123');
    
    // Should contain app name prefix by default
    this.assertContains(order, 'GasDelivery - Order Confirmed', 'Order confirmation should include app name prefix by default');
    
    // Should contain company signature by default
    this.assertContains(order, 'Thank you,', 'Order confirmation should include company signature by default');
    
    // Should contain app footer by default
    this.assertContains(order, 'Download GasDelivery:', 'Order confirmation should include app footer by default');
    
    // Should contain order ID
    this.assertContains(order, 'ORDER_123', 'Order confirmation should contain order ID');
  }

  private testOrderConfirmedWithoutBranding(): void {
    console.log('\n📦 Testing Order Confirmed Without Branding...');
    
    const order = this.messageService.orderConfirmed('ORDER_123', {
      includeAppNamePrefix: false,
      includeCompanySignature: false,
      includeAppFooter: false,
    });
    
    // Should NOT contain app name prefix when disabled
    this.assertNotContains(order, 'GasDelivery -', 'Order confirmation should not include app name prefix when disabled');
    
    // Should NOT contain company signature when disabled
    this.assertNotContains(order, 'Thank you,', 'Order confirmation should not include company signature when disabled');
    
    // Should NOT contain app footer when disabled
    this.assertNotContains(order, 'Download GasDelivery:', 'Order confirmation should not include app footer when disabled');
    
    // Should contain order ID
    this.assertContains(order, 'ORDER_123', 'Order confirmation should contain order ID');
  }

  private testDeliveryAssignedDefaults(): void {
    console.log('\n🚚 Testing Delivery Assigned Defaults...');
    
    const delivery = this.messageService.deliveryAssigned('ORDER_123', 'John Doe', '123 Main St');
    
    // Should contain app name prefix by default
    this.assertContains(delivery, 'GasDelivery - New Delivery', 'Delivery assignment should include app name prefix by default');
    
    // Should contain company signature by default
    this.assertContains(delivery, 'Thank you,', 'Delivery assignment should include company signature by default');
    
    // Should contain order ID and customer info
    this.assertContains(delivery, 'ORDER_123', 'Delivery assignment should contain order ID');
    this.assertContains(delivery, 'John Doe', 'Delivery assignment should contain customer name');
    this.assertContains(delivery, '123 Main St', 'Delivery assignment should contain address');
  }

  private testDeliveryStartedDefaults(): void {
    console.log('\n🚚 Testing Delivery Started Defaults...');
    
    const delivery = this.messageService.deliveryStarted('ORDER_123', 'Agent Smith', '+1234567890');
    
    // Should contain app name prefix by default
    this.assertContains(delivery, 'GasDelivery - Delivery Started', 'Delivery started should include app name prefix by default');
    
    // Should NOT contain company signature by default
    this.assertNotContains(delivery, 'Thank you,', 'Delivery started should not include company signature by default');
    
    // Should contain order ID and agent info
    this.assertContains(delivery, 'ORDER_123', 'Delivery started should contain order ID');
    this.assertContains(delivery, 'Agent Smith', 'Delivery started should contain agent name');
    this.assertContains(delivery, '+1234567890', 'Delivery started should contain agent phone');
  }

  private testLowStockAlertDefaults(): void {
    console.log('\n⚠️ Testing Low Stock Alert Defaults...');
    
    const alert = this.messageService.lowStockAlert('Gas Cylinder 13kg', 5);
    
    // Should contain app name prefix by default
    this.assertContains(alert, 'GasDelivery - Low Stock Alert', 'Low stock alert should include app name prefix by default');
    
    // Should NOT contain company signature by default
    this.assertNotContains(alert, 'Thank you,', 'Low stock alert should not include company signature by default');
    
    // Should contain item and quantity
    this.assertContains(alert, 'Gas Cylinder 13kg', 'Low stock alert should contain item name');
    this.assertContains(alert, '5', 'Low stock alert should contain quantity');
  }

  private testEmergencyAlertDefaults(): void {
    console.log('\n🚨 Testing Emergency Alert Defaults...');
    
    const alert = this.messageService.emergencyAlert('System maintenance required');
    
    // Should contain app name prefix by default
    this.assertContains(alert, 'URGENT - GasDelivery', 'Emergency alert should include app name prefix by default');
    
    // Should NOT contain company signature by default
    this.assertNotContains(alert, 'Thank you,', 'Emergency alert should not include company signature by default');
    
    // Should contain the emergency message
    this.assertContains(alert, 'System maintenance required', 'Emergency alert should contain the message');
    
    // Should contain phone number
    this.assertContains(alert, '+1234567890', 'Emergency alert should contain phone number');
  }

  private testPaymentConfirmedDefaults(): void {
    console.log('\n💳 Testing Payment Confirmed Defaults...');
    
    const payment = this.messageService.paymentConfirmed('ORDER_123', 25.99);
    
    // Should contain app name prefix by default
    this.assertContains(payment, 'GasDelivery - Payment Confirmed', 'Payment confirmed should include app name prefix by default');
    
    // Should NOT contain company signature by default
    this.assertNotContains(payment, 'Thank you,', 'Payment confirmed should not include company signature by default');
    
    // Should contain order ID and amount
    this.assertContains(payment, 'ORDER_123', 'Payment confirmed should contain order ID');
    this.assertContains(payment, '25.99', 'Payment confirmed should contain amount');
  }

  private testPaymentFailedDefaults(): void {
    console.log('\n💳 Testing Payment Failed Defaults...');
    
    const payment = this.messageService.paymentFailed('ORDER_123', 'Insufficient funds');
    
    // Should contain app name prefix by default
    this.assertContains(payment, 'GasDelivery - Payment Failed', 'Payment failed should include app name prefix by default');
    
    // Should contain company signature by default
    this.assertContains(payment, 'Thank you,', 'Payment failed should include company signature by default');
    
    // Should contain order ID and reason
    this.assertContains(payment, 'ORDER_123', 'Payment failed should contain order ID');
    this.assertContains(payment, 'Insufficient funds', 'Payment failed should contain reason');
  }

  private testWelcomeMessageDefaults(): void {
    console.log('\n👋 Testing Welcome Message Defaults...');
    
    const welcome = this.messageService.welcomeMessage('John Doe', 'customer');
    
    // Should NOT contain app name prefix by default
    this.assertNotContains(welcome, 'GasDelivery -', 'Welcome message should not include app name prefix by default');
    
    // Should contain company signature by default
    this.assertContains(welcome, 'Thank you,', 'Welcome message should include company signature by default');
    
    // Should contain user name
    this.assertContains(welcome, 'John Doe', 'Welcome message should contain user name');
  }

  private testUtilityMethods(): void {
    console.log('\n🔧 Testing Utility Methods...');
    
    const testMessage = this.messageService.otpMessage('123456');
    const length = this.messageService.getMessageLength(testMessage);
    const exceedsLimit = this.messageService.exceedsSMSLimit(testMessage);
    const currentLang = this.messageService.getCurrentLanguage();
    const supportedLangs = AppMessageService.getSupportedLanguages();
    
    // Test message length
    if (typeof length === 'number' && length > 0) {
      this.testResults.passed++;
      console.log('✅ Message length calculation works');
    } else {
      this.testResults.failed++;
      console.log('❌ Message length calculation failed');
    }
    this.testResults.total++;
    
    // Test SMS limit check
    if (typeof exceedsLimit === 'boolean') {
      this.testResults.passed++;
      console.log('✅ SMS limit check works');
    } else {
      this.testResults.failed++;
      console.log('❌ SMS limit check failed');
    }
    this.testResults.total++;
    
    // Test current language
    if (currentLang === 'en') {
      this.testResults.passed++;
      console.log('✅ Current language retrieval works');
    } else {
      this.testResults.failed++;
      console.log('❌ Current language retrieval failed');
    }
    this.testResults.total++;
    
    // Test supported languages
    if (Array.isArray(supportedLangs) && supportedLangs.includes('en') && supportedLangs.includes('so')) {
      this.testResults.passed++;
      console.log('✅ Supported languages retrieval works');
    } else {
      this.testResults.failed++;
      console.log('❌ Supported languages retrieval failed');
    }
    this.testResults.total++;
  }

  private testMultilingualSupport(): void {
    console.log('\n🌍 Testing Multilingual Support...');
    
    const somaliService = new AppMessageService({ lang: 'so' });
    const englishService = new AppMessageService({ lang: 'en' });
    
    const somaliOtp = somaliService.otpMessage('123456');
    const englishOtp = englishService.otpMessage('123456');
    
    // Test Somali language
    this.assertContains(somaliOtp, 'Koodka Xaqiijinta', 'Somali OTP should contain Somali text');
    this.assertNotContains(somaliOtp, 'Verification Code', 'Somali OTP should not contain English text');
    
    // Test English language
    this.assertContains(englishOtp, 'Verification Code', 'English OTP should contain English text');
    this.assertNotContains(englishOtp, 'Koodka Xaqiijinta', 'English OTP should not contain Somali text');
  }

  private printResults(): void {
    console.log('\n📊 Enhanced Messaging Service Test Results:');
    console.log(`Total Tests: ${this.testResults.total}`);
    console.log(`Passed: ${this.testResults.passed}`);
    console.log(`Failed: ${this.testResults.failed}`);
    console.log(`Success Rate: ${((this.testResults.passed / this.testResults.total) * 100).toFixed(1)}%`);
    
    if (this.testResults.failed === 0) {
      console.log('\n🎉 All enhanced messaging service tests passed!');
      console.log('✅ Per-method branding control is working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Please review the output above.');
    }
  }
}

/**
 * Run the enhanced messaging service tests
 */
export function runEnhancedMessagingTests(): void {
  const tester = new EnhancedMessagingTester();
  tester.runTests();
}

// Run the test if this file is executed directly
if (require.main === module) {
  runEnhancedMessagingTests();
}

export { EnhancedMessagingTester };
