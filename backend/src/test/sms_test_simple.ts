/**
 * Simple SMS Test Script
 * Run this with: npm run test:sms:simple
 * 
 * This is a quick test to verify SMS service is working
 */

import { smsService } from '../services/sms.services';

async function simpleSmsTest() {
  console.log('📱 Simple SMS Test Starting...\n');

  const testPhoneNumber = '613656021'; // Your specified test number
  const testMessage = `Ciribey Gas Delivery - SMS Test

Mahadsanid! (Thank you!)

This is a test message from the Ciribey Gas Delivery SMS service.

Service Status: Working
Test Time: ${new Date().toLocaleString()}
Test Number: ${testPhoneNumber}

If you received this message, the SMS service is configured correctly!

Kooxda Ciribey Gas Delivery Team
Phone: +252613656021`;

  try {
    console.log('📤 Sending test SMS to:', testPhoneNumber);
    console.log('📝 Message preview:', testMessage.substring(0, 100) + '...');
    
    // Check service health first
    console.log('\n🔍 Checking SMS service health...');
    const isHealthy = await smsService.checkHealth();
    
    if (!isHealthy) {
      throw new Error('SMS service health check failed');
    }
    console.log('✅ SMS service is healthy');

    // Send the test SMS
    console.log('\n📤 Sending SMS...');
    const result = await smsService.sendSms(
      testPhoneNumber,
      testMessage,
      {
        isOtp: false,
        senderId: 'CIRIBEY', // Optional custom sender ID
        refId: `simple-test-${Date.now()}`,
      }
    );

    if (result.messageId) {
      console.log('✅ SMS sent successfully!');
      console.log('📧 Message ID:', result.messageId);
      console.log('📱 Check your phone at:', testPhoneNumber);
      
      // Show service metrics
      console.log('\n📊 Service Metrics:');
      const metrics = smsService.getMetrics();
      console.log('   Sent Count:', metrics.sentCount);
      console.log('   Failed Count:', metrics.failedCount);
      console.log('   Success Rate:', (metrics.successRate * 100).toFixed(1) + '%');
      
    } else {
      console.log('❌ SMS failed to send - no message ID returned');
    }

  } catch (error) {
    console.error('💥 Test failed with error:', error.message);
    
    // Log additional error details if available
    if (error.code) {
      console.error('🚨 Error Code:', error.code);
    }
    if (error.details) {
      console.error('📋 Error Details:', error.details);
    }
    
    console.error('📋 Stack trace:', error.stack);
    
    // Show service metrics even on failure
    try {
      console.log('\n📊 Service Metrics (after failure):');
      const metrics = smsService.getMetrics();
      console.log('   Sent Count:', metrics.sentCount);
      console.log('   Failed Count:', metrics.failedCount);
      console.log('   Success Rate:', (metrics.successRate * 100).toFixed(1) + '%');
      if (metrics.lastError) {
        console.log('   Last Error:', metrics.lastError.message);
      }
    } catch (metricsError) {
      console.error('Failed to get metrics:', metricsError.message);
    }
  }
}

// Test OTP SMS specifically
async function testOtpSms() {
  console.log('\n📱 OTP SMS Test Starting...\n');

  const testPhoneNumber = '613656021';
  const otpCode = Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit OTP
  const otpMessage = `Your Ciribey Gas Delivery verification code is: ${otpCode}

This code will expire in 5 minutes.

Do not share this code with anyone.

Mahadsanid! (Thank you!)
Ciribey Gas Delivery Team`;

  try {
    console.log('📤 Sending OTP SMS to:', testPhoneNumber);
    console.log('🔐 OTP Code:', otpCode);
    
    const result = await smsService.sendSms(
      testPhoneNumber,
      otpMessage,
      {
        isOtp: true, // This is an OTP message
        refId: `otp-test-${Date.now()}`,
      }
    );

    if (result.messageId) {
      console.log('✅ OTP SMS sent successfully!');
      console.log('📧 Message ID:', result.messageId);
      console.log('🔐 OTP Code sent:', otpCode);
      console.log('📱 Check your phone at:', testPhoneNumber);
    } else {
      console.log('❌ OTP SMS failed to send');
    }

  } catch (error) {
    console.error('💥 OTP test failed with error:', error.message);
    console.error('📋 Stack trace:', error.stack);
  }
}

// Combined test function
async function runSimpleTests() {
  console.log('🚀 Starting Simple SMS Tests');
  console.log('=' .repeat(40));
  console.log(`📅 Test Started: ${new Date().toISOString()}`);
  console.log(`📱 Test Phone: 613656021`);
  console.log('=' .repeat(40));

  // Run simple SMS test
  await simpleSmsTest();
  
  // Wait a bit between tests to respect rate limiting
  console.log('\n⏳ Waiting 2 seconds before OTP test...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Run OTP test
  await testOtpSms();
  
  console.log('\n' + '='.repeat(40));
  console.log('🎯 Simple SMS Tests Completed');
  console.log(`📅 Test Ended: ${new Date().toISOString()}`);
  console.log('=' .repeat(40));
}

// Export for use in other files
export { simpleSmsTest, testOtpSms, runSimpleTests };

// Run if executed directly
if (require.main === module) {
  runSimpleTests();
}
