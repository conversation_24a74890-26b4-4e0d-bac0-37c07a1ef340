/**
 * Simple Email Test Script
 * Run this with: npm run test:email or node -r ts-node/register src/test/email_test_simple.ts
 */

import { emailService } from '../services/email.services';

async function simpleEmailTest() {
  console.log('📧 Simple Email Test Starting...\n');

  const testEmail = '<EMAIL>';

  try {
    console.log('📤 Sending test email to:', testEmail);
    
    const result = await emailService.sendEmail(
      testEmail,
      {
        subject: '🔥 Ciribey Gas Delivery - Email Test',
        body: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0;">
              <h1 style="margin: 0;">🔥 Ciribey Gas Delivery</h1>
              <p style="margin: 5px 0 0 0; opacity: 0.9;">Email Service Test</p>
            </div>
            
            <div style="background: white; padding: 30px; border: 1px solid #e0e0e0; border-top: none; border-radius: 0 0 10px 10px;">
              <h2 style="color: #2c3e50; margin-top: 0;">✅ Email Service Working!</h2>
              
              <p>Congratulations! If you're reading this email, the Ciribey Gas Delivery email service is working correctly.</p>
              
              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #495057; margin-top: 0;">📊 Test Details:</h3>
                <ul style="color: #6c757d;">
                  <li><strong>Service:</strong> Email Service</li>
                  <li><strong>Test Email:</strong> ${testEmail}</li>
                  <li><strong>Timestamp:</strong> ${new Date().toISOString()}</li>
                  <li><strong>Language Support:</strong> Somali & English</li>
                  <li><strong>Features:</strong> HTML Templates, Bulk Sending, Error Handling</li>
                </ul>
              </div>
              
              <div style="background: #e8f5e8; border: 1px solid #4caf50; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <p style="margin: 0; color: #2e7d32;">
                  <strong>🎉 Success!</strong> The email service is configured and working properly.
                </p>
              </div>
              
              <h3 style="color: #495057;">🚀 What's Next?</h3>
              <ul style="color: #6c757d;">
                <li>OTP verification emails ✅</li>
                <li>Order confirmation emails ✅</li>
                <li>Low stock alerts ✅</li>
                <li>Delivery notifications ✅</li>
                <li>Bulk email campaigns ✅</li>
              </ul>
              
              <div style="background: #fff3e0; border: 1px solid #ff9800; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <p style="margin: 0; color: #f57c00;">
                  <strong>📱 Mobile App Integration:</strong> This email service is fully integrated with the Flutter mobile app for seamless user notifications.
                </p>
              </div>
            </div>
            
            <div style="background: #f5f5f5; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; margin-top: -1px;">
              <p style="margin: 0; color: #666; font-size: 14px;">
                <strong>Mahadsanid</strong> (Thank you)<br>
                Kooxda Ciribey Gas Delivery Team
              </p>
              <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #ddd;">
                <p style="margin: 0; font-size: 12px; color: #999;">
                  📞 +252613656021 | 📧 <EMAIL><br>
                  🌐 Ciribey Gas Delivery Services - Mogadishu, Somalia
                </p>
              </div>
            </div>
          </div>
        `
      }
    );

    if (result.success) {
      console.log('✅ Email sent successfully!');
      console.log('📧 Message ID:', result.messageId);
      console.log('📬 Check your inbox at:', testEmail);
    } else {
      console.log('❌ Email failed to send');
      console.log('🚨 Error:', result.error);
    }

  } catch (error) {
    console.error('💥 Test failed with error:', error.message);
    console.error('📋 Stack trace:', error.stack);
  }
}

// Export for use in other files
export { simpleEmailTest };

// Run if executed directly
if (require.main === module) {
  simpleEmailTest();
}
