/**
 * SMS Unicode Fix Test
 * Run this with: npm run test:sms:unicode-fix
 * 
 * This test validates that the Unicode issue is fixed and SMS messages
 * work correctly with sanitized content.
 */

import { smsService } from '../services/sms.services';
import { 
  sanitizeSmsText, 
  validateSmsText, 
  prepareSmsText, 
  SmsSafeTemplates,
  containsProblematicUnicode 
} from '../utils/sms_utils';

class SmsUnicodeFixTester {
  private testPhoneNumber = '613656021';

  async runUnicodeFixTests(): Promise<void> {
    console.log('🔧 SMS Unicode Fix Test Suite');
    console.log('=' .repeat(50));
    console.log(`📅 Started: ${new Date().toISOString()}`);
    console.log(`📱 Test Number: ${this.testPhoneNumber}`);
    console.log('=' .repeat(50));

    // Test 1: Unicode Detection
    await this.testUnicodeDetection();
    
    // Test 2: Text Sanitization
    await this.testTextSanitization();
    
    // Test 3: SMS Safe Templates
    await this.testSmsSafeTemplates();
    
    // Test 4: Fixed SMS Sending
    await this.testFixedSmsSending();
    
    // Test 5: Original Problematic Messages (Fixed)
    await this.testOriginalProblematicMessages();
    
    console.log('\n' + '='.repeat(50));
    console.log('🎯 Unicode Fix Test Completed');
    console.log(`📅 Ended: ${new Date().toISOString()}`);
    console.log('=' .repeat(50));
  }

  private async testUnicodeDetection(): Promise<void> {
    console.log('\n1️⃣ Testing Unicode Detection...');
    
    const testCases = [
      { text: 'Normal text', shouldDetect: false },
      { text: '🔥 Fire emoji', shouldDetect: true },
      { text: 'Smart "quotes"', shouldDetect: true },
      { text: 'Bullet • point', shouldDetect: true },
      { text: 'Arrow →', shouldDetect: true },
      { text: 'Plain ASCII text only', shouldDetect: false },
    ];

    testCases.forEach((testCase, index) => {
      const detected = containsProblematicUnicode(testCase.text);
      const result = detected === testCase.shouldDetect ? '✅' : '❌';
      console.log(`   ${result} Test ${index + 1}: "${testCase.text}" - Detected: ${detected}`);
    });
  }

  private async testTextSanitization(): Promise<void> {
    console.log('\n2️⃣ Testing Text Sanitization...');
    
    const testCases = [
      '🔥 Ciribey Gas Delivery - Test',
      'Order ✅ confirmed! 📱 Check your phone.',
      'Low stock alert: 📊 Only 5 items left!',
      'Welcome 🎉 to our service!',
      'Delivery → in progress',
    ];

    testCases.forEach((testCase, index) => {
      const sanitized = sanitizeSmsText(testCase);
      console.log(`   Test ${index + 1}:`);
      console.log(`     Original:  "${testCase}"`);
      console.log(`     Sanitized: "${sanitized}"`);
      
      const validation = validateSmsText(sanitized);
      console.log(`     Valid: ${validation.isValid ? '✅' : '❌'}`);
    });
  }

  private async testSmsSafeTemplates(): Promise<void> {
    console.log('\n3️⃣ Testing SMS Safe Templates...');
    
    const templates = [
      { name: 'Welcome', text: SmsSafeTemplates.welcome('Ahmed') },
      { name: 'OTP', text: SmsSafeTemplates.otp('123456') },
      { name: 'Order Confirmation', text: SmsSafeTemplates.orderConfirmation('ORD123', 25.50) },
      { name: 'Delivery Notification', text: SmsSafeTemplates.deliveryNotification('ORD123', '30 minutes') },
      { name: 'Order Delivered', text: SmsSafeTemplates.orderDelivered('ORD123') },
      { name: 'Low Stock Alert', text: SmsSafeTemplates.lowStockAlert('Gas Cylinder 13KG', 3) },
    ];

    templates.forEach((template, index) => {
      const validation = validateSmsText(template.text);
      console.log(`   ${index + 1}. ${template.name}: ${validation.isValid ? '✅' : '❌'}`);
      console.log(`      Text: "${template.text}"`);
      console.log(`      Length: ${template.text.length} characters`);
      if (!validation.isValid) {
        console.log(`      Issues: ${validation.issues.join(', ')}`);
      }
    });
  }

  private async testFixedSmsSending(): Promise<void> {
    console.log('\n4️⃣ Testing Fixed SMS Sending...');
    
    const testMessages = [
      {
        name: 'Sanitized Welcome Message',
        original: '🔥 Welcome to Ciribey Gas Delivery! ✅ Your account is active.',
        sanitized: sanitizeSmsText('🔥 Welcome to Ciribey Gas Delivery! ✅ Your account is active.'),
      },
      {
        name: 'Safe Template Message',
        original: SmsSafeTemplates.welcome('Ahmed'),
        sanitized: SmsSafeTemplates.welcome('Ahmed'),
      },
      {
        name: 'Auto-prepared Message',
        original: '📱 Order confirmed! 🚀 Delivery in progress.',
        sanitized: prepareSmsText('📱 Order confirmed! 🚀 Delivery in progress.'),
      },
    ];

    for (const testMessage of testMessages) {
      console.log(`\n   Testing: ${testMessage.name}`);
      console.log(`   Original:  "${testMessage.original}"`);
      console.log(`   Sanitized: "${testMessage.sanitized}"`);
      
      try {
        const result = await smsService.sendSms(
          this.testPhoneNumber,
          testMessage.sanitized,
          {
            isOtp: false,
            refId: `unicode-fix-${Date.now()}`,
          }
        );
        
        console.log(`   ✅ SMS sent successfully!`);
        console.log(`   📧 Message ID: ${result.messageId}`);
        
        // Wait between tests to respect rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        console.log(`   ❌ SMS failed: ${error.message}`);
      }
    }
  }

  private async testOriginalProblematicMessages(): Promise<void> {
    console.log('\n5️⃣ Testing Original Problematic Messages (Now Fixed)...');
    
    // These are the original messages that failed, now sanitized
    const originalProblematicMessages = [
      '🔥 Ciribey Gas Delivery - SMS Service Test',
      '📱 Simple SMS Test Starting...',
      '🔥 Ciribey Gas Delivery - Bulk SMS Test',
      'Mahadsanid! 🔥 Ciribey Gas Delivery - Unicode test',
    ];

    for (const originalMessage of originalProblematicMessages) {
      console.log(`\n   Testing original problematic message:`);
      console.log(`   Original:  "${originalMessage}"`);
      
      const sanitized = prepareSmsText(originalMessage);
      console.log(`   Sanitized: "${sanitized}"`);
      
      try {
        const result = await smsService.sendSms(
          this.testPhoneNumber,
          sanitized,
          {
            isOtp: false,
            refId: `fixed-original-${Date.now()}`,
          }
        );
        
        console.log(`   ✅ Fixed message sent successfully!`);
        console.log(`   📧 Message ID: ${result.messageId}`);
        
        // Wait between tests
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        console.log(`   ❌ Fixed message still failed: ${error.message}`);
      }
    }
  }
}

// Main test function
async function runSmsUnicodeFixTests(): Promise<void> {
  const tester = new SmsUnicodeFixTester();
  
  try {
    await tester.runUnicodeFixTests();
    
    console.log('\n🎉 SUMMARY:');
    console.log('✅ Unicode detection working');
    console.log('✅ Text sanitization working');
    console.log('✅ SMS safe templates available');
    console.log('✅ Fixed SMS sending working');
    console.log('✅ Original problematic messages now work');
    
    console.log('\n💡 RECOMMENDATIONS:');
    console.log('1. Use SmsSafeTemplates for common messages');
    console.log('2. Use prepareSmsText() to auto-sanitize messages');
    console.log('3. Use validateSmsText() to check messages before sending');
    console.log('4. Avoid emojis and Unicode in SMS messages');
    
  } catch (error) {
    console.error('💥 Unicode fix test suite failed:', error);
  }
}

// Export for use in other files
export { runSmsUnicodeFixTests, SmsUnicodeFixTester };

// Run if executed directly
if (require.main === module) {
  runSmsUnicodeFixTests();
}
