import fs from 'fs/promises';
import path from 'path';
import { FileCleanupService } from '../utils/file_cleanup';

/**
 * Simple test script to verify image cleanup functionality
 * This creates a test image file and then deletes it
 */
async function testImageCleanup() {
  console.log('🧪 Testing Image Cleanup Functionality...\n');

  // Create test directory structure
  const testImagePath = 'uploads/images/test/2025/01/test-image.jpg';
  const testImageDir = path.dirname(testImagePath);
  const testImageAbsolutePath = path.resolve(testImagePath);

  try {
    // 1. Create test directory structure
    console.log('📁 Creating test directory structure...');
    await fs.mkdir(testImageDir, { recursive: true });
    console.log(`✅ Created directory: ${testImageDir}`);

    // 2. Create a test image file
    console.log('🖼️ Creating test image file...');
    const testImageContent = 'This is a test image file content';
    await fs.writeFile(testImageAbsolutePath, testImageContent);
    console.log(`✅ Created test file: ${testImagePath}`);

    // 3. Verify file exists
    console.log('🔍 Verifying file exists...');
    const stats = await fs.stat(testImageAbsolutePath);
    console.log(`✅ File exists, size: ${stats.size} bytes`);

    // 4. Test file size utility
    console.log('📏 Testing file size utility...');
    const fileSize = await FileCleanupService.getFileSize(testImagePath);
    console.log(`✅ File size from utility: ${fileSize} bytes`);

    // 5. Test path validation
    console.log('🔒 Testing path validation...');
    const isValid = FileCleanupService.isValidImagePath(testImagePath);
    console.log(`✅ Path validation result: ${isValid}`);

    // 6. Test invalid path validation
    const invalidPath = '../../../etc/passwd';
    const isInvalid = FileCleanupService.isValidImagePath(invalidPath);
    console.log(`✅ Invalid path validation result: ${isInvalid} (should be false)`);

    // 7. Test image file deletion
    console.log('🗑️ Testing image file deletion...');
    const deleted = await FileCleanupService.deleteImageFile(testImagePath);
    console.log(`✅ File deletion result: ${deleted}`);

    // 8. Verify file no longer exists
    console.log('🔍 Verifying file was deleted...');
    try {
      await fs.access(testImageAbsolutePath);
      console.log('❌ File still exists (unexpected)');
    } catch (error) {
      console.log('✅ File successfully deleted');
    }

    // 9. Test cleanup of empty directories
    console.log('🧹 Testing empty directory cleanup...');
    await FileCleanupService.cleanupEmptyDirectories(testImagePath);
    console.log('✅ Empty directory cleanup completed');

    // 10. Test deletion of non-existent file
    console.log('👻 Testing deletion of non-existent file...');
    const deletedNonExistent = await FileCleanupService.deleteImageFile('uploads/images/nonexistent.jpg');
    console.log(`✅ Non-existent file deletion result: ${deletedNonExistent} (should be false)`);

    console.log('\n🎉 All tests passed! Image cleanup functionality is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    // Cleanup: Remove test directory if it still exists
    try {
      await fs.rmdir(path.resolve('uploads/images/test'), { recursive: true });
      console.log('🧹 Cleaned up test directory');
    } catch (error) {
      // Ignore cleanup errors
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testImageCleanup();
}

export { testImageCleanup };
