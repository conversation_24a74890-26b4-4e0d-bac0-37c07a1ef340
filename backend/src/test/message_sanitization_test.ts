import { AppMessageService } from '../constants/app_message.service';

/**
 * Simple test runner for message sanitization functionality
 * Can be run directly without Jest dependencies
 */
class MessageSanitizationTester {
  private messageService: AppMessageService;
  private testResults: { passed: number; failed: number; total: number } = {
    passed: 0,
    failed: 0,
    total: 0
  };

  constructor() {
    this.messageService = new AppMessageService();
  }

  private assertEqual(actual: string, expected: string, testName: string): void {
    this.testResults.total++;
    if (actual === expected) {
      this.testResults.passed++;
      console.log(`✅ ${testName}`);
    } else {
      this.testResults.failed++;
      console.log(`❌ ${testName}`);
      console.log(`   Expected: "${expected}"`);
      console.log(`   Actual:   "${actual}"`);
    }
  }

  private sanitizeMessage(text: string): string {
    return (this.messageService as any).sanitizeMessageContent(text);
  }

  runTests(): void {
    console.log('🧪 Running Message Sanitization Tests...\n');

    // Character Cleaning Tests
    this.testEmojiRemoval();
    this.testUnicodeQuotes();
    this.testUnicodeDashes();
    this.testEllipses();
    this.testFancyApostrophes();

    // Whitespace Normalization Tests
    this.testMultipleNewlines();
    this.testLeadingTrailingWhitespace();
    this.testNonBreakingSpaces();
    this.testConsecutiveSpaces();

    // Punctuation Standardization Tests
    this.testPunctuationSpacing();
    this.testRedundantPunctuation();

    // Edge Cases
    this.testEmptyString();
    this.testNullInput();
    this.testUndefinedInput();
    this.testOnlyEmojis();
    this.testOnlyWhitespace();

    // Complex Transformations
    this.testComplexMixedContent();
    this.testPreserveEssentialFormatting();

    // Performance Tests
    this.testLargeTextPerformance();
    this.testRepeatedSanitization();

    // Integration Tests
    this.testOtpMessageSanitization();
    this.testOrderConfirmationSanitization();
    this.testDeliveryMessageSanitization();
    this.testWelcomeMessageSanitization();

    this.printResults();
  }

  private testEmojiRemoval(): void {
    const input = '🔥 New Order 🔥\n\nYour gas will arrive in 1–2 hours…\n\nThank you! ❤️';
    const expected = 'New Order. Your gas will arrive in 1-2 hours. Thank you!';
    this.assertEqual(this.sanitizeMessage(input), expected, 'Remove all emojis and Unicode symbols');
  }

  private testUnicodeQuotes(): void {
    const input = 'Order "confirmed" with "special" quotes';
    const expected = 'Order "confirmed" with "special" quotes';
    this.assertEqual(this.sanitizeMessage(input), expected, 'Convert special quotes to standard ASCII quotes');
  }

  private testUnicodeDashes(): void {
    const input = 'Delivery time: 30–45 minutes — emergency: 10–20 minutes';
    const expected = 'Delivery time: 30-45 minutes - emergency: 10-20 minutes';
    this.assertEqual(this.sanitizeMessage(input), expected, 'Replace Unicode dashes with standard hyphens');
  }

  private testEllipses(): void {
    const input = 'Processing your order… please wait…';
    const expected = 'Processing your order... please wait...';
    this.assertEqual(this.sanitizeMessage(input), expected, 'Convert ellipses to three periods');
  }

  private testFancyApostrophes(): void {
    const input = "Customer's order can't be processed";
    const expected = "Customer's order can't be processed";
    this.assertEqual(this.sanitizeMessage(input), expected, 'Convert fancy apostrophes to straight ones');
  }

  private testMultipleNewlines(): void {
    const input = 'Line 1\n\n\nLine 2\n\n\n\nLine 3';
    const expected = 'Line 1\nLine 2\nLine 3';
    this.assertEqual(this.sanitizeMessage(input), expected, 'Collapse multiple newlines into single line breaks');
  }

  private testLeadingTrailingWhitespace(): void {
    const input = '  Line 1  \n  Line 2  \n  Line 3  ';
    const expected = 'Line 1\nLine 2\nLine 3';
    this.assertEqual(this.sanitizeMessage(input), expected, 'Remove leading/trailing whitespace from each line');
  }

  private testNonBreakingSpaces(): void {
    const input = 'Order\u00A0ID:\u00A012345';
    const expected = 'Order ID: 12345';
    this.assertEqual(this.sanitizeMessage(input), expected, 'Convert non-breaking spaces to regular spaces');
  }

  private testConsecutiveSpaces(): void {
    const input = 'Order    ID:    12345';
    const expected = 'Order ID: 12345';
    this.assertEqual(this.sanitizeMessage(input), expected, 'Trim consecutive spaces to single space');
  }

  private testPunctuationSpacing(): void {
    const input = 'Order confirmed,delivery time:30 minutes.Thank you!';
    const expected = 'Order confirmed, delivery time: 30 minutes. Thank you!';
    this.assertEqual(this.sanitizeMessage(input), expected, 'Ensure proper spacing after punctuation');
  }

  private testRedundantPunctuation(): void {
    const input = 'Order confirmed,,, delivery time: 30 minutes... Thank you!!!';
    const expected = 'Order confirmed, delivery time: 30 minutes. Thank you!';
    this.assertEqual(this.sanitizeMessage(input), expected, 'Remove redundant punctuation');
  }

  private testEmptyString(): void {
    const input = '';
    const expected = '';
    this.assertEqual(this.sanitizeMessage(input), expected, 'Handle empty string');
  }

  private testNullInput(): void {
    const input = null as any;
    const expected = '';
    this.assertEqual(this.sanitizeMessage(input), expected, 'Handle null input');
  }

  private testUndefinedInput(): void {
    const input = undefined as any;
    const expected = '';
    this.assertEqual(this.sanitizeMessage(input), expected, 'Handle undefined input');
  }

  private testOnlyEmojis(): void {
    const input = '🔥🚚💳📦⚠️🚨';
    const expected = '';
    this.assertEqual(this.sanitizeMessage(input), expected, 'Handle string with only emojis');
  }

  private testOnlyWhitespace(): void {
    const input = '   \n\n   \t   ';
    const expected = '';
    this.assertEqual(this.sanitizeMessage(input), expected, 'Handle string with only whitespace');
  }

  private testComplexMixedContent(): void {
    const input = '🔥  New Order  🔥\n\nYour gas will arrive in 1–2 hours…\n\nThank you! ❤️\n\nContact us: 📞 +252613656021';
    const expected = 'New Order. Your gas will arrive in 1-2 hours. Thank you! Contact us: +252613656021';
    this.assertEqual(this.sanitizeMessage(input), expected, 'Handle complex mixed content');
  }

  private testPreserveEssentialFormatting(): void {
    const input = 'Order ID: 12345\n\nStatus: Confirmed\n\nDelivery: 30–45 minutes';
    const expected = 'Order ID: 12345\nStatus: Confirmed\nDelivery: 30-45 minutes';
    this.assertEqual(this.sanitizeMessage(input), expected, 'Preserve essential formatting while cleaning');
  }

  private testLargeTextPerformance(): void {
    const largeText = '🔥'.repeat(1000) + '\n\nTest message with lots of content...\n\n' + '🚚'.repeat(500);
    const startTime = performance.now();
    
    const sanitized = this.sanitizeMessage(largeText);
    const endTime = performance.now();
    
    const expected = 'Test message with lots of content...';
    this.assertEqual(sanitized, expected, 'Handle large text efficiently');
    
    if (endTime - startTime < 100) {
      this.testResults.passed++;
      console.log('✅ Large text performance test passed (< 100ms)');
    } else {
      this.testResults.failed++;
      console.log(`❌ Large text performance test failed (${(endTime - startTime).toFixed(2)}ms)`);
    }
    this.testResults.total++;
  }

  private testRepeatedSanitization(): void {
    const input = '🔥 New Order 🔥\n\nYour gas will arrive in 1–2 hours…\n\nThank you! ❤️';
    const iterations = 1000;
    const startTime = performance.now();
    
    for (let i = 0; i < iterations; i++) {
      this.sanitizeMessage(input);
    }
    
    const endTime = performance.now();
    const averageTime = (endTime - startTime) / iterations;
    
    if (averageTime < 1) {
      this.testResults.passed++;
      console.log('✅ Repeated sanitization performance test passed (< 1ms per iteration)');
    } else {
      this.testResults.failed++;
      console.log(`❌ Repeated sanitization performance test failed (${averageTime.toFixed(4)}ms per iteration)`);
    }
    this.testResults.total++;
  }

  private testOtpMessageSanitization(): void {
    const otpMessage = this.messageService.otpMessage('123456', 5);
    if (!otpMessage.includes('🔐') && !otpMessage.includes('⚠️') && otpMessage.includes('123456')) {
      this.testResults.passed++;
      console.log('✅ OTP message sanitization test passed');
    } else {
      this.testResults.failed++;
      console.log('❌ OTP message sanitization test failed');
    }
    this.testResults.total++;
  }

  private testOrderConfirmationSanitization(): void {
    const orderMessage = this.messageService.orderConfirmed('ORDER123');
    if (!orderMessage.includes('✅') && orderMessage.includes('ORDER123')) {
      this.testResults.passed++;
      console.log('✅ Order confirmation sanitization test passed');
    } else {
      this.testResults.failed++;
      console.log('❌ Order confirmation sanitization test failed');
    }
    this.testResults.total++;
  }

  private testDeliveryMessageSanitization(): void {
    const deliveryMessage = this.messageService.orderDelivered('ORDER123');
    if (!deliveryMessage.includes('🚚') && deliveryMessage.includes('ORDER123')) {
      this.testResults.passed++;
      console.log('✅ Delivery message sanitization test passed');
    } else {
      this.testResults.failed++;
      console.log('❌ Delivery message sanitization test failed');
    }
    this.testResults.total++;
  }

  private testWelcomeMessageSanitization(): void {
    const welcomeMessage = this.messageService.welcomeMessage('John Doe', 'customer');
    if (!welcomeMessage.includes('🎉') && welcomeMessage.includes('John Doe')) {
      this.testResults.passed++;
      console.log('✅ Welcome message sanitization test passed');
    } else {
      this.testResults.failed++;
      console.log('❌ Welcome message sanitization test failed');
    }
    this.testResults.total++;
  }

  private printResults(): void {
    console.log('\n📊 Test Results:');
    console.log(`Total Tests: ${this.testResults.total}`);
    console.log(`Passed: ${this.testResults.passed}`);
    console.log(`Failed: ${this.testResults.failed}`);
    console.log(`Success Rate: ${((this.testResults.passed / this.testResults.total) * 100).toFixed(1)}%`);
    
    if (this.testResults.failed === 0) {
      console.log('\n🎉 All tests passed! Message sanitization is working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Please review the output above.');
    }
  }
}

/**
 * Performance benchmark utility
 */
export function benchmarkSanitization(): void {
  const messageService = new AppMessageService();
  const testCases = [
    '🔥 New Order 🔥\n\nYour gas will arrive in 1–2 hours…\n\nThank you! ❤️',
    'Order "confirmed" with "special" quotes',
    'Delivery time: 30–45 minutes — emergency: 10–20 minutes',
    'Processing your order… please wait…',
    "Customer's order can't be processed",
    '  Line 1  \n  Line 2  \n  Line 3  ',
    'Order confirmed,delivery time:30 minutes.Thank you!',
    'Order confirmed,,, delivery time: 30 minutes... Thank you!!!',
  ];

  console.log('🧪 Running Message Sanitization Benchmarks...\n');

  testCases.forEach((testCase, index) => {
    const iterations = 10000;
    const startTime = performance.now();
    
    for (let i = 0; i < iterations; i++) {
      (messageService as any).sanitizeMessageContent(testCase);
    }
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    const averageTime = totalTime / iterations;
    
    console.log(`Test Case ${index + 1}:`);
    console.log(`  Input: "${testCase.substring(0, 50)}${testCase.length > 50 ? '...' : ''}"`);
    console.log(`  Average time: ${averageTime.toFixed(4)}ms per iteration`);
    console.log(`  Total time: ${totalTime.toFixed(2)}ms for ${iterations} iterations\n`);
  });
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new MessageSanitizationTester();
  tester.runTests();
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  benchmarkSanitization();
} 