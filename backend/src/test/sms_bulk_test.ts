/**
 * Bulk SMS Test Script
 * Run this with: npm run test:sms:bulk
 * 
 * This test validates bulk SMS functionality including:
 * - Sending to multiple recipients
 * - Duplicate number handling
 * - Progress tracking
 * - Error handling for individual failures
 * - Performance metrics
 */

import { smsService } from '../services/sms.services';

// Test configuration for bulk SMS
const BULK_TEST_CONFIG = {
  primaryTestNumber: '613656021', // Your specified test number
  testNumbers: [
    '613656021', // Primary test number
    '613656021', // Duplicate to test deduplication
    '617777777', // Additional test number (if valid)
    '618888888', // Another test number (if valid)
  ],
  invalidNumbers: [
    '123456789',     // Invalid number to test error handling
    '613656021',     // Valid number mixed with invalid
    'invalid',       // Non-numeric
  ],
  testMessage: `Ciribey Gas Delivery - Bulk SMS Test

Mahadsanid! (Thank you!)

This is a bulk SMS test from Ciribey Gas Delivery service.

Test Details:
- Service: Bulk SMS Testing
- Time: ${new Date().toLocaleString()}
- Purpose: System validation

If you received this message, our bulk SMS system is working correctly!

Kooxda Ciribey Gas Delivery Team
Phone: +252613656021
Location: Mogadishu, Somalia`,
};

class BulkSmsTestRunner {
  private startTime: number = 0;
  private progressUpdates: Array<{ success: number; failed: number; total: number; timestamp: number }> = [];

  private onProgress = (success: number, failed: number, total: number) => {
    const timestamp = Date.now();
    this.progressUpdates.push({ success, failed, total, timestamp });
    
    const elapsed = timestamp - this.startTime;
    const percentage = ((success + failed) / total * 100).toFixed(1);
    
    console.log(`📊 Progress: ${success} sent, ${failed} failed, ${total} total (${percentage}%) - ${elapsed}ms elapsed`);
  };

  async testBasicBulkSms(): Promise<void> {
    console.log('📱 Testing Basic Bulk SMS...\n');
    console.log('📋 Recipients:', BULK_TEST_CONFIG.testNumbers);
    console.log('📝 Message length:', BULK_TEST_CONFIG.testMessage.length, 'characters');

    this.startTime = Date.now();
    this.progressUpdates = [];

    const result = await smsService.sendSmsToMany(
      BULK_TEST_CONFIG.testNumbers,
      BULK_TEST_CONFIG.testMessage,
      {
        isOtp: false,
        refId: `bulk-basic-${Date.now()}`,
        onProgress: this.onProgress,
      }
    );

    const totalTime = Date.now() - this.startTime;

    console.log('\n✅ Bulk SMS Test Results:');
    console.log('=' .repeat(40));
    console.log('📊 Status:', result.status);
    console.log('📝 Message:', result.message);
    console.log('⏱️  Total Time:', totalTime, 'ms');
    
    console.log('\n📈 Counts:');
    console.log('   Requested:', result.data.counts.requested);
    console.log('   Duplicates Removed:', result.data.counts.duplicatesRemoved);
    console.log('   Attempted:', result.data.counts.attempted);
    console.log('   Successful:', result.data.counts.successful);
    console.log('   Failed:', result.data.counts.failed);

    console.log('\n✅ Successful Deliveries:');
    result.data.messageIds.successes.forEach((success, index) => {
      console.log(`   ${index + 1}. ${success.phoneNumber} → ${success.messageId}`);
    });

    if (result.data.messageIds.failures.length > 0) {
      console.log('\n❌ Failed Deliveries:');
      result.data.messageIds.failures.forEach((failure, index) => {
        console.log(`   ${index + 1}. ${failure.phoneNumber} → ${failure.error.message}`);
      });
    }

    console.log('\n📊 Performance Metrics:');
    if (result.data.counts.successful > 0) {
      const avgTimePerSms = totalTime / result.data.counts.successful;
      console.log(`   Average time per SMS: ${avgTimePerSms.toFixed(2)}ms`);
      console.log(`   SMS per second: ${(1000 / avgTimePerSms).toFixed(2)}`);
    }

    console.log('\n📅 Sent At:', result.data.sentAt);
  }

  async testBulkSmsWithErrors(): Promise<void> {
    console.log('\n📱 Testing Bulk SMS with Invalid Numbers...\n');
    console.log('📋 Mixed Recipients (valid + invalid):', BULK_TEST_CONFIG.invalidNumbers);

    this.startTime = Date.now();
    this.progressUpdates = [];

    try {
      const result = await smsService.sendSmsToMany(
        BULK_TEST_CONFIG.invalidNumbers,
        'Test message with mixed valid/invalid numbers',
        {
          isOtp: false,
          refId: `bulk-error-${Date.now()}`,
          onProgress: this.onProgress,
        }
      );

      console.log('\n📊 Mixed Results (Expected some failures):');
      console.log('=' .repeat(40));
      console.log('📊 Status:', result.status);
      console.log('📝 Message:', result.message);
      
      console.log('\n📈 Counts:');
      console.log('   Requested:', result.data.counts.requested);
      console.log('   Attempted:', result.data.counts.attempted);
      console.log('   Successful:', result.data.counts.successful);
      console.log('   Failed:', result.data.counts.failed);

      if (result.data.messageIds.failures.length > 0) {
        console.log('\n❌ Expected Failures (Invalid Numbers):');
        result.data.messageIds.failures.forEach((failure, index) => {
          console.log(`   ${index + 1}. ${failure.phoneNumber} → ${failure.error.message}`);
        });
      }

      if (result.data.messageIds.successes.length > 0) {
        console.log('\n✅ Successful Deliveries:');
        result.data.messageIds.successes.forEach((success, index) => {
          console.log(`   ${index + 1}. ${success.phoneNumber} → ${success.messageId}`);
        });
      }

    } catch (error) {
      console.error('💥 Bulk SMS with errors test failed:', error.message);
      throw error;
    }
  }

  async testLargeBulkSms(): Promise<void> {
    console.log('\n📱 Testing Large Bulk SMS (Performance Test)...\n');
    
    // Create a larger list with the test number repeated
    const largeNumberList = Array(10).fill(BULK_TEST_CONFIG.primaryTestNumber);
    console.log('📋 Recipients count:', largeNumberList.length);
    console.log('📱 All messages going to:', BULK_TEST_CONFIG.primaryTestNumber);

    this.startTime = Date.now();
    this.progressUpdates = [];

    const result = await smsService.sendSmsToMany(
      largeNumberList,
      'Performance test message from Ciribey Gas Delivery bulk SMS system.',
      {
        isOtp: false,
        refId: `bulk-performance-${Date.now()}`,
        onProgress: this.onProgress,
      }
    );

    const totalTime = Date.now() - this.startTime;

    console.log('\n📊 Performance Test Results:');
    console.log('=' .repeat(40));
    console.log('📊 Status:', result.status);
    console.log('⏱️  Total Time:', totalTime, 'ms');
    console.log('📈 Successful:', result.data.counts.successful);
    console.log('📈 Failed:', result.data.counts.failed);
    console.log('📈 Duplicates Removed:', result.data.counts.duplicatesRemoved);

    if (result.data.counts.successful > 0) {
      const avgTimePerSms = totalTime / result.data.counts.successful;
      console.log(`📊 Average time per SMS: ${avgTimePerSms.toFixed(2)}ms`);
      console.log(`📊 SMS per second: ${(1000 / avgTimePerSms).toFixed(2)}`);
    }

    // Analyze progress updates
    if (this.progressUpdates.length > 1) {
      console.log('\n📈 Progress Analysis:');
      const firstUpdate = this.progressUpdates[0];
      const lastUpdate = this.progressUpdates[this.progressUpdates.length - 1];
      const progressTime = lastUpdate.timestamp - firstUpdate.timestamp;
      console.log(`   Progress updates: ${this.progressUpdates.length}`);
      console.log(`   Progress tracking time: ${progressTime}ms`);
    }
  }

  async runAllBulkTests(): Promise<void> {
    console.log('🚀 Starting Bulk SMS Test Suite');
    console.log('=' .repeat(50));
    console.log(`📅 Test Started: ${new Date().toISOString()}`);
    console.log(`📱 Primary Test Number: ${BULK_TEST_CONFIG.primaryTestNumber}`);
    console.log('=' .repeat(50));

    try {
      // Test 1: Basic bulk SMS
      await this.testBasicBulkSms();
      
      // Wait between tests
      console.log('\n⏳ Waiting 3 seconds before next test...');
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Test 2: Bulk SMS with errors
      await this.testBulkSmsWithErrors();
      
      // Wait between tests
      console.log('\n⏳ Waiting 3 seconds before performance test...');
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Test 3: Large bulk SMS (performance)
      await this.testLargeBulkSms();

      // Final service metrics
      console.log('\n📊 Final Service Metrics:');
      const metrics = smsService.getMetrics();
      console.log('   Total Sent:', metrics.sentCount);
      console.log('   Total Failed:', metrics.failedCount);
      console.log('   Success Rate:', (metrics.successRate * 100).toFixed(1) + '%');

      console.log('\n🎉 All bulk SMS tests completed successfully!');

    } catch (error) {
      console.error('💥 Bulk SMS test suite failed:', error.message);
      console.error('📋 Stack trace:', error.stack);
      throw error;
    } finally {
      console.log('\n' + '='.repeat(50));
      console.log('🎯 Bulk SMS Test Suite Completed');
      console.log(`📅 Test Ended: ${new Date().toISOString()}`);
      console.log('=' .repeat(50));
    }
  }
}

// Main test function
async function runBulkSmsTests(): Promise<void> {
  const testRunner = new BulkSmsTestRunner();
  await testRunner.runAllBulkTests();
}

// Export for use in other files
export { runBulkSmsTests, BulkSmsTestRunner, BULK_TEST_CONFIG };

// Run if executed directly
if (require.main === module) {
  runBulkSmsTests();
}
