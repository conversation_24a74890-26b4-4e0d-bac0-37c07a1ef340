/**
 * SMS Service Diagnostic Test
 * Run this with: npm run test:sms:diagnostic
 * 
 * This test helps diagnose SMS service issues by:
 * - Testing authentication separately
 * - Checking API endpoints
 * - Validating request/response format
 * - Testing different message types
 * - Providing detailed error analysis
 */

import axios from 'axios';
import qs from 'querystring';
import { config } from '../config/env_config';
import { smsService } from '../services/sms.services';
import logger from '../config/logger';

class SmsDiagnosticTester {
  private testPhoneNumber = '613656021';

  async runDiagnostics(): Promise<void> {
    console.log('🔍 SMS Service Diagnostic Test');
    console.log('=' .repeat(50));
    console.log(`📅 Started: ${new Date().toISOString()}`);
    console.log(`📱 Test Number: ${this.testPhoneNumber}`);
    console.log('=' .repeat(50));

    // Test 1: Configuration Check
    await this.testConfiguration();
    
    // Test 2: Authentication Test
    await this.testAuthentication();
    
    // Test 3: Direct API Test
    await this.testDirectApiCall();
    
    // Test 4: Service Method Test with Detailed Logging
    await this.testServiceMethodWithLogging();
    
    // Test 5: Different Message Types
    await this.testDifferentMessageTypes();
    
    console.log('\n' + '='.repeat(50));
    console.log('🎯 Diagnostic Test Completed');
    console.log(`📅 Ended: ${new Date().toISOString()}`);
    console.log('=' .repeat(50));
  }

  private async testConfiguration(): Promise<void> {
    console.log('\n🔧 Testing Configuration...');
    
    try {
      console.log('📋 SMS Configuration:');
      console.log(`   Provider URL: ${config.sms.providerUrl}`);
      console.log(`   Username: ${config.sms.username ? '✅ Set' : '❌ Missing'}`);
      console.log(`   Password: ${config.sms.password ? '✅ Set' : '❌ Missing'}`);
      console.log(`   Sender ID: ${config.sms.senderId}`);
      console.log(`   Timeout: ${config.sms.timeout}ms`);
      
      // Check if all required fields are present
      const required = ['providerUrl', 'username', 'password', 'senderId'];
      const missing = required.filter(key => !config.sms[key as keyof typeof config.sms]);
      
      if (missing.length > 0) {
        console.log(`❌ Missing configuration: ${missing.join(', ')}`);
      } else {
        console.log('✅ All required configuration present');
      }
      
    } catch (error) {
      console.log('❌ Configuration test failed:', error.message);
    }
  }

  private async testAuthentication(): Promise<void> {
    console.log('\n🔐 Testing Authentication...');
    
    try {
      const url = `${config.sms.providerUrl}/token`;
      const payload = qs.stringify({
        username: config.sms.username,
        password: config.sms.password,
        grant_type: 'password',
      });

      console.log(`📡 Auth URL: ${url}`);
      console.log(`📝 Payload: grant_type=password&username=${config.sms.username}&password=***`);

      const response = await axios.post(url, payload, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        timeout: 10000,
      });

      console.log(`✅ Authentication successful`);
      console.log(`📊 Status: ${response.status}`);
      console.log(`🔑 Token received: ${response.data.access_token ? 'Yes' : 'No'}`);
      console.log(`⏰ Expires in: ${response.data.expires_in} seconds`);
      
      return response.data.access_token;
      
    } catch (error) {
      console.log('❌ Authentication failed');
      if (axios.isAxiosError(error)) {
        console.log(`📊 Status: ${error.response?.status}`);
        console.log(`📝 Response: ${JSON.stringify(error.response?.data, null, 2)}`);
        console.log(`🌐 URL: ${error.config?.url}`);
      }
      console.log(`💥 Error: ${error.message}`);
    }
  }

  private async testDirectApiCall(): Promise<void> {
    console.log('\n📡 Testing Direct API Call...');
    
    try {
      // First get token
      const tokenUrl = `${config.sms.providerUrl}/token`;
      const tokenPayload = qs.stringify({
        username: config.sms.username,
        password: config.sms.password,
        grant_type: 'password',
      });

      const tokenResponse = await axios.post(tokenUrl, tokenPayload, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        timeout: 10000,
      });

      const token = tokenResponse.data.access_token;
      console.log('✅ Token obtained for direct API test');

      // Now test SMS API
      const smsUrl = `${config.sms.providerUrl}/api/SendSMS`;
      const smsPayload = {
        mobile: this.testPhoneNumber,
        message: 'Direct API test from Ciribey Gas Delivery diagnostic tool',
        senderid: config.sms.senderId,
        refid: `diagnostic-${Date.now()}`,
        validity: 0,
      };

      console.log(`📡 SMS URL: ${smsUrl}`);
      console.log(`📝 SMS Payload:`, JSON.stringify(smsPayload, null, 2));

      const smsResponse = await axios.post(smsUrl, smsPayload, {
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000,
      });

      console.log('✅ Direct API call successful');
      console.log(`📊 Status: ${smsResponse.status}`);
      console.log(`📝 Response:`, JSON.stringify(smsResponse.data, null, 2));

    } catch (error) {
      console.log('❌ Direct API call failed');
      if (axios.isAxiosError(error)) {
        console.log(`📊 Status: ${error.response?.status}`);
        console.log(`📝 Response:`, JSON.stringify(error.response?.data, null, 2));
        console.log(`🌐 URL: ${error.config?.url}`);
        console.log(`📋 Headers:`, JSON.stringify(error.config?.headers, null, 2));
      }
      console.log(`💥 Error: ${error.message}`);
    }
  }

  private async testServiceMethodWithLogging(): Promise<void> {
    console.log('\n🔧 Testing Service Method with Detailed Logging...');
    
    try {
      // Enable more detailed logging temporarily
      const originalLog = console.log;
      const logs: string[] = [];
      
      console.log = (...args) => {
        const logMessage = args.join(' ');
        logs.push(logMessage);
        originalLog(...args);
      };

      console.log('📤 Attempting to send SMS via service method...');
      
      const result = await smsService.sendSms(
        this.testPhoneNumber,
        'Service method diagnostic test - Ciribey Gas Delivery',
        {
          isOtp: false,
          refId: `service-diagnostic-${Date.now()}`,
        }
      );

      console.log = originalLog; // Restore original logging

      console.log('✅ Service method call successful');
      console.log(`📧 Message ID: ${result.messageId}`);
      
    } catch (error) {
      console.log('❌ Service method call failed');
      console.log(`💥 Error Type: ${error.constructor.name}`);
      console.log(`💥 Error Message: ${error.message}`);
      
      if (error.code) {
        console.log(`🔢 Error Code: ${error.code}`);
      }
      
      if (error.details) {
        console.log(`📋 Error Details:`, JSON.stringify(error.details, null, 2));
      }
      
      if (error.stack) {
        console.log(`📚 Stack Trace: ${error.stack}`);
      }
    }
  }

  private async testDifferentMessageTypes(): Promise<void> {
    console.log('\n📝 Testing Different Message Types...');
    
    const testCases = [
      {
        name: 'Short Message',
        message: 'Test',
        isOtp: false,
      },
      {
        name: 'Medium Message',
        message: 'This is a medium length test message from Ciribey Gas Delivery diagnostic tool.',
        isOtp: false,
      },
      {
        name: 'OTP Message',
        message: 'Your verification code is: 123456',
        isOtp: true,
      },
      {
        name: 'Unicode Message',
        message: 'Mahadsanid! 🔥 Ciribey Gas Delivery - Unicode test',
        isOtp: false,
      },
    ];

    for (const testCase of testCases) {
      console.log(`\n📋 Testing: ${testCase.name}`);
      console.log(`📝 Message: "${testCase.message}"`);
      console.log(`📏 Length: ${testCase.message.length} characters`);
      console.log(`🔐 Is OTP: ${testCase.isOtp}`);
      
      try {
        const result = await smsService.sendSms(
          this.testPhoneNumber,
          testCase.message,
          {
            isOtp: testCase.isOtp,
            refId: `diagnostic-${testCase.name.toLowerCase().replace(' ', '-')}-${Date.now()}`,
          }
        );
        
        console.log(`✅ ${testCase.name} sent successfully`);
        console.log(`📧 Message ID: ${result.messageId}`);
        
        // Wait between tests to respect rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        console.log(`❌ ${testCase.name} failed: ${error.message}`);
      }
    }
  }
}

// Main diagnostic function
async function runSmsDiagnostics(): Promise<void> {
  const diagnosticTester = new SmsDiagnosticTester();
  
  try {
    await diagnosticTester.runDiagnostics();
  } catch (error) {
    console.error('💥 Diagnostic test suite failed:', error);
    logger.error('SMS diagnostic test failed', { error: error.message, stack: error.stack });
  }
}

// Export for use in other files
export { runSmsDiagnostics, SmsDiagnosticTester };

// Run if executed directly
if (require.main === module) {
  runSmsDiagnostics();
}
