import { emailService } from '../services/email.services';
import { AppMessageService } from '../constants/app_message.service';

/**
 * Comprehensive test script for Email Service functionality
 * Tests various email scenarios including templates, bulk sending, and error handling
 */
async function testEmailService() {
  console.log('📧 Testing Email Service Functionality...\n');

  const testEmail = '<EMAIL>';
  const messageService = new AppMessageService({ lang: 'so', includePhoneInFooter: true });

  try {
    // 1. Test email service health check
    console.log('🏥 Testing email service health...');
    const isHealthy = await emailService.checkHealth();
    console.log(`✅ Email service health: ${isHealthy ? 'Healthy' : 'Unhealthy'}`);

    // 2. Test simple email sending
    console.log('📤 Testing simple email sending...');
    const simpleEmailResult = await emailService.sendEmail(
      testEmail,
      {
        subject: '🔥 Ciribey Gas Delivery - Test Email',
        body: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #2c3e50;">📧 Email Service Test</h2>
            <p>This is a test email from the Ciribey Gas Delivery system.</p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <p><strong>Test Details:</strong></p>
              <ul>
                <li>Service: Email Service</li>
                <li>Language: Somali (Primary)</li>
                <li>Timestamp: ${new Date().toISOString()}</li>
              </ul>
            </div>
            <p style="color: #7f8c8d;">If you received this email, the email service is working correctly!</p>
            <hr style="margin: 20px 0;">
            <p style="font-size: 12px; color: #95a5a6;">
              This is an automated test email from Ciribey Gas Delivery System.
            </p>
          </div>
        `
      }
    );

    if (simpleEmailResult.success) {
      console.log(`✅ Simple email sent successfully! Message ID: ${simpleEmailResult.messageId}`);
    } else {
      console.log(`❌ Simple email failed: ${simpleEmailResult.error}`);
    }

    // 3. Test OTP email template
    console.log('🔐 Testing OTP email template...');
    const otpMessage = messageService.otpMessage('123456', 5);
    const otpEmailResult = await emailService.sendEmail(
      testEmail,
      {
        subject: '🔐 Ciribey Gas Delivery - Koodka Xaqiijinta',
        body: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center;">
              <h1 style="margin: 0;">🔐 Ciribey Gas Delivery</h1>
              <p style="margin: 5px 0 0 0; opacity: 0.9;">Koodka Xaqiijinta</p>
            </div>
            <div style="padding: 30px;">
              <div style="background: #e3f2fd; border: 1px solid #2196f3; padding: 20px; border-radius: 5px; text-align: center; margin-bottom: 20px;">
                <h2 style="color: #1976d2; margin: 0 0 10px 0;">Koodkaagu waa:</h2>
                <div style="font-size: 32px; font-weight: bold; color: #1976d2; letter-spacing: 5px;">${'123456'}</div>
                <p style="margin: 10px 0 0 0; color: #666;">Wuxuu dhacayaa 5 daqiiqo</p>
              </div>
              <div style="background: #fff3e0; border: 1px solid #ff9800; padding: 15px; border-radius: 5px;">
                <p style="margin: 0; color: #f57c00;"><strong>⚠️ Digniin:</strong> Ha la wadaagin qof kale koodkan.</p>
              </div>
            </div>
            <div style="background: #f5f5f5; padding: 15px; text-align: center; font-size: 12px; color: #666;">
              Mahadsanid,<br>Kooxda Ciribey Gas Delivery
            </div>
          </div>
        `
      }
    );

    if (otpEmailResult.success) {
      console.log(`✅ OTP email sent successfully! Message ID: ${otpEmailResult.messageId}`);
    } else {
      console.log(`❌ OTP email failed: ${otpEmailResult.error}`);
    }

    // 4. Test low stock alert email
    console.log('📊 Testing low stock alert email...');
    const lowStockEmail = messageService.lowStockEmail('13kg Gas Cylinder', 3);
    const lowStockResult = await emailService.sendEmail(
      testEmail,
      lowStockEmail
    );

    if (lowStockResult.success) {
      console.log(`✅ Low stock alert email sent successfully! Message ID: ${lowStockResult.messageId}`);
    } else {
      console.log(`❌ Low stock alert email failed: ${lowStockResult.error}`);
    }

    // 5. Test order confirmation email
    console.log('📦 Testing order confirmation email...');
    const orderConfirmationResult = await emailService.sendEmail(
      testEmail,
      {
        subject: '✅ Ciribey Gas Delivery - Dalabka la Xaqiijiyay',
        body: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #4caf50 0%, #45a049 100%); color: white; padding: 20px;">
              <h1 style="margin: 0;">✅ Dalabka la Xaqiijiyay</h1>
              <p style="margin: 5px 0 0 0; opacity: 0.9;">Ciribey Gas Delivery</p>
            </div>
            <div style="padding: 30px;">
              <h2 style="color: #4caf50;">Mahadsanid dalabkaaga!</h2>
              <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                <p><strong>Lambarka Dalabka:</strong> ORD-TEST-${Date.now()}</p>
                <p><strong>Waqtiga Gaarsiinta:</strong> 30-45 daqiiqo</p>
                <p><strong>Halka la gaarsiin doono:</strong> Test Address, Mogadishu</p>
              </div>
              <p>Waan kula soo xiriiri doonaa marka dalabkaagu diyaar yahay.</p>
              <div style="background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <p style="margin: 0; color: #2e7d32;"><strong>📱 App Download:</strong></p>
                <p style="margin: 5px 0 0 0;">Soo dejiso Ciribey Gas Delivery app-ka si aad u la socoto dalabkaaga.</p>
              </div>
            </div>
            <div style="background: #f5f5f5; padding: 15px; text-align: center;">
              <p style="margin: 0; color: #666;">Mahadsanid,<br>Kooxda Ciribey Gas Delivery</p>
              <p style="margin: 10px 0 0 0; font-size: 12px; color: #999;">
                📞 +252613656021 | 📧 <EMAIL>
              </p>
            </div>
          </div>
        `
      }
    );

    if (orderConfirmationResult.success) {
      console.log(`✅ Order confirmation email sent successfully! Message ID: ${orderConfirmationResult.messageId}`);
    } else {
      console.log(`❌ Order confirmation email failed: ${orderConfirmationResult.error}`);
    }

    // 6. Test bulk email sending (small batch)
    console.log('📬 Testing bulk email sending...');
    const bulkRecipients = [testEmail]; // Only sending to test email for safety
    const bulkResult = await emailService.sendBulkEmail(
      bulkRecipients,
      {
        subject: '📢 Ciribey Gas Delivery - Bulk Test Email',
        body: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #2c3e50;">📢 Bulk Email Test</h2>
            <p>This is a bulk email test from the Ciribey Gas Delivery system.</p>
            <div style="background: #f0f8ff; padding: 15px; border-radius: 5px;">
              <p><strong>Bulk Email Features:</strong></p>
              <ul>
                <li>✅ Batch processing</li>
                <li>✅ Progress tracking</li>
                <li>✅ Error handling</li>
                <li>✅ Rate limiting</li>
              </ul>
            </div>
            <p style="color: #7f8c8d;">Sent at: ${new Date().toLocaleString()}</p>
          </div>
        `
      },
      {
        batchSize: 10,
        delayBetweenBatches: 1000,
        onProgress: (sent, failed, total) => {
          console.log(`📊 Bulk email progress: ${sent} sent, ${failed} failed, ${total} total`);
        }
      }
    );

    console.log(`✅ Bulk email result: ${bulkResult.status}`);
    console.log(`📊 Bulk email stats: ${bulkResult.data.counts.successful} successful, ${bulkResult.data.counts.failed} failed`);

    // 7. Test email service metrics
    console.log('📈 Testing email service metrics...');
    const metrics = emailService.getMetrics();
    console.log(`✅ Email metrics:`, {
      sent: metrics.sentCount,
      failed: metrics.failedCount,
      successRate: `${(metrics.successRate * 100).toFixed(2)}%`
    });

    // 8. Test error handling with invalid email
    console.log('❌ Testing error handling with invalid email...');
    const errorResult = await emailService.sendEmail(
      'invalid-email-address',
      {
        subject: 'Test Error Handling',
        body: 'This should fail due to invalid email address'
      }
    );

    if (!errorResult.success) {
      console.log(`✅ Error handling works correctly: ${errorResult.error}`);
    } else {
      console.log(`❌ Error handling failed - email should not have been sent`);
    }

    console.log('\n🎉 Email service testing completed!');
    console.log('📧 Check your email <NAME_EMAIL> for test emails.');

  } catch (error) {
    console.error('❌ Email service test failed:', error);
    console.error('Stack trace:', error.stack);
  }
}

// Test individual email components
async function testEmailComponents() {
  console.log('\n🧪 Testing Email Components...\n');

  try {
    // Test message service templates
    console.log('📝 Testing message service templates...');
    const messageService = new AppMessageService({ lang: 'so', includePhoneInFooter: true });

    // Test various message types
    const otpMessage = messageService.otpMessage('654321', 10);
    console.log('✅ OTP message generated:', otpMessage.substring(0, 50) + '...');

    const lowStockEmail = messageService.lowStockEmail('6kg Gas Cylinder', 2);
    console.log('✅ Low stock email generated:', lowStockEmail.subject);

    console.log('✅ All message templates working correctly');

  } catch (error) {
    console.error('❌ Email components test failed:', error);
  }
}

// Run comprehensive email tests
async function runAllEmailTests() {
  console.log('🔥 Ciribey Gas Delivery - Email Service Comprehensive Test\n');
  console.log('=' .repeat(60));

  await testEmailService();
  await testEmailComponents();

  console.log('\n' + '=' .repeat(60));
  console.log('✅ All email tests completed!');
  console.log('📧 <NAME_EMAIL> for received emails.');
}

// Run the test if this file is executed directly
if (require.main === module) {
  runAllEmailTests();
}

export { testEmailService, testEmailComponents, runAllEmailTests };
