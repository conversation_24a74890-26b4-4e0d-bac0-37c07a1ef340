import { AppMessageService } from '../constants/app_message.service';

/**
 * Test suite for the buildMessage core message factory method
 */

class BuildMessageTester {
  private messageService: AppMessageService;
  private testResults: { passed: number; failed: number; total: number } = {
    passed: 0,
    failed: 0,
    total: 0
  };

  constructor() {
    this.messageService = new AppMessageService({ lang: 'en' });
  }

  private assertContains(actual: string, expected: string, testName: string): void {
    this.testResults.total++;
    if (actual.includes(expected)) {
      this.testResults.passed++;
      console.log(`✅ ${testName}`);
    } else {
      this.testResults.failed++;
      console.log(`❌ ${testName}`);
      console.log(`   Expected to contain: "${expected}"`);
      console.log(`   Actual: "${actual}"`);
    }
  }

  private assertNotContains(actual: string, unexpected: string, testName: string): void {
    this.testResults.total++;
    if (!actual.includes(unexpected)) {
      this.testResults.passed++;
      console.log(`✅ ${testName}`);
    } else {
      this.testResults.failed++;
      console.log(`❌ ${testName}`);
      console.log(`   Expected NOT to contain: "${unexpected}"`);
      console.log(`   Actual: "${actual}"`);
    }
  }

  runTests(): void {
    console.log('🧪 Running buildMessage Core Factory Tests...\n');

    // Test basic functionality
    this.testBasicMessageConstruction();
    this.testBrandingInjection();
    this.testLanguageSelection();
    this.testSanitizationAndTruncation();
    this.testEdgeCases();

    this.printResults();
  }

  private testBasicMessageConstruction(): void {
    console.log('\n📝 Testing Basic Message Construction...');
    
    const templates = {
      so: { subject: 'Test Subject', body: 'Test body content' },
      en: { subject: 'Test Subject', body: 'Test body content' }
    };

    const result = (this.messageService as any).buildMessage(templates, {
      includeAppNamePrefix: false,
      includeCompanySignature: false,
      includeAppFooter: false
    });

    this.assertContains(result, 'Test Subject', 'Should contain subject');
    this.assertContains(result, 'Test body content', 'Should contain body');
    this.assertNotContains(result, 'GasDelivery -', 'Should not contain app name prefix');
    this.assertNotContains(result, 'Thank you,', 'Should not contain company signature');
  }

  private testBrandingInjection(): void {
    console.log('\n🎨 Testing Branding Injection...');
    
    const templates = {
      so: { subject: 'Test Subject', body: 'Test body content' },
      en: { subject: 'Test Subject', body: 'Test body content' }
    };

    // Test with app name prefix
    const resultWithPrefix = (this.messageService as any).buildMessage(templates, {
      includeAppNamePrefix: true,
      includeCompanySignature: false,
      includeAppFooter: false
    });

    this.assertContains(resultWithPrefix, 'GasDelivery - Test Subject', 'Should contain app name prefix');

    // Test with company signature
    const resultWithSignature = (this.messageService as any).buildMessage(templates, {
      includeAppNamePrefix: false,
      includeCompanySignature: true,
      includeAppFooter: false
    });

    this.assertContains(resultWithSignature, 'Thank you,', 'Should contain company signature');

    // Test with app footer
    const resultWithFooter = (this.messageService as any).buildMessage(templates, {
      includeAppNamePrefix: false,
      includeCompanySignature: false,
      includeAppFooter: true
    });

    this.assertContains(resultWithFooter, 'Download GasDelivery:', 'Should contain app footer');
  }

  private testLanguageSelection(): void {
    console.log('\n🌍 Testing Language Selection...');
    
    const templates = {
      so: { subject: 'Somali Subject', body: 'Somali body content' },
      en: { subject: 'English Subject', body: 'English body content' }
    };

    // Test English language
    const englishService = new AppMessageService({ lang: 'en' });
    const englishResult = (englishService as any).buildMessage(templates, {
      includeAppNamePrefix: false,
      includeCompanySignature: false,
      includeAppFooter: false
    });

    this.assertContains(englishResult, 'English Subject', 'Should contain English subject');
    this.assertContains(englishResult, 'English body content', 'Should contain English body');
    this.assertNotContains(englishResult, 'Somali Subject', 'Should not contain Somali subject');

    // Test Somali language
    const somaliService = new AppMessageService({ lang: 'so' });
    const somaliResult = (somaliService as any).buildMessage(templates, {
      includeAppNamePrefix: false,
      includeCompanySignature: false,
      includeAppFooter: false
    });

    this.assertContains(somaliResult, 'Somali Subject', 'Should contain Somali subject');
    this.assertContains(somaliResult, 'Somali body content', 'Should contain Somali body');
    this.assertNotContains(somaliResult, 'English Subject', 'Should not contain English subject');
  }

  private testSanitizationAndTruncation(): void {
    console.log('\n🧹 Testing Sanitization and Truncation...');
    
    const templates = {
      so: { subject: 'Test Subject', body: 'Test body with emoji 🔥 and unicode dashes – —' },
      en: { subject: 'Test Subject', body: 'Test body with emoji 🔥 and unicode dashes – —' }
    };

    const result = (this.messageService as any).buildMessage(templates, {
      includeAppNamePrefix: false,
      includeCompanySignature: false,
      includeAppFooter: false
    });

    this.assertNotContains(result, '🔥', 'Should remove emojis');
    this.assertContains(result, 'Test body with emoji', 'Should preserve text content');
    this.assertContains(result, 'unicode dashes - -', 'Should convert unicode dashes to standard hyphens');
  }

  private testEdgeCases(): void {
    console.log('\n⚠️ Testing Edge Cases...');
    
    // Test empty templates
    const emptyTemplates = {
      so: { subject: '', body: '' },
      en: { subject: '', body: '' }
    };

    const emptyResult = (this.messageService as any).buildMessage(emptyTemplates, {
      includeAppNamePrefix: false,
      includeCompanySignature: false,
      includeAppFooter: false
    });

    this.assertContains(emptyResult, '\n', 'Should handle empty templates gracefully');

    // Test very long content
    const longTemplates = {
      so: { 
        subject: 'A'.repeat(200), 
        body: 'B'.repeat(300) 
      },
      en: { 
        subject: 'A'.repeat(200), 
        body: 'B'.repeat(300) 
      }
    };

    const longResult = (this.messageService as any).buildMessage(longTemplates, {
      includeAppNamePrefix: true,
      includeCompanySignature: true,
      includeAppFooter: true
    });

    // Should be truncated to SMS limit
    if (longResult.length <= 150) {
      this.testResults.passed++;
      console.log('✅ Should truncate long messages to SMS limit');
    } else {
      this.testResults.failed++;
      console.log('❌ Should truncate long messages to SMS limit');
      console.log(`   Expected length <= 150, got ${longResult.length}`);
    }
    this.testResults.total++;
  }

  private printResults(): void {
    console.log('\n📊 BuildMessage Test Results:');
    console.log(`Total Tests: ${this.testResults.total}`);
    console.log(`Passed: ${this.testResults.passed}`);
    console.log(`Failed: ${this.testResults.failed}`);
    console.log(`Success Rate: ${((this.testResults.passed / this.testResults.total) * 100).toFixed(1)}%`);
    
    if (this.testResults.failed === 0) {
      console.log('\n🎉 All buildMessage tests passed!');
      console.log('✅ Core message factory is working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Please review the output above.');
    }
  }
}

/**
 * Run the buildMessage tests
 */
export function runBuildMessageTests(): void {
  const tester = new BuildMessageTester();
  tester.runTests();
}

// Run the test if this file is executed directly
if (require.main === module) {
  runBuildMessageTests();
}

export { BuildMessageTester, runBuildMessageTests };
