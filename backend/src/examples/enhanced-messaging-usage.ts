import { AppMessageService } from '../constants/app_message.service';

/**
 * Enhanced Messaging Service Usage Examples
 * 
 * This file demonstrates the new per-method branding control features
 * of the AppMessageService.
 */

// Initialize the messaging service
const messageService = new AppMessageService({
  lang: 'en', // or 'so' for Somali
  includePhoneInFooter: true,
});

/**
 * Example 1: OTP Messages (No Branding by Default)
 * 
 * Critical messages like OTPs are optimized for SMS length and clarity
 * by excluding branding elements by default.
 */
export function demonstrateOTPMessages() {
  console.log('=== OTP Messages (No Branding by Default) ===');
  
  // Default OTP message - clean and concise
  const defaultOtp = messageService.otpMessage('123456');
  console.log('Default OTP:', defaultOtp);
  // Output: "Verification Code\nYour code is: 123456\nExpires in 5 minutes.\n\nDo not share this code."
  
  // OTP with forced branding (rare case)
  const brandedOtp = messageService.otpMessage('123456', 5, {
    includeAppNamePrefix: true,
    includeCompanySignature: true,
  });
  console.log('Branded OTP:', brandedOtp);
  // Output: "GasDelivery - Verification Code\nYour code is: 123456\nExpires in 5 minutes.\n\nDo not share this code.\n\nThank you,\nGasDelivery Team"
}

/**
 * Example 2: Order Confirmation (Full Branding by Default)
 * 
 * Transactional messages like order confirmations include full branding
 * for professionalism and brand recognition.
 */
export function demonstrateOrderMessages() {
  console.log('\n=== Order Messages (Full Branding by Default) ===');
  
  // Default order confirmation - full branding
  const defaultOrder = messageService.orderConfirmed('ORDER_123');
  console.log('Default Order Confirmation:', defaultOrder);
  // Output: "GasDelivery - Order Confirmed\nOrder ID: ORDER_123\nWe will contact you when ready.\n\nDelivery time: 2-4 hours\n\nThank you,\nGasDelivery Team\n\nDownload GasDelivery: https://app.link\nContact us: +1234567890"
  
  // Order confirmation without footer
  const orderWithoutFooter = messageService.orderConfirmed('ORDER_123', {
    includeAppFooter: false,
  });
  console.log('Order without Footer:', orderWithoutFooter);
  // Output: "GasDelivery - Order Confirmed\nOrder ID: ORDER_123\nWe will contact you when ready.\n\nDelivery time: 2-4 hours\n\nThank you,\nGasDelivery Team"
  
  // Order confirmation without any branding
  const minimalOrder = messageService.orderConfirmed('ORDER_123', {
    includeAppNamePrefix: false,
    includeCompanySignature: false,
    includeAppFooter: false,
  });
  console.log('Minimal Order:', minimalOrder);
  // Output: "Order Confirmed\nOrder ID: ORDER_123\nWe will contact you when ready.\n\nDelivery time: 2-4 hours"
}

/**
 * Example 3: Delivery Messages (Mixed Branding)
 * 
 * Delivery messages have different branding strategies based on their purpose.
 */
export function demonstrateDeliveryMessages() {
  console.log('\n=== Delivery Messages (Mixed Branding) ===');
  
  // Delivery assignment for agents - includes app name and signature
  const deliveryAssignment = messageService.deliveryAssigned('ORDER_123', 'John Doe', '123 Main St');
  console.log('Delivery Assignment:', deliveryAssignment);
  // Output: "GasDelivery - New Delivery\nOrder: ORDER_123\nCustomer: John Doe\nAddress: 123 Main St\n\nComplete within 4 hours.\n\nThank you,\nGasDelivery Team"
  
  // Delivery started for customers - includes app name but no signature
  const deliveryStarted = messageService.deliveryStarted('ORDER_123', 'Agent Smith', '+1234567890');
  console.log('Delivery Started:', deliveryStarted);
  // Output: "GasDelivery - Delivery Started\nOrder: ORDER_123\nAgent: Agent Smith\nPhone: +1234567890\n\nOn the way!"
}

/**
 * Example 4: Alert Messages (Appropriate Branding)
 * 
 * Alert messages use branding strategically based on urgency and audience.
 */
export function demonstrateAlertMessages() {
  console.log('\n=== Alert Messages (Appropriate Branding) ===');
  
  // Low stock alert - includes app name but no signature/footer
  const lowStock = messageService.lowStockAlert('Gas Cylinder 13kg', 5);
  console.log('Low Stock Alert:', lowStock);
  // Output: "GasDelivery - Low Stock Alert\nGas Cylinder 13kg: Only 5 remaining!\n\nPlease restock to avoid shortages."
  
  // Emergency alert - includes app name and phone number
  const emergency = messageService.emergencyAlert('System maintenance required immediately');
  console.log('Emergency Alert:', emergency);
  // Output: "URGENT - GasDelivery\nSystem maintenance required immediately\n\n+1234567890"
  
  // System maintenance - includes app name and signature
  const maintenance = messageService.maintenanceNotification('2024-01-15 02:00', '2 hours');
  console.log('Maintenance Notification:', maintenance);
  // Output: "GasDelivery - System Maintenance\nTime: 2024-01-15 02:00\nDuration: 2 hours\n\nWe apologize for the inconvenience.\n\nThank you,\nGasDelivery Team"
}

/**
 * Example 5: Payment Messages (Strategic Branding)
 * 
 * Payment messages use branding based on success/failure context.
 */
export function demonstratePaymentMessages() {
  console.log('\n=== Payment Messages (Strategic Branding) ===');
  
  // Payment confirmed - includes app name but no signature/footer
  const paymentConfirmed = messageService.paymentConfirmed('ORDER_123', 25.99);
  console.log('Payment Confirmed:', paymentConfirmed);
  // Output: "GasDelivery - Payment Confirmed\nOrder: ORDER_123\nAmount: $25.99\n\nPayment processed successfully."
  
  // Payment failed - includes app name and signature for reassurance
  const paymentFailed = messageService.paymentFailed('ORDER_123', 'Insufficient funds');
  console.log('Payment Failed:', paymentFailed);
  // Output: "GasDelivery - Payment Failed\nOrder: ORDER_123\nReason: Insufficient funds\n\nPlease try again.\n\nThank you,\nGasDelivery Team"
}

/**
 * Example 6: Welcome Messages (Role-Based Branding)
 * 
 * Welcome messages adapt branding based on user role.
 */
export function demonstrateWelcomeMessages() {
  console.log('\n=== Welcome Messages (Role-Based Branding) ===');
  
  // Customer welcome - includes signature but no prefix/footer
  const customerWelcome = messageService.welcomeMessage('John Doe', 'customer');
  console.log('Customer Welcome:', customerWelcome);
  // Output: "Welcome to GasDelivery!\nName: John Doe\n\nYou can now order gas for your home.\n\nThank you,\nGasDelivery Team"
  
  // Agent welcome - includes signature but no prefix/footer
  const agentWelcome = messageService.welcomeMessage('Agent Smith', 'agent');
  console.log('Agent Welcome:', agentWelcome);
  // Output: "Welcome to the delivery team!\nName: Agent Smith\n\nGet ready for new orders.\n\nThank you,\nGasDelivery Team"
}

/**
 * Example 7: Generic Notifications (Flexible Branding)
 * 
 * Generic notifications allow full control over branding elements.
 */
export function demonstrateGenericNotifications() {
  console.log('\n=== Generic Notifications (Flexible Branding) ===');
  
  // Default generic notification - includes app name prefix
  const defaultNotification = messageService.genericNotification('System Update', 'New features available');
  console.log('Default Notification:', defaultNotification);
  // Output: "GasDelivery - System Update\nNew features available"
  
  // Generic notification with full branding
  const fullBrandedNotification = messageService.genericNotification('System Update', 'New features available', {
    includeAppNamePrefix: true,
    includeCompanySignature: true,
    includeAppFooter: true,
  });
  console.log('Full Branded Notification:', fullBrandedNotification);
  // Output: "GasDelivery - System Update\nNew features available\n\nThank you,\nGasDelivery Team\n\nDownload GasDelivery: https://app.link\nContact us: +1234567890"
}

/**
 * Example 8: Character Count and SMS Limit Checking
 * 
 * Utility methods for SMS planning and optimization.
 */
export function demonstrateUtilityMethods() {
  console.log('\n=== Utility Methods ===');
  
  const testMessage = messageService.otpMessage('123456');
  const messageLength = messageService.getMessageLength(testMessage);
  const exceedsLimit = messageService.exceedsSMSLimit(testMessage);
  
  console.log(`Message length: ${messageLength} characters`);
  console.log(`Exceeds SMS limit: ${exceedsLimit}`);
  console.log(`Current language: ${messageService.getCurrentLanguage()}`);
  console.log(`Supported languages: ${AppMessageService.getSupportedLanguages().join(', ')}`);
}

/**
 * Example 9: Somali Language Support
 * 
 * Demonstrates the service working with Somali language.
 */
export function demonstrateSomaliLanguage() {
  console.log('\n=== Somali Language Support ===');
  
  const somaliService = new AppMessageService({ lang: 'so' });
  
  const somaliOtp = somaliService.otpMessage('123456');
  console.log('Somali OTP:', somaliOtp);
  // Output: "Koodka Xaqiijinta\nKoodkaagu waa: 123456\nWuxuu dhacayaa 5 daqiiqo.\n\nHa la wadaagin qof kale."
  
  const somaliOrder = somaliService.orderConfirmed('ORDER_123');
  console.log('Somali Order:', somaliOrder);
  // Output: "GasDelivery - Dalabka la Xaqiijiyay\nLambarka dalabka: ORDER_123\nWaan kula soo xiriiri doonaa marka uu diyaar yahay.\n\nWaqtiga Deliveryta: 2-4 saacadood\n\nMahadsanid,\nCompany GasDelivery\n\nLasoo dag GasDelivery: https://app.link\nNagala soo xiriir: +1234567890"
}

/**
 * Main demonstration function
 */
export function runAllExamples() {
  console.log('🚀 Enhanced Messaging Service Examples\n');
  
  demonstrateOTPMessages();
  demonstrateOrderMessages();
  demonstrateDeliveryMessages();
  demonstrateAlertMessages();
  demonstratePaymentMessages();
  demonstrateWelcomeMessages();
  demonstrateGenericNotifications();
  demonstrateUtilityMethods();
  demonstrateSomaliLanguage();
  
  console.log('\n✅ All examples completed successfully!');
}

// Export for use in other files
export {
  messageService,
  demonstrateOTPMessages,
  demonstrateOrderMessages,
  demonstrateDeliveryMessages,
  demonstrateAlertMessages,
  demonstratePaymentMessages,
  demonstrateWelcomeMessages,
  demonstrateGenericNotifications,
  demonstrateUtilityMethods,
  demonstrateSomaliLanguage,
  runAllExamples,
};
