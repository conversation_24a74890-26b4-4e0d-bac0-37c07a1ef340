import { ErrorRequestHandler, Request, Response, NextFunction } from 'express';
import { AppError } from '../errors/app_errors';
import { sendResponse } from '../utils/response';
import logger from '../config/logger';

export const globalErrorHandler: ErrorRequestHandler = (
  err: unknown,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  // logger.error(
  // `Error type received: ${typeof err}, constructor: ${err?.constructor?.name}, instance of AppError: ${err instanceof AppError}`
  // );

  logger.error(
    `Error type received: ${typeof err}, constructor: ${err?.constructor?.name}, instanceof AppError: ${
      err instanceof AppError
    }, file path: ${__filename}, AppError imported from: ${require.resolve('../errors/app_errors')}`
  );

  // ✅ Handle custom AppError
  if (isAppError(err)) {
    logger.warn(`${req.method} ${req.originalUrl} → ${err.statusCode} ${err.message}`);

    sendResponse(res, err.statusCode, err.status, err.message, {
      errors: {
        type: err.parentCode, // e.g., VALIDATION_ERROR, NOT_FOUND
        code: err.code || err.name, // e.g., INVALID_PHONE or ValidationError
        details: err.details || undefined,
      },
      stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
    });

    return;
  }

  // ❌ Handle unknown/unexpected errors
  logger.error(`UNEXPECTED ERROR → ${req.method} ${req.originalUrl}`, err);

  const errorMessage = err instanceof Error ? err.message : 'Internal Server Error';

  sendResponse(res, 500, 'error', errorMessage, {
    errors: {
      type: 'INTERNAL_SERVER_ERROR',
      code: err instanceof Error ? err.name : 'UnknownError',
    },
    stack: process.env.NODE_ENV === 'development' && err instanceof Error ? err.stack : undefined,
  });
};

const isAppError = (err: unknown): err is AppError => {
  return (
    err instanceof AppError ||
    (typeof err === 'object' &&
      err !== null &&
      'isOperational' in err &&
      (err as any).isOperational === true &&
      'statusCode' in err &&
      'status' in err &&
      'parentCode' in err)
  );
};
