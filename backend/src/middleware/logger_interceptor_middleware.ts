import { Request, Response, NextFunction } from 'express';
import logger from '../config/logger';

export const loggerInterceptor = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now();

  let requestPayload = {
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.url,
    // headers: req.headers,
    body: req.body,
    // query: req.query,
    // params: req.params,
    ip: req.ip,
  };

  // Log request details
  logger.info(`Request: ${JSON.stringify(requestPayload)}`);

  //   // Store the original response functions
  const originalSend = res.send;
  const originalJson = res.json;

  //   // Response interception
  let responsePayload = (body: any) => {
    return {
      timestamp: new Date().toISOString(),
      status: res.statusCode,
      duration: `${Date.now() - start}ms`,
      //   headers: res.getHeaders(),
      body: body,
    };
  };

  res.send = function (body) {
    // Log response before sending
    logger.info(`Response: ${JSON.stringify(responsePayload(body))}`);

    return originalSend.apply(res, arguments);
  };

  // res.json = function (body) {
  //   // Log response before sending
  //   logger.info(`Response: ${JSON.stringify(responsePayload(body))}`);

  //   return originalJson.apply(res, arguments);
  // };

  next();
};
