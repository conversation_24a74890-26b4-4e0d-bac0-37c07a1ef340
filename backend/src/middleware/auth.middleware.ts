import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { UnauthorizedError } from '../errors/app_errors';
import { TokenPayload } from '../types/interfaces';
import { config } from '../config/env_config';
import { User } from '../models/index';

export const authenticate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedError('Authentication required');
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      throw new UnauthorizedError('Authentication token missing');
    }

    // Verify token
    const decoded = jwt.verify(token, config.server.jwtSecret) as TokenPayload;

    // Check if user exists
    const user = await User.findById(decoded.userId).select('_id role');
    if (!user) {
      throw new UnauthorizedError('User not found');
    }

    // Add user info to request
    req.user = decoded;
    console.log('User authenticated', {
      userId: decoded.userId,
      role: decoded.role,
    });

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(new UnauthorizedError('Invalid authentication token'));
    } else if (error instanceof jwt.TokenExpiredError) {
      next(new UnauthorizedError('Authentication token expired'));
    } else {
      next(error);
    }
  }
};
