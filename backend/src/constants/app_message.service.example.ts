/**
 * Usage Examples for AppMessageService
 * Demonstrates how to use the multilingual messaging service
 */

import { AppMessageService } from './app_message.service';

// =============================================================================
// BASIC USAGE EXAMPLES
// =============================================================================

console.log('🔥 Gas Delivery System - Multilingual Messaging Service Examples\n');

// Example 1: Default Somali messaging for customers
const somaliCustomerService = new AppMessageService({
  lang: 'so',
  recipientRole: 'customer',
  includePhoneInFooter: true
});

console.log('📱 SOMALI CUSTOMER MESSAGES:');
console.log('─'.repeat(50));

// OTP Message
const otpSomali = somaliCustomerService.otpMessage('123456', 5);
console.log('OTP Message (Somali):');
console.log(otpSomali);
console.log(`Length: ${somaliCustomerService.getMessageLength(otpSomali)} chars\n`);

// Order Confirmation
const orderConfirmedSomali = somaliCustomerService.orderConfirmed('ORD-2024-001');
console.log('Order Confirmed (Somali):');
console.log(orderConfirmedSomali);
console.log(`Length: ${somaliCustomerService.getMessageLength(orderConfirmedSomali)} chars\n`);

// Order Delivered
const orderDeliveredSomali = somaliCustomerService.orderDelivered('ORD-2024-001');
console.log('Order Delivered (Somali):');
console.log(orderDeliveredSomali);
console.log(`Length: ${somaliCustomerService.getMessageLength(orderDeliveredSomali)} chars\n`);

// =============================================================================
// ENGLISH MESSAGING FOR INTERNATIONAL CUSTOMERS
// =============================================================================

const englishCustomerService = new AppMessageService({
  lang: 'en',
  recipientRole: 'customer',
  includePhoneInFooter: true
});

console.log('🌍 ENGLISH CUSTOMER MESSAGES:');
console.log('─'.repeat(50));

// OTP Message
const otpEnglish = englishCustomerService.otpMessage('123456', 5);
console.log('OTP Message (English):');
console.log(otpEnglish);
console.log(`Length: ${englishCustomerService.getMessageLength(otpEnglish)} chars\n`);

// Order Confirmation
const orderConfirmedEnglish = englishCustomerService.orderConfirmed('ORD-2024-001');
console.log('Order Confirmed (English):');
console.log(orderConfirmedEnglish);
console.log(`Length: ${englishCustomerService.getMessageLength(orderConfirmedEnglish)} chars\n`);

// =============================================================================
// AGENT MESSAGING (DELIVERY ASSIGNMENTS)
// =============================================================================

const somaliAgentService = new AppMessageService({
  lang: 'so',
  recipientRole: 'agent',
  includePhoneInFooter: false // Agents don't need customer contact info
});

console.log('🚚 SOMALI AGENT MESSAGES:');
console.log('─'.repeat(50));

// Delivery Assignment
const deliveryAssignment = somaliAgentService.deliveryAssigned(
  'ORD-2024-001',
  'Ahmed Mohamed',
  'Hodan District, Mogadishu'
);
console.log('Delivery Assignment (Somali):');
console.log(deliveryAssignment);
console.log(`Length: ${somaliAgentService.getMessageLength(deliveryAssignment)} chars\n`);

// Welcome Message for New Agent
const agentWelcome = somaliAgentService.welcomeMessage('Abdi Hassan', 'agent');
console.log('Agent Welcome (Somali):');
console.log(agentWelcome);
console.log(`Length: ${somaliAgentService.getMessageLength(agentWelcome)} chars\n`);

// =============================================================================
// ADMIN/SUPERVISOR MESSAGING (INVENTORY ALERTS)
// =============================================================================

const somaliAdminService = new AppMessageService({
  lang: 'so',
  recipientRole: 'admin',
  includePhoneInFooter: false
});

console.log('👔 SOMALI ADMIN MESSAGES:');
console.log('─'.repeat(50));

// Low Stock Alert
const lowStockSMS = somaliAdminService.lowStockAlert('13kg Gas Cylinder', 5);
console.log('Low Stock Alert SMS (Somali):');
console.log(lowStockSMS);
console.log(`Length: ${somaliAdminService.getMessageLength(lowStockSMS)} chars\n`);

// Out of Stock Alert
const outOfStockSMS = somaliAdminService.outOfStockAlert('6kg Gas Cylinder');
console.log('Out of Stock Alert SMS (Somali):');
console.log(outOfStockSMS);
console.log(`Length: ${somaliAdminService.getMessageLength(outOfStockSMS)} chars\n`);

// Low Stock Email (HTML)
const lowStockEmail = somaliAdminService.lowStockEmail('13kg Gas Cylinder', 5);
console.log('Low Stock Email Subject (Somali):');
console.log(lowStockEmail.subject);
console.log('\nLow Stock Email Body (HTML - truncated):');
console.log(lowStockEmail.body.substring(0, 200) + '...\n');

// =============================================================================
// PAYMENT MESSAGING
// =============================================================================

console.log('💳 PAYMENT MESSAGES:');
console.log('─'.repeat(50));

// Payment Confirmed
const paymentConfirmed = somaliCustomerService.paymentConfirmed('ORD-2024-001', 25.50);
console.log('Payment Confirmed (Somali):');
console.log(paymentConfirmed);
console.log(`Length: ${somaliCustomerService.getMessageLength(paymentConfirmed)} chars\n`);

// Payment Failed
const paymentFailed = somaliCustomerService.paymentFailed('ORD-2024-001', 'Insufficient balance');
console.log('Payment Failed (Somali):');
console.log(paymentFailed);
console.log(`Length: ${somaliCustomerService.getMessageLength(paymentFailed)} chars\n`);

// =============================================================================
// UTILITY FUNCTIONS DEMONSTRATION
// =============================================================================

console.log('🛠️ UTILITY FUNCTIONS:');
console.log('─'.repeat(50));

// Check SMS limits
const longMessage = somaliCustomerService.orderConfirmed('ORD-2024-001-VERY-LONG-ORDER-ID-FOR-TESTING');
console.log(`Long message exceeds SMS limit: ${somaliCustomerService.exceedsSMSLimit(longMessage)}`);
console.log(`Current language: ${somaliCustomerService.getCurrentLanguage()}`);
console.log(`Supported languages: ${AppMessageService.getSupportedLanguages().join(', ')}`);

// Emergency Alert
const emergencyAlert = somaliCustomerService.emergencyAlert('Xaalad degdeg ah - dhammaan dalabka waa la joojiyay');
console.log('\nEmergency Alert (Somali):');
console.log(emergencyAlert);
console.log(`Length: ${somaliCustomerService.getMessageLength(emergencyAlert)} chars\n`);

// Generic Notification
const genericNotification = somaliCustomerService.genericNotification(
  'Cusbooneysiin Nidaam',
  'Nidaamka waa la cusbooneysiinayaa 2:00 AM - 4:00 AM'
);
console.log('Generic Notification (Somali):');
console.log(genericNotification);
console.log(`Length: ${somaliCustomerService.getMessageLength(genericNotification)} chars\n`);

// =============================================================================
// ROLE-BASED MESSAGING COMPARISON
// =============================================================================

console.log('👥 ROLE-BASED WELCOME MESSAGES:');
console.log('─'.repeat(50));

const roles = ['customer', 'agent', 'admin', 'supervisor'];
roles.forEach(role => {
  const service = new AppMessageService({ lang: 'so', recipientRole: role as 'customer' | 'agent' | 'admin' | 'supervisor' });
  const welcome = service.welcomeMessage('Mohamed Ali', role);
  console.log(`${role.toUpperCase()} Welcome (Somali):`);
  console.log(welcome);
  console.log(`Length: ${service.getMessageLength(welcome)} chars\n`);
});

console.log('✅ All messaging examples completed successfully!');
console.log('📊 Summary:');
console.log(`- Primary Language: Somali (so)`);
console.log(`- Secondary Language: English (en)`);
console.log(`- SMS Character Limit: 150 chars`);
console.log(`- Supported Roles: customer, agent, admin, supervisor`);
console.log(`- Total Message Types: 15+ different scenarios`);
