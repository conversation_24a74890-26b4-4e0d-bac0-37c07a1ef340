import { AppMessageService } from './src/constants/app_message.service';

const messageService = new AppMessageService();

function debugSanitization(input: string, testName: string, expected: string): void {
  console.log(`\n=== ${testName} ===`);
  console.log(`Input: "${input}"`);
  console.log(`Expected: "${expected}"`);

  const result = (messageService as any).sanitizeMessageContent(input);
  console.log(`Actual: "${result}"`);
  console.log(`Match: ${result === expected ? '✅' : '❌'}`);

  // Debug step by step
  let step = input;
  console.log(`\nStep-by-step debug:`);
  console.log(`1. Original: "${step}"`);

  // Unicode dashes
  step = step.replace(/[–—]/g, '-');
  console.log(`2. After dashes: "${step}"`);

  // Ellipses
  step = step.replace(/…/g, '...');
  console.log(`3. After ellipses: "${step}"`);

  // Quotes
  step = step.replace(/[""«»„‟]/g, '"').replace(/[''‚‛‹›]/g, "'");
  console.log(`4. After quotes: "${step}"`);

  // Emojis (simplified)
  step = step.replace(/[\uD83D][\uDE00-\uDE4F]/g, '');
  step = step.replace(
    /[\uD83C][\uDF00-\uDFFF]|[\uD83D][\uDC00-\uDDFF]|[\uD83D][\uDE80-\uDEFF]/g,
    ''
  );
  step = step.replace(/[\uD83D][\uDE80-\uDEFF]/g, '');
  step = step.replace(/[\uD83E][\uDD10-\uDD6B]/g, '');
  step = step.replace(/[\u2600-\u26FF]/g, '');
  step = step.replace(/[\u2700-\u27BF]/g, '');
  step = step.replace(/[\uD83C][\uDD00-\uDDFF]/g, '');
  step = step.replace(/[\uFE0E\uFE0F]/g, '');
  step = step.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g, '');
  console.log(`5. After emojis: "${step}"`);

  // Non-breaking spaces
  step = step.replace(/\u00A0/g, ' ');
  console.log(`6. After non-breaking spaces: "${step}"`);

  // Newlines
  step = step.replace(/\n{3,}/g, '\n\n');
  step = step.replace(/^[ \t]+|[ \t]+$/gm, '');
  step = step.replace(/\n\s*\n\s*\n/g, '\n\n');
  console.log(`7. After newline normalization: "${step}"`);

  // Spaces
  step = step.replace(/[ \t]+/g, ' ');
  console.log(`8. After space normalization: "${step}"`);

  // Redundant punctuation
  step = step.replace(/([,!?])\1+/g, '$1');
  step = step.replace(/\.\.\./g, '___ELLIPSIS___');
  step = step.replace(/\.{2,}/g, '.');
  step = step.replace(/___ELLIPSIS___/g, '...');
  console.log(`9. After redundant punctuation: "${step}"`);

  // Colon spacing
  step = step.replace(/\s*:\s*/g, ': ');
  console.log(`10. After colon spacing: "${step}"`);

  // Punctuation spacing
  step = step.replace(/\.\.\./g, '___ELLIPSIS___');
  step = step.replace(/([,.!?])([^\s\n])/g, '$1 $2');
  step = step.replace(/([,.!?])[ \t]+/g, '$1 ');
  step = step.replace(/___ELLIPSIS___/g, '...');
  console.log(`11. After punctuation spacing: "${step}"`);

  // Content analysis
  const hasStructuredData = /^[^:\n]*:\s*[^:\n]*$/m.test(step);
  const hasMultipleParagraphs = /\n\n/.test(step);
  const isSimpleLineList =
    !hasMultipleParagraphs &&
    /\n/.test(step) &&
    !/\b(arrive|gas|thank|please|your|will|can|processing)\b/i.test(step);

  console.log(`12. Content analysis:`);
  console.log(`    hasStructuredData: ${hasStructuredData}`);
  console.log(`    hasMultipleParagraphs: ${hasMultipleParagraphs}`);
  console.log(`    isSimpleLineList: ${isSimpleLineList}`);

  // Newline handling
  if (hasStructuredData || isSimpleLineList) {
    step = step.replace(/\n{2,}/g, '\n');
    console.log(`13. After structured/list newline handling: "${step}"`);
  } else if (hasMultipleParagraphs) {
    step = step.replace(/\n\n/g, '\n');
    step = step.replace(/([^.!?\n])\n(?=\S)/g, '$1. ');
    step = step.replace(/\n/g, ' ');
    console.log(`13. After paragraph newline handling: "${step}"`);
  } else {
    step = step.replace(/\n{2,}/g, '\n');
    console.log(`13. After simple newline handling: "${step}"`);
  }

  // Final cleanup
  step = step.replace(/[ \t]+/g, ' ');
  step = step.trim();
  console.log(`14. After final cleanup: "${step}"`);
}

// Test the failing cases
debugSanitization(
  '🔥 New Order 🔥\n\nYour gas will arrive in 1–2 hours…\n\nThank you! ❤️',
  'Remove all emojis and Unicode symbols',
  'New Order. Your gas will arrive in 1-2 hours. Thank you!'
);

debugSanitization(
  'Line 1\n\n\nLine 2\n\n\n\nLine 3',
  'Collapse multiple newlines into single line breaks',
  'Line 1\nLine 2\nLine 3'
);

debugSanitization(
  '  Line 1  \n  Line 2  \n  Line 3  ',
  'Remove leading/trailing whitespace from each line',
  'Line 1\nLine 2\nLine 3'
);

debugSanitization(
  'Order confirmed,,, delivery time: 30 minutes... Thank you!!!',
  'Remove redundant punctuation',
  'Order confirmed, delivery time: 30 minutes. Thank you!'
);

debugSanitization(
  'Order ID: 12345\n\nStatus: Confirmed\n\nDelivery: 30–45 minutes',
  'Preserve essential formatting while cleaning',
  'Order ID: 12345\nStatus: Confirmed\nDelivery: 30-45 minutes'
);
