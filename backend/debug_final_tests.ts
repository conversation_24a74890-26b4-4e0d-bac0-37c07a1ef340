import { AppMessageService } from './src/constants/app_message.service';

const messageService = new AppMessageService();

function debugSanitization(input: string, testName: string, expected: string): void {
  console.log(`\n=== ${testName} ===`);
  console.log(`Input: "${input}"`);
  console.log(`Expected: "${expected}"`);

  const result = (messageService as any).sanitizeMessageContent(input);
  console.log(`Actual: "${result}"`);
  console.log(`Match: ${result === expected ? '✅' : '❌'}`);

  // Debug content analysis
  let step = input;

  // Apply transformations up to content analysis
  step = step.replace(/[–—]/g, '-');
  step = step.replace(/…/g, '...');
  step = step.replace(/[""«»„‟]/g, '"').replace(/[''‚‛‹›]/g, "'");

  // Emojis
  step = step.replace(/[\uD83D][\uDE00-\uDE4F]/g, '');
  step = step.replace(
    /[\uD83C][\uDF00-\uDFFF]|[\uD83D][\uDC00-\uDDFF]|[\uD83D][\uDE80-\uDEFF]/g,
    ''
  );
  step = step.replace(/[\uD83D][\uDE80-\uDEFF]/g, '');
  step = step.replace(/[\uD83E][\uDD10-\uDD6B]/g, '');
  step = step.replace(/[\u2600-\u26FF]/g, '');
  step = step.replace(/[\u2700-\u27BF]/g, '');
  step = step.replace(/[\uD83C][\uDD00-\uDDFF]/g, '');
  step = step.replace(/[\uFE0E\uFE0F]/g, '');
  step = step.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g, '');

  step = step.replace(/\u00A0/g, ' ');
  step = step.replace(/\n{3,}/g, '\n\n');
  step = step.replace(/^[ \t]+|[ \t]+$/gm, '');
  step = step.replace(/\n\s*\n\s*\n/g, '\n\n');
  step = step.replace(/[ \t]+/g, ' ');

  // Redundant punctuation
  step = step.replace(/([,!?])\1+/g, '$1');
  step = step.replace(/\.\.\./g, '___ELLIPSIS___');
  step = step.replace(/\.{2,}/g, '.');
  step = step.replace(/___ELLIPSIS___/g, '...');

  step = step.replace(/\s*:\s*/g, ': ');

  // Punctuation spacing
  step = step.replace(/\.\.\./g, '___ELLIPSIS___');
  step = step.replace(/([,.!?])([^\s\n])/g, '$1 $2');
  step = step.replace(/([,.!?])[ \t]+/g, '$1 ');
  step = step.replace(/___ELLIPSIS___/g, '...');

  console.log(`\nAfter preprocessing: "${step}"`);

  // Content analysis
  const hasStructuredData = /^\s*(Order\s+ID|Status|Reference|Code|Number)\s*:\s*/im.test(step);
  const hasMultipleParagraphs = /\n\n/.test(step);
  const hasNarrativeWords =
    /\b(arrive|gas|thank|please|your|will|can|processing|order|new|contact|us)\b/i.test(step);
  const isSimpleLineList = /\n/.test(step) && !hasNarrativeWords;
  const isNarrativeContent = hasMultipleParagraphs && hasNarrativeWords;

  console.log(`Content analysis:`);
  console.log(`  hasStructuredData: ${hasStructuredData}`);
  console.log(`  hasMultipleParagraphs: ${hasMultipleParagraphs}`);
  console.log(`  hasNarrativeWords: ${hasNarrativeWords}`);
  console.log(`  isSimpleLineList: ${isSimpleLineList}`);
  console.log(`  isNarrativeContent: ${isNarrativeContent}`);

  if (hasStructuredData || isSimpleLineList) {
    console.log(`  -> Will preserve line structure`);
  } else if (isNarrativeContent) {
    console.log(`  -> Will flatten to narrative content`);
  } else {
    console.log(`  -> Will preserve simple line breaks`);
  }
}

// Test the failing cases
debugSanitization(
  'Order confirmed,,, delivery time: 30 minutes... Thank you!!!',
  'Remove redundant punctuation',
  'Order confirmed, delivery time: 30 minutes. Thank you!'
);

debugSanitization(
  '🔥  New Order  🔥\n\nYour gas will arrive in 1–2 hours…\n\nThank you! ❤️\n\nContact us: 📞 +252613656021',
  'Handle complex mixed content',
  'New Order. Your gas will arrive in 1-2 hours. Thank you! Contact us: +252613656021'
);
