import { AppMessageService } from './src/constants/app_message.service';

const messageService = new AppMessageService();

// Test the ellipses issue
const testInput = 'Processing your order… please wait…';
console.log('Input:', JSON.stringify(testInput));

// Access the private method
const result = (messageService as any).sanitizeMessageContent(testInput);
console.log('Output:', JSON.stringify(result));
console.log('Expected:', JSON.stringify('Processing your order... please wait...'));

// Test step by step
let step = testInput;
console.log('\nStep by step:');
console.log('1. Original:', JSON.stringify(step));

// Step 2: Replace ellipses
step = step.replace(/…/g, '...');
console.log('2. After ellipses:', JSON.stringify(step));

// Step 8: Protect ellipses
step = step.replace(/\.\.\./g, '___ELLIPSIS___');
console.log('3. After protect:', JSON.stringify(step));

// Remove redundant punctuation
step = step.replace(/([,!?])\1+/g, '$1');
step = step.replace(/\.{2,}/g, '.');
console.log('4. After redundant removal:', JSON.stringify(step));

// Restore ellipses
step = step.replace(/___ELLIPSIS___/g, '...');
console.log('5. After restore:', JSON.stringify(step));
