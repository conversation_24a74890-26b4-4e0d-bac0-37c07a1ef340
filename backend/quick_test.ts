import { AppMessageService } from './src/constants/app_message.service';

const messageService = new AppMessageService();

// Test case 1: Convert ellipses to three periods
const input1 = 'Processing your order… please wait…';
const expected1 = 'Processing your order... please wait...';

console.log('Testing ellipses conversion...');
console.log('Input:', input1);
console.log('Expected:', expected1);

const result1 = (messageService as any).sanitizeMessageContent(input1);
console.log('Actual:', result1);
console.log('Match:', result1 === expected1 ? '✅' : '❌');

console.log('\n---\n');

// Test case 2: Remove redundant punctuation
const input2 = 'Order confirmed,,, delivery time: 30 minutes... Thank you!!!';
const expected2 = 'Order confirmed, delivery time: 30 minutes. Thank you!';

console.log('Testing redundant punctuation...');
console.log('Input:', input2);
console.log('Expected:', expected2);

const result2 = (messageService as any).sanitizeMessageContent(input2);
console.log('Actual:', result2);
console.log('Match:', result2 === expected2 ? '✅' : '❌');
