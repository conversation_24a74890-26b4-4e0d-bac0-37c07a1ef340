# buildMessage(): The Core Message Factory

## Overview

The `buildMessage()` method is the **centralized message construction pipeline** that handles the complete message generation process with type safety, localization, branding control, sanitization, and SMS optimization.

## Purpose

This private method serves as the **single source of truth** for message construction, eliminating duplicate code across 15+ message methods while providing:

- **Localization** (Somali/English selection)
- **Branding** (prefix/signature/footer injection)
- **Sanitization** (content cleaning)
- **Truncation** (SMS length optimization)

## Method Signature

```typescript
private buildMessage(
  templates: {
    so: { subject: string; body: string };
    en: { subject: string; body: string }
  },
  options: MessageOptions
): string
```

## Architecture

### 1. Template-Driven Approach

Accepts language-specific templates as parameters, enabling 100% consistent message construction across all methods:

```typescript
const templates = {
  so: { subject: 'Dalabka la Xaqiijiyay', body: `Lambarka dalabka: ${orderId}` },
  en: { subject: 'Order Confirmed', body: `Order ID: ${orderId}` },
};
```

### 2. Branding Injection Points

#### App Name Prefix (Title Case)

```typescript
// With prefix
'Ciribey Gas - Order Confirmed';

// Without prefix
'Order Confirmed';
```

#### Company Signature (Localized)

```typescript
// Somali
'\n\nMahadsanid,\nCompany Ciribey Gas';

// English
'\n\nThank you,\nCiribey Gas Team';
```

#### App Footer (Dynamic Contact Info)

```typescript
'\n\nDownload App: https://...\nContact: +252...';
```

### 3. Optimized Processing Pipeline

```
Input Templates → Language Selection → Branding Injection → Content Assembly → Sanitization → Truncation → Output
```

## Implementation Details

### Step 1: Language Selection

```typescript
const t = templates[this.lang];
```

- Automatically selects Somali (`so`) or English (`en`) based on service configuration
- Defaults to Somali if language not supported

### Step 2: Branding Prefix Application

```typescript
const subject = options.includeAppNamePrefix
  ? `${this.localizedAppName} - ${t.subject}`
  : t.subject;
```

- Conditionally prepends app name based on `includeAppNamePrefix` option
- Uses localized app name (Somali/English)

### Step 3: Base Message Construction

```typescript
let message = `${subject}\n${t.body}`;
```

- Combines subject and body with newline separator
- Maintains consistent formatting across all messages

### Step 4: Optional Component Injection

```typescript
if (options.includeCompanySignature) {
  message += this.companySignature;
}

if (options.includeAppFooter) {
  message += this.appFooter;
}
```

- Appends company signature if requested
- Appends app footer (download link + contact) if requested
- Maintains proper spacing and formatting

### Step 5: Content Processing

```typescript
const sanitizedMessage = this.sanitizeMessageContent(message);
return this.truncateForSMS(sanitizedMessage);
```

- Sanitizes content (removes emojis, normalizes whitespace, etc.)
- Truncates to SMS character limit if necessary

## Usage in Message Methods

### Standard Pattern

All message methods now follow this consistent pattern:

```typescript
orderConfirmed(orderId: string, options?: MessageOptions) {
  const templates = {
    so: {
      subject: 'Dalabka la Xaqiijiyay',
      body: `Lambarka dalabka: ${orderId}\nWaqtiga Deliveryta: ${AppConstants.standardDeliveryTimeSomali}`
    },
    en: {
      subject: 'Order Confirmed',
      body: `Order ID: ${orderId}\nDelivery time: ${AppConstants.standardDeliveryTime}`
    }
  };

  return this.buildMessage(templates, {
    ...DEFAULT_ORDER_OPTIONS,
    ...options
  });
}
```

### Real Output Example (Somali)

```text
Ciribey Gas - Dalabka la Xaqiijiyay
Lambarka dalabka: ORD123
Waqtiga Deliveryta: 30 daqiiqo

Mahadsanid,
Ciribey Gas

Lasoo dag Ciribey Gas: https://...
Nagala soo xiriir: +252...
```

## Key Design Decisions

### 1. DRY Compliance

**Before (Duplicate Code):**

```typescript
// Each method had 20+ lines of duplicate construction logic
const t = templates[this.lang];
const subject = options.includeAppNamePrefix
  ? `${this.localizedAppName} - ${t.subject}`
  : t.subject;
let message = `${subject}\n${t.body}`;
if (options.includeCompanySignature) message += this.companySignature;
if (options.includeAppFooter) message += this.appFooter;
const sanitizedMessage = this.sanitizeMessageContent(message);
return this.truncateForSMS(sanitizedMessage);
```

**After (Centralized):**

```typescript
// Single line in each method
return this.buildMessage(templates, options);
```

### 2. Flexible Branding Control

Example customization:

```typescript
// Emergency alert with forced branding
buildMessage(
  { so: {...}, en: {...} },
  { includeAppNamePrefix: true, includeAppFooter: false }
)
```

### 3. Performance Optimized

- **Language resolution** happens once per template
- **Sanitization** only runs on final assembled message
- **Cached regex patterns** for efficient processing

### 4. Maintenance Benefits

- **Changing branding rules** requires only one method update
- **New message types** add just templates (no construction logic)
- **Consistent behavior** across all message types

## Edge Case Handling

### Long Messages

- Truncates at 497 chars (leaves room for `...`)
- Preserves most critical info (order IDs, amounts)
- Maintains readability

### Mixed Branding

```typescript
// Signature without footer
buildMessage(templates, {
  includeCompanySignature: true,
  includeAppFooter: false,
});
```

### Empty Content

- Returns empty string if sanitization removes all content
- Logs warning to monitoring system
- Graceful degradation

## Testing

### Unit Tests

```typescript
describe('buildMessage', () => {
  it('should construct basic message without branding', () => {
    const templates = {
      so: { subject: 'Test', body: 'Content' },
      en: { subject: 'Test', body: 'Content' },
    };

    const result = service.buildMessage(templates, {
      includeAppNamePrefix: false,
      includeCompanySignature: false,
      includeAppFooter: false,
    });

    expect(result).toBe('Test\nContent');
  });

  it('should apply branding when requested', () => {
    const result = service.buildMessage(templates, {
      includeAppNamePrefix: true,
      includeCompanySignature: true,
      includeAppFooter: true,
    });

    expect(result).toContain('Ciribey Gas - Test');
    expect(result).toContain('Thank you,');
    expect(result).toContain('Download Ciribey Gas:');
  });
});
```

### Integration Tests

```typescript
it('should handle multilingual templates correctly', () => {
  const somaliService = new AppMessageService({ lang: 'so' });
  const englishService = new AppMessageService({ lang: 'en' });

  const templates = {
    so: { subject: 'Somali', body: 'Content' },
    en: { subject: 'English', body: 'Content' },
  };

  const somaliResult = somaliService.buildMessage(templates, {});
  const englishResult = englishService.buildMessage(templates, {});

  expect(somaliResult).toContain('Somali');
  expect(englishResult).toContain('English');
});
```

## Benefits Achieved

### 1. Code Reduction

- **Eliminated 300+ lines** of duplicate construction code
- **Single source of truth** for message building logic
- **Consistent behavior** across all message types

### 2. Maintainability

- **Easy to modify** branding rules (one place)
- **Simple to add** new message types (just templates)
- **Type-safe** template structure

### 3. Performance

- **Optimized processing** pipeline
- **Cached patterns** for sanitization
- **Efficient language** resolution

### 4. Quality

- **Consistent output** format
- **Proper sanitization** and truncation
- **Edge case handling**

## Future Enhancements

### 1. Template Engine

```typescript
// Future: Support for custom templates
buildMessage(templateId, variables, options);
```

### 2. Dynamic Branding

```typescript
// Future: Branding based on user preferences
buildMessage(templates, options, userPreferences);
```

### 3. A/B Testing

```typescript
// Future: Support for message variants
buildMessage(templates, options, { variant: 'A' });
```

## Conclusion

The `buildMessage()` method successfully achieves **enterprise-grade message consistency** while allowing **per-message customization** when needed. It serves as the foundation for all messaging functionality, providing:

- ✅ **Type Safety** - Compile-time validation of templates and options
- ✅ **Performance** - Optimized processing pipeline
- ✅ **Maintainability** - Single source of truth for message construction
- ✅ **Flexibility** - Configurable branding per message type
- ✅ **Quality** - Consistent sanitization and truncation

This architecture makes the messaging service **production-ready** and **future-proof** for the gas delivery system's evolving needs.

---

**Implementation Date**: January 2024  
**Version**: 2.1.0  
**Status**: ✅ Complete and Production-Ready
