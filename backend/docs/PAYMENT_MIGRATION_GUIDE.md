# Payment Service Migration Guide

This guide provides step-by-step instructions for migrating to the enhanced payment service with raw gateway response storage.

## 📋 **Migration Overview**

### **What's Changed**
- ✅ **Payment Model**: Added `gatewayResponses` field
- ✅ **Payment Service**: Enhanced with response tracking
- ✅ **New API Endpoints**: Added analytics and debugging endpoints
- ✅ **Security**: Implemented data sanitization and masking
- ✅ **Backward Compatibility**: All existing functionality preserved

### **What's NOT Changed**
- ❌ **Existing API contracts**: All current endpoints work unchanged
- ❌ **Order lifecycle**: Integration remains the same
- ❌ **Authentication**: No changes to auth requirements
- ❌ **Payment flow**: Customer experience unchanged

## 🔄 **Database Migration**

### **Automatic Schema Updates**
The new `gatewayResponses` field will be automatically added to existing payment documents:

```javascript
// Existing payments will have:
{
  _id: ObjectId("..."),
  orderId: ObjectId("..."),
  userId: ObjectId("..."),
  amount: 50.00,
  status: "CAPTURED",
  // ... existing fields ...
  gatewayResponses: [] // New field, defaults to empty array
}
```

### **No Manual Migration Required**
- ✅ **Existing payments** continue to work normally
- ✅ **New payments** automatically get response tracking
- ✅ **Indexes** are automatically created for performance
- ✅ **No downtime** required for migration

### **Optional: Backfill Historical Data**
If you want to add placeholder entries for historical payments:

```javascript
// Optional migration script (run in MongoDB shell)
db.payments.updateMany(
  { gatewayResponses: { $exists: false } },
  { 
    $set: { 
      gatewayResponses: [] 
    } 
  }
);
```

## 🚀 **Deployment Steps**

### **1. Pre-Deployment Checklist**
- [ ] **Backup database** before deployment
- [ ] **Test in staging** environment first
- [ ] **Verify API endpoints** are accessible
- [ ] **Check role permissions** for new endpoints

### **2. Deployment Process**
```bash
# 1. Deploy the enhanced payment service
npm run build
npm run start

# 2. Verify deployment
curl -X GET http://localhost:3000/health

# 3. Test payment functionality
curl -X POST http://localhost:3000/api/v1/payments/{id}/preauthorize \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{"mobile": "************", "amount": 10.00}'
```

### **3. Post-Deployment Verification**
- [ ] **Create test payment** and verify response tracking
- [ ] **Check analytics endpoint** returns data
- [ ] **Verify existing payments** still work
- [ ] **Test webhook handling** if applicable

## 📊 **Testing the Enhanced Features**

### **1. Test Payment with Response Tracking**
```javascript
// Create a payment and verify gateway responses are stored
const payment = await Payment.findById(paymentId);
console.log('Gateway responses:', payment.gatewayResponses);

// Should show array with captured responses
[
  {
    provider: 'WAAFI',
    operation: 'PREAUTHORIZE',
    timestamp: Date,
    responseCode: '2001',
    processingTime: 1200,
    // ... other fields
  }
]
```

### **2. Test Analytics Endpoint**
```bash
# Get payment analytics
curl -X GET "http://localhost:3000/api/v1/payments/{paymentId}/analytics" \
  -H "Authorization: Bearer {admin-token}"

# Expected response structure
{
  "status": "success",
  "data": {
    "payment": { /* payment object */ },
    "analytics": {
      "totalGatewayInteractions": 1,
      "operationCounts": { "PREAUTHORIZE": 1 },
      "averageResponseTime": 1200,
      "successRate": 100,
      "errorCount": 0
    }
  }
}
```

### **3. Test Gateway Response Retrieval**
```bash
# Get raw gateway responses (admin only)
curl -X GET "http://localhost:3000/api/v1/payments/{paymentId}/gateway-responses" \
  -H "Authorization: Bearer {admin-token}"

# Filter by operation
curl -X GET "http://localhost:3000/api/v1/payments/{paymentId}/gateway-responses?operation=PREAUTHORIZE" \
  -H "Authorization: Bearer {admin-token}"
```

## 🔧 **Configuration Updates**

### **Environment Variables**
No new environment variables required. All existing payment configuration remains the same:

```env
# Existing payment configuration (unchanged)
PAYMENT_API_URL=https://api.waafi.com
PAYMENT_API_KEY=your-api-key
PAYMENT_API_USER_ID=your-user-id
PAYMENT_MERCHANT_UID=your-merchant-uid
```

### **Logging Configuration**
Enhanced logging is automatically enabled. You may want to adjust log levels:

```javascript
// In your logging configuration
{
  level: 'info', // Set to 'debug' for detailed gateway response logging
  // ... other config
}
```

## 🔒 **Security Considerations**

### **Data Sanitization**
The service automatically sanitizes sensitive data:

```javascript
// Original request (never stored)
{
  serviceParams: {
    apiKey: "full-api-key-here",
    merchantUid: "full-merchant-uid",
    payerInfo: {
      accountNo: "************"
    }
  }
}

// Stored sanitized version
{
  serviceParams: {
    apiKey: "abcd****efgh",
    merchantUid: "merc****",
    payerInfo: {
      accountNo: "252****021"
    }
  }
}
```

### **Access Control**
New endpoints require appropriate permissions:

- **Analytics endpoints**: Admin only
- **Gateway responses**: Admin only  
- **Webhook endpoints**: Public (no auth required)
- **Payment operations**: Existing role requirements unchanged

## 📈 **Monitoring & Alerting**

### **Key Metrics to Monitor**
```javascript
// Payment success rate
const successRate = (successfulPayments / totalPayments) * 100;

// Average response time
const avgResponseTime = totalResponseTime / totalRequests;

// Error rate
const errorRate = (failedPayments / totalPayments) * 100;
```

### **Recommended Alerts**
- **Success rate < 95%**: Payment gateway issues
- **Average response time > 5000ms**: Performance degradation
- **Error rate > 5%**: Integration problems
- **No gateway responses**: Service not capturing data

## 🐛 **Troubleshooting**

### **Common Issues**

#### **1. Gateway Responses Not Being Stored**
```javascript
// Check if payment service is properly initialized
console.log('Payment service instance:', paymentService);

// Verify payment exists before storing responses
const payment = await Payment.findById(paymentId);
console.log('Payment found:', !!payment);
```

#### **2. Analytics Endpoint Returns Empty Data**
```javascript
// Check if payment has gateway responses
const payment = await Payment.findById(paymentId);
console.log('Gateway responses count:', payment.gatewayResponses?.length || 0);
```

#### **3. Permission Denied on New Endpoints**
```javascript
// Verify user role
console.log('User role:', req.user?.role);

// Check if user has admin privileges
const isAdmin = req.user?.role === UserRole.ADMIN;
console.log('Is admin:', isAdmin);
```

## 🔄 **Rollback Plan**

If issues arise, you can safely rollback:

### **1. Code Rollback**
```bash
# Revert to previous version
git checkout previous-version-tag
npm run build
npm run start
```

### **2. Database Rollback**
```javascript
// Remove gatewayResponses field (optional)
db.payments.updateMany(
  {},
  { $unset: { gatewayResponses: "" } }
);
```

### **3. Verification**
- [ ] **Existing payments** work normally
- [ ] **New payments** can be created
- [ ] **Order lifecycle** functions correctly

## ✅ **Migration Checklist**

### **Pre-Migration**
- [ ] **Backup database**
- [ ] **Test in staging environment**
- [ ] **Review security implications**
- [ ] **Plan rollback strategy**

### **During Migration**
- [ ] **Deploy enhanced service**
- [ ] **Verify health endpoints**
- [ ] **Test basic payment flow**
- [ ] **Check new endpoints**

### **Post-Migration**
- [ ] **Monitor payment success rates**
- [ ] **Verify response tracking**
- [ ] **Test analytics endpoints**
- [ ] **Update documentation**
- [ ] **Train support team**

## 📞 **Support**

### **Getting Help**
- **Check logs** for detailed error information
- **Use analytics endpoints** to debug payment issues
- **Review gateway responses** for integration problems
- **Monitor system metrics** for performance issues

The enhanced payment service provides comprehensive debugging and monitoring capabilities while maintaining full backward compatibility with existing functionality.
