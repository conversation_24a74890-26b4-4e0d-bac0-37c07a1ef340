# Enhanced Messaging Service with Per-Method Branding Control

## Overview

The `AppMessageService` has been enhanced with **per-method branding control**, allowing precise control over branding elements in each message type while maintaining sensible defaults for optimal user experience.

## Key Features

- ✅ **Strict Type Safety** – Dedicated `MessageOptions` interface for consistent overrides
- ✅ **Sensible Defaults** – Critical messages (OTP, alerts) exclude branding by default; transactional messages (orders, payments) include it
- ✅ **Clean API** – Most calls require no extra params; override only when needed
- ✅ **Scalable** – Easy to add new message types with their own defaults
- ✅ **Multilingual Support** – Somali (primary) and English (secondary)
- ✅ **SMS Optimized** – Character limit awareness and content sanitization
- ✅ **Professional Branding** – Consistent company signature and footer options

## MessageOptions Interface

```typescript
/**
 * Controls branding elements in generated messages.
 * - Applied per-message with type-safe defaults.
 */
interface MessageOptions {
  /** Include app name prefix (e.g., "AppName - Verification Code")? */
  includeAppNamePrefix?: boolean;
  /** Include company signature (e.g., "Thank you, AppName Team")? */
  includeCompanySignature?: boolean;
  /** Include app download link and contact info? */
  includeAppFooter?: boolean;
}
```

## Message Types and Default Branding

### Critical Messages (No Branding by Default)

These messages prioritize clarity and SMS length optimization:

| Method              | Default Branding | Use Case               |
| ------------------- | ---------------- | ---------------------- |
| `otpMessage()`      | No branding      | OTP verification codes |
| `emergencyAlert()`  | App name only    | Urgent system alerts   |
| `lowStockAlert()`   | App name only    | Inventory alerts       |
| `outOfStockAlert()` | App name only    | Stock depletion alerts |

### Transactional Messages (Full Branding by Default)

These messages include professional branding for brand recognition:

| Method               | Default Branding     | Use Case               |
| -------------------- | -------------------- | ---------------------- |
| `orderConfirmed()`   | Full branding        | Order confirmations    |
| `orderDelivered()`   | App name + signature | Delivery confirmations |
| `orderCancelled()`   | App name + signature | Order cancellations    |
| `paymentConfirmed()` | App name only        | Payment confirmations  |
| `paymentFailed()`    | App name + signature | Payment failures       |
| `welcomeMessage()`   | Signature only       | User onboarding        |

### Operational Messages (Mixed Branding)

These messages use strategic branding based on context:

| Method                      | Default Branding     | Use Case               |
| --------------------------- | -------------------- | ---------------------- |
| `deliveryAssigned()`        | App name + signature | Agent assignments      |
| `deliveryStarted()`         | App name only        | Delivery notifications |
| `maintenanceNotification()` | App name + signature | System maintenance     |
| `genericNotification()`     | App name only        | General notifications  |

## Usage Examples

### Default Behavior (Recommended)

```typescript
import { AppMessageService } from '../constants/app_message.service';

const messageService = new AppMessageService({ lang: 'en' });

// OTP: No branding (clean, concise)
const otp = messageService.otpMessage('123456');
// Output: "Verification Code\nYour code is: 123456\nExpires in 5 minutes.\n\nDo not share this code."

// Order confirmation: Full branding
const order = messageService.orderConfirmed('ORDER_123');
// Output: "GasDelivery - Order Confirmed\nOrder ID: ORDER_123\nWe will contact you when ready.\n\nDelivery time: 2-4 hours\n\nThank you,\nGasDelivery Team\n\nDownload GasDelivery: https://app.link\nContact us: +1234567890"
```

### Override Defaults (When Needed)

```typescript
// OTP with forced branding (rare case)
const brandedOtp = messageService.otpMessage('123456', 5, {
  includeAppNamePrefix: true,
});
// Output: "GasDelivery - Verification Code\nYour code is: 123456\nExpires in 5 minutes.\n\nDo not share this code."

// Order confirmation without footer
const orderWithoutFooter = messageService.orderConfirmed('ORDER_123', {
  includeAppFooter: false,
});
// Output: "GasDelivery - Order Confirmed\nOrder ID: ORDER_123\nWe will contact you when ready.\n\nDelivery time: 2-4 hours\n\nThank you,\nGasDelivery Team"
```

### Advanced Usage

```typescript
// Complete branding control
const customMessage = messageService.orderConfirmed('ORDER_123', {
  includeAppNamePrefix: true, // "GasDelivery - Order Confirmed"
  includeCompanySignature: false, // No signature
  includeAppFooter: true, // Include download link + contact
});

// Minimal branding for internal use
const internalMessage = messageService.orderConfirmed('ORDER_123', {
  includeAppNamePrefix: false,
  includeCompanySignature: false,
  includeAppFooter: false,
});
```

## Method Reference

### OTP Messages

```typescript
otpMessage(
  otp: string,
  expiresIn?: number,
  options?: MessageOptions
): string
```

**Default Options:**

- `includeAppNamePrefix: false`
- `includeCompanySignature: false`
- `includeAppFooter: false`

### Order Messages

```typescript
orderConfirmed(
  orderId: string,
  options?: MessageOptions
): string

orderDelivered(
  orderId: string,
  options?: MessageOptions
): string

orderCancelled(
  orderId: string,
  reason?: string,
  options?: MessageOptions
): string
```

**Default Options:**

- `includeAppNamePrefix: true`
- `includeCompanySignature: true`
- `includeAppFooter: true` (orderConfirmed only)

### Delivery Messages

```typescript
deliveryAssigned(
  orderId: string,
  customerName: string,
  address: string,
  options?: MessageOptions
): string

deliveryStarted(
  orderId: string,
  agentName: string,
  agentPhone: string,
  options?: MessageOptions
): string
```

### Alert Messages

```typescript
lowStockAlert(
  item: string,
  quantity: number,
  options?: MessageOptions
): string

outOfStockAlert(
  item: string,
  options?: MessageOptions
): string

emergencyAlert(
  message: string,
  options?: MessageOptions
): string
```

### Payment Messages

```typescript
paymentConfirmed(
  orderId: string,
  amount: number,
  options?: MessageOptions
): string

paymentFailed(
  orderId: string,
  reason: string,
  options?: MessageOptions
): string
```

### Utility Messages

```typescript
welcomeMessage(
  userName: string,
  userRole: string,
  options?: MessageOptions
): string

genericNotification(
  title: string,
  message: string,
  options?: MessageOptions
): string

maintenanceNotification(
  startTime: string,
  duration: string,
  options?: MessageOptions
): string
```

## Best Practices

### 1. Use Defaults When Possible

```typescript
// ✅ Good - Use sensible defaults
const otp = messageService.otpMessage('123456');
const order = messageService.orderConfirmed('ORDER_123');

// ❌ Avoid - Only override when necessary
const otp = messageService.otpMessage('123456', 5, {
  includeAppNamePrefix: false,
  includeCompanySignature: false,
  includeAppFooter: false,
});
```

### 2. Override for Specific Use Cases

```typescript
// ✅ Good - Override for specific needs
const internalOrder = messageService.orderConfirmed('ORDER_123', {
  includeAppFooter: false, // Remove footer for internal use
});

const brandedOtp = messageService.otpMessage('123456', 5, {
  includeAppNamePrefix: true, // Force branding for marketing
});
```

### 3. Consider SMS Length

```typescript
// ✅ Good - Check message length for SMS planning
const message = messageService.orderConfirmed('ORDER_123');
const length = messageService.getMessageLength(message);
const exceedsLimit = messageService.exceedsSMSLimit(message);

if (exceedsLimit) {
  // Consider removing optional branding elements
  const shorterMessage = messageService.orderConfirmed('ORDER_123', {
    includeAppFooter: false,
  });
}
```

### 4. Language Considerations

```typescript
// ✅ Good - Use appropriate language
const somaliService = new AppMessageService({ lang: 'so' });
const englishService = new AppMessageService({ lang: 'en' });

const somaliOtp = somaliService.otpMessage('123456');
const englishOtp = englishService.otpMessage('123456');
```

## Migration Guide

### From Previous Version

If you're upgrading from the previous version:

1. **No Breaking Changes** - All existing method calls will continue to work
2. **New Optional Parameters** - Add `MessageOptions` when you need branding control
3. **Enhanced Defaults** - Messages now have more intelligent default branding

```typescript
// Before (still works)
const otp = messageService.otpMessage('123456');

// After (enhanced with options)
const otp = messageService.otpMessage('123456', 5, {
  includeAppNamePrefix: false, // Explicit control
});
```

## Testing

### Unit Tests

```typescript
import { AppMessageService } from '../constants/app_message.service';

describe('AppMessageService', () => {
  let service: AppMessageService;

  beforeEach(() => {
    service = new AppMessageService({ lang: 'en' });
  });

  it('should generate OTP without branding by default', () => {
    const otp = service.otpMessage('123456');
    expect(otp).not.toContain('GasDelivery -');
    expect(otp).not.toContain('Thank you,');
  });

  it('should generate order confirmation with full branding by default', () => {
    const order = service.orderConfirmed('ORDER_123');
    expect(order).toContain('GasDelivery - Order Confirmed');
    expect(order).toContain('Thank you,');
    expect(order).toContain('Download GasDelivery:');
  });

  it('should allow branding overrides', () => {
    const otp = service.otpMessage('123456', 5, {
      includeAppNamePrefix: true,
    });
    expect(otp).toContain('GasDelivery - Verification Code');
  });
});
```

### Integration Tests

```typescript
it('should handle multilingual messaging', () => {
  const somaliService = new AppMessageService({ lang: 'so' });
  const englishService = new AppMessageService({ lang: 'en' });

  const somaliOtp = somaliService.otpMessage('123456');
  const englishOtp = englishService.otpMessage('123456');

  expect(somaliOtp).toContain('Koodka Xaqiijinta');
  expect(englishOtp).toContain('Verification Code');
});
```

## Performance Considerations

- **Cached Regex Patterns** - Service uses cached patterns for efficient sanitization
- **Lazy Evaluation** - Branding elements are only added when requested
- **Memory Efficient** - No unnecessary string concatenations
- **Type Safety** - Compile-time validation of options

## Future Enhancements

- [ ] **Template Engine** - Support for custom message templates
- [ ] **Dynamic Branding** - Branding based on user preferences
- [ ] **A/B Testing** - Support for message variant testing
- [ ] **Analytics Integration** - Message performance tracking
- [ ] **Rich Media Support** - Enhanced email templates with images

## Support

For questions or issues with the enhanced messaging service:

1. Check the [usage examples](../examples/enhanced-messaging-usage.ts)
2. Review the [API documentation](./API_REFERENCE.md)
3. Open an issue in the project repository

---

**Version:** 2.0.0  
**Last Updated:** January 2024  
**Compatibility:** TypeScript 4.5+, Node.js 16+
