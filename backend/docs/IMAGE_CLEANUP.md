# 🧹 Image Cleanup System

## Overview

The Image Cleanup System automatically manages image files when inventory items (cylinders, spare parts, packages) are permanently deleted from the database. This ensures that orphaned image files don't accumulate in the uploads directory, keeping storage clean and organized.

## Key Features

- ✅ **Automatic Cleanup**: Images are deleted when entities are hard deleted
- ✅ **Safe Operations**: Only deletes files within the uploads directory
- ✅ **Soft Delete Protection**: Images are preserved during soft deletes
- ✅ **Directory Cleanup**: Removes empty directories after file deletion
- ✅ **Error Handling**: Graceful handling of missing files and permission errors
- ✅ **Security**: Path validation prevents directory traversal attacks
- ✅ **Logging**: Comprehensive logging for audit trails

## How It Works

### Soft Delete vs Hard Delete

**Soft Delete (Images Preserved):**
- Entity is marked as `isDeleted: true` in database
- Image files remain in uploads directory
- Entity can be restored later with images intact

**Hard Delete (Images Cleaned Up):**
- Entity is permanently removed from database
- Associated image files are automatically deleted
- Empty directories are cleaned up
- Operation is irreversible

### File Cleanup Process

1. **Database Transaction**: Entity is deleted from MongoDB
2. **Image Path Extraction**: Get `imagePath` from deleted entity
3. **Security Validation**: Ensure path is within uploads directory
4. **File Deletion**: Remove image file from filesystem
5. **Directory Cleanup**: Remove empty parent directories
6. **Logging**: Record cleanup operation results

## Implementation Details

### Services Updated

**Cylinder Service:**
```typescript
// backend/src/services/cylinder.services.ts
async permanentlyDeleteCylinder(cylinderId: string): Promise<ICylinder> {
  // ... database deletion logic ...
  
  // Clean up image file after successful database deletion
  if (cylinder.imagePath) {
    const deleted = await FileCleanupService.deleteImageFile(cylinder.imagePath);
    if (deleted) {
      await FileCleanupService.cleanupEmptyDirectories(cylinder.imagePath);
    }
  }
  
  return cylinder;
}
```

**Spare Part Service:**
```typescript
// backend/src/services/spare_part.services.ts
async permanentlyDeleteSparePart(id: string | Types.ObjectId): Promise<ISparePart> {
  // ... database deletion logic ...
  
  // Clean up image file after successful database deletion
  if (sparePart.imagePath) {
    const deleted = await FileCleanupService.deleteImageFile(sparePart.imagePath);
    if (deleted) {
      await FileCleanupService.cleanupEmptyDirectories(sparePart.imagePath);
    }
  }
  
  return sparePart;
}
```

**Package Service:**
```typescript
// backend/src/services/package.services.ts
async permanentlyDeletePackage(id: string | Types.ObjectId): Promise<IPackage> {
  // ... database deletion logic ...
  
  // Clean up image file after successful database deletion
  if (packageDoc.imagePath) {
    const deleted = await FileCleanupService.deleteImageFile(packageDoc.imagePath);
    if (deleted) {
      await FileCleanupService.cleanupEmptyDirectories(packageDoc.imagePath);
    }
  }
  
  return packageDoc;
}
```

### FileCleanupService API

**Core Methods:**

```typescript
// Delete a single image file
static async deleteImageFile(imagePath: string): Promise<boolean>

// Delete multiple image files
static async deleteMultipleImageFiles(imagePaths: string[]): Promise<{deleted: number, failed: number}>

// Clean up empty directories
static async cleanupEmptyDirectories(imagePath: string): Promise<void>

// Validate image path for security
static isValidImagePath(filePath: string): boolean

// Get file size for logging
static async getFileSize(imagePath: string): Promise<number>
```

## Security Features

### Path Validation
- Only files within `uploads/` directory can be deleted
- Path normalization prevents directory traversal attacks
- File extension validation ensures only image files are processed

### Error Handling
- File cleanup failures don't break main operations
- Missing files are handled gracefully
- Permission errors are logged but don't throw exceptions

## Usage Examples

### Manual Cleanup (if needed)
```typescript
import { FileCleanupService } from '../utils/file_cleanup';

// Delete a single image
const deleted = await FileCleanupService.deleteImageFile('uploads/images/cylinders/2025/07/image.jpg');

// Delete multiple images
const result = await FileCleanupService.deleteMultipleImageFiles([
  'uploads/images/cylinders/2025/07/image1.jpg',
  'uploads/images/spare-parts/2025/07/image2.jpg'
]);
console.log(`Deleted: ${result.deleted}, Failed: ${result.failed}`);
```

### Testing
```bash
# Run the image cleanup test
npx ts-node src/test/image_cleanup_test.ts
```

## Monitoring and Logging

All cleanup operations are logged with the following information:
- Entity ID and type
- Image path being deleted
- Success/failure status
- File size (for audit purposes)
- Directory cleanup results

**Example Log Output:**
```
2025-07-27 06:38:20 [info]: Cleaning up cylinder image file
{
  "cylinderId": "60f7b3b3b3b3b3b3b3b3b3b3",
  "imagePath": "uploads/images/cylinders/2025/07/1753605856803-b74sk1gmhhh.jpg"
}

2025-07-27 06:38:20 [info]: Image file deleted successfully
{
  "imagePath": "uploads/images/cylinders/2025/07/1753605856803-b74sk1gmhhh.jpg",
  "absolutePath": "/app/uploads/images/cylinders/2025/07/1753605856803-b74sk1gmhhh.jpg"
}
```

## Best Practices

1. **Always use hard delete endpoints for permanent removal**
2. **Monitor logs for cleanup failures**
3. **Regular backup of uploads directory before bulk deletions**
4. **Test cleanup functionality in staging environment**
5. **Consider implementing cleanup job for orphaned files**

## API Endpoints

**Hard Delete Endpoints:**
- `DELETE /api/v1/cylinders/:id/permanent` - Permanently delete cylinder + image
- `DELETE /api/v1/spare-parts/:id/permanent` - Permanently delete spare part + image  
- `DELETE /api/v1/packages/:id/permanent` - Permanently delete package + image

**Soft Delete Endpoints (Images Preserved):**
- `DELETE /api/v1/cylinders/:id` - Soft delete cylinder (image preserved)
- `DELETE /api/v1/spare-parts/:id` - Soft delete spare part (image preserved)
- `DELETE /api/v1/packages/:id` - Soft delete package (image preserved)

## Future Enhancements

- **Bulk Cleanup Job**: Scheduled job to clean up orphaned files
- **Storage Analytics**: Track storage usage and cleanup statistics
- **Image Versioning**: Support for multiple image versions per entity
- **Cloud Storage**: Extend cleanup to cloud storage providers (AWS S3, etc.)
