# Enhanced Payment Service Documentation

This document provides comprehensive documentation for the enhanced payment service that includes raw gateway response storage for audit trails and debugging purposes.

## 🔄 **Overview**

The enhanced payment service now captures and stores complete raw payment gateway responses for:
- **Audit trails** and compliance requirements
- **Debugging** payment issues and integration problems
- **Analytics** and performance monitoring
- **Historical transaction analysis**

## 🏗️ **Architecture Enhancements**

### **1. Payment Model Updates**

#### **New Field: `gatewayResponses`**
```typescript
gatewayResponses?: IGatewayRawResponse[]; // Array to store all gateway interactions
```

#### **Gateway Response Interface**
```typescript
interface IGatewayRawResponse {
  provider: 'WAAFI' | 'ZAAD' | 'EDAHAB' | 'OTHER';
  operation: 'PREAUTHORIZE' | 'CAPTURE' | 'CANCEL' | 'REFUND' | 'WEBHOOK' | 'ERROR';
  timestamp: Date;
  requestId?: string;
  responseCode?: string;
  responseMessage?: string;
  rawRequest?: Record<string, any>; // Sanitized request payload
  rawResponse?: Record<string, any>; // Complete gateway response
  httpStatus?: number;
  processingTime?: number; // Response time in milliseconds
  errorDetails?: {
    errorCode?: string;
    errorMessage?: string;
    stackTrace?: string;
  };
  metadata?: Record<string, any>; // Additional provider-specific data
}
```

### **2. Security & Data Sanitization**

#### **Sensitive Data Masking**
- **API Keys**: Masked to show only first 4 and last 4 characters
- **Merchant UIDs**: Masked to show only first 4 characters
- **Account Numbers**: Masked to show only first 3 and last 3 digits
- **Full Card Numbers**: Never stored (if applicable)
- **CVV Codes**: Never stored (if applicable)

#### **Example Sanitized Request**
```json
{
  "serviceParams": {
    "apiKey": "abcd****efgh",
    "merchantUid": "merc****",
    "payerInfo": {
      "accountNo": "252****021"
    }
  }
}
```

### **3. Enhanced Service Methods**

#### **Core Payment Operations**
All existing payment methods now capture gateway responses:
- `initiatePreauthorization()` - Stores PREAUTHORIZE responses
- `cancelPreauthorization()` - Stores CANCEL responses  
- `capturePreauthorizedPayment()` - Stores CAPTURE responses

#### **New Analytics Methods**
- `getGatewayResponses()` - Retrieve filtered gateway responses
- `getPaymentAnalytics()` - Get comprehensive payment analytics
- `handleWebhookResponse()` - Process and store webhook data

## 📊 **Analytics & Monitoring**

### **Payment Analytics Structure**
```typescript
{
  payment: IPayment;
  analytics: {
    totalGatewayInteractions: number;
    operationCounts: Record<string, number>;
    averageResponseTime: number;
    successRate: number;
    lastInteraction?: Date;
    errorCount: number;
  };
}
```

### **Key Metrics Tracked**
- **Response Times**: Measured for all gateway calls
- **Success Rates**: Calculated based on response codes
- **Operation Counts**: Breakdown by operation type
- **Error Tracking**: Detailed error logging and counting
- **Provider Performance**: Analytics by payment provider

## 🔌 **API Endpoints**

### **Payment Operations**
```
POST /api/v1/payments/:id/preauthorize  # Initiate preauth (Customer, Admin)
POST /api/v1/payments/:id/cancel        # Cancel preauth (Customer, Admin, Agent)  
POST /api/v1/payments/:id/capture       # Capture payment (Admin, Agent)
```

### **Analytics & Debugging**
```
GET /api/v1/payments/:id/gateway-responses  # Get raw responses (Admin only)
GET /api/v1/payments/:id/analytics          # Get payment analytics (Admin only)
GET /api/v1/payments/system/analytics       # System-wide analytics (Admin only)
```

### **Webhook Handling**
```
POST /api/v1/payments/:id/webhook  # Handle gateway webhooks (Public)
```

## 🛠️ **Usage Examples**

### **1. Initiate Payment with Response Tracking**
```javascript
// Request
POST /api/v1/payments/60f7b3b4e1234567890abcde/preauthorize
{
  "mobile": "252613656021",
  "amount": 50.00,
  "deliveryDetails": {
    "estimatedDeliveryTime": "30 minutes",
    "deliveryAddress": "123 Main Street, Hargeisa"
  }
}

// Response includes payment data + gateway response is automatically stored
{
  "status": "success",
  "data": {
    "payment": { /* payment object */ },
    "cashierUrl": "https://gateway.waafi.com/...",
    "preauthCode": "AUTH123456"
  }
}
```

### **2. Get Payment Analytics**
```javascript
// Request
GET /api/v1/payments/60f7b3b4e1234567890abcde/analytics

// Response
{
  "status": "success",
  "data": {
    "payment": { /* payment object */ },
    "analytics": {
      "totalGatewayInteractions": 3,
      "operationCounts": {
        "PREAUTHORIZE": 1,
        "CAPTURE": 1,
        "WEBHOOK": 1
      },
      "averageResponseTime": 1250,
      "successRate": 100,
      "lastInteraction": "2023-07-20T15:30:00.000Z",
      "errorCount": 0
    }
  }
}
```

### **3. Debug Gateway Responses**
```javascript
// Request
GET /api/v1/payments/60f7b3b4e1234567890abcde/gateway-responses?operation=PREAUTHORIZE

// Response
{
  "status": "success",
  "data": [
    {
      "provider": "WAAFI",
      "operation": "PREAUTHORIZE",
      "timestamp": "2023-07-20T15:25:00.000Z",
      "requestId": "1690728300000-abc123def",
      "responseCode": "2001",
      "responseMessage": "Success",
      "rawRequest": { /* sanitized request */ },
      "rawResponse": { /* complete response */ },
      "httpStatus": 200,
      "processingTime": 1200,
      "metadata": {
        "apiVersion": "1.0",
        "channelName": "WEB",
        "serviceName": "API_PREAUTHORIZE"
      }
    }
  ],
  "meta": {
    "paymentId": "60f7b3b4e1234567890abcde",
    "totalResponses": 1,
    "filters": { "operation": "PREAUTHORIZE" }
  }
}
```

## 🔍 **Debugging & Troubleshooting**

### **Common Use Cases**

#### **1. Payment Failure Analysis**
```javascript
// Get all gateway responses for failed payment
GET /api/v1/payments/{paymentId}/gateway-responses

// Look for:
// - Error responses with responseCode !== "2001"
// - High processing times indicating timeout issues
// - HTTP status codes indicating network problems
// - Error details with specific error codes
```

#### **2. Performance Monitoring**
```javascript
// Get payment analytics to identify performance issues
GET /api/v1/payments/{paymentId}/analytics

// Monitor:
// - averageResponseTime for slow responses
// - successRate for reliability issues
// - errorCount for failure patterns
```

#### **3. Integration Debugging**
```javascript
// Filter responses by operation type
GET /api/v1/payments/{paymentId}/gateway-responses?operation=CAPTURE

// Check:
// - Request payload formatting
// - Response structure changes
// - API version compatibility
```

## 🔒 **Security Considerations**

### **Data Protection**
- ✅ **Sensitive data masking** in stored requests
- ✅ **No storage of CVV** or full card numbers
- ✅ **API key protection** with partial masking
- ✅ **Account number masking** for privacy

### **Access Control**
- ✅ **Admin-only access** to raw gateway responses
- ✅ **Role-based permissions** for different operations
- ✅ **Audit logging** for all access attempts

### **Compliance**
- ✅ **PCI DSS compliance** through data sanitization
- ✅ **Audit trail requirements** met through complete logging
- ✅ **Data retention policies** can be implemented

## 🚀 **Benefits**

### **For Developers**
- **Complete visibility** into payment gateway interactions
- **Detailed error information** for faster debugging
- **Performance metrics** for optimization
- **Historical data** for trend analysis

### **For Operations**
- **Audit trails** for compliance and security
- **Issue resolution** with complete transaction history
- **Performance monitoring** and alerting capabilities
- **Integration health** monitoring

### **For Business**
- **Payment success rate** tracking and improvement
- **Customer experience** optimization through faster issue resolution
- **Compliance reporting** with detailed audit trails
- **Cost optimization** through performance analysis

## 🔄 **Integration with Order Lifecycle**

The enhanced payment service seamlessly integrates with the existing order lifecycle:

1. **Order Creation** → Payment preauthorization with response tracking
2. **Order Cancellation** → Payment cancellation with response tracking  
3. **Order Completion** → Payment capture with response tracking

All payment operations maintain **backward compatibility** while adding comprehensive audit capabilities.

This enhancement provides a robust foundation for payment processing with enterprise-grade monitoring, debugging, and compliance capabilities.
