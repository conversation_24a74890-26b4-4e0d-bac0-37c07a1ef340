# Railway Deployment Documentation

## 🚀 Overview
This document provides comprehensive guidance for deploying and managing the Gas Booking System backend on Railway.

## 📋 Prerequisites
- Railway account ([railway.app](https://railway.app))
- GitHub repository connected
- Node.js backend application
- MongoDB database (external)

## 🔧 Initial Setup

### 1. Install Railway CLI
```bash
# Using npm (recommended)
npm install -g @railway/cli

# Using curl (macOS/Linux)
curl -fsSL https://railway.app/install.sh | sh

# Using Homebrew (macOS)
brew install railway
```

### 2. Login to Railway
```bash
railway login
```
This opens your browser for authentication.

### 3. Link Project
```bash
railway link
```
Select your project from the list.

## 🚀 Deployment Commands

### Basic Deployment
```bash
# Deploy current branch
railway up

# Deploy specific branch
railway up --branch main

# Deploy with environment
railway up --environment production
```

### Service Management
```bash
# List all services
railway services

# Create new service
railway service create

# Delete service
railway service delete [SERVICE_NAME]
```

## 📊 Monitoring & Logs

### View Logs
```bash
# Recent logs
railway logs

# Real-time logs (follow)
railway logs --tail

# Specific number of lines
railway logs --lines 100

# Logs for specific service
railway logs --service [SERVICE_NAME]
```

### Service Status
```bash
# Check service status
railway status

# View deployments
railway deployments

# View specific deployment
railway deployment [DEPLOYMENT_ID]
```

## ⚙️ Configuration

### Environment Variables
```bash
# List environment variables
railway variables

# Set environment variable
railway variables set KEY=value

# Delete environment variable
railway variables delete KEY

# Load from .env file
railway variables set --from-file .env
```

### Service Configuration
```bash
# View service configuration
railway service

# Update service settings
railway service update
```

## 🔧 Troubleshooting

### Common Issues & Solutions

#### 1. Deployment Caching Issues
**Problem**: Old code still running despite new deployments
**Solution**: 
```bash
# Delete and recreate service
railway service delete [SERVICE_NAME]
railway service create
# Reconnect to GitHub and redeploy
```

#### 2. Build Failures
**Problem**: npm command not found or build errors
**Solution**:
- Ensure `package.json` is in the correct directory
- Check Railway service root directory setting
- Verify build commands in Railway dashboard

#### 3. Environment Variables Not Loading
**Problem**: App can't access environment variables
**Solution**:
```bash
# Verify variables are set
railway variables

# Re-set critical variables
railway variables set NODE_ENV=production
railway variables set DATABASE_URL=your_mongodb_url
```

## 📁 Project Structure for Railway

### Recommended Structure
```
gas_system_project/
├── backend/                 # Root directory for Railway
│   ├── src/
│   ├── package.json        # Main package.json
│   ├── tsconfig.json
│   └── dist/               # Compiled output
├── frontend/
└── README.md
```

### Railway Service Settings
- **Root Directory**: `/backend`
- **Build Command**: `npm ci && npm run build`
- **Start Command**: `npm run start`
- **Watch Paths**: `src/**, package.json`

## 🌍 Environment Configuration

### Required Environment Variables
```bash
NODE_ENV=production
DATABASE_URL=mongodb+srv://...
JWT_SECRET=your_jwt_secret
CORS_ORIGIN=your_frontend_url
PORT=3010
```

### Setting Up Environment Variables
1. Go to Railway Dashboard
2. Select your service
3. Navigate to "Variables" tab
4. Add each variable manually or upload .env file

## 🔄 Deployment Workflow

### 1. Development to Production
```bash
# 1. Commit changes
git add .
git commit -m "feat: your changes"
git push origin main

# 2. Railway auto-deploys from GitHub
# Monitor deployment in Railway dashboard

# 3. Verify deployment
railway logs --tail
```

### 2. Manual Deployment
```bash
# Deploy current directory
railway up

# Deploy with specific environment
railway up --environment production
```

## 📈 Best Practices

### 1. Service Management
- Use descriptive service names
- Set up proper environment separation (dev/staging/prod)
- Monitor resource usage regularly

### 2. Deployment Strategy
- Always test locally before deploying
- Use Railway's preview deployments for testing
- Monitor logs during deployment

### 3. Troubleshooting
- Check Railway dashboard for build/deploy logs
- Use `railway logs --tail` for real-time monitoring
- Delete and recreate service if caching issues persist

## 🆘 Emergency Procedures

### Service Not Responding
1. Check Railway dashboard for service status
2. View recent deployment logs
3. Restart service if needed:
   ```bash
   railway service restart
   ```

### Complete Service Reset
1. Backup environment variables
2. Delete service: `railway service delete [NAME]`
3. Create new service: `railway service create`
4. Reconnect to GitHub repository
5. Restore environment variables
6. Redeploy

## 📞 Support Resources

- **Railway Documentation**: [docs.railway.app](https://docs.railway.app)
- **Railway Discord**: [discord.gg/railway](https://discord.gg/railway)
- **Railway Status**: [status.railway.app](https://status.railway.app)

## 📝 Notes

- Railway automatically detects Node.js projects
- Free tier includes 500 hours of usage per month
- Deployments are triggered automatically on GitHub pushes
- Custom domains available on paid plans

---

**Last Updated**: August 2025
**Project**: Gas Booking System Backend
**Platform**: Railway.app
