{"info": {"name": "Gas Delivery System - Inventory Management API", "description": "Comprehensive API collection for the gas delivery system inventory management. Includes automated order-driven inventory operations and administrative management endpoints.", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api/v1", "type": "string"}, {"key": "authToken", "value": "your-jwt-token-here", "type": "string"}, {"key": "cylinderId", "value": "60f7b3b4e1234567890abcde", "type": "string"}, {"key": "packageId", "value": "60f7b3b4e1234567890abcdf", "type": "string"}, {"key": "sparePartId", "value": "60f7b3b4e1234567890abce0", "type": "string"}, {"key": "orderId", "value": "60f7b3b4e1234567890abce1", "type": "string"}, {"key": "customerId", "value": "60f7b3b4e1234567890abce2", "type": "string"}], "item": [{"name": "🔧 Spare Parts Management", "description": "Administrative operations for spare parts inventory management", "item": [{"name": "CRUD Operations", "item": [{"name": "Create Spare Part", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Regulator Valve\",\n  \"description\": \"High-pressure gas regulator valve\",\n  \"price\": 25.99,\n  \"cost\": 15.50,\n  \"category\": \"REGULATOR\",\n  \"compatibleCylinderTypes\": [\"SixKg\", \"ThirteenKg\"],\n  \"barcode\": \"SP001234\",\n  \"minimumStockLevel\": 10,\n  \"imageUrl\": \"https://example.com/regulator-valve.jpg\"\n}"}, "url": {"raw": "{{baseUrl}}/spare-parts", "host": ["{{baseUrl}}"], "path": ["spare-parts"]}, "description": "Create a new spare part. **Access**: Admin only"}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Regulator Valve\",\n  \"price\": 25.99,\n  \"cost\": 15.50,\n  \"category\": \"REGULATOR\"\n}"}, "url": {"raw": "{{baseUrl}}/spare-parts", "host": ["{{baseUrl}}"], "path": ["spare-parts"]}}, "status": "Created", "code": 201, "body": "{\n  \"status\": \"success\",\n  \"message\": \"Spare part created successfully\",\n  \"data\": {\n    \"_id\": \"60f7b3b4e1234567890abce0\",\n    \"name\": \"Regulator Valve\",\n    \"price\": 25.99,\n    \"cost\": 15.50,\n    \"category\": \"REGULATOR\",\n    \"quantity\": 0,\n    \"reserved\": 0,\n    \"sold\": 0,\n    \"isActive\": true,\n    \"createdAt\": \"2023-07-20T10:30:00.000Z\",\n    \"updatedAt\": \"2023-07-20T10:30:00.000Z\"\n  }\n}"}]}, {"name": "Get All Spare Parts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/spare-parts?page=1&limit=10&category=REGULATOR&search=valve", "host": ["{{baseUrl}}"], "path": ["spare-parts"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Items per page (default: 10, max: 50)"}, {"key": "category", "value": "REGULATOR", "description": "Filter by category"}, {"key": "search", "value": "valve", "description": "Text search in name/description"}, {"key": "status", "value": "ACTIVE", "description": "Filter by status", "disabled": true}, {"key": "compatibleWith", "value": "SixKg", "description": "Filter by compatible cylinder type", "disabled": true}, {"key": "lowStock", "value": "true", "description": "Show only low stock items", "disabled": true}]}, "description": "Get all spare parts with filtering and pagination. **Access**: All authenticated users"}}, {"name": "Get Spare Part by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/spare-parts/{{sparePartId}}", "host": ["{{baseUrl}}"], "path": ["spare-parts", "{{sparePartId}}"]}, "description": "Get spare part details by ID. **Access**: All authenticated users"}}, {"name": "Update Spare Part", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"price\": 27.99,\n  \"minimumStockLevel\": 15,\n  \"description\": \"Updated high-pressure gas regulator valve with improved design\"\n}"}, "url": {"raw": "{{baseUrl}}/spare-parts/{{sparePartId}}", "host": ["{{baseUrl}}"], "path": ["spare-parts", "{{sparePartId}}"]}, "description": "Update spare part details. **Access**: Admin only"}}, {"name": "Search Spare Parts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/spare-parts/search?q=regulator&limit=10", "host": ["{{baseUrl}}"], "path": ["spare-parts", "search"], "query": [{"key": "q", "value": "regulator", "description": "Search query (required)"}, {"key": "limit", "value": "10", "description": "Maximum results (default: 10, max: 50)"}]}, "description": "Search spare parts by text query. **Access**: All authenticated users"}}]}, {"name": "Administrative Operations", "item": [{"name": "Restock Spare Part", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quantity\": 50\n}"}, "url": {"raw": "{{baseUrl}}/spare-parts/{{sparePartId}}/restock", "host": ["{{baseUrl}}"], "path": ["spare-parts", "{{sparePartId}}", "restock"]}, "description": "Add new stock to existing spare part inventory. **Access**: Admin only"}}, {"name": "Discontinue Spare Part", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/spare-parts/{{sparePartId}}/discontinue", "host": ["{{baseUrl}}"], "path": ["spare-parts", "{{sparePartId}}", "discontinue"]}, "description": "Mark spare part as discontinued. **Access**: Admin only"}}]}, {"name": "Analytics & Reports", "item": [{"name": "Get Low Stock Spare Parts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/spare-parts/low-stock", "host": ["{{baseUrl}}"], "path": ["spare-parts", "low-stock"]}, "description": "Get spare parts with low stock levels. **Access**: <PERSON><PERSON>, Agent"}}, {"name": "Get Spare Parts Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/spare-parts/statistics", "host": ["{{baseUrl}}"], "path": ["spare-parts", "statistics"]}, "description": "Get spare parts sales and inventory statistics. **Access**: Admin only"}}]}]}, {"name": "📦 Package Management", "description": "Administrative operations for package inventory management", "item": [{"name": "CRUD Operations", "item": [{"name": "Create Package", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Standard Gas Kit\",\n  \"description\": \"Complete gas cylinder package with accessories\",\n  \"cylinder\": \"{{cylinderId}}\",\n  \"includedSpareParts\": [\n    {\n      \"part\": \"{{sparePartId}}\",\n      \"quantity\": 1\n    }\n  ],\n  \"discount\": 10,\n  \"imageUrl\": \"https://example.com/gas-kit.jpg\",\n  \"quantity\": 25,\n  \"minimumStockLevel\": 5\n}"}, "url": {"raw": "{{baseUrl}}/packages", "host": ["{{baseUrl}}"], "path": ["packages"]}, "description": "Create a new package. **Access**: Admin only"}}, {"name": "Get All Packages", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/packages?page=1&limit=10&populate=true&isActive=true", "host": ["{{baseUrl}}"], "path": ["packages"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Items per page (default: 10, max: 50)"}, {"key": "populate", "value": "true", "description": "Include related data (default: true)"}, {"key": "search", "value": "gas kit", "description": "Text search in name/description", "disabled": true}, {"key": "cylinder", "value": "{{cylinderId}}", "description": "Filter by cylinder ID", "disabled": true}, {"key": "isActive", "value": "true", "description": "Filter by active status"}, {"key": "minPrice", "value": "10", "description": "Minimum price filter", "disabled": true}, {"key": "maxPrice", "value": "100", "description": "Maximum price filter", "disabled": true}]}, "description": "Get all packages with filtering and pagination. **Access**: All authenticated users"}}, {"name": "Get Package by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/packages/{{packageId}}?populate=true", "host": ["{{baseUrl}}"], "path": ["packages", "{{packageId}}"], "query": [{"key": "populate", "value": "true", "description": "Include related data (default: true)"}]}, "description": "Get package details by ID with optional population. **Access**: All authenticated users"}}, {"name": "Update Package", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Premium Gas Kit\",\n  \"description\": \"Enhanced gas cylinder package with premium accessories\",\n  \"discount\": 15\n}"}, "url": {"raw": "{{baseUrl}}/packages/{{packageId}}", "host": ["{{baseUrl}}"], "path": ["packages", "{{packageId}}"]}, "description": "Update package details. **Access**: Admin only"}}, {"name": "Toggle Package Status", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/packages/{{packageId}}/toggle-status", "host": ["{{baseUrl}}"], "path": ["packages", "{{packageId}}", "toggle-status"]}, "description": "Toggle package active/inactive status. **Access**: Admin only"}}]}, {"name": "Availability Checks", "item": [{"name": "Check Package Availability", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/packages/{{packageId}}/availability?quantity=5", "host": ["{{baseUrl}}"], "path": ["packages", "{{packageId}}", "availability"], "query": [{"key": "quantity", "value": "5", "description": "Requested quantity (default: 1)"}]}, "description": "Check if requested quantity is available. **Access**: All authenticated users"}}, {"name": "Get Available Quantity", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/packages/{{packageId}}/available-quantity", "host": ["{{baseUrl}}"], "path": ["packages", "{{packageId}}", "available-quantity"]}, "description": "Get current available quantity (total - reserved). **Access**: All authenticated users"}}]}, {"name": "Administrative Operations", "item": [{"name": "Restock Package", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quantity\": 20\n}"}, "url": {"raw": "{{baseUrl}}/packages/{{packageId}}/restock", "host": ["{{baseUrl}}"], "path": ["packages", "{{packageId}}", "restock"]}, "description": "Add new stock to existing package inventory. **Access**: Admin only"}}, {"name": "Adjust Package Stock", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"adjustment\": -5,\n  \"reason\": \"Damaged during transport\"\n}"}, "url": {"raw": "{{baseUrl}}/packages/{{packageId}}/adjust-stock", "host": ["{{baseUrl}}"], "path": ["packages", "{{packageId}}", "adjust-stock"]}, "description": "Adjust package stock for damage, loss, or corrections. **Access**: Admin only"}}, {"name": "Bulk Update Package Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"packageIds\": [\"{{packageId}}\", \"60f7b3b4e1234567890abce3\"],\n  \"isActive\": false\n}"}, "url": {"raw": "{{baseUrl}}/packages/bulk-status", "host": ["{{baseUrl}}"], "path": ["packages", "bulk-status"]}, "description": "Update status for multiple packages efficiently. **Access**: Admin only"}}]}, {"name": "Analytics & Reports", "item": [{"name": "Get Package Analytics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/packages/analytics", "host": ["{{baseUrl}}"], "path": ["packages", "analytics"]}, "description": "Get comprehensive package analytics including totals, averages, and trends. **Access**: Admin only"}}, {"name": "Get Low Stock Packages", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/packages/low-stock", "host": ["{{baseUrl}}"], "path": ["packages", "low-stock"]}, "description": "Get packages with stock levels at or below minimum threshold. **Access**: <PERSON><PERSON>, Agent"}}, {"name": "Get Package Sales Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/packages/sales-statistics", "host": ["{{baseUrl}}"], "path": ["packages", "sales-statistics"]}, "description": "Get detailed sales statistics including revenue and top-selling packages. **Access**: Admin only"}}]}]}, {"name": "🛢️ Cylinder Management", "description": "Administrative operations for cylinder inventory management", "item": [{"name": "CRUD Operations", "item": [{"name": "Create <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"SixKg\",\n  \"material\": \"Metal\",\n  \"price\": 45.99,\n  \"cost\": 30.00,\n  \"imageUrl\": \"https://example.com/6kg-cylinder.jpg\",\n  \"description\": \"6KG Metal Gas Cylinder\",\n  \"quantity\": 100,\n  \"minimumStockLevel\": 20,\n  \"status\": \"Active\"\n}"}, "url": {"raw": "{{baseUrl}}/cylinders", "host": ["{{baseUrl}}"], "path": ["cylinders"]}, "description": "Create a new cylinder type. **Access**: Admin only"}}, {"name": "Get All Cylinders", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cylinders?page=1&limit=10&type=SixKg&material=Metal", "host": ["{{baseUrl}}"], "path": ["cylinders"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Items per page (default: 10, max: 50)"}, {"key": "type", "value": "SixKg", "description": "Filter by cylinder type (SixKg, ThirteenKg, SeventeenKg)"}, {"key": "material", "value": "Metal", "description": "Filter by material (Metal, Plastic)"}, {"key": "status", "value": "Active", "description": "Filter by status", "disabled": true}, {"key": "lowStockOnly", "value": "true", "description": "Show only low stock items", "disabled": true}]}, "description": "Get all cylinders with filtering and pagination. **Access**: All authenticated users"}}, {"name": "Get Cylinder by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cylinders/{{cylinderId}}", "host": ["{{baseUrl}}"], "path": ["cylinders", "{{cylinderId}}"]}, "description": "Get cylinder details by ID. **Access**: All authenticated users"}}, {"name": "Update Cylinder", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"price\": 47.99,\n  \"minimumStockLevel\": 25,\n  \"description\": \"Updated 6KG Iron Gas Cylinder with improved valve\"\n}"}, "url": {"raw": "{{baseUrl}}/cylinders/{{cylinderId}}", "host": ["{{baseUrl}}"], "path": ["cylinders", "{{cylinderId}}"]}, "description": "Update cylinder details. **Access**: Admin only"}}, {"name": "Delete Cylinder", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/cylinders/{{cylinderId}}", "host": ["{{baseUrl}}"], "path": ["cylinders", "{{cylinderId}}"]}, "description": "Delete a cylinder type. **Access**: Admin only"}}, {"name": "Check Cylinder Availability", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cylinders/{{cylinderId}}/availability?quantity=10", "host": ["{{baseUrl}}"], "path": ["cylinders", "{{cylinderId}}", "availability"], "query": [{"key": "quantity", "value": "10", "description": "Requested quantity (default: 1)"}]}, "description": "Check if requested quantity is available. **Access**: All authenticated users"}}]}, {"name": "Administrative Operations", "item": [{"name": "Restock Cylinder", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quantity\": 50\n}"}, "url": {"raw": "{{baseUrl}}/cylinders/{{cylinderId}}/restock", "host": ["{{baseUrl}}"], "path": ["cylinders", "{{cylinderId}}", "restock"]}, "description": "Add new stock to existing cylinder inventory. **Access**: Admin only"}}, {"name": "Bulk Update Cylinder Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"cylinderIds\": [\"{{cylinderId}}\", \"60f7b3b4e1234567890abce4\"],\n  \"status\": \"Discontinued\"\n}"}, "url": {"raw": "{{baseUrl}}/cylinders/bulk-status", "host": ["{{baseUrl}}"], "path": ["cylinders", "bulk-status"]}, "description": "Update status for multiple cylinders efficiently. **Access**: Admin only"}}]}, {"name": "Analytics & Reports", "item": [{"name": "Get Low Stock Cylinders", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cylinders/low-stock", "host": ["{{baseUrl}}"], "path": ["cylinders", "low-stock"]}, "description": "Get cylinders with stock levels at or below minimum threshold. **Access**: <PERSON><PERSON>, Agent"}}, {"name": "Get Cylinder Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cylinders/statistics", "host": ["{{baseUrl}}"], "path": ["cylinders", "statistics"]}, "description": "Get detailed cylinder sales statistics by type, material, and status. **Access**: Admin only"}}]}]}, {"name": "🔄 Order Management (Automated Inventory)", "description": "Order lifecycle operations that automatically handle inventory reservation, release, and sales tracking", "item": [{"name": "Order Lifecycle", "item": [{"name": "Create Order (Auto-Reserve Inventory)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customerId\": \"{{customerId}}\",\n  \"items\": [\n    {\n      \"itemType\": \"CYLINDER\",\n      \"itemId\": \"{{cylinderId}}\",\n      \"quantity\": 2\n    },\n    {\n      \"itemType\": \"PACKAGE\",\n      \"itemId\": \"{{packageId}}\",\n      \"quantity\": 1\n    },\n    {\n      \"itemType\": \"SPARE_PART\",\n      \"itemId\": \"{{sparePartId}}\",\n      \"quantity\": 3\n    }\n  ],\n  \"deliveryAddress\": \"123 Main Street, Hargeisa, Somaliland\",\n  \"paymentMethod\": \"WAAFI_PREAUTH\"\n}"}, "url": {"raw": "{{baseUrl}}/orders", "host": ["{{baseUrl}}"], "path": ["orders"]}, "description": "Create a new order. **Automatically reserves inventory** for all items. **Access**: All authenticated users"}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"customerId\": \"60f7b3b4e1234567890abce2\",\n  \"items\": [\n    {\n      \"itemType\": \"CYLINDER\",\n      \"itemId\": \"60f7b3b4e1234567890abcde\",\n      \"quantity\": 2\n    }\n  ],\n  \"deliveryAddress\": \"123 Main Street\",\n  \"paymentMethod\": \"WAAFI_PREAUTH\"\n}"}, "url": {"raw": "{{baseUrl}}/orders", "host": ["{{baseUrl}}"], "path": ["orders"]}}, "status": "Created", "code": 201, "body": "{\n  \"status\": \"success\",\n  \"message\": \"Order created successfully\",\n  \"data\": {\n    \"_id\": \"60f7b3b4e1234567890abce1\",\n    \"customer\": \"60f7b3b4e1234567890abce2\",\n    \"items\": [\n      {\n        \"itemType\": \"CYLINDER\",\n        \"itemId\": \"60f7b3b4e1234567890abcde\",\n        \"quantity\": 2,\n        \"price\": 45.99\n      }\n    ],\n    \"totalAmount\": 91.98,\n    \"status\": \"PENDING\",\n    \"deliveryAddress\": \"123 Main Street\",\n    \"paymentMethod\": \"WAAFI_PREAUTH\",\n    \"qrCode\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n    \"createdAt\": \"2023-07-20T10:30:00.000Z\"\n  }\n}"}]}, {"name": "Get Orders", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orders?status=PENDING&customer={{customerId}}", "host": ["{{baseUrl}}"], "path": ["orders"], "query": [{"key": "status", "value": "PENDING", "description": "Filter by order status (PENDING, ASSIGNED, DELIVERED, CANCELLED)"}, {"key": "customer", "value": "{{customerId}}", "description": "Filter by customer ID"}, {"key": "paymentMethod", "value": "WAAFI_PREAUTH", "description": "Filter by payment method", "disabled": true}, {"key": "deliveryAgent", "value": "agent_id", "description": "Filter by delivery agent ID", "disabled": true}]}, "description": "Get orders with filtering options. **Access**: All authenticated users (customers see only their orders)"}}, {"name": "Assign Agent to Order", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"agentId\": \"60f7b3b4e1234567890abce5\"\n}"}, "url": {"raw": "{{baseUrl}}/orders/{{orderId}}/assign", "host": ["{{baseUrl}}"], "path": ["orders", "{{orderId}}", "assign"]}, "description": "Assign a delivery agent to an order. **Access**: Admin only"}}, {"name": "Cancel Order (Auto-Release Inventory)", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/orders/{{orderId}}/cancel", "host": ["{{baseUrl}}"], "path": ["orders", "{{orderId}}", "cancel"]}, "description": "Cancel an order. **Automatically releases reserved inventory** for all items. **Access**: Customer (own orders), <PERSON><PERSON>, Agent"}}, {"name": "Complete Order (Auto-Mark as Sold)", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/orders/{{orderId}}/complete", "host": ["{{baseUrl}}"], "path": ["orders", "{{orderId}}", "complete"]}, "description": "Mark order as delivered/completed. **Automatically marks items as sold** and updates inventory. **Access**: <PERSON><PERSON>, Agent"}}]}, {"name": "Order Utilities", "item": [{"name": "Validate QR Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"qrCode\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.signature\"\n}"}, "url": {"raw": "{{baseUrl}}/orders/validate-qr", "host": ["{{baseUrl}}"], "path": ["orders", "validate-qr"]}, "description": "Validate order QR code and retrieve order details. **Access**: Agent, Admin"}}]}]}, {"name": "📊 System Information", "description": "Additional endpoints for system information and testing", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}, "description": "Check API health status. **Access**: Public"}}, {"name": "API Documentation", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/docs", "host": ["{{baseUrl}}"], "path": ["docs"]}, "description": "Access API documentation. **Access**: Public"}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set timestamp for requests", "pm.globals.set('timestamp', new Date().toISOString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script for all requests", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Response has correct content type', function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/json');", "});", "", "// Extract common IDs from responses for use in subsequent requests", "if (pm.response.code === 201 || pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    if (responseJson.data && responseJson.data._id) {", "        const endpoint = pm.request.url.path.join('/');", "        if (endpoint.includes('spare-parts')) {", "            pm.globals.set('sparePartId', responseJson.data._id);", "        } else if (endpoint.includes('packages')) {", "            pm.globals.set('packageId', responseJson.data._id);", "        } else if (endpoint.includes('cylinders')) {", "            pm.globals.set('cylinderId', responseJson.data._id);", "        } else if (endpoint.includes('orders')) {", "            pm.globals.set('orderId', responseJson.data._id);", "        }", "    }", "}"]}}]}