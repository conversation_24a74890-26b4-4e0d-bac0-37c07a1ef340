# ==============================================
# PM2 COMMAND CHEATSHEET FOR NODE.JS SERVER
# ==============================================

# 1. START YOUR SERVER WITH PM2
# -----------------------------
# Starts your server.js and names the process "node_server_socketio"
pm2 start src/server.js --name "node_server_socketio"

# 2. RESTART SERVER (APPLIES CODE CHANGES)
# ----------------------------------------
# Stops and starts your server (brief downtime ~1-2 seconds)
pm2 restart node_server_socketio

# 3. ZERO-DOWNTIME RELOAD (PRODUCTION SAFE)
# -----------------------------------------
# Gracefully reloads without dropping connections
# (Starts new instance before killing old one)
pm2 reload node_server_socketio

# 4. UPDATE ENVIRONMENT VARIABLES
# -------------------------------
# Use when you modify .env or config files
pm2 restart node_server_socketio --update-env

# 5. AUTO-RESTART ON FILE CHANGES (DEV ONLY)
# ------------------------------------------
# Watches files and restarts automatically (Don't use in production!)
pm2 start src/server.js --name "node_server_socketio" --watch

# 6. STOP SERVER
# --------------
# Stops the process but keeps it in PM2's list
pm2 stop node_server_socketio

# 7. DELETE SERVER FROM PM2
# -------------------------
# Removes the process from PM2's management
pm2 delete node_server_socketio

# 8. MONITORING COMMANDS
# ----------------------
# List all running PM2 processes
pm2 list

# Show real-time logs
pm2 logs node_server_socketio

# Show only error logs
pm2 logs node_server_socketio --err

# Monitor CPU/Memory usage
pm2 monit

# 9. PERSISTENCE COMMANDS
# -----------------------
# Save current processes to restart after reboot
pm2 save

# Show saved processes
pm2 dump

# ==============================================
# TIPS:
# 1. After code changes, use 'restart' or 'reload'
# 2. For production, use 'reload' for zero downtime
# 3. Use '--update-env' when changing .env files
# ==============================================