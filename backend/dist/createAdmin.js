"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const user_model_1 = require("./models/user.model");
const enums_1 = require("./enums/enums");
const env_config_1 = require("./config/env_config");
const jwt_utils_1 = require("./utils/jwt_utils");
/// npx ts-node src/createAdmin.ts
async function createAdmin() {
    try {
        // Connect to database using the configured URL
        await mongoose_1.default.connect(env_config_1.config.server.databaseUrl);
        console.log('Connected to database');
        // Create admin user
        const admin = await user_model_1.User.create({
            phone: '+252619597745',
            email: '<EMAIL>',
            role: enums_1.UserRole.ADMIN,
            isActive: true,
        });
        const token = (0, jwt_utils_1.generateToken)(admin._id.toString(), admin.role);
        console.log('Admin created:', {
            adminId: admin._id,
            token,
        });
        process.exit(0);
    }
    catch (error) {
        console.error('Error creating admin:', error);
        process.exit(1);
    }
}
// Execute the function
createAdmin();
//# sourceMappingURL=createAdmin.js.map