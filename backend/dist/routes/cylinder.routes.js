"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const cylinder_controller_1 = require("../controllers/cylinder.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const role_middleware_1 = require("../middleware/role.middleware");
const enums_1 = require("../enums/enums");
const image_upload_service_1 = require("../services/image-upload.service");
const cylinderRouter = (0, express_1.Router)();
// Apply authentication middleware to all cylinder routes
cylinderRouter.use(auth_middleware_1.authenticate);
// ==================== CRUD OPERATIONS ====================
/**
 * @route   POST /api/v1/cylinders
 * @desc    Create a new cylinder type
 * @access  Private (Admin only)
 * @body    { type, material, price, cost, imageUrl?, description?, quantity?, minimumStockLevel?, status? }
 * @file    image (optional multipart/form-data)
 */
cylinderRouter.post('/', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), image_upload_service_1.uploadSingleImage, cylinder_controller_1.cylinderController.createCylinder);
/**
 * @route   GET /api/v1/cylinders
 * @desc    Get all cylinders with filtering and pagination
 * @access  Private (All authenticated users)
 * @query   { type?, material?, status?, lowStockOnly?, page?, limit? }
 */
cylinderRouter.get('/', cylinder_controller_1.cylinderController.getCylinders);
/**
 * @route   GET /api/v1/cylinders/low-stock
 * @desc    Get cylinders with low stock
 * @access  Private (Admin, Agent)
 */
cylinderRouter.get('/low-stock', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), cylinder_controller_1.cylinderController.getLowStockCylinders);
/**
 * @route   GET /api/v1/cylinders/statistics
 * @desc    Get cylinder sales statistics
 * @access  Private (Admin only)
 */
cylinderRouter.get('/statistics', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), cylinder_controller_1.cylinderController.getCylinderStatistics);
/**
 * @route   GET /api/v1/cylinders/deleted
 * @desc    Get soft-deleted cylinders
 * @access  Private (Admin only)
 * @query   { page?, limit? }
 */
cylinderRouter.get('/deleted', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), cylinder_controller_1.cylinderController.getDeletedCylinders);
/**
 * @route   GET /api/v1/cylinders/:id
 * @desc    Get cylinder by ID
 * @access  Private (All authenticated users)
 */
cylinderRouter.get('/:id', cylinder_controller_1.cylinderController.getCylinderById);
/**
 * @route   PUT /api/v1/cylinders/:id
 * @desc    Update cylinder details
 * @access  Private (Admin only)
 * @body    { type?, material?, price?, cost?, imageUrl?, description?, minimumStockLevel?, status? }
 * @file    image (optional multipart/form-data)
 */
cylinderRouter.put('/:id', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), image_upload_service_1.uploadSingleImage, cylinder_controller_1.cylinderController.updateCylinder);
/**
 * @route   DELETE /api/v1/cylinders/:id
 * @desc    Soft delete a cylinder type
 * @access  Private (Admin only)
 */
cylinderRouter.delete('/:id', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), cylinder_controller_1.cylinderController.deleteCylinder);
/**
 * @route   POST /api/v1/cylinders/:id/restore
 * @desc    Restore a soft-deleted cylinder
 * @access  Private (Admin only)
 */
cylinderRouter.post('/:id/restore', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), cylinder_controller_1.cylinderController.restoreCylinder);
/**
 * @route   DELETE /api/v1/cylinders/:id/permanent
 * @desc    Permanently delete a cylinder (hard delete)
 * @access  Private (Admin only)
 */
cylinderRouter.delete('/:id/permanent', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), cylinder_controller_1.cylinderController.permanentlyDeleteCylinder);
/**
 * @route   GET /api/v1/cylinders/:id/packages
 * @desc    Get packages that use a specific cylinder
 * @access  Private (Admin only)
 * @query   { includeDeleted? }
 */
cylinderRouter.get('/:id/packages', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), cylinder_controller_1.cylinderController.getPackagesUsingCylinder);
/**
 * @route   GET /api/v1/cylinders/:id/availability
 * @desc    Check cylinder availability
 * @access  Private (All authenticated users)
 * @query   { quantity? }
 */
cylinderRouter.get('/:id/availability', cylinder_controller_1.cylinderController.checkCylinderAvailability);
// ==================== ADMINISTRATIVE INVENTORY MANAGEMENT ====================
// Note: Reservation, release, and sales operations are now handled automatically
// through the order lifecycle. Only administrative operations remain.
/**
 * @route   POST /api/v1/cylinders/:id/restock
 * @desc    Restock cylinder
 * @access  Private (Admin only)
 * @body    { quantity }
 */
cylinderRouter.post('/:id/restock', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), cylinder_controller_1.cylinderController.restockCylinder);
/**
 * @route   PUT /api/v1/cylinders/bulk-status
 * @desc    Bulk update cylinder statuses
 * @access  Private (Admin only)
 * @body    { cylinderIds, status }
 */
cylinderRouter.put('/bulk-status', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), cylinder_controller_1.cylinderController.bulkUpdateCylinderStatus);
exports.default = cylinderRouter;
//# sourceMappingURL=cylinder.routes.js.map