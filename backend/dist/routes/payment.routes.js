"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const payment_controller_1 = require("../controllers/payment.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const role_middleware_1 = require("../middleware/role.middleware");
const enums_1 = require("../enums/enums");
const paymentRouter = (0, express_1.Router)();
// ==================== PAYMENT OPERATIONS ====================
/**
 * @route   POST /api/v1/payments/:id/preauthorize
 * @desc    Initiate payment preauthorization with gateway
 * @access  Private (Customer, Admin)
 * @body    { mobile, amount, deliveryDetails? }
 */
paymentRouter.post('/:id/preauthorize', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.CUSTOMER, enums_1.UserRole.ADMIN]), payment_controller_1.paymentController.initiatePreauthorization);
/**
 * @route   POST /api/v1/payments/:id/cancel
 * @desc    Cancel payment preauthorization
 * @access  Private (Customer, Admin, Agent)
 */
paymentRouter.post('/:id/cancel', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.CUSTOMER, enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), payment_controller_1.paymentController.cancelPreauthorization);
/**
 * @route   POST /api/v1/payments/:id/capture
 * @desc    Capture preauthorized payment
 * @access  Private (Admin, Agent)
 */
paymentRouter.post('/:id/capture', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), payment_controller_1.paymentController.capturePreauthorizedPayment);
// ==================== GATEWAY RESPONSE ANALYTICS ====================
/**
 * @route   GET /api/v1/payments/:id/gateway-responses
 * @desc    Get raw gateway responses for debugging and audit
 * @access  Private (Admin only)
 * @query   { operation?, provider? }
 */
paymentRouter.get('/:id/gateway-responses', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), payment_controller_1.paymentController.getGatewayResponses);
/**
 * @route   GET /api/v1/payments/:id/analytics
 * @desc    Get payment analytics including gateway statistics
 * @access  Private (Admin only)
 */
paymentRouter.get('/:id/analytics', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), payment_controller_1.paymentController.getPaymentAnalytics);
// ==================== WEBHOOK HANDLING ====================
/**
 * @route   POST /api/v1/payments/:id/webhook
 * @desc    Handle webhook/callback from payment gateway
 * @access  Public (webhook endpoint - no authentication required)
 * @query   { provider? }
 * @body    Gateway-specific webhook payload
 */
paymentRouter.post('/:id/webhook', payment_controller_1.paymentController.handleWebhook);
// ==================== SYSTEM ANALYTICS ====================
/**
 * @route   GET /api/v1/payments/system/analytics
 * @desc    Get system-wide payment analytics
 * @access  Private (Admin only)
 * @query   { startDate?, endDate?, provider?, operation? }
 */
paymentRouter.get('/system/analytics', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), payment_controller_1.paymentController.getSystemAnalytics);
exports.default = paymentRouter;
//# sourceMappingURL=payment.routes.js.map