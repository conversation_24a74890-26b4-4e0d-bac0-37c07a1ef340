"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const notification_controller_1 = require("../controllers/notification.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const notificationRouter = (0, express_1.Router)();
// Apply authentication middleware to all notification routes
notificationRouter.use(auth_middleware_1.authenticate);
/**
 * @route   POST /api/v1/notifications/send-to-user
 * @desc    Send notification to a specific user
 * @access  Private (Admin, Agent)
 * @body    { userId, title, body, data?, imageUrl? }
 */
notificationRouter.post('/send-to-user', notification_controller_1.notificationController.sendToUser);
/**
 * @route   POST /api/v1/notifications/send-to-topic
 * @desc    Send notification to all users subscribed to a topic
 * @access  Private (Admin only)
 * @body    { topic, title, body, data?, imageUrl?, onlyActiveUsers? }
 */
notificationRouter.post('/send-to-topic', notification_controller_1.notificationController.sendToTopic);
/**
 * @route   PUT /api/v1/notifications/fcm-token
 * @desc    Update user's FCM token
 * @access  Private (All authenticated users)
 * @body    { token }
 */
notificationRouter.put('/fcm-token', notification_controller_1.notificationController.updateFcmToken);
/**
 * @route   POST /api/v1/notifications/subscribe
 * @desc    Subscribe user to notification topic
 * @access  Private (All authenticated users)
 * @body    { topic }
 */
notificationRouter.post('/subscribe', notification_controller_1.notificationController.subscribeToTopic);
/**
 * @route   PUT /api/v1/notifications/toggle
 * @desc    Toggle user's notification settings
 * @access  Private (All authenticated users)
 * @body    { enabled }
 */
notificationRouter.put('/toggle', notification_controller_1.notificationController.toggleNotifications);
/**
 * @route   GET /api/v1/notifications/history
 * @desc    Get user's notification history
 * @access  Private (All authenticated users)
 * @query   { page?, limit?, status? }
 */
notificationRouter.get('/history', notification_controller_1.notificationController.getNotificationHistory);
/**
 * @route   GET /api/v1/notifications/stats
 * @desc    Get notification statistics
 * @access  Private (Admin only)
 * @query   { startDate?, endDate? }
 */
notificationRouter.get('/stats', notification_controller_1.notificationController.getNotificationStats);
/**
 * @route   GET /api/v1/notifications/topics
 * @desc    Get available notification topics
 * @access  Private (All authenticated users)
 */
notificationRouter.get('/topics', notification_controller_1.notificationController.getTopics);
exports.default = notificationRouter;
//# sourceMappingURL=notification.routes.js.map