"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const userController = __importStar(require("../controllers/user.controller"));
const auth_middleware_1 = require("../middleware/auth.middleware");
const role_middleware_1 = require("../middleware/role.middleware");
const enums_1 = require("../enums/enums");
const userRouter = (0, express_1.Router)();
// Public routes
userRouter.post('/login', userController.login);
userRouter.post('/resend-otp', userController.resendOtp);
userRouter.post('/verify-otp', userController.verifyOtp);
// Protected routes
userRouter.get('/me', auth_middleware_1.authenticate, userController.getCurrentUser);
userRouter.get('/:id', auth_middleware_1.authenticate, userController.getSingleUser);
userRouter.put('/:id', auth_middleware_1.authenticate, userController.updateUser);
userRouter.delete('/:id', auth_middleware_1.authenticate, userController.deleteUser);
userRouter.patch('/:id/active', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), userController.toggleUserActiveStatus);
userRouter.patch('/:id/on-duty', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), userController.toggleAgentOnDutyStatus);
userRouter.patch('/:id/role', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), userController.changeUserRole);
// Admin only routes
userRouter.post('/admin', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), userController.registerAdminOrAgent);
// Supervisor routes
userRouter.post('/customer', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN, enums_1.UserRole.SUPERVISOR]), userController.registerCustomer);
/**
 * @route   GET /api/v1/users/search
 * @desc    Advanced user search with filtering and autocomplete
 * @access  Private (Admin, Supervisor)
 * @query   { q, limit?, role?, isActive?, fuzzy?, autocomplete? }
 */
userRouter.get('/search', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN, enums_1.UserRole.SUPERVISOR]), userController.searchUsers);
userRouter.get('/', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN, enums_1.UserRole.SUPERVISOR]), userController.getAllUsers);
// Notification routes
userRouter.post('/notifications/subscribe', auth_middleware_1.authenticate, userController.subscribeToNotifications);
exports.default = userRouter;
//# sourceMappingURL=user.routes.js.map