"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const package_controller_1 = require("../controllers/package.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const role_middleware_1 = require("../middleware/role.middleware");
const enums_1 = require("../enums/enums");
const image_upload_service_1 = require("../services/image-upload.service");
const packageRouter = (0, express_1.Router)();
// Apply authentication middleware to all package routes
packageRouter.use(auth_middleware_1.authenticate);
// ==================== CRUD OPERATIONS ====================
/**
 * @route   POST /api/v1/packages
 * @desc    Create a new package
 * @access  Private (Admin only)
 * @body    { name, description?, cylinder, includedSpareParts, totalPrice?, costPrice?, discount?, imageUrl?, quantity?, minimumStockLevel? }
 * @file    image (optional multipart/form-data)
 */
packageRouter.post('/', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), image_upload_service_1.uploadSingleImage, package_controller_1.packageController.createPackage);
/**
 * @route   GET /api/v1/packages
 * @desc    Get all packages with filtering and pagination
 * @access  Private (All authenticated users)
 * @query   { search?, cylinder?, isActive?, minPrice?, maxPrice?, page?, limit?, populate? }
 */
packageRouter.get('/', package_controller_1.packageController.getPackages);
/**
 * @route   GET /api/v1/packages/analytics
 * @desc    Get package analytics
 * @access  Private (Admin only)
 */
packageRouter.get('/analytics', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), package_controller_1.packageController.getPackageAnalytics);
/**
 * @route   GET /api/v1/packages/low-stock
 * @desc    Get packages with low stock
 * @access  Private (Admin, Agent)
 */
packageRouter.get('/low-stock', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), package_controller_1.packageController.getLowStockPackages);
/**
 * @route   GET /api/v1/packages/sales-statistics
 * @desc    Get package sales statistics
 * @access  Private (Admin only)
 */
packageRouter.get('/sales-statistics', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), package_controller_1.packageController.getPackageSalesStatistics);
/**
 * @route   GET /api/v1/packages/deleted
 * @desc    Get soft-deleted packages
 * @access  Private (Admin only)
 * @query   { page?, limit? }
 */
packageRouter.get('/deleted', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), package_controller_1.packageController.getDeletedPackages);
/**
 * @route   POST /api/v1/packages/cleanup-deleted-parts
 * @desc    Clean up packages that reference deleted spare parts
 * @access  Private (Admin only)
 */
packageRouter.post('/cleanup-deleted-parts', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), package_controller_1.packageController.cleanupPackagesWithDeletedParts);
/**
 * @route   GET /api/v1/packages/:id
 * @desc    Get package by ID
 * @access  Private (All authenticated users)
 * @query   { populate? }
 */
packageRouter.get('/:id', package_controller_1.packageController.getPackageById);
/**
 * @route   DELETE /api/v1/packages/:id
 * @desc    Soft delete a package
 * @access  Private (Admin only)
 */
packageRouter.delete('/:id', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), package_controller_1.packageController.deletePackage);
/**
 * @route   POST /api/v1/packages/:id/restore
 * @desc    Restore a soft-deleted package
 * @access  Private (Admin only)
 */
packageRouter.post('/:id/restore', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), package_controller_1.packageController.restorePackage);
/**
 * @route   DELETE /api/v1/packages/:id/permanent
 * @desc    Permanently delete a package (hard delete)
 * @access  Private (Admin only)
 */
packageRouter.delete('/:id/permanent', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), package_controller_1.packageController.permanentlyDeletePackage);
/**
 * @route   PUT /api/v1/packages/:id
 * @desc    Update package details
 * @access  Private (Admin only)
 * @body    { name?, description?, includedSpareParts?, discount?, imageUrl? }
 * @file    image (optional multipart/form-data)
 */
packageRouter.put('/:id', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), image_upload_service_1.uploadSingleImage, package_controller_1.packageController.updatePackage);
/**
 * @route   PUT /api/v1/packages/:id/toggle-status
 * @desc    Toggle package active status
 * @access  Private (Admin only)
 */
packageRouter.put('/:id/toggle-status', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), package_controller_1.packageController.togglePackageStatus);
/**
 * @route   GET /api/v1/packages/:id/availability
 * @desc    Check package availability
 * @access  Private (All authenticated users)
 * @query   { quantity? }
 */
packageRouter.get('/:id/availability', package_controller_1.packageController.checkPackageAvailability);
/**
 * @route   GET /api/v1/packages/:id/available-quantity
 * @desc    Get available package quantity
 * @access  Private (All authenticated users)
 */
packageRouter.get('/:id/available-quantity', package_controller_1.packageController.getAvailableQuantity);
// ==================== ADMINISTRATIVE INVENTORY MANAGEMENT ====================
// Note: Reservation, release, and sales operations are now handled automatically
// through the order lifecycle. Only administrative operations remain.
/**
 * @route   POST /api/v1/packages/:id/restock
 * @desc    Restock package
 * @access  Private (Admin only)
 * @body    { quantity }
 */
packageRouter.post('/:id/restock', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), package_controller_1.packageController.restockPackage);
/**
 * @route   POST /api/v1/packages/:id/adjust-stock
 * @desc    Adjust package stock
 * @access  Private (Admin only)
 * @body    { adjustment, reason }
 */
packageRouter.post('/:id/adjust-stock', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), package_controller_1.packageController.adjustPackageStock);
/**
 * @route   PUT /api/v1/packages/bulk-status
 * @desc    Bulk update package statuses
 * @access  Private (Admin only)
 * @body    { packageIds, isActive }
 */
packageRouter.put('/bulk-status', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), package_controller_1.packageController.bulkUpdatePackageStatus);
exports.default = packageRouter;
//# sourceMappingURL=package.routes.js.map