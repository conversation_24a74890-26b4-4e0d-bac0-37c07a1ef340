{"version": 3, "file": "dashboard.routes.js", "sourceRoot": "", "sources": ["../../src/routes/dashboard.routes.ts"], "names": [], "mappings": ";;AAAA,qCAAiC;AACjC,8EAA0E;AAC1E,mEAA6D;AAC7D,mEAA6D;AAC7D,0CAA0C;AAE1C,MAAM,eAAe,GAAG,IAAA,gBAAM,GAAE,CAAC;AAEjC;;;GAGG;AAEH,2DAA2D;AAC3D,eAAe,CAAC,GAAG,CAAC,UAAU,EAAE,8BAAY,EAAE,0CAAmB,CAAC,YAAY,CAAC,CAAC;AAEhF,oCAAoC;AACpC,eAAe,CAAC,GAAG,CACjB,QAAQ,EACR,8BAAY,EACZ,IAAA,8BAAY,EAAC,CAAC,gBAAQ,CAAC,KAAK,CAAC,CAAC,EAC9B,0CAAmB,CAAC,iBAAiB,CACtC,CAAC;AAEF,eAAe,CAAC,GAAG,CACjB,aAAa,EACb,8BAAY,EACZ,IAAA,8BAAY,EAAC,CAAC,gBAAQ,CAAC,UAAU,CAAC,CAAC,EACnC,0CAAmB,CAAC,sBAAsB,CAC3C,CAAC;AAEF,eAAe,CAAC,GAAG,CACjB,QAAQ,EACR,8BAAY,EACZ,IAAA,8BAAY,EAAC,CAAC,gBAAQ,CAAC,KAAK,CAAC,CAAC,EAC9B,0CAAmB,CAAC,iBAAiB,CACtC,CAAC;AAEF,eAAe,CAAC,GAAG,CACjB,WAAW,EACX,8BAAY,EACZ,IAAA,8BAAY,EAAC,CAAC,gBAAQ,CAAC,QAAQ,CAAC,CAAC,EACjC,0CAAmB,CAAC,oBAAoB,CACzC,CAAC;AAEF,yBAAyB;AACzB,eAAe,CAAC,GAAG,CACjB,gBAAgB,EAChB,8BAAY,EACZ,IAAA,8BAAY,EAAC,CAAC,gBAAQ,CAAC,KAAK,CAAC,CAAC,EAC9B,0CAAmB,CAAC,UAAU,CAC/B,CAAC;AAEF,kBAAe,eAAe,CAAC"}