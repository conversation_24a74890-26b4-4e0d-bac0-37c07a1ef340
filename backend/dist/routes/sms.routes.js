"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const sms_controller_1 = require("../controllers/sms.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const smsRouter = (0, express_1.Router)();
// Apply authentication middleware to all SMS routes
smsRouter.use(auth_middleware_1.authenticate);
/**
 * @route   POST /api/v1/sms/send
 * @desc    Send SMS to a single recipient
 * @access  Private (Admin, Agent)
 * @body    { phoneNumber, message, senderId?, refId?, isOtp? }
 */
smsRouter.post('/send', sms_controller_1.smsController.sendSms);
/**
 * @route   POST /api/v1/sms/send-bulk
 * @desc    Send SMS to multiple recipients
 * @access  Private (Admin only)
 * @body    { phoneNumbers[], message, senderId?, refId?, isOtp? }
 */
smsRouter.post('/send-bulk', sms_controller_1.smsController.sendBulkSms);
/**
 * @route   POST /api/v1/sms/send-to-role
 * @desc    Send SMS to all users with a specific role
 * @access  Private (Admin only)
 * @body    { role, message, senderId?, refId?, isOtp?, onlyActiveUsers? }
 */
smsRouter.post('/send-to-role', sms_controller_1.smsController.sendSmsToRole);
/**
 * @route   POST /api/v1/sms/send-otp
 * @desc    Send OTP SMS to a user
 * @access  Private (Admin, Agent)
 * @body    { phoneNumber, otp, customMessage? }
 */
smsRouter.post('/send-otp', sms_controller_1.smsController.sendOtp);
/**
 * @route   GET /api/v1/sms/health
 * @desc    Get SMS service health status
 * @access  Private (Admin only)
 */
smsRouter.get('/health', sms_controller_1.smsController.getHealthStatus);
/**
 * @route   GET /api/v1/sms/metrics
 * @desc    Get SMS service metrics
 * @access  Private (Admin only)
 */
smsRouter.get('/metrics', sms_controller_1.smsController.getMetrics);
exports.default = smsRouter;
//# sourceMappingURL=sms.routes.js.map