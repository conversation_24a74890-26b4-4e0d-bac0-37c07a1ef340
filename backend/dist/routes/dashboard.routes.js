"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const dashboard_controller_1 = require("../controllers/dashboard.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const role_middleware_1 = require("../middleware/role.middleware");
const enums_1 = require("../enums/enums");
const dashboardRouter = (0, express_1.Router)();
/**
 * Dashboard Routes
 * All routes require authentication
 */
// Legacy endpoint - can be kept for backward compatibility
dashboardRouter.get('/summary', auth_middleware_1.authenticate, dashboard_controller_1.dashboardController.getDashboard);
// Role-specific dashboard endpoints
dashboardRouter.get('/admin', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), dashboard_controller_1.dashboardController.getAdminDashboard);
dashboardRouter.get('/supervisor', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.SUPERVISOR]), dashboard_controller_1.dashboardController.getSupervisorDashboard);
dashboardRouter.get('/agent', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.AGENT]), dashboard_controller_1.dashboardController.getAgentDashboard);
dashboardRouter.get('/customer', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.CUSTOMER]), dashboard_controller_1.dashboardController.getCustomerDashboard);
// Admin reports endpoint
dashboardRouter.get('/reports/:type', auth_middleware_1.authenticate, (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), dashboard_controller_1.dashboardController.getReports);
exports.default = dashboardRouter;
//# sourceMappingURL=dashboard.routes.js.map