"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const order_controller_1 = require("../controllers/order.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const role_middleware_1 = require("../middleware/role.middleware");
const enums_1 = require("../enums/enums");
const orderRouter = (0, express_1.Router)();
// Apply authentication middleware to all order routes
orderRouter.use(auth_middleware_1.authenticate);
/**
 * @route   POST /api/v1/orders
 * @desc    Create a new order
 * @access  Private (Customers and admins only)
 * @body    { items[], deliveryAddress, paymentMethod? }
 */
orderRouter.post('/', (0, role_middleware_1.validateRole)([enums_1.UserRole.CUSTOMER, enums_1.UserRole.ADMIN, enums_1.UserRole.SUPERVISOR]), order_controller_1.orderController.createOrder);
/**
 * @route   GET /api/v1/orders
 * @desc    Get all orders
 * @access  Private (Customers only)
 * @query   { customer?, status?, paymentMethod?, startDate?, endDate? }
 */
orderRouter.get('/', order_controller_1.orderController.getOrders);
/**
 * @route   PUT /api/v1/orders/:id/assign-agent
 * @desc    Assign agent to order
 * @access  Private (Admin, Supervisor)
 * @body    { agentId }
 */
orderRouter.put('/:id/assign-agent', (0, role_middleware_1.validateRole)([
    enums_1.UserRole.ADMIN,
    enums_1.UserRole.SUPERVISOR,
    //  UserRole.AGENT,
]), order_controller_1.orderController.assignAgentToOrder);
/**
 * @route   PUT /api/v1/orders/:id/cancel
 * @desc    Cancel order
 * @access  Private (Admin, Supervisor )
 */
orderRouter.put('/:id/cancel', (0, role_middleware_1.validateRole)([
    enums_1.UserRole.ADMIN,
    enums_1.UserRole.SUPERVISOR,
    // UserRole.AGENT,
]), order_controller_1.orderController.cancelOrder);
/**
 * @route   PUT /api/v1/orders/:id/complete
 * @desc    Complete order
 * @access  Private (Admin, Agent, Supervisor)
 */
orderRouter.put('/:id/complete', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT, enums_1.UserRole.SUPERVISOR]), order_controller_1.orderController.completeOrder);
/**
 * @route   PUT /api/v1/orders/:id/regenerate-qr
 * @desc    Regenerate QR code for an order
 * @access  Private (Admin, Supervisor)
 */
orderRouter.put('/:id/regenerate-qr', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN, enums_1.UserRole.SUPERVISOR]), order_controller_1.orderController.regenerateQRCode);
/**
 * @route   POST /api/v1/orders/validate
 * @desc    Validate order in QR code
 * @access  Private (Admin, Agent,Supervisor)
 * @body    { qrCode }
 */
orderRouter.post('/validate-qr', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT, enums_1.UserRole.SUPERVISOR]), order_controller_1.orderController.validateOrderInQRCode);
/**
 * @route   PUT /api/v1/orders/:id
 * @desc    Update order
 * @access  Private (Admin)
 * @body    { customerId?, items?, deliveryAddress?, paymentMethod? }
 */
orderRouter.put('/:id', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), order_controller_1.orderController.updateOrder);
/**
 * @route   DELETE /api/v1/orders/:id
 * @desc    Delete order
 * @access  Private (Admin)
 */
orderRouter.delete('/:id', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), order_controller_1.orderController.deleteOrder);
exports.default = orderRouter;
//# sourceMappingURL=order.routes.js.map