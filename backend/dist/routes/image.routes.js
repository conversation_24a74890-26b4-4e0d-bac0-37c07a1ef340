"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const image_controller_1 = require("../controllers/image.controller");
const image_upload_service_1 = require("../services/image-upload.service");
const auth_middleware_1 = require("../middleware/auth.middleware");
const role_middleware_1 = require("../middleware/role.middleware");
const enums_1 = require("../enums/enums");
const async_handler_1 = __importDefault(require("../utils/async-handler"));
const router = (0, express_1.Router)();
/**
 * Image upload and management routes
 * All routes require authentication
 */
/**
 * @route POST /api/v1/images/upload
 * @desc Upload a new image
 * @access Admin only
 * @body {category: ImageCategory, entityId?: string, entityType?: string}
 * @file image (multipart/form-data)
 */
router.post('/upload', auth_middleware_1.authenticate, (0, role_middleware_1.authorizeRoles)([enums_1.UserRole.ADMIN]), image_upload_service_1.uploadSingleImage, (0, async_handler_1.default)(image_controller_1.ImageController.uploadImage));
/**
 * @route GET /api/v1/images/info/:entityType/:entityId
 * @desc Get image information for a specific entity
 * @access Admin, Agent (read-only)
 * @params {entityType: 'cylinder'|'sparepart'|'package', entityId: string}
 */
router.get('/info/:entityType/:entityId', auth_middleware_1.authenticate, (0, role_middleware_1.authorizeRoles)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), (0, async_handler_1.default)(image_controller_1.ImageController.getImageInfo));
/**
 * @route PUT /api/v1/images/:entityType/:entityId
 * @desc Replace image for a specific entity
 * @access Admin only
 * @params {entityType: 'cylinder'|'sparepart'|'package', entityId: string}
 * @body {category: ImageCategory}
 * @file image (multipart/form-data)
 */
router.put('/:entityType/:entityId', auth_middleware_1.authenticate, (0, role_middleware_1.authorizeRoles)([enums_1.UserRole.ADMIN]), image_upload_service_1.uploadSingleImage, (0, async_handler_1.default)(image_controller_1.ImageController.replaceImage));
/**
 * @route DELETE /api/v1/images/:entityType/:entityId
 * @desc Delete image for a specific entity
 * @access Admin only
 * @params {entityType: 'cylinder'|'sparepart'|'package', entityId: string}
 */
router.delete('/:entityType/:entityId', auth_middleware_1.authenticate, (0, role_middleware_1.authorizeRoles)([enums_1.UserRole.ADMIN]), (0, async_handler_1.default)(image_controller_1.ImageController.deleteImage));
exports.default = router;
//# sourceMappingURL=image.routes.js.map