"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importStar(require("express"));
const user_routes_1 = __importDefault(require("./user.routes"));
const sms_routes_1 = __importDefault(require("./sms.routes"));
const notification_routes_1 = __importDefault(require("./notification.routes"));
const order_routes_1 = __importDefault(require("./order.routes"));
const spare_part_routes_1 = __importDefault(require("./spare_part.routes"));
const package_routes_1 = __importDefault(require("./package.routes"));
const cylinder_routes_1 = __importDefault(require("./cylinder.routes"));
const payment_routes_1 = __importDefault(require("./payment.routes"));
const dashboard_routes_1 = __importDefault(require("./dashboard.routes"));
const image_routes_1 = __importDefault(require("./image.routes"));
const test_routes_1 = __importDefault(require("./test.routes"));
const path_1 = __importDefault(require("path"));
const appRouter = (0, express_1.Router)();
const baseRoute = '/api/v1';
// Existing routes
appRouter.use(`${baseRoute}/users`, user_routes_1.default);
appRouter.use(`${baseRoute}/sms`, sms_routes_1.default);
appRouter.use(`${baseRoute}/notifications`, notification_routes_1.default);
appRouter.use(`${baseRoute}/orders`, order_routes_1.default);
// Payment management routes
appRouter.use(`${baseRoute}/payments`, payment_routes_1.default);
// Inventory management routes
appRouter.use(`${baseRoute}/spare-parts`, spare_part_routes_1.default);
appRouter.use(`${baseRoute}/packages`, package_routes_1.default);
appRouter.use(`${baseRoute}/cylinders`, cylinder_routes_1.default);
// Dashboard routes
appRouter.use(`${baseRoute}/dashboard`, dashboard_routes_1.default);
// Image management routes
appRouter.use(`${baseRoute}/images`, image_routes_1.default);
// Test Routes
appRouter.use(test_routes_1.default);
// Health Check Route
appRouter.get('/health', (req, res) => {
    res.send('Service is up and running');
});
// Documentation Route - Serve the HTML documentation file
appRouter.get('/docs', (_req, res) => {
    const documentationPath = path_1.default.join(__dirname, '..', '..', '..', 'Gas_Delivery_System_Documentation.html');
    res.sendFile(documentationPath, err => {
        if (err) {
            console.error('Error serving documentation:', err);
            res.status(404).json({
                status: 'error',
                message: 'Documentation file not found',
                error: err.message,
            });
        }
    });
});
// Client Presentation Route - Serve the client-friendly presentation
appRouter.get('/presentation', (_req, res) => {
    const presentationPath = path_1.default.join(__dirname, '..', '..', '..', 'Gas_Delivery_System_Client_Presentation.html');
    res.sendFile(presentationPath, err => {
        if (err) {
            console.error('Error serving presentation:', err);
            res.status(404).json({
                status: 'error',
                message: 'Presentation file not found',
                error: err.message,
            });
        }
    });
});
// Static routes for legacy images (existing public folder)
const cylinderPath = path_1.default.join(__dirname, '..', '..', 'public', 'images', 'cylinders');
const sparePartPath = path_1.default.join(__dirname, '..', '..', 'public', 'images', 'spare_parts');
const packagePath = path_1.default.join(__dirname, '..', '..', 'public', 'images', 'packages');
// Static routes for uploaded images (new uploads folder)
const uploadsPath = path_1.default.join(__dirname, '..', '..', 'uploads', 'images');
// console.log(`cylinder path: ${cylinderPath}`);
// console.log(`uploads path: ${uploadsPath}`);
appRouter.use(
// '/images/cylinders',
'/api/v1/images/cylinders', express_1.default.static(cylinderPath
// {
// fallthrough: false,
// setHeaders: (res, path) => {
//   if (!fs.existsSync(path)) {
//     res.redirect('/images/cylinders/default.png');
//   }
// },
// }
));
appRouter.use(
// '/images/spare-parts',
'/api/v1/images/spare-parts', express_1.default.static(sparePartPath
// {
// fallthrough: false,
// setHeaders: (res, path) => {
//   if (!fs.existsSync(path)) {
//     res.redirect('/images/spare-parts/default.png');
//   }
// },
// }
));
appRouter.use(
// '/images/packages',
'/api/v1/images/packages', express_1.default.static(packagePath
// {
// fallthrough: false,
// setHeaders: (res, path) => {
//   if (!fs.existsSync(path)) {
//     res.redirect('/images/packages/default.png');
//   }
// },
// }
));
// Serve uploaded images (new system)
appRouter.use('/api/v1/images/images', express_1.default.static(uploadsPath));
exports.default = appRouter;
//# sourceMappingURL=index.js.map