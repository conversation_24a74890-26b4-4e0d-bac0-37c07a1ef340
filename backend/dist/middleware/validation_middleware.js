"use strict";
// import { Request, Response, NextFunction } from 'express';
// import { validationResult } from 'express-validator';
// import { BadRequestError } from '../errors/app_errors';
Object.defineProperty(exports, "__esModule", { value: true });
// /**
//  * Middleware to handle express-validator validation results
//  */
// export const validateRequest = (req: Request, res: Response, next: NextFunction) => {
//   const errors = validationResult(req);
//   if (!errors.isEmpty()) {
//     const errorMessages = errors.array().map(error => ({
//       field: error.type === 'field' ? error.path : 'unknown',
//       message: error.msg,
//       value: error.type === 'field' ? error.value : undefined,
//     }));
//     throw new BadRequestError('Validation failed', {
//       errors: errorMessages,
//     });
//   }
//   next();
// };
//# sourceMappingURL=validation_middleware.js.map