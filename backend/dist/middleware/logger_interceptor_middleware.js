"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loggerInterceptor = void 0;
const logger_1 = __importDefault(require("../config/logger"));
const loggerInterceptor = (req, res, next) => {
    const start = Date.now();
    let requestPayload = {
        timestamp: new Date().toISOString(),
        method: req.method,
        url: req.url,
        // headers: req.headers,
        body: req.body,
        // query: req.query,
        // params: req.params,
        ip: req.ip,
    };
    // Log request details
    logger_1.default.info(`Request: ${JSON.stringify(requestPayload)}`);
    //   // Store the original response functions
    const originalSend = res.send;
    const originalJson = res.json;
    //   // Response interception
    let responsePayload = (body) => {
        return {
            timestamp: new Date().toISOString(),
            status: res.statusCode,
            duration: `${Date.now() - start}ms`,
            //   headers: res.getHeaders(),
            body: body,
        };
    };
    res.send = function (body) {
        // Log response before sending
        logger_1.default.info(`Response: ${JSON.stringify(responsePayload(body))}`);
        return originalSend.apply(res, arguments);
    };
    // res.json = function (body) {
    //   // Log response before sending
    //   logger.info(`Response: ${JSON.stringify(responsePayload(body))}`);
    //   return originalJson.apply(res, arguments);
    // };
    next();
};
exports.loggerInterceptor = loggerInterceptor;
//# sourceMappingURL=logger_interceptor_middleware.js.map