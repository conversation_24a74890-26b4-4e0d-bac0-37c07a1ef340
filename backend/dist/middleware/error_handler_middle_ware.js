"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.globalErrorHandler = void 0;
const app_errors_1 = require("../errors/app_errors");
const response_1 = require("../utils/response");
const logger_1 = __importDefault(require("../config/logger"));
const globalErrorHandler = (err, req, res, _next) => {
    // logger.error(
    // `Error type received: ${typeof err}, constructor: ${err?.constructor?.name}, instance of AppError: ${err instanceof AppError}`
    // );
    logger_1.default.error(`Error type received: ${typeof err}, constructor: ${err?.constructor?.name}, instanceof AppError: ${err instanceof app_errors_1.AppError}, file path: ${__filename}, AppError imported from: ${require.resolve('../errors/app_errors')}`);
    // ✅ Handle custom AppError
    if (isAppError(err)) {
        logger_1.default.warn(`${req.method} ${req.originalUrl} → ${err.statusCode} ${err.message}`);
        (0, response_1.sendResponse)(res, err.statusCode, err.status, err.message, {
            errors: {
                type: err.parentCode, // e.g., VALIDATION_ERROR, NOT_FOUND
                code: err.code || err.name, // e.g., INVALID_PHONE or ValidationError
                details: err.details || undefined,
            },
            stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
        });
        return;
    }
    // ❌ Handle unknown/unexpected errors
    logger_1.default.error(`UNEXPECTED ERROR → ${req.method} ${req.originalUrl}`, err);
    const errorMessage = err instanceof Error ? err.message : 'Internal Server Error';
    (0, response_1.sendResponse)(res, 500, 'error', errorMessage, {
        errors: {
            type: 'INTERNAL_SERVER_ERROR',
            code: err instanceof Error ? err.name : 'UnknownError',
        },
        stack: process.env.NODE_ENV === 'development' && err instanceof Error ? err.stack : undefined,
    });
};
exports.globalErrorHandler = globalErrorHandler;
const isAppError = (err) => {
    return (err instanceof app_errors_1.AppError ||
        (typeof err === 'object' &&
            err !== null &&
            'isOperational' in err &&
            err.isOperational === true &&
            'statusCode' in err &&
            'status' in err &&
            'parentCode' in err));
};
//# sourceMappingURL=error_handler_middle_ware.js.map