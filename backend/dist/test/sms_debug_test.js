"use strict";
/**
 * SMS Debug Test - Quick Issue Identification
 * Run this with: npm run test:sms:debug
 *
 * This test helps quickly identify the SMS service issue
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.debugSmsIssue = debugSmsIssue;
exports.analyzeTestResults = analyzeTestResults;
exports.runDebugTest = runDebugTest;
const sms_services_1 = require("../services/sms.services");
async function debugSmsIssue() {
    console.log('🐛 SMS Debug Test - Issue Analysis');
    console.log('='.repeat(40));
    try {
        // Test 1: Check service health
        console.log('\n1️⃣ Testing service health...');
        const isHealthy = await sms_services_1.smsService.checkHealth();
        console.log(`   Health status: ${isHealthy ? '✅ Healthy' : '❌ Unhealthy'}`);
        // Test 2: Get initial metrics
        console.log('\n2️⃣ Getting initial metrics...');
        const initialMetrics = sms_services_1.smsService.getMetrics();
        console.log(`   Initial metrics:`, initialMetrics);
        // Test 3: Try a simple SMS with detailed error catching
        console.log('\n3️⃣ Attempting simple SMS with detailed error handling...');
        try {
            const result = await sms_services_1.smsService.sendSms('613656021', 'Debug test message', {
                isOtp: false,
                refId: `debug-${Date.now()}`
            });
            console.log('   ✅ SMS sent successfully!');
            console.log(`   📧 Message ID: ${result.messageId}`);
        }
        catch (smsError) {
            console.log('   ❌ SMS failed with detailed error:');
            console.log(`   📛 Error Type: ${smsError.constructor.name}`);
            console.log(`   📛 Error Message: ${smsError.message}`);
            // Check for specific error properties
            if (smsError.code) {
                console.log(`   📛 Error Code: ${smsError.code}`);
            }
            if (smsError.details) {
                console.log(`   📛 Error Details:`, JSON.stringify(smsError.details, null, 2));
            }
            if (smsError.response) {
                console.log(`   📛 HTTP Response:`, JSON.stringify(smsError.response.data, null, 2));
                console.log(`   📛 HTTP Status: ${smsError.response.status}`);
            }
            if (smsError.request) {
                console.log(`   📛 Request Config:`, {
                    url: smsError.config?.url,
                    method: smsError.config?.method,
                    headers: smsError.config?.headers,
                    data: smsError.config?.data,
                });
            }
            // Check if it's a network error
            if (smsError.code === 'ECONNABORTED') {
                console.log('   🌐 This appears to be a timeout error');
            }
            else if (smsError.code === 'ENOTFOUND') {
                console.log('   🌐 This appears to be a DNS/network error');
            }
            else if (smsError.code === 'ECONNREFUSED') {
                console.log('   🌐 This appears to be a connection refused error');
            }
        }
        // Test 4: Get final metrics
        console.log('\n4️⃣ Getting final metrics...');
        const finalMetrics = sms_services_1.smsService.getMetrics();
        console.log(`   Final metrics:`, finalMetrics);
        // Test 5: Try OTP SMS (which seemed to work in your tests)
        console.log('\n5️⃣ Testing OTP SMS (which worked before)...');
        try {
            const otpResult = await sms_services_1.smsService.sendSms('613656021', 'Your OTP is: 123456', {
                isOtp: true,
                refId: `debug-otp-${Date.now()}`
            });
            console.log('   ✅ OTP SMS sent successfully!');
            console.log(`   📧 Message ID: ${otpResult.messageId}`);
        }
        catch (otpError) {
            console.log('   ❌ OTP SMS also failed:');
            console.log(`   📛 Error: ${otpError.message}`);
        }
    }
    catch (error) {
        console.log('💥 Debug test failed completely:', error.message);
    }
    console.log('\n' + '='.repeat(40));
    console.log('🎯 Debug Test Completed');
    console.log('='.repeat(40));
}
// Analysis based on your test results
function analyzeTestResults() {
    console.log('\n📊 ANALYSIS OF YOUR TEST RESULTS:');
    console.log('='.repeat(50));
    console.log('\n✅ WHAT WORKED:');
    console.log('• Service health check ✅');
    console.log('• Service metrics retrieval ✅');
    console.log('• OTP SMS sending ✅ (Message ID: 6397e947-d238-4083-b52b-ce5880cab11f)');
    console.log('• Phone number validation ✅');
    console.log('• Message validation ✅');
    console.log('• Some bulk SMS messages ✅');
    console.log('\n❌ WHAT FAILED:');
    console.log('• Single SMS Sending ❌ (Unexpected SMS service error)');
    console.log('• Some bulk SMS messages ❌ (HTTP 500 errors)');
    console.log('\n🔍 POSSIBLE CAUSES:');
    console.log('1. 📝 Message content/length issues');
    console.log('2. 🔧 Sender ID configuration');
    console.log('3. 🌐 API endpoint differences');
    console.log('4. ⏰ Rate limiting or timing issues');
    console.log('5. 📊 Request payload format differences');
    console.log('\n💡 RECOMMENDATIONS:');
    console.log('1. Run: npm run test:sms:diagnostic');
    console.log('2. Check if OTP vs regular SMS use different endpoints');
    console.log('3. Compare successful vs failed request payloads');
    console.log('4. Verify sender ID configuration');
    console.log('5. Check message content encoding');
}
// Main function
async function runDebugTest() {
    await debugSmsIssue();
    analyzeTestResults();
}
// Run if executed directly
if (require.main === module) {
    runDebugTest();
}
//# sourceMappingURL=sms_debug_test.js.map