"use strict";
/**
 * Email Configuration Test
 * Quick test to verify email configuration is working
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.testEmailConfig = testEmailConfig;
const env_config_1 = require("../config/env_config");
const nodemailer_1 = __importDefault(require("nodemailer"));
async function testEmailConfig() {
    console.log('🔧 Testing Email Configuration...\n');
    // Display current configuration
    console.log('📋 Current Email Configuration:');
    console.log('Environment:', env_config_1.config.server.env);
    console.log('Host:', env_config_1.config.email?.host);
    console.log('Port:', env_config_1.config.email?.port);
    console.log('Secure:', env_config_1.config.email?.secure);
    console.log('User:', env_config_1.config.email?.user);
    console.log('From:', env_config_1.config.email?.from);
    console.log('Password Length:', env_config_1.config.email?.pass?.length || 0, 'characters');
    console.log('');
    // Test transporter creation
    try {
        console.log('🔌 Creating email transporter...');
        const transporter = nodemailer_1.default.createTransport({
            host: env_config_1.config.email?.host,
            port: env_config_1.config.email?.port,
            secure: env_config_1.config.email?.secure,
            auth: {
                user: env_config_1.config.email?.user,
                pass: env_config_1.config.email?.pass,
            },
        });
        console.log('✅ Transporter created successfully');
        // Test connection
        console.log('🔍 Testing SMTP connection...');
        const isConnected = await transporter.verify();
        if (isConnected) {
            console.log('✅ SMTP connection successful!');
            // Send test email
            console.log('📧 Sending test <NAME_EMAIL>...');
            const result = await transporter.sendMail({
                from: env_config_1.config.email?.from,
                to: '<EMAIL>',
                subject: '🔥 Ciribey Gas Delivery - Configuration Test',
                html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: linear-gradient(135deg, #4caf50 0%, #45a049 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0;">
              <h1 style="margin: 0;">✅ Configuration Test Successful!</h1>
              <p style="margin: 5px 0 0 0; opacity: 0.9;">Ciribey Gas Delivery Email Service</p>
            </div>
            
            <div style="background: white; padding: 30px; border: 1px solid #e0e0e0; border-top: none; border-radius: 0 0 10px 10px;">
              <h2 style="color: #2c3e50; margin-top: 0;">🎉 Email Service Working!</h2>
              
              <p>Great news! Your email configuration is working perfectly.</p>
              
              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #495057; margin-top: 0;">📊 Configuration Details:</h3>
                <ul style="color: #6c757d;">
                  <li><strong>Environment:</strong> ${env_config_1.config.server.env}</li>
                  <li><strong>SMTP Host:</strong> ${env_config_1.config.email?.host}</li>
                  <li><strong>SMTP Port:</strong> ${env_config_1.config.email?.port}</li>
                  <li><strong>Secure:</strong> ${env_config_1.config.email?.secure}</li>
                  <li><strong>From Address:</strong> ${env_config_1.config.email?.from}</li>
                  <li><strong>Test Time:</strong> ${new Date().toISOString()}</li>
                </ul>
              </div>
              
              <div style="background: #e8f5e8; border: 1px solid #4caf50; padding: 15px; border-radius: 5px;">
                <p style="margin: 0; color: #2e7d32;">
                  <strong>✅ Ready for Production!</strong> Your email service is configured correctly and ready to send notifications.
                </p>
              </div>
            </div>
            
            <div style="background: #f5f5f5; padding: 15px; text-align: center; border-radius: 0 0 10px 10px;">
              <p style="margin: 0; color: #666; font-size: 12px;">
                Mahadsanid - Kooxda Ciribey Gas Delivery<br>
                📞 +252613656021 | 📧 <EMAIL>
              </p>
            </div>
          </div>
        `
            });
            console.log('✅ Test email sent successfully!');
            console.log('📧 Message ID:', result.messageId);
            console.log('📬 Check <EMAIL> for the test email');
        }
        else {
            console.log('❌ SMTP connection failed');
        }
    }
    catch (error) {
        console.error('❌ Email configuration test failed:');
        console.error('Error:', error.message);
        if (error.message.includes('Invalid login')) {
            console.log('\n🔧 Troubleshooting Tips:');
            console.log('1. Verify Gmail App Password is correct');
            console.log('2. Ensure 2-Factor Authentication is enabled on Gmail');
            console.log('3. Check that "Less secure app access" is disabled (use App Password instead)');
            console.log('4. Verify the Gmail account email address is correct');
        }
        if (error.message.includes('ECONNREFUSED')) {
            console.log('\n🔧 Troubleshooting Tips:');
            console.log('1. Check internet connection');
            console.log('2. Verify SMTP host and port settings');
            console.log('3. Check if firewall is blocking SMTP connections');
        }
    }
}
// Run if executed directly
if (require.main === module) {
    testEmailConfig();
}
//# sourceMappingURL=email_config_test.js.map