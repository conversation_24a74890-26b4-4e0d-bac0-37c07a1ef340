{"version": 3, "file": "message_sanitization_test.js", "sourceRoot": "", "sources": ["../../src/test/message_sanitization_test.ts"], "names": [], "mappings": ";;AAuSA,sDAgCC;AAvUD,0EAAqE;AAErE;;;GAGG;AACH,MAAM,yBAAyB;IACrB,cAAc,CAAoB;IAClC,WAAW,GAAsD;QACvE,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,CAAC;QACT,KAAK,EAAE,CAAC;KACT,CAAC;IAEF;QACE,IAAI,CAAC,cAAc,GAAG,IAAI,uCAAiB,EAAE,CAAC;IAChD,CAAC;IAEO,WAAW,CAAC,MAAc,EAAE,QAAgB,EAAE,QAAgB;QACpE,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QACzB,IAAI,MAAM,KAAK,QAAQ,EAAE,CAAC;YACxB,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC,CAAC;QAC/B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC,CAAC;YAC7B,OAAO,CAAC,GAAG,CAAC,iBAAiB,QAAQ,GAAG,CAAC,CAAC;YAC1C,OAAO,CAAC,GAAG,CAAC,iBAAiB,MAAM,GAAG,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,IAAY;QAClC,OAAQ,IAAI,CAAC,cAAsB,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;IACnE,CAAC;IAED,QAAQ;QACN,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAE1D,2BAA2B;QAC3B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,iCAAiC;QACjC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,6BAA6B,EAAE,CAAC;QACrC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,oCAAoC;QACpC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAEhC,aAAa;QACb,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,0BAA0B;QAC1B,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,+BAA+B,EAAE,CAAC;QAEvC,oBAAoB;QACpB,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAEhC,oBAAoB;QACpB,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAClC,IAAI,CAAC,iCAAiC,EAAE,CAAC;QACzC,IAAI,CAAC,+BAA+B,EAAE,CAAC;QACvC,IAAI,CAAC,8BAA8B,EAAE,CAAC;QAEtC,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAEO,gBAAgB;QACtB,MAAM,KAAK,GAAG,wEAAwE,CAAC;QACvF,MAAM,QAAQ,GAAG,0DAA0D,CAAC;QAC5E,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,uCAAuC,CAAC,CAAC;IACnG,CAAC;IAEO,iBAAiB;QACvB,MAAM,KAAK,GAAG,yCAAyC,CAAC;QACxD,MAAM,QAAQ,GAAG,yCAAyC,CAAC;QAC3D,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,iDAAiD,CAAC,CAAC;IAC7G,CAAC;IAEO,iBAAiB;QACvB,MAAM,KAAK,GAAG,yDAAyD,CAAC;QACxE,MAAM,QAAQ,GAAG,yDAAyD,CAAC;QAC3E,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,8CAA8C,CAAC,CAAC;IAC1G,CAAC;IAEO,YAAY;QAClB,MAAM,KAAK,GAAG,qCAAqC,CAAC;QACpD,MAAM,QAAQ,GAAG,yCAAyC,CAAC;QAC3D,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,mCAAmC,CAAC,CAAC;IAC/F,CAAC;IAEO,oBAAoB;QAC1B,MAAM,KAAK,GAAG,qCAAqC,CAAC;QACpD,MAAM,QAAQ,GAAG,qCAAqC,CAAC;QACvD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,4CAA4C,CAAC,CAAC;IACxG,CAAC;IAEO,oBAAoB;QAC1B,MAAM,KAAK,GAAG,kCAAkC,CAAC;QACjD,MAAM,QAAQ,GAAG,wBAAwB,CAAC;QAC1C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,oDAAoD,CAAC,CAAC;IAChH,CAAC;IAEO,6BAA6B;QACnC,MAAM,KAAK,GAAG,oCAAoC,CAAC;QACnD,MAAM,QAAQ,GAAG,wBAAwB,CAAC;QAC1C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,mDAAmD,CAAC,CAAC;IAC/G,CAAC;IAEO,qBAAqB;QAC3B,MAAM,KAAK,GAAG,2BAA2B,CAAC;QAC1C,MAAM,QAAQ,GAAG,iBAAiB,CAAC;QACnC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,+CAA+C,CAAC,CAAC;IAC3G,CAAC;IAEO,qBAAqB;QAC3B,MAAM,KAAK,GAAG,uBAAuB,CAAC;QACtC,MAAM,QAAQ,GAAG,iBAAiB,CAAC;QACnC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,yCAAyC,CAAC,CAAC;IACrG,CAAC;IAEO,sBAAsB;QAC5B,MAAM,KAAK,GAAG,qDAAqD,CAAC;QACpE,MAAM,QAAQ,GAAG,wDAAwD,CAAC;QAC1E,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,yCAAyC,CAAC,CAAC;IACrG,CAAC;IAEO,wBAAwB;QAC9B,MAAM,KAAK,GAAG,8DAA8D,CAAC;QAC7E,MAAM,QAAQ,GAAG,wDAAwD,CAAC;QAC1E,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,8BAA8B,CAAC,CAAC;IAC1F,CAAC;IAEO,eAAe;QACrB,MAAM,KAAK,GAAG,EAAE,CAAC;QACjB,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAAC;IACjF,CAAC;IAEO,aAAa;QACnB,MAAM,KAAK,GAAG,IAAW,CAAC;QAC1B,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,mBAAmB,CAAC,CAAC;IAC/E,CAAC;IAEO,kBAAkB;QACxB,MAAM,KAAK,GAAG,SAAgB,CAAC;QAC/B,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,wBAAwB,CAAC,CAAC;IACpF,CAAC;IAEO,cAAc;QACpB,MAAM,KAAK,GAAG,cAAc,CAAC;QAC7B,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,gCAAgC,CAAC,CAAC;IAC5F,CAAC;IAEO,kBAAkB;QACxB,MAAM,KAAK,GAAG,iBAAiB,CAAC;QAChC,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,oCAAoC,CAAC,CAAC;IAChG,CAAC;IAEO,uBAAuB;QAC7B,MAAM,KAAK,GAAG,0GAA0G,CAAC;QACzH,MAAM,QAAQ,GAAG,oFAAoF,CAAC;QACtG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,8BAA8B,CAAC,CAAC;IAC1F,CAAC;IAEO,+BAA+B;QACrC,MAAM,KAAK,GAAG,iEAAiE,CAAC;QAChF,MAAM,QAAQ,GAAG,6DAA6D,CAAC;QAC/E,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,8CAA8C,CAAC,CAAC;IAC1G,CAAC;IAEO,wBAAwB;QAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,8CAA8C,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACxG,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAClD,MAAM,OAAO,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAElC,MAAM,QAAQ,GAAG,sCAAsC,CAAC;QACxD,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,EAAE,+BAA+B,CAAC,CAAC;QAEvE,IAAI,OAAO,GAAG,SAAS,GAAG,GAAG,EAAE,CAAC;YAC9B,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAChE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,OAAO,GAAG,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAC9F,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAEO,wBAAwB;QAC9B,MAAM,KAAK,GAAG,wEAAwE,CAAC;QACvF,MAAM,UAAU,GAAG,IAAI,CAAC;QACxB,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;QAED,MAAM,OAAO,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAClC,MAAM,WAAW,GAAG,CAAC,OAAO,GAAG,SAAS,CAAC,GAAG,UAAU,CAAC;QAEvD,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,uEAAuE,CAAC,CAAC;QACvF,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,oDAAoD,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC;QAC7G,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAEO,0BAA0B;QAChC,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9F,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QACxD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAEO,iCAAiC;QACvC,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QACpE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACrE,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;QAC/D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;QAC/D,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAEO,+BAA+B;QACrC,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QACvE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5E,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAEO,8BAA8B;QACpC,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QAClF,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1E,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAC5D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAC5D,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAEO,YAAY;QAClB,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAClC,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAEvG,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,mEAAmE,CAAC,CAAC;QACnF,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;CACF;AAED;;GAEG;AACH,SAAgB,qBAAqB;IACnC,MAAM,cAAc,GAAG,IAAI,uCAAiB,EAAE,CAAC;IAC/C,MAAM,SAAS,GAAG;QAChB,wEAAwE;QACxE,yCAAyC;QACzC,yDAAyD;QACzD,qCAAqC;QACrC,qCAAqC;QACrC,oCAAoC;QACpC,qDAAqD;QACrD,8DAA8D;KAC/D,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;IAE/D,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;QACpC,MAAM,UAAU,GAAG,KAAK,CAAC;QACzB,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,cAAsB,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAC3D,CAAC;QAED,MAAM,OAAO,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAClC,MAAM,SAAS,GAAG,OAAO,GAAG,SAAS,CAAC;QACtC,MAAM,WAAW,GAAG,SAAS,GAAG,UAAU,CAAC;QAE3C,OAAO,CAAC,GAAG,CAAC,aAAa,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;QACvC,OAAO,CAAC,GAAG,CAAC,aAAa,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC3F,OAAO,CAAC,GAAG,CAAC,mBAAmB,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC;QACzE,OAAO,CAAC,GAAG,CAAC,iBAAiB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,UAAU,eAAe,CAAC,CAAC;IACxF,CAAC,CAAC,CAAC;AACL,CAAC;AAED,8CAA8C;AAC9C,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,MAAM,MAAM,GAAG,IAAI,yBAAyB,EAAE,CAAC;IAC/C,MAAM,CAAC,QAAQ,EAAE,CAAC;IAElB,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAE1C,qBAAqB,EAAE,CAAC;AAC1B,CAAC"}