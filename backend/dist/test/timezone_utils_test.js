"use strict";
/**
 * Timezone Management System Test
 * Comprehensive testing for timezone-aware date filtering and conversion
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.testTimezoneDetection = testTimezoneDetection;
exports.testDayBoundaries = testDayBoundaries;
exports.testDateRangeConversion = testDateRangeConversion;
exports.testMongoDBQueryBuilding = testMongoDBQueryBuilding;
exports.testDateFormatting = testDateFormatting;
exports.testRealWorldScenarios = testRealWorldScenarios;
exports.testEdgeCases = testEdgeCases;
exports.testServiceIntegrations = testServiceIntegrations;
exports.testComprehensiveScenarios = testComprehensiveScenarios;
exports.runAllTimezoneTests = runAllTimezoneTests;
const timezone_utils_1 = require("../utils/timezone_utils");
const enums_1 = require("../enums/enums");
/**
 * Test timezone detection and user timezone resolution
 */
async function testTimezoneDetection() {
    console.log('🌍 Testing Timezone Detection...\n');
    try {
        // Test default timezone
        console.log('1. Testing default timezone:');
        const defaultTz = timezone_utils_1.timezoneManager.getUserTimezone();
        console.log(`✅ Default timezone: ${defaultTz}`);
        console.log(`✅ Matches app timezone: ${defaultTz === timezone_utils_1.APP_TIMEZONE}`);
        // Test with client timezone
        console.log('\n2. Testing client timezone override:');
        const context = {
            clientTimezone: 'America/New_York',
            role: enums_1.UserRole.CUSTOMER,
        };
        const clientTz = timezone_utils_1.timezoneManager.getUserTimezone(context);
        console.log(`✅ Client timezone: ${clientTz}`);
        console.log(`✅ Uses client timezone: ${clientTz === 'America/New_York'}`);
        // Test with invalid timezone (should fallback)
        console.log('\n3. Testing invalid timezone fallback:');
        const invalidContext = {
            clientTimezone: 'Invalid/Timezone',
            role: enums_1.UserRole.ADMIN,
        };
        const fallbackTz = timezone_utils_1.timezoneManager.getUserTimezone(invalidContext);
        console.log(`✅ Fallback timezone: ${fallbackTz}`);
        console.log(`✅ Falls back to app timezone: ${fallbackTz === timezone_utils_1.APP_TIMEZONE}`);
        console.log('\n✅ Timezone detection tests passed!');
    }
    catch (error) {
        console.error('❌ Timezone detection test failed:', error.message);
    }
}
/**
 * Test day boundaries calculation
 */
async function testDayBoundaries() {
    console.log('\n📅 Testing Day Boundaries...\n');
    try {
        // Test today's boundaries
        console.log("1. Testing today's boundaries:");
        // const today = new Date('2025-08-05T15:30:00.000Z'); // 3:30 PM UTC
        const today = new Date(); // This will give the current date and time
        console.log(`✅ Today's date: ${today}`);
        console.log(`✅ Today's date toISOString: ${today.toISOString()}`);
        const boundaries = timezone_utils_1.timezoneManager.getDayBoundaries(today);
        console.log(`✅ Input date (UTC): ${today.toISOString()}`);
        console.log(`✅ Start boundary (UTC): ${boundaries.start.toISOString()}`);
        console.log(`✅ End boundary (UTC): ${boundaries.end.toISOString()}`);
        console.log(`✅ Timezone: ${boundaries.timezone}`);
        // Verify boundaries are for the same day in Mogadishu time
        const mogadishuStart = new Date(boundaries.start.toLocaleString('en-US', { timeZone: timezone_utils_1.APP_TIMEZONE }));
        const mogadishuEnd = new Date(boundaries.end.toLocaleString('en-US', { timeZone: timezone_utils_1.APP_TIMEZONE }));
        console.log(`✅ Mogadishu start: ${mogadishuStart.toISOString()}`);
        console.log(`✅ Mogadishu end: ${mogadishuEnd.toISOString()}`);
        // Test role-specific boundaries
        console.log('\n2. Testing role-specific boundaries:');
        const supervisorBoundaries = timezone_utils_1.timezoneManager.getTodayBoundariesForRole(enums_1.UserRole.SUPERVISOR);
        console.log(`✅ Supervisor today start: ${supervisorBoundaries.start.toISOString()}`);
        console.log(`✅ Supervisor today end: ${supervisorBoundaries.end.toISOString()}`);
        console.log('\n✅ Day boundaries tests passed!');
    }
    catch (error) {
        console.error('❌ Day boundaries test failed:', error.message);
    }
}
/**
 * Test date range conversion
 */
async function testDateRangeConversion() {
    console.log('\n🔄 Testing Date Range Conversion...\n');
    try {
        // Test date range conversion
        console.log('1. Testing date range conversion:');
        const startDate = '2025-08-01';
        const endDate = '2025-08-05';
        const context = {
            role: enums_1.UserRole.ADMIN,
        };
        const dateRange = timezone_utils_1.timezoneManager.convertDateRangeToUTC(startDate, endDate, context);
        if (dateRange) {
            console.log(`✅ Original start: ${startDate}`);
            console.log(`✅ Original end: ${endDate}`);
            console.log(`✅ UTC start: ${dateRange.start.toISOString()}`);
            console.log(`✅ UTC end: ${dateRange.end.toISOString()}`);
            console.log(`✅ Timezone: ${dateRange.timezone}`);
        }
        // Test with only start date
        console.log('\n2. Testing with only start date:');
        const startOnlyRange = timezone_utils_1.timezoneManager.convertDateRangeToUTC('2025-08-01', undefined, context);
        if (startOnlyRange) {
            console.log(`✅ Start only - UTC start: ${startOnlyRange.start.toISOString()}`);
            console.log(`✅ Start only - UTC end: ${startOnlyRange.end.toISOString()}`);
        }
        // Test with only end date
        console.log('\n3. Testing with only end date:');
        const endOnlyRange = timezone_utils_1.timezoneManager.convertDateRangeToUTC(undefined, '2025-08-05', context);
        if (endOnlyRange) {
            console.log(`✅ End only - UTC start: ${endOnlyRange.start.toISOString()}`);
            console.log(`✅ End only - UTC end: ${endOnlyRange.end.toISOString()}`);
        }
        console.log('\n✅ Date range conversion tests passed!');
    }
    catch (error) {
        console.error('❌ Date range conversion test failed:', error.message);
    }
}
/**
 * Test MongoDB query building
 */
async function testMongoDBQueryBuilding() {
    console.log('\n🗄️ Testing MongoDB Query Building...\n');
    try {
        // Test date query building
        console.log('1. Testing date query building:');
        const context = {
            role: enums_1.UserRole.SUPERVISOR,
        };
        const query = timezone_utils_1.timezoneManager.buildDateQuery('2025-08-01', '2025-08-05', context);
        if (query) {
            console.log(`✅ Generated query:`, JSON.stringify(query, null, 2));
            console.log(`✅ Has createdAt filter: ${!!query.createdAt}`);
            console.log(`✅ Has $gte operator: ${!!query.createdAt?.$gte}`);
            console.log(`✅ Has $lte operator: ${!!query.createdAt?.$lte}`);
        }
        // Test with no dates (should return null)
        console.log('\n2. Testing with no dates:');
        const emptyQuery = timezone_utils_1.timezoneManager.buildDateQuery(undefined, undefined, context);
        console.log(`✅ Empty query result: ${emptyQuery}`);
        console.log(`✅ Returns null for no dates: ${emptyQuery === null}`);
        console.log('\n✅ MongoDB query building tests passed!');
    }
    catch (error) {
        console.error('❌ MongoDB query building test failed:', error.message);
    }
}
/**
 * Test date formatting for users
 */
async function testDateFormatting() {
    console.log('\n📝 Testing Date Formatting...\n');
    try {
        // Test date formatting
        console.log('1. Testing date formatting:');
        const utcDate = new Date('2025-08-05T12:30:00.000Z');
        const context = {
            role: enums_1.UserRole.CUSTOMER,
        };
        const formatted = timezone_utils_1.timezoneManager.formatDateForUser(utcDate, context);
        console.log(`✅ UTC date: ${utcDate.toISOString()}`);
        console.log(`✅ Formatted for user: ${formatted}`);
        // Test with custom formatting options
        console.log('\n2. Testing custom formatting:');
        const customFormatted = timezone_utils_1.timezoneManager.formatDateForUser(utcDate, context, {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
        console.log(`✅ Custom formatted: ${customFormatted}`);
        console.log('\n✅ Date formatting tests passed!');
    }
    catch (error) {
        console.error('❌ Date formatting test failed:', error.message);
    }
}
/**
 * Test real-world scenarios
 */
async function testRealWorldScenarios() {
    console.log('\n🌟 Testing Real-World Scenarios...\n');
    try {
        // Scenario 1: Supervisor viewing today's orders
        console.log("1. Scenario: Supervisor viewing today's orders");
        const supervisorContext = {
            userId: 'supervisor123',
            role: enums_1.UserRole.SUPERVISOR,
        };
        const todayOrders = timezone_utils_1.timezoneManager.getTodayBoundariesForRole(enums_1.UserRole.SUPERVISOR, supervisorContext);
        console.log(`✅ Supervisor today filter:`);
        console.log(`   Start: ${todayOrders.start.toISOString()}`);
        console.log(`   End: ${todayOrders.end.toISOString()}`);
        console.log(`   Timezone: ${todayOrders.timezone}`);
        // Scenario 2: Admin filtering orders by date range
        console.log('\n2. Scenario: Admin filtering orders by date range');
        const adminContext = {
            userId: 'admin123',
            role: enums_1.UserRole.ADMIN,
        };
        const adminQuery = timezone_utils_1.timezoneManager.buildDateQuery('2025-08-01', '2025-08-07', adminContext);
        console.log(`✅ Admin date filter query:`, JSON.stringify(adminQuery, null, 2));
        // Scenario 3: Agent viewing earnings for this week
        console.log('\n3. Scenario: Agent viewing earnings for this week');
        const agentContext = {
            userId: 'agent123',
            role: enums_1.UserRole.AGENT,
        };
        const weekStart = new Date();
        weekStart.setDate(weekStart.getDate() - 7);
        const weekBoundaries = timezone_utils_1.timezoneManager.getDayBoundaries(weekStart, agentContext);
        console.log(`✅ Agent week start boundary:`);
        console.log(`   Start: ${weekBoundaries.start.toISOString()}`);
        console.log(`   End: ${weekBoundaries.end.toISOString()}`);
        // Scenario 4: Customer viewing order history
        console.log('\n4. Scenario: Customer viewing order history');
        const customerContext = {
            userId: 'customer123',
            role: enums_1.UserRole.CUSTOMER,
        };
        const lastMonth = new Date();
        lastMonth.setMonth(lastMonth.getMonth() - 1);
        const customerQuery = timezone_utils_1.timezoneManager.buildDateQuery(lastMonth, new Date(), customerContext);
        console.log(`✅ Customer history query:`, JSON.stringify(customerQuery, null, 2));
        console.log('\n✅ Real-world scenarios tests passed!');
    }
    catch (error) {
        console.error('❌ Real-world scenarios test failed:', error.message);
    }
}
/**
 * Test timezone edge cases
 */
async function testEdgeCases() {
    console.log('\n⚠️ Testing Edge Cases...\n');
    try {
        // Test daylight saving time boundaries (if applicable)
        console.log('1. Testing DST boundaries:');
        const dstDate = new Date('2025-03-30T01:30:00.000Z'); // Around DST change
        const dstBoundaries = timezone_utils_1.timezoneManager.getDayBoundaries(dstDate);
        console.log(`✅ DST date boundaries:`);
        console.log(`   Start: ${dstBoundaries.start.toISOString()}`);
        console.log(`   End: ${dstBoundaries.end.toISOString()}`);
        // Test year boundary
        console.log('\n2. Testing year boundary:');
        const yearEnd = new Date('2024-12-31T23:30:00.000Z');
        const yearBoundaries = timezone_utils_1.timezoneManager.getDayBoundaries(yearEnd);
        console.log(`✅ Year end boundaries:`);
        console.log(`   Start: ${yearBoundaries.start.toISOString()}`);
        console.log(`   End: ${yearBoundaries.end.toISOString()}`);
        // Test leap year
        console.log('\n3. Testing leap year:');
        const leapDay = new Date('2024-02-29T12:00:00.000Z');
        const leapBoundaries = timezone_utils_1.timezoneManager.getDayBoundaries(leapDay);
        console.log(`✅ Leap day boundaries:`);
        console.log(`   Start: ${leapBoundaries.start.toISOString()}`);
        console.log(`   End: ${leapBoundaries.end.toISOString()}`);
        console.log('\n✅ Edge cases tests passed!');
    }
    catch (error) {
        console.error('❌ Edge cases test failed:', error.message);
    }
}
/**
 * Run all timezone management tests
 */
async function runAllTimezoneTests() {
    console.log('🔥 Ciribey Gas Delivery - Timezone Management Test Suite\n');
    console.log('='.repeat(70));
    await testTimezoneDetection();
    await testDayBoundaries();
    await testDateRangeConversion();
    await testMongoDBQueryBuilding();
    await testDateFormatting();
    await testRealWorldScenarios();
    await testEdgeCases();
    await testServiceIntegrations();
    await testComprehensiveScenarios();
    console.log('\n' + '='.repeat(70));
    console.log('✅ All timezone management tests completed!');
    console.log('\n📊 Test Summary:');
    console.log('- Timezone Detection: ✅ Working');
    console.log('- Day Boundaries: ✅ Working');
    console.log('- Date Range Conversion: ✅ Working');
    console.log('- MongoDB Query Building: ✅ Working');
    console.log('- Date Formatting: ✅ Working');
    console.log('- Real-World Scenarios: ✅ Working');
    console.log('- Edge Cases: ✅ Working');
    console.log('- Service Integrations: ✅ Working');
    console.log('- Comprehensive Scenarios: ✅ Working');
    console.log('\n🌍 Timezone management system is ready for global deployment!');
    console.log(`🇸🇴 Primary timezone: ${timezone_utils_1.APP_TIMEZONE} (East Africa Time)`);
    console.log('🚀 Server can be deployed anywhere - users will see correct local times!');
    console.log('\n🎯 Service Coverage:');
    console.log('- Payment Service: ✅ Expiry date formatting');
    console.log('- Notification Service: ✅ Timestamp formatting & statistics');
    console.log('- User Service: ✅ Registration date formatting & analytics');
    console.log('- Order Service: ✅ Date filtering & queries');
    console.log('- Dashboard Service: ✅ All analytics & boundaries');
}
/**
 * Test service integrations with timezone management
 */
async function testServiceIntegrations() {
    console.log('\n🔧 Testing Service Integrations...\n');
    try {
        // Test Payment Service timezone formatting
        console.log('1. Testing Payment Service timezone formatting:');
        const paymentExpiryDate = new Date('2025-08-05T18:30:00.000Z');
        // Mock payment service method (would normally import from payment service)
        const mockFormatPaymentExpiry = (date) => {
            const context = { role: enums_1.UserRole.CUSTOMER };
            return timezone_utils_1.timezoneManager.formatDateForUser(date, context, {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                timeZoneName: 'short',
            });
        };
        const formattedExpiry = mockFormatPaymentExpiry(paymentExpiryDate);
        console.log(`✅ Payment expiry (UTC): ${paymentExpiryDate.toISOString()}`);
        console.log(`✅ Payment expiry (Mogadishu): ${formattedExpiry}`);
        // Test Notification Service timezone formatting
        console.log('\n2. Testing Notification Service timezone formatting:');
        const notificationDate = new Date('2025-08-05T14:15:00.000Z');
        const mockFormatNotificationTime = (date) => {
            const context = { role: enums_1.UserRole.CUSTOMER };
            return timezone_utils_1.timezoneManager.formatDateForUser(date, context, {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                timeZoneName: 'short',
            });
        };
        const formattedNotification = mockFormatNotificationTime(notificationDate);
        console.log(`✅ Notification time (UTC): ${notificationDate.toISOString()}`);
        console.log(`✅ Notification time (Mogadishu): ${formattedNotification}`);
        // Test User Service timezone formatting
        console.log('\n3. Testing User Service timezone formatting:');
        const registrationDate = new Date('2025-08-01T09:30:00.000Z');
        const mockFormatRegistrationDate = (date, role) => {
            const context = { role };
            return timezone_utils_1.timezoneManager.formatDateForUser(date, context, {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                timeZoneName: 'short',
            });
        };
        const formattedRegistration = mockFormatRegistrationDate(registrationDate, enums_1.UserRole.CUSTOMER);
        console.log(`✅ Registration date (UTC): ${registrationDate.toISOString()}`);
        console.log(`✅ Registration date (Mogadishu): ${formattedRegistration}`);
        // Test date filtering for user statistics
        console.log('\n4. Testing User Service date filtering:');
        const statsDateRange = timezone_utils_1.timezoneManager.buildDateQuery('2025-08-01', '2025-08-07', {
            role: enums_1.UserRole.ADMIN,
        });
        console.log(`✅ User stats date query:`, JSON.stringify(statsDateRange, null, 2));
        console.log('\n✅ Service integrations tests passed!');
    }
    catch (error) {
        console.error('❌ Service integrations test failed:', error.message);
    }
}
/**
 * Test comprehensive timezone scenarios
 */
async function testComprehensiveScenarios() {
    console.log('\n🌟 Testing Comprehensive Timezone Scenarios...\n');
    try {
        // Scenario 1: Payment expiry in different timezones
        console.log('1. Scenario: Payment expiry handling');
        const paymentCreated = new Date('2025-08-05T12:00:00.000Z'); // 12:00 UTC
        const paymentExpiry = new Date(paymentCreated.getTime() + 24 * 60 * 60 * 1000); // +24 hours
        const mogadishuExpiry = timezone_utils_1.timezoneManager.formatDateForUser(paymentExpiry, { role: enums_1.UserRole.CUSTOMER }, {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            timeZoneName: 'short',
        });
        console.log(`✅ Payment created (UTC): ${paymentCreated.toISOString()}`);
        console.log(`✅ Payment expires (UTC): ${paymentExpiry.toISOString()}`);
        console.log(`✅ Payment expires (Mogadishu): ${mogadishuExpiry}`);
        // Scenario 2: Notification delivery times
        console.log('\n2. Scenario: Notification delivery times');
        const notificationTimes = [
            new Date('2025-08-05T06:00:00.000Z'), // 6 AM UTC = 9 AM Mogadishu
            new Date('2025-08-05T12:00:00.000Z'), // 12 PM UTC = 3 PM Mogadishu
            new Date('2025-08-05T18:00:00.000Z'), // 6 PM UTC = 9 PM Mogadishu
        ];
        notificationTimes.forEach((time, index) => {
            const formatted = timezone_utils_1.timezoneManager.formatDateForUser(time, { role: enums_1.UserRole.CUSTOMER }, { hour: '2-digit', minute: '2-digit', timeZoneName: 'short' });
            console.log(`✅ Notification ${index + 1}: ${time.toISOString()} → ${formatted}`);
        });
        // Scenario 3: User registration analytics
        console.log('\n3. Scenario: User registration analytics');
        const today = new Date();
        const todayBoundaries = timezone_utils_1.timezoneManager.getDayBoundaries(today, { role: enums_1.UserRole.ADMIN });
        console.log(`✅ Today's registration window (Mogadishu time):`);
        console.log(`   Start: ${todayBoundaries.start.toISOString()}`);
        console.log(`   End: ${todayBoundaries.end.toISOString()}`);
        console.log(`   Timezone: ${todayBoundaries.timezone}`);
        // Scenario 4: Cross-service timezone consistency
        console.log('\n4. Scenario: Cross-service timezone consistency');
        const testDate = new Date('2025-08-05T15:30:00.000Z');
        const contexts = [
            { service: 'Payment', role: enums_1.UserRole.CUSTOMER },
            { service: 'Notification', role: enums_1.UserRole.CUSTOMER },
            { service: 'User', role: enums_1.UserRole.ADMIN },
            { service: 'Dashboard', role: enums_1.UserRole.SUPERVISOR },
        ];
        contexts.forEach(({ service, role }) => {
            const formatted = timezone_utils_1.timezoneManager.formatDateForUser(testDate, { role }, { hour: '2-digit', minute: '2-digit', timeZoneName: 'short' });
            console.log(`✅ ${service} Service (${role}): ${formatted}`);
        });
        console.log('\n✅ Comprehensive scenarios tests passed!');
    }
    catch (error) {
        console.error('❌ Comprehensive scenarios test failed:', error.message);
    }
}
// Run tests if this file is executed directly
if (require.main === module) {
    runAllTimezoneTests();
}
//# sourceMappingURL=timezone_utils_test.js.map