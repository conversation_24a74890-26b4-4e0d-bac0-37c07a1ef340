"use strict";
/**
 * SMS Service Diagnostic Test
 * Run this with: npm run test:sms:diagnostic
 *
 * This test helps diagnose SMS service issues by:
 * - Testing authentication separately
 * - Checking API endpoints
 * - Validating request/response format
 * - Testing different message types
 * - Providing detailed error analysis
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SmsDiagnosticTester = void 0;
exports.runSmsDiagnostics = runSmsDiagnostics;
const axios_1 = __importDefault(require("axios"));
const querystring_1 = __importDefault(require("querystring"));
const env_config_1 = require("../config/env_config");
const sms_services_1 = require("../services/sms.services");
const logger_1 = __importDefault(require("../config/logger"));
class SmsDiagnosticTester {
    testPhoneNumber = '613656021';
    async runDiagnostics() {
        console.log('🔍 SMS Service Diagnostic Test');
        console.log('='.repeat(50));
        console.log(`📅 Started: ${new Date().toISOString()}`);
        console.log(`📱 Test Number: ${this.testPhoneNumber}`);
        console.log('='.repeat(50));
        // Test 1: Configuration Check
        await this.testConfiguration();
        // Test 2: Authentication Test
        await this.testAuthentication();
        // Test 3: Direct API Test
        await this.testDirectApiCall();
        // Test 4: Service Method Test with Detailed Logging
        await this.testServiceMethodWithLogging();
        // Test 5: Different Message Types
        await this.testDifferentMessageTypes();
        console.log('\n' + '='.repeat(50));
        console.log('🎯 Diagnostic Test Completed');
        console.log(`📅 Ended: ${new Date().toISOString()}`);
        console.log('='.repeat(50));
    }
    async testConfiguration() {
        console.log('\n🔧 Testing Configuration...');
        try {
            console.log('📋 SMS Configuration:');
            console.log(`   Provider URL: ${env_config_1.config.sms.providerUrl}`);
            console.log(`   Username: ${env_config_1.config.sms.username ? '✅ Set' : '❌ Missing'}`);
            console.log(`   Password: ${env_config_1.config.sms.password ? '✅ Set' : '❌ Missing'}`);
            console.log(`   Sender ID: ${env_config_1.config.sms.senderId}`);
            console.log(`   Timeout: ${env_config_1.config.sms.timeout}ms`);
            // Check if all required fields are present
            const required = ['providerUrl', 'username', 'password', 'senderId'];
            const missing = required.filter(key => !env_config_1.config.sms[key]);
            if (missing.length > 0) {
                console.log(`❌ Missing configuration: ${missing.join(', ')}`);
            }
            else {
                console.log('✅ All required configuration present');
            }
        }
        catch (error) {
            console.log('❌ Configuration test failed:', error.message);
        }
    }
    async testAuthentication() {
        console.log('\n🔐 Testing Authentication...');
        try {
            const url = `${env_config_1.config.sms.providerUrl}/token`;
            const payload = querystring_1.default.stringify({
                username: env_config_1.config.sms.username,
                password: env_config_1.config.sms.password,
                grant_type: 'password',
            });
            console.log(`📡 Auth URL: ${url}`);
            console.log(`📝 Payload: grant_type=password&username=${env_config_1.config.sms.username}&password=***`);
            const response = await axios_1.default.post(url, payload, {
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                timeout: 10000,
            });
            console.log(`✅ Authentication successful`);
            console.log(`📊 Status: ${response.status}`);
            console.log(`🔑 Token received: ${response.data.access_token ? 'Yes' : 'No'}`);
            console.log(`⏰ Expires in: ${response.data.expires_in} seconds`);
            return response.data.access_token;
        }
        catch (error) {
            console.log('❌ Authentication failed');
            if (axios_1.default.isAxiosError(error)) {
                console.log(`📊 Status: ${error.response?.status}`);
                console.log(`📝 Response: ${JSON.stringify(error.response?.data, null, 2)}`);
                console.log(`🌐 URL: ${error.config?.url}`);
            }
            console.log(`💥 Error: ${error.message}`);
        }
    }
    async testDirectApiCall() {
        console.log('\n📡 Testing Direct API Call...');
        try {
            // First get token
            const tokenUrl = `${env_config_1.config.sms.providerUrl}/token`;
            const tokenPayload = querystring_1.default.stringify({
                username: env_config_1.config.sms.username,
                password: env_config_1.config.sms.password,
                grant_type: 'password',
            });
            const tokenResponse = await axios_1.default.post(tokenUrl, tokenPayload, {
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                timeout: 10000,
            });
            const token = tokenResponse.data.access_token;
            console.log('✅ Token obtained for direct API test');
            // Now test SMS API
            const smsUrl = `${env_config_1.config.sms.providerUrl}/api/SendSMS`;
            const smsPayload = {
                mobile: this.testPhoneNumber,
                message: 'Direct API test from Ciribey Gas Delivery diagnostic tool',
                senderid: env_config_1.config.sms.senderId,
                refid: `diagnostic-${Date.now()}`,
                validity: 0,
            };
            console.log(`📡 SMS URL: ${smsUrl}`);
            console.log(`📝 SMS Payload:`, JSON.stringify(smsPayload, null, 2));
            const smsResponse = await axios_1.default.post(smsUrl, smsPayload, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                timeout: 30000,
            });
            console.log('✅ Direct API call successful');
            console.log(`📊 Status: ${smsResponse.status}`);
            console.log(`📝 Response:`, JSON.stringify(smsResponse.data, null, 2));
        }
        catch (error) {
            console.log('❌ Direct API call failed');
            if (axios_1.default.isAxiosError(error)) {
                console.log(`📊 Status: ${error.response?.status}`);
                console.log(`📝 Response:`, JSON.stringify(error.response?.data, null, 2));
                console.log(`🌐 URL: ${error.config?.url}`);
                console.log(`📋 Headers:`, JSON.stringify(error.config?.headers, null, 2));
            }
            console.log(`💥 Error: ${error.message}`);
        }
    }
    async testServiceMethodWithLogging() {
        console.log('\n🔧 Testing Service Method with Detailed Logging...');
        try {
            // Enable more detailed logging temporarily
            const originalLog = console.log;
            const logs = [];
            console.log = (...args) => {
                const logMessage = args.join(' ');
                logs.push(logMessage);
                originalLog(...args);
            };
            console.log('📤 Attempting to send SMS via service method...');
            const result = await sms_services_1.smsService.sendSms(this.testPhoneNumber, 'Service method diagnostic test - Ciribey Gas Delivery', {
                isOtp: false,
                refId: `service-diagnostic-${Date.now()}`,
            });
            console.log = originalLog; // Restore original logging
            console.log('✅ Service method call successful');
            console.log(`📧 Message ID: ${result.messageId}`);
        }
        catch (error) {
            console.log('❌ Service method call failed');
            console.log(`💥 Error Type: ${error.constructor.name}`);
            console.log(`💥 Error Message: ${error.message}`);
            if (error.code) {
                console.log(`🔢 Error Code: ${error.code}`);
            }
            if (error.details) {
                console.log(`📋 Error Details:`, JSON.stringify(error.details, null, 2));
            }
            if (error.stack) {
                console.log(`📚 Stack Trace: ${error.stack}`);
            }
        }
    }
    async testDifferentMessageTypes() {
        console.log('\n📝 Testing Different Message Types...');
        const testCases = [
            {
                name: 'Short Message',
                message: 'Test',
                isOtp: false,
            },
            {
                name: 'Medium Message',
                message: 'This is a medium length test message from Ciribey Gas Delivery diagnostic tool.',
                isOtp: false,
            },
            {
                name: 'OTP Message',
                message: 'Your verification code is: 123456',
                isOtp: true,
            },
            {
                name: 'Unicode Message',
                message: 'Mahadsanid! 🔥 Ciribey Gas Delivery - Unicode test',
                isOtp: false,
            },
        ];
        for (const testCase of testCases) {
            console.log(`\n📋 Testing: ${testCase.name}`);
            console.log(`📝 Message: "${testCase.message}"`);
            console.log(`📏 Length: ${testCase.message.length} characters`);
            console.log(`🔐 Is OTP: ${testCase.isOtp}`);
            try {
                const result = await sms_services_1.smsService.sendSms(this.testPhoneNumber, testCase.message, {
                    isOtp: testCase.isOtp,
                    refId: `diagnostic-${testCase.name.toLowerCase().replace(' ', '-')}-${Date.now()}`,
                });
                console.log(`✅ ${testCase.name} sent successfully`);
                console.log(`📧 Message ID: ${result.messageId}`);
                // Wait between tests to respect rate limiting
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            catch (error) {
                console.log(`❌ ${testCase.name} failed: ${error.message}`);
            }
        }
    }
}
exports.SmsDiagnosticTester = SmsDiagnosticTester;
// Main diagnostic function
async function runSmsDiagnostics() {
    const diagnosticTester = new SmsDiagnosticTester();
    try {
        await diagnosticTester.runDiagnostics();
    }
    catch (error) {
        console.error('💥 Diagnostic test suite failed:', error);
        logger_1.default.error('SMS diagnostic test failed', { error: error.message, stack: error.stack });
    }
}
// Run if executed directly
if (require.main === module) {
    runSmsDiagnostics();
}
//# sourceMappingURL=sms_diagnostic_test.js.map