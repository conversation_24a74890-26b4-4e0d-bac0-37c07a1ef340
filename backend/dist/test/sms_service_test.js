"use strict";
/**
 * Comprehensive SMS Service Test Suite
 * Run this with: npm run test:sms
 *
 * This test suite validates:
 * - SMS service configuration
 * - Authentication with SMS provider
 * - Single SMS sending
 * - Bulk SMS sending
 * - Error handling
 * - Rate limiting
 * - Phone number validation
 * - Message validation
 * - Service health checks
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TEST_CONFIG = exports.SmsServiceTester = void 0;
exports.runSmsServiceTests = runSmsServiceTests;
const sms_services_1 = require("../services/sms.services");
const logger_1 = __importDefault(require("../config/logger"));
// Test configuration
const TEST_CONFIG = {
    testPhoneNumber: '613656021', // Your specified test number
    testMessage: 'Ciribey Gas Delivery - SMS Service Test (Unicode-safe)',
    otpTestMessage: 'Your OTP code is: 123456. Valid for 5 minutes.',
    bulkTestNumbers: [
        '613656021', // Primary test number
        '613656021', // Duplicate to test deduplication
        '617777777', // Additional test number (if valid)
    ],
    invalidNumbers: [
        '123456789', // Too short
        '25261365602111', // Too long
        '252123456789', // Invalid prefix
        'invalid', // Non-numeric
    ],
    longMessage: 'A'.repeat(500), // Message too long for testing
};
exports.TEST_CONFIG = TEST_CONFIG;
class SmsServiceTester {
    testResults = {
        passed: 0,
        failed: 0,
        tests: [],
    };
    async runTest(testName, testFunction) {
        const startTime = Date.now();
        console.log(`\n🧪 Running: ${testName}`);
        try {
            await testFunction();
            const duration = Date.now() - startTime;
            console.log(`✅ PASSED: ${testName} (${duration}ms)`);
            this.testResults.passed++;
            this.testResults.tests.push({
                name: testName,
                status: 'PASS',
                duration,
            });
        }
        catch (error) {
            const duration = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : String(error);
            console.log(`❌ FAILED: ${testName} (${duration}ms)`);
            console.log(`   Error: ${errorMessage}`);
            this.testResults.failed++;
            this.testResults.tests.push({
                name: testName,
                status: 'FAIL',
                message: errorMessage,
                duration,
            });
        }
    }
    async testServiceHealth() {
        const isHealthy = await sms_services_1.smsService.checkHealth();
        if (!isHealthy) {
            throw new Error('SMS service health check failed');
        }
        console.log('   ✓ SMS service is healthy');
    }
    async testServiceMetrics() {
        const metrics = sms_services_1.smsService.getMetrics();
        console.log('   ✓ Service metrics retrieved:', {
            sentCount: metrics.sentCount,
            failedCount: metrics.failedCount,
            successRate: metrics.successRate,
        });
    }
    async testSingleSms() {
        const result = await sms_services_1.smsService.sendSms(TEST_CONFIG.testPhoneNumber, TEST_CONFIG.testMessage, { isOtp: false, refId: `test-${Date.now()}` });
        if (!result.messageId) {
            throw new Error('No message ID returned');
        }
        console.log(`   ✓ SMS sent successfully to ${TEST_CONFIG.testPhoneNumber}`);
        console.log(`   ✓ Message ID: ${result.messageId}`);
    }
    async testOtpSms() {
        const result = await sms_services_1.smsService.sendSms(TEST_CONFIG.testPhoneNumber, TEST_CONFIG.otpTestMessage, { isOtp: true, refId: `otp-test-${Date.now()}` });
        if (!result.messageId) {
            throw new Error('No message ID returned for OTP');
        }
        console.log(`   ✓ OTP SMS sent successfully to ${TEST_CONFIG.testPhoneNumber}`);
        console.log(`   ✓ OTP Message ID: ${result.messageId}`);
    }
    async testPhoneNumberValidation() {
        for (const invalidNumber of TEST_CONFIG.invalidNumbers) {
            try {
                await sms_services_1.smsService.sendSms(invalidNumber, 'Test message');
                throw new Error(`Should have failed for invalid number: ${invalidNumber}`);
            }
            catch (error) {
                if (error.message.includes('Should have failed')) {
                    throw error;
                }
                console.log(`   ✓ Correctly rejected invalid number: ${invalidNumber}`);
            }
        }
    }
    async testMessageValidation() {
        try {
            await sms_services_1.smsService.sendSms(TEST_CONFIG.testPhoneNumber, TEST_CONFIG.longMessage);
            throw new Error('Should have failed for message too long');
        }
        catch (error) {
            if (error.message.includes('Should have failed')) {
                throw error;
            }
            console.log('   ✓ Correctly rejected message that is too long');
        }
    }
    async testBulkSms() {
        const result = await sms_services_1.smsService.sendSmsToMany(TEST_CONFIG.bulkTestNumbers, 'Bulk SMS test from Ciribey Gas Delivery', {
            isOtp: false,
            refId: `bulk-test-${Date.now()}`,
            onProgress: (success, failed, total) => {
                console.log(`   📊 Progress: ${success} success, ${failed} failed, ${total} total`);
            },
        });
        console.log(`   ✓ Bulk SMS result: ${result.status}`);
        console.log(`   ✓ Successful: ${result.data.counts.successful}`);
        console.log(`   ✓ Failed: ${result.data.counts.failed}`);
        console.log(`   ✓ Duplicates removed: ${result.data.counts.duplicatesRemoved}`);
        if (result.data.counts.successful === 0) {
            throw new Error('No SMS messages were sent successfully in bulk test');
        }
    }
    async runAllTests() {
        console.log('🚀 Starting SMS Service Test Suite');
        console.log('='.repeat(50));
        console.log(`📱 Test Phone Number: ${TEST_CONFIG.testPhoneNumber}`);
        console.log(`📅 Test Started: ${new Date().toISOString()}`);
        // Run all tests
        await this.runTest('Service Health Check', () => this.testServiceHealth());
        await this.runTest('Service Metrics', () => this.testServiceMetrics());
        await this.runTest('Single SMS Sending', () => this.testSingleSms());
        await this.runTest('OTP SMS Sending', () => this.testOtpSms());
        await this.runTest('Phone Number Validation', () => this.testPhoneNumberValidation());
        await this.runTest('Message Validation', () => this.testMessageValidation());
        await this.runTest('Bulk SMS Sending', () => this.testBulkSms());
        // Print final results
        this.printResults();
    }
    printResults() {
        console.log('\n' + '='.repeat(50));
        console.log('📊 SMS SERVICE TEST RESULTS');
        console.log('='.repeat(50));
        console.log(`✅ Passed: ${this.testResults.passed}`);
        console.log(`❌ Failed: ${this.testResults.failed}`);
        console.log(`📈 Success Rate: ${((this.testResults.passed / (this.testResults.passed + this.testResults.failed)) * 100).toFixed(1)}%`);
        console.log('\n📋 Detailed Results:');
        this.testResults.tests.forEach((test, index) => {
            const status = test.status === 'PASS' ? '✅' : '❌';
            const duration = test.duration ? ` (${test.duration}ms)` : '';
            console.log(`${index + 1}. ${status} ${test.name}${duration}`);
            if (test.message) {
                console.log(`   └─ ${test.message}`);
            }
        });
        // Final service metrics
        console.log('\n📊 Final Service Metrics:');
        const finalMetrics = sms_services_1.smsService.getMetrics();
        console.log(`   Sent Count: ${finalMetrics.sentCount}`);
        console.log(`   Failed Count: ${finalMetrics.failedCount}`);
        console.log(`   Success Rate: ${(finalMetrics.successRate * 100).toFixed(1)}%`);
        console.log('\n🎯 Test Summary:');
        if (this.testResults.failed === 0) {
            console.log('🎉 All tests passed! SMS service is working correctly.');
        }
        else {
            console.log(`⚠️  ${this.testResults.failed} test(s) failed. Please check the configuration and try again.`);
        }
        console.log(`\n📅 Test Completed: ${new Date().toISOString()}`);
        console.log('='.repeat(50));
    }
}
exports.SmsServiceTester = SmsServiceTester;
// Main test function
async function runSmsServiceTests() {
    const tester = new SmsServiceTester();
    try {
        await tester.runAllTests();
    }
    catch (error) {
        console.error('💥 Test suite failed with error:', error);
        logger_1.default.error('SMS test suite failed', { error: error.message, stack: error.stack });
        process.exit(1);
    }
}
// Run if executed directly
if (require.main === module) {
    runSmsServiceTests();
}
//# sourceMappingURL=sms_service_test.js.map