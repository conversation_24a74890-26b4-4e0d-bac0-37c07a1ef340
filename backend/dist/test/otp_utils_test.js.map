{"version": 3, "file": "otp_utils_test.js", "sourceRoot": "", "sources": ["../../src/test/otp_utils_test.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AA2QD,8CAAiB;AACjB,8CAAiB;AACjB,0CAAe;AACf,oDAAoB;AACpB,kCAAW;AACX,0CAAe;AACf,wCAAc;AA/QhB,kDAAoH;AAEpH;;GAEG;AACH,KAAK,UAAU,iBAAiB;IAC9B,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAE9C,IAAI,CAAC;QACH,6BAA6B;QAC7B,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACjD,MAAM,IAAI,GAAG,IAAA,uBAAW,GAAE,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAClE,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,MAAM,aAAa,CAAC,CAAC;QAC7D,OAAO,CAAC,GAAG,CAAC,iBAAiB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAExD,0CAA0C;QAC1C,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;QAC5C,MAAM,IAAI,GAAG,IAAA,uBAAW,GAAE,CAAC;QAC3B,MAAM,IAAI,GAAG,IAAA,uBAAW,GAAE,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QACpC,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QACpC,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QACpC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAEjH,wCAAwC;QACxC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAC7C,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC;QAC/D,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,iBAAiB,WAAW,UAAU,CAAC,CAAC;QACpD,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,CAAC,cAAc,GAAG,GAAG,EAAE,CAAC,CAAC;QAE9D,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAElD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IAChE,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,iBAAiB;IAC9B,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAEhD,IAAI,CAAC;QACH,kBAAkB;QAClB,MAAM,OAAO,GAAS;YACpB,IAAI,EAAE,QAAQ;YACd,cAAc,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,qBAAqB;SAC3E,CAAC;QAEF,iBAAiB;QACjB,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACrC,MAAM,WAAW,GAAG,IAAA,uBAAW,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QACnD,OAAO,CAAC,GAAG,CAAC,uBAAuB,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1D,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC;QAEzD,sBAAsB;QACtB,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAChD,MAAM,eAAe,GAAG,IAAA,uBAAW,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7D,OAAO,CAAC,GAAG,CAAC,oBAAoB,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC;QAEzD,mBAAmB;QACnB,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QACzC,MAAM,UAAU,GAAS;YACvB,IAAI,EAAE,QAAQ;YACd,cAAc,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,eAAe;SAC5D,CAAC;QACF,MAAM,aAAa,GAAG,IAAA,uBAAW,EAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QACxD,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,oBAAoB,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC;QAEvD,gBAAgB;QAChB,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;QACtC,MAAM,UAAU,GAAG,IAAA,uBAAW,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC/C,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;QACrD,OAAO,CAAC,GAAG,CAAC,oBAAoB,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC;QAEpD,qBAAqB;QACrB,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,MAAM,eAAe,GAAG,IAAA,uBAAW,EAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,oBAAoB,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC;QAEzD,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAElD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IAChE,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,eAAe;IAC5B,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IAEtD,IAAI,CAAC;QACH,iBAAiB;QACjB,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACrC,MAAM,QAAQ,GAAS;YACrB,IAAI,EAAE,QAAQ;YACd,cAAc,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;SACrD,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAA,sBAAU,EAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAE7D,mBAAmB;QACnB,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QACzC,MAAM,UAAU,GAAS;YACvB,IAAI,EAAE,QAAQ;YACd,cAAc,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;SAC5C,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,IAAA,sBAAU,EAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QAEpE,gBAAgB;QAChB,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,IAAA,sBAAU,EAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAE3D,qBAAqB;QACrB,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,IAAA,sBAAU,EAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAErE,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAEhD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IAC9D,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,oBAAoB;IACjC,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IAEpD,IAAI,CAAC;QACH,oCAAoC;QACpC,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QACxD,MAAM,UAAU,GAAS;YACvB,IAAI,EAAE,QAAQ;YACd,cAAc,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;SACrD,CAAC;QACF,MAAM,UAAU,GAAG,IAAA,+BAAmB,EAAC,UAAU,CAAC,CAAC;QACnD,OAAO,CAAC,GAAG,CAAC,qBAAqB,UAAU,UAAU,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,8BAA8B,UAAU,IAAI,GAAG,IAAI,UAAU,IAAI,GAAG,EAAE,CAAC,CAAC;QAEpF,mBAAmB;QACnB,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QACzC,MAAM,UAAU,GAAS;YACvB,IAAI,EAAE,QAAQ;YACd,cAAc,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;SAC5C,CAAC;QACF,MAAM,gBAAgB,GAAG,IAAA,+BAAmB,EAAC,UAAU,CAAC,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,iCAAiC,gBAAgB,UAAU,CAAC,CAAC;QACzE,OAAO,CAAC,GAAG,CAAC,cAAc,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC;QAEpD,gBAAgB;QAChB,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;QACtC,MAAM,aAAa,GAAG,IAAA,+BAAmB,EAAC,IAAI,CAAC,CAAC;QAChD,OAAO,CAAC,GAAG,CAAC,8BAA8B,aAAa,UAAU,CAAC,CAAC;QACnE,OAAO,CAAC,GAAG,CAAC,cAAc,aAAa,KAAK,CAAC,EAAE,CAAC,CAAC;QAEjD,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IAEtD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IACpE,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,WAAW;IACxB,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;IAExD,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAClD,MAAM,UAAU,GAAG,IAAA,yBAAa,GAAE,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,sBAAsB,UAAU,EAAE,CAAC,CAAC;QAChD,OAAO,CAAC,GAAG,CAAC,+BAA+B,OAAO,UAAU,KAAK,SAAS,EAAE,CAAC,CAAC;QAE9E,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAC5D,MAAM,GAAG,GAAG,IAAA,uBAAW,GAAE,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,yBAAyB,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,2BAA2B,GAAG,CAAC,cAAc,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAE3E,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,qCAAqC,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC,CAAC;QAC5E,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,qCAAqC,GAAG,CAAC,IAAI,KAAK,QAAQ,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC,CAAC;QACrG,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAE5C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IAC1D,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,eAAe;IAC5B,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IAEvD,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAChC,MAAM,GAAG,GAAG,IAAA,uBAAW,GAAE,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,gBAAgB,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;QAExC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC1C,MAAM,WAAW,GAAG,IAAA,uBAAW,EAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;QAC/C,OAAO,CAAC,GAAG,CAAC,4BAA4B,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;QAE/D,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QACxC,MAAM,OAAO,GAAG,IAAA,sBAAU,EAAC,GAAG,CAAC,CAAC;QAChC,OAAO,CAAC,GAAG,CAAC,mBAAmB,OAAO,EAAE,CAAC,CAAC;QAE1C,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QACxC,MAAM,SAAS,GAAG,IAAA,+BAAmB,EAAC,GAAG,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,qBAAqB,SAAS,UAAU,CAAC,CAAC;QAEtD,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QACpC,MAAM,WAAW,GAAG,IAAA,uBAAW,EAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QAC/C,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;QAE7D,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IAExD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IAC9D,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,cAAc;IAC3B,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;IACpE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7B,MAAM,iBAAiB,EAAE,CAAC;IAC1B,MAAM,iBAAiB,EAAE,CAAC;IAC1B,MAAM,eAAe,EAAE,CAAC;IACxB,MAAM,oBAAoB,EAAE,CAAC;IAC7B,MAAM,WAAW,EAAE,CAAC;IACpB,MAAM,eAAe,EAAE,CAAC;IAExB,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IACpC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IAClD,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;IAClC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;IAC3C,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;IAC3C,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IAC/C,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IAC/C,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;IAC3C,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAC9C,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;AAC9D,CAAC;AAaD,8CAA8C;AAC9C,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,cAAc,EAAE,CAAC;AACnB,CAAC"}