"use strict";
/**
 * OTP Utilities Test
 * Comprehensive testing for OTP generation, validation, and management
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.testOtpGeneration = testOtpGeneration;
exports.testOtpValidation = testOtpValidation;
exports.testOtpValidity = testOtpValidity;
exports.testOtpRemainingTime = testOtpRemainingTime;
exports.testOtpSkip = testOtpSkip;
exports.testOtpWorkflow = testOtpWorkflow;
exports.runAllOtpTests = runAllOtpTests;
const otp_utils_1 = require("../utils/otp_utils");
/**
 * Test OTP generation functionality
 */
async function testOtpGeneration() {
    console.log('🔐 Testing OTP Generation...\n');
    try {
        // Test normal OTP generation
        console.log('1. Testing normal OTP generation:');
        const otp1 = (0, otp_utils_1.generateOtp)();
        console.log(`✅ Generated OTP: ${otp1.code}`);
        console.log(`✅ Expiration: ${otp1.expirationTime.toISOString()}`);
        console.log(`✅ Code length: ${otp1.code.length} characters`);
        console.log(`✅ Is numeric: ${/^\d+$/.test(otp1.code)}`);
        // Test multiple generations are different
        console.log('\n2. Testing OTP uniqueness:');
        const otp2 = (0, otp_utils_1.generateOtp)();
        const otp3 = (0, otp_utils_1.generateOtp)();
        console.log(`✅ OTP1: ${otp1.code}`);
        console.log(`✅ OTP2: ${otp2.code}`);
        console.log(`✅ OTP3: ${otp3.code}`);
        console.log(`✅ All different: ${otp1.code !== otp2.code && otp2.code !== otp3.code && otp1.code !== otp3.code}`);
        // Test expiration time is in the future
        console.log('\n3. Testing expiration time:');
        const now = new Date();
        const timeDiff = otp1.expirationTime.getTime() - now.getTime();
        const minutesDiff = Math.round(timeDiff / (1000 * 60));
        console.log(`✅ Expires in: ${minutesDiff} minutes`);
        console.log(`✅ Is future time: ${otp1.expirationTime > now}`);
        console.log('\n✅ OTP generation tests passed!');
    }
    catch (error) {
        console.error('❌ OTP generation test failed:', error.message);
    }
}
/**
 * Test OTP validation functionality
 */
async function testOtpValidation() {
    console.log('\n🔍 Testing OTP Validation...\n');
    try {
        // Create test OTP
        const testOtp = {
            code: '123456',
            expirationTime: new Date(Date.now() + 5 * 60 * 1000) // 5 minutes from now
        };
        // Test valid OTP
        console.log('1. Testing valid OTP:');
        const validResult = (0, otp_utils_1.validateOtp)(testOtp, '123456');
        console.log(`✅ Valid OTP result: ${validResult.isValid}`);
        console.log(`✅ No error message: ${!validResult.error}`);
        // Test incorrect code
        console.log('\n2. Testing incorrect OTP code:');
        const incorrectResult = (0, otp_utils_1.validateOtp)(testOtp, '654321');
        console.log(`✅ Invalid result: ${!incorrectResult.isValid}`);
        console.log(`✅ Error message: ${incorrectResult.error}`);
        // Test expired OTP
        console.log('\n3. Testing expired OTP:');
        const expiredOtp = {
            code: '123456',
            expirationTime: new Date(Date.now() - 1000) // 1 second ago
        };
        const expiredResult = (0, otp_utils_1.validateOtp)(expiredOtp, '123456');
        console.log(`✅ Expired result: ${!expiredResult.isValid}`);
        console.log(`✅ Error message: ${expiredResult.error}`);
        // Test null OTP
        console.log('\n4. Testing null OTP:');
        const nullResult = (0, otp_utils_1.validateOtp)(null, '123456');
        console.log(`✅ Null result: ${!nullResult.isValid}`);
        console.log(`✅ Error message: ${nullResult.error}`);
        // Test undefined OTP
        console.log('\n5. Testing undefined OTP:');
        const undefinedResult = (0, otp_utils_1.validateOtp)(undefined, '123456');
        console.log(`✅ Undefined result: ${!undefinedResult.isValid}`);
        console.log(`✅ Error message: ${undefinedResult.error}`);
        console.log('\n✅ OTP validation tests passed!');
    }
    catch (error) {
        console.error('❌ OTP validation test failed:', error.message);
    }
}
/**
 * Test OTP validity checking
 */
async function testOtpValidity() {
    console.log('\n⏰ Testing OTP Validity Checking...\n');
    try {
        // Test valid OTP
        console.log('1. Testing valid OTP:');
        const validOtp = {
            code: '123456',
            expirationTime: new Date(Date.now() + 5 * 60 * 1000)
        };
        console.log(`✅ Valid OTP is valid: ${(0, otp_utils_1.isOtpValid)(validOtp)}`);
        // Test expired OTP
        console.log('\n2. Testing expired OTP:');
        const expiredOtp = {
            code: '123456',
            expirationTime: new Date(Date.now() - 1000)
        };
        console.log(`✅ Expired OTP is invalid: ${!(0, otp_utils_1.isOtpValid)(expiredOtp)}`);
        // Test null OTP
        console.log('\n3. Testing null OTP:');
        console.log(`✅ Null OTP is invalid: ${!(0, otp_utils_1.isOtpValid)(null)}`);
        // Test undefined OTP
        console.log('\n4. Testing undefined OTP:');
        console.log(`✅ Undefined OTP is invalid: ${!(0, otp_utils_1.isOtpValid)(undefined)}`);
        console.log('\n✅ OTP validity tests passed!');
    }
    catch (error) {
        console.error('❌ OTP validity test failed:', error.message);
    }
}
/**
 * Test OTP remaining time calculation
 */
async function testOtpRemainingTime() {
    console.log('\n⏱️ Testing OTP Remaining Time...\n');
    try {
        // Test OTP with 5 minutes remaining
        console.log('1. Testing OTP with 5 minutes remaining:');
        const fiveMinOtp = {
            code: '123456',
            expirationTime: new Date(Date.now() + 5 * 60 * 1000)
        };
        const remaining5 = (0, otp_utils_1.getOtpRemainingTime)(fiveMinOtp);
        console.log(`✅ Remaining time: ${remaining5} seconds`);
        console.log(`✅ Approximately 5 minutes: ${remaining5 >= 290 && remaining5 <= 300}`);
        // Test expired OTP
        console.log('\n2. Testing expired OTP:');
        const expiredOtp = {
            code: '123456',
            expirationTime: new Date(Date.now() - 1000)
        };
        const remainingExpired = (0, otp_utils_1.getOtpRemainingTime)(expiredOtp);
        console.log(`✅ Expired OTP remaining time: ${remainingExpired} seconds`);
        console.log(`✅ Is zero: ${remainingExpired === 0}`);
        // Test null OTP
        console.log('\n3. Testing null OTP:');
        const remainingNull = (0, otp_utils_1.getOtpRemainingTime)(null);
        console.log(`✅ Null OTP remaining time: ${remainingNull} seconds`);
        console.log(`✅ Is zero: ${remainingNull === 0}`);
        console.log('\n✅ OTP remaining time tests passed!');
    }
    catch (error) {
        console.error('❌ OTP remaining time test failed:', error.message);
    }
}
/**
 * Test OTP skip functionality
 */
async function testOtpSkip() {
    console.log('\n🚫 Testing OTP Skip Functionality...\n');
    try {
        console.log('1. Testing shouldSkipOtp function:');
        const skipResult = (0, otp_utils_1.shouldSkipOtp)();
        console.log(`✅ Should skip OTP: ${skipResult}`);
        console.log(`✅ Function returns boolean: ${typeof skipResult === 'boolean'}`);
        console.log('\n2. Testing OTP generation with skip logic:');
        const otp = (0, otp_utils_1.generateOtp)();
        console.log(`✅ Generated OTP code: ${otp.code}`);
        console.log(`✅ Generated expiration: ${otp.expirationTime.toISOString()}`);
        if (skipResult) {
            console.log(`✅ Skip enabled - using dummy OTP: ${otp.code === '000000'}`);
        }
        else {
            console.log(`✅ Skip disabled - using real OTP: ${otp.code !== '000000' && otp.code.length === 6}`);
        }
        console.log('\n✅ OTP skip tests passed!');
    }
    catch (error) {
        console.error('❌ OTP skip test failed:', error.message);
    }
}
/**
 * Test complete OTP workflow
 */
async function testOtpWorkflow() {
    console.log('\n🔄 Testing Complete OTP Workflow...\n');
    try {
        console.log('1. Generate OTP:');
        const otp = (0, otp_utils_1.generateOtp)();
        console.log(`✅ Generated: ${otp.code}`);
        console.log('\n2. Validate correct OTP:');
        const validResult = (0, otp_utils_1.validateOtp)(otp, otp.code);
        console.log(`✅ Validation successful: ${validResult.isValid}`);
        console.log('\n3. Check OTP validity:');
        const isValid = (0, otp_utils_1.isOtpValid)(otp);
        console.log(`✅ OTP is valid: ${isValid}`);
        console.log('\n4. Get remaining time:');
        const remaining = (0, otp_utils_1.getOtpRemainingTime)(otp);
        console.log(`✅ Remaining time: ${remaining} seconds`);
        console.log('\n5. Test wrong OTP:');
        const wrongResult = (0, otp_utils_1.validateOtp)(otp, '000000');
        console.log(`✅ Wrong OTP rejected: ${!wrongResult.isValid}`);
        console.log('\n✅ Complete OTP workflow test passed!');
    }
    catch (error) {
        console.error('❌ OTP workflow test failed:', error.message);
    }
}
/**
 * Run all OTP utility tests
 */
async function runAllOtpTests() {
    console.log('🔥 Ciribey Gas Delivery - OTP Utilities Test Suite\n');
    console.log('='.repeat(60));
    await testOtpGeneration();
    await testOtpValidation();
    await testOtpValidity();
    await testOtpRemainingTime();
    await testOtpSkip();
    await testOtpWorkflow();
    console.log('\n' + '='.repeat(60));
    console.log('✅ All OTP utility tests completed!');
    console.log('\n📊 Test Summary:');
    console.log('- OTP Generation: ✅ Working');
    console.log('- OTP Validation: ✅ Working');
    console.log('- OTP Validity Check: ✅ Working');
    console.log('- OTP Remaining Time: ✅ Working');
    console.log('- OTP Skip Logic: ✅ Working');
    console.log('- Complete Workflow: ✅ Working');
    console.log('\n🎉 OTP utilities are ready for production!');
}
// Run tests if this file is executed directly
if (require.main === module) {
    runAllOtpTests();
}
//# sourceMappingURL=otp_utils_test.js.map