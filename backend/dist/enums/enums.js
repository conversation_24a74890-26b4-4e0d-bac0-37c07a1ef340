"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationStatus = exports.DeliveryVerificationMethod = exports.PaymentStatus = exports.PaymentMethod = exports.VehicleType = exports.CylinderMaterial = exports.CylinderStatus = exports.CylinderType = exports.SparePartCategory = exports.SparePartStatus = exports.EntityType = exports.OrderStatus = exports.UserRole = void 0;
// User Roles
var UserRole;
(function (UserRole) {
    UserRole["CUSTOMER"] = "customer";
    UserRole["AGENT"] = "agent";
    UserRole["ADMIN"] = "admin";
    UserRole["SUPERVISOR"] = "supervisor";
})(UserRole || (exports.UserRole = UserRole = {}));
// Order Lifecycle
var OrderStatus;
(function (OrderStatus) {
    // Core Flow
    OrderStatus["PENDING"] = "PENDING";
    OrderStatus["CONFIRMED"] = "CONFIRMED";
    OrderStatus["IN_TRANSIT"] = "IN_TRANSIT";
    OrderStatus["DELIVERED"] = "DELIVERED";
    // Termination States
    OrderStatus["CANCELLED"] = "CANCELLED";
    OrderStatus["FAILED"] = "FAILED";
})(OrderStatus || (exports.OrderStatus = OrderStatus = {}));
var EntityType;
(function (EntityType) {
    EntityType["SparePart"] = "SPARE_PART";
    EntityType["Cylinder"] = "CYLINDER";
    EntityType["Package"] = "PACKAGE";
})(EntityType || (exports.EntityType = EntityType = {}));
var SparePartStatus;
(function (SparePartStatus) {
    SparePartStatus["AVAILABLE"] = "AVAILABLE";
    SparePartStatus["LOW_STOCK"] = "LOW_STOCK";
    SparePartStatus["OUT_OF_STOCK"] = "OUT_OF_STOCK";
    SparePartStatus["DISCONTINUED"] = "DISCONTINUED";
})(SparePartStatus || (exports.SparePartStatus = SparePartStatus = {}));
var SparePartCategory;
(function (SparePartCategory) {
    SparePartCategory["BRASS_CONTROL_VALVE_KIT"] = "BRASS_CONTROL_VALVE_KIT";
    SparePartCategory["REGULATOR_HIGH_PRESSURE"] = "REGULATOR_HIGH_PRESSURE";
    SparePartCategory["REGULATOR_LOW_PRESSURE"] = "REGULATOR_LOW_PRESSURE";
    SparePartCategory["SINGLE_BURNER_GAS_STOVE"] = "SINGLE_BURNER_GAS_STOVE";
    SparePartCategory["THREE_BURNER_LPG_STOVE"] = "THREE_BURNER_LPG_STOVE";
    SparePartCategory["TUBO_2_METER"] = "TUBO_2_METER";
    SparePartCategory["VALUE_BURNER"] = "VALUE_BURNER";
})(SparePartCategory || (exports.SparePartCategory = SparePartCategory = {}));
// Cylinder Types
var CylinderType;
(function (CylinderType) {
    CylinderType["SixKg"] = "6KG";
    CylinderType["ThirteenKg"] = "13KG";
    CylinderType["SeventeenKg"] = "17KG";
    CylinderType["TwentyKg"] = "20KG";
    CylinderType["TwentyFiveKg"] = "25KG";
})(CylinderType || (exports.CylinderType = CylinderType = {}));
var CylinderStatus;
(function (CylinderStatus) {
    CylinderStatus["Active"] = "ACTIVE";
    CylinderStatus["Discontinued"] = "DISCONTINUED";
    CylinderStatus["OutOfStock"] = "OUT_OF_STOCK";
})(CylinderStatus || (exports.CylinderStatus = CylinderStatus = {}));
var CylinderMaterial;
(function (CylinderMaterial) {
    CylinderMaterial["Metal"] = "METAL";
    CylinderMaterial["Plastic"] = "PLASTIC";
})(CylinderMaterial || (exports.CylinderMaterial = CylinderMaterial = {}));
var VehicleType;
(function (VehicleType) {
    VehicleType["BIKE"] = "bike";
    VehicleType["CAR"] = "car";
    VehicleType["MOTORCYCLE"] = "motorcycle";
})(VehicleType || (exports.VehicleType = VehicleType = {}));
// export enum Warehouse { // instead of `warehouse`
//   MAIN = 'ceelasha_warehouse',
//   SECONDARY = 'seybiyaano_warehouse',
//   THIRD = 'towfiiq_warehouse',
//   FOURTH = 'bakaaro_warehouse',
// }
// Payment Methods
var PaymentMethod;
(function (PaymentMethod) {
    PaymentMethod["CASH"] = "cash";
    PaymentMethod["WAAFI_PREAUTH"] = "waafi_preauth";
})(PaymentMethod || (exports.PaymentMethod = PaymentMethod = {}));
var PaymentStatus;
(function (PaymentStatus) {
    PaymentStatus["PENDING"] = "PENDING";
    PaymentStatus["PREAUTHORIZED"] = "PREAUTHORIZED";
    PaymentStatus["CAPTURED"] = "CAPTURED";
    PaymentStatus["CANCELLED"] = "CANCELLED";
    PaymentStatus["FAILED"] = "FAILED";
})(PaymentStatus || (exports.PaymentStatus = PaymentStatus = {}));
// Delivery Verification
var DeliveryVerificationMethod;
(function (DeliveryVerificationMethod) {
    DeliveryVerificationMethod["QR_CODE"] = "qr_code";
    DeliveryVerificationMethod["OTP"] = "otp";
    DeliveryVerificationMethod["BIOMETRIC"] = "biometric";
})(DeliveryVerificationMethod || (exports.DeliveryVerificationMethod = DeliveryVerificationMethod = {}));
var NotificationStatus;
(function (NotificationStatus) {
    NotificationStatus["PENDING"] = "pending";
    NotificationStatus["DELIVERED"] = "delivered";
    NotificationStatus["FAILED"] = "failed";
})(NotificationStatus || (exports.NotificationStatus = NotificationStatus = {}));
//# sourceMappingURL=enums.js.map