"use strict";
/**
 * Comprehensive Usage Examples for Inventory Alert System
 * Demonstrates how to use the inventory monitoring and notification system
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.exampleManualStockAlerts = exampleManualStockAlerts;
exports.exampleComprehensiveInventoryCheck = exampleComprehensiveInventoryCheck;
exports.exampleOrderProcessingWithAlerts = exampleOrderProcessingWithAlerts;
exports.exampleDailyInventorySummary = exampleDailyInventorySummary;
exports.exampleRestockingOperations = exampleRestockingOperations;
exports.exampleEmergencyStockAlerts = exampleEmergencyStockAlerts;
exports.exampleScheduledMonitoring = exampleScheduledMonitoring;
exports.runAllInventoryExamples = runAllInventoryExamples;
const inventory_monitor_service_1 = require("../services/inventory-monitor.service");
const notification_helper_1 = require("../utils/notification_helper");
/**
 * Example 1: Manual Stock Alert Testing
 * Test individual stock alert functions
 */
async function exampleManualStockAlerts() {
    console.log('📊 Example: Manual Stock Alert Testing');
    console.log('─'.repeat(50));
    try {
        // Test low stock alert
        console.log('Testing low stock alert...');
        const lowStockResult = await notification_helper_1.Notifications.Inventory.sendLowStockAlert('13kg Gas Cylinder', 3, // Only 3 remaining
        'so');
        console.log('Low stock alert result:', {
            success: lowStockResult.success,
            recipients: lowStockResult.totalRecipients,
            delivered: lowStockResult.successfulDeliveries,
        });
        // Test out of stock alert
        console.log('\nTesting out of stock alert...');
        const outOfStockResult = await notification_helper_1.Notifications.Inventory.sendOutOfStockAlert('6kg Gas Cylinder', 'so');
        console.log('Out of stock alert result:', {
            success: outOfStockResult.success,
            recipients: outOfStockResult.totalRecipients,
            delivered: outOfStockResult.successfulDeliveries,
        });
        // Test stock level checking
        console.log('\nTesting stock level checking...');
        const checkResult = await notification_helper_1.Notifications.Inventory.checkStockAndAlert('Regulator Spare Part', 2, // Current quantity
        5, // Minimum stock level
        'so');
        if (checkResult) {
            console.log('Stock alert triggered:', {
                success: checkResult.success,
                recipients: checkResult.totalRecipients,
            });
        }
        else {
            console.log('Stock levels are adequate - no alert sent');
        }
    }
    catch (error) {
        console.error('Manual stock alert testing failed:', error.message);
    }
}
/**
 * Example 2: Comprehensive Inventory Monitoring
 * Check all inventory levels across the system
 */
async function exampleComprehensiveInventoryCheck() {
    console.log('🔍 Example: Comprehensive Inventory Check');
    console.log('─'.repeat(50));
    try {
        // Check all inventory levels
        const results = await inventory_monitor_service_1.inventoryMonitor.checkAllInventoryLevels();
        console.log('Inventory Check Results:');
        console.log(`Cylinders: ${results.cylinders.checked} checked, ${results.cylinders.alerts} alerts sent`);
        console.log(`Spare Parts: ${results.spareParts.checked} checked, ${results.spareParts.alerts} alerts sent`);
        console.log(`Packages: ${results.packages.checked} checked, ${results.packages.alerts} alerts sent`);
        console.log(`Total Alerts: ${results.totalAlerts}`);
        // Get low stock items
        const lowStockItems = await inventory_monitor_service_1.inventoryMonitor.getLowStockItems();
        console.log('\nLow Stock Items:');
        console.log('Cylinders:', lowStockItems.cylinders.length);
        lowStockItems.cylinders.forEach(item => {
            console.log(`  - ${item.name}: ${item.available}/${item.minimum}`);
        });
        console.log('Spare Parts:', lowStockItems.spareParts.length);
        lowStockItems.spareParts.forEach(item => {
            console.log(`  - ${item.name}: ${item.available}/${item.minimum}`);
        });
        console.log('Packages:', lowStockItems.packages.length);
        lowStockItems.packages.forEach(item => {
            console.log(`  - ${item.name}: ${item.available}/${item.minimum}`);
        });
        // Get out of stock items
        const outOfStockItems = await inventory_monitor_service_1.inventoryMonitor.getOutOfStockItems();
        console.log('\nOut of Stock Items:');
        console.log('Cylinders:', outOfStockItems.cylinders.length);
        console.log('Spare Parts:', outOfStockItems.spareParts.length);
        console.log('Packages:', outOfStockItems.packages.length);
    }
    catch (error) {
        console.error('Comprehensive inventory check failed:', error.message);
    }
}
/**
 * Example 3: Simulating Order Processing with Alerts
 * Demonstrate how alerts are triggered during normal operations
 */
async function exampleOrderProcessingWithAlerts() {
    console.log('🛒 Example: Order Processing with Stock Alerts');
    console.log('─'.repeat(50));
    try {
        // This would typically be called from the order service
        // when an order is completed and items are marked as sold
        console.log('Simulating order completion...');
        // Example: Mark cylinder as sold (this would trigger stock check)
        console.log('Processing cylinder sale...');
        // Note: In real usage, this would be called with actual IDs
        // const cylinderResult = await cylinderService.markAsSold('cylinder_id', 2);
        console.log('Cylinder sale processed - stock alerts checked automatically');
        // Example: Mark spare part as sold
        console.log('Processing spare part sale...');
        // const sparePartResult = await sparePartService.markAsSold('spare_part_id', 1);
        console.log('Spare part sale processed - stock alerts checked automatically');
        // Example: Mark package as sold
        console.log('Processing package sale...');
        // const packageResult = await packageService.markPackageAsSold('package_id', 1);
        console.log('Package sale processed - stock alerts checked automatically');
        console.log('\n✅ All sales processed with automatic stock monitoring');
    }
    catch (error) {
        console.error('Order processing simulation failed:', error.message);
    }
}
/**
 * Example 4: Daily Inventory Summary
 * Send daily summary to administrators
 */
async function exampleDailyInventorySummary() {
    console.log('📅 Example: Daily Inventory Summary');
    console.log('─'.repeat(50));
    try {
        await inventory_monitor_service_1.inventoryMonitor.sendDailyInventorySummary();
        console.log('Daily inventory summary sent to administrators');
    }
    catch (error) {
        console.error('Daily inventory summary failed:', error.message);
    }
}
/**
 * Example 5: Restocking Notifications
 * Demonstrate restocking operations and notifications
 */
async function exampleRestockingOperations() {
    console.log('📦 Example: Restocking Operations');
    console.log('─'.repeat(50));
    try {
        console.log('Simulating restocking operations...');
        // Example: Restock cylinder
        console.log('Restocking cylinder...');
        // const cylinderRestock = await cylinderService.restock('cylinder_id', 20);
        console.log('Cylinder restocked - inventory levels updated');
        // Example: Restock spare part
        console.log('Restocking spare part...');
        // const sparePartRestock = await sparePartService.restock('spare_part_id', 15);
        console.log('Spare part restocked - inventory levels updated');
        // Example: Restock package
        console.log('Restocking package...');
        // const packageRestock = await packageService.restockPackage('package_id', 10);
        console.log('Package restocked - inventory levels updated');
        console.log('\n✅ All restocking operations completed');
    }
    catch (error) {
        console.error('Restocking operations failed:', error.message);
    }
}
/**
 * Example 6: Emergency Stock Alerts
 * Handle critical stock situations
 */
async function exampleEmergencyStockAlerts() {
    console.log('🚨 Example: Emergency Stock Alerts');
    console.log('─'.repeat(50));
    try {
        // Simulate critical stock situation
        const criticalItems = [
            { name: '13kg Gas Cylinder', quantity: 0 },
            { name: '6kg Gas Cylinder', quantity: 1 },
            { name: 'Regulator Spare Part', quantity: 0 },
        ];
        for (const item of criticalItems) {
            if (item.quantity === 0) {
                console.log(`🚨 CRITICAL: ${item.name} is out of stock!`);
                await notification_helper_1.Notifications.Inventory.sendOutOfStockAlert(item.name, 'so');
            }
            else if (item.quantity <= 2) {
                console.log(`⚠️ WARNING: ${item.name} is critically low (${item.quantity} remaining)`);
                await notification_helper_1.Notifications.Inventory.sendLowStockAlert(item.name, item.quantity, 'so');
            }
        }
        console.log('\n✅ Emergency alerts processed');
    }
    catch (error) {
        console.error('Emergency stock alerts failed:', error.message);
    }
}
/**
 * Example 7: Scheduled Inventory Monitoring
 * Set up periodic inventory checks
 */
async function exampleScheduledMonitoring() {
    console.log('⏰ Example: Scheduled Inventory Monitoring');
    console.log('─'.repeat(50));
    try {
        console.log('Setting up scheduled inventory monitoring...');
        // This would typically be set up with a cron job or scheduler
        // Example: Check inventory every hour
        const scheduleInventoryCheck = () => {
            setInterval(async () => {
                try {
                    console.log('Running scheduled inventory check...');
                    const results = await inventory_monitor_service_1.inventoryMonitor.checkAllInventoryLevels();
                    console.log(`Scheduled check completed: ${results.totalAlerts} alerts sent`);
                }
                catch (error) {
                    console.error('Scheduled inventory check failed:', error.message);
                }
            }, 60 * 60 * 1000); // Every hour
        };
        // Example: Send daily summary at 8 AM
        const scheduleDailySummary = () => {
            setInterval(async () => {
                const now = new Date();
                if (now.getHours() === 8 && now.getMinutes() === 0) {
                    try {
                        console.log('Sending daily inventory summary...');
                        await inventory_monitor_service_1.inventoryMonitor.sendDailyInventorySummary();
                        console.log('Daily summary sent successfully');
                    }
                    catch (error) {
                        console.error('Daily summary failed:', error.message);
                    }
                }
            }, 60 * 1000); // Check every minute
        };
        console.log('✅ Scheduled monitoring configured');
        console.log('- Hourly inventory checks enabled');
        console.log('- Daily summary at 8:00 AM enabled');
        // Note: In production, you would use a proper job scheduler like node-cron
        // scheduleInventoryCheck();
        // scheduleDailySummary();
    }
    catch (error) {
        console.error('Scheduled monitoring setup failed:', error.message);
    }
}
/**
 * Run all inventory alert examples
 */
async function runAllInventoryExamples() {
    console.log('🔥 Gas Delivery System - Inventory Alert System Examples\n');
    await exampleManualStockAlerts();
    console.log('\n' + '═'.repeat(60) + '\n');
    await exampleComprehensiveInventoryCheck();
    console.log('\n' + '═'.repeat(60) + '\n');
    await exampleOrderProcessingWithAlerts();
    console.log('\n' + '═'.repeat(60) + '\n');
    await exampleDailyInventorySummary();
    console.log('\n' + '═'.repeat(60) + '\n');
    await exampleRestockingOperations();
    console.log('\n' + '═'.repeat(60) + '\n');
    await exampleEmergencyStockAlerts();
    console.log('\n' + '═'.repeat(60) + '\n');
    await exampleScheduledMonitoring();
    console.log('\n✅ All inventory alert examples completed!');
    console.log('\n📊 Summary:');
    console.log('- Manual stock alerts: ✅ Implemented');
    console.log('- Comprehensive monitoring: ✅ Implemented');
    console.log('- Automatic order processing alerts: ✅ Implemented');
    console.log('- Daily inventory summaries: ✅ Implemented');
    console.log('- Restocking notifications: ✅ Implemented');
    console.log('- Emergency alerts: ✅ Implemented');
    console.log('- Scheduled monitoring: ✅ Configured');
}
// Run examples if this file is executed directly
if (require.main === module) {
    runAllInventoryExamples().catch(console.error);
}
//# sourceMappingURL=inventory-alerts-usage.js.map