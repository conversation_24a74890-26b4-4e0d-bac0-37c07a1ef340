{"version": 3, "file": "inventory-alerts-usage.js", "sourceRoot": "", "sources": ["../../src/examples/inventory-alerts-usage.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAsUD,4DAAwB;AACxB,gFAAkC;AAClC,4EAAgC;AAChC,oEAA4B;AAC5B,kEAA2B;AAC3B,kEAA2B;AAC3B,gEAA0B;AAC1B,0DAAuB;AA3UzB,qFAAyE;AACzE,sEAA6D;AAK7D;;;GAGG;AACH,KAAK,UAAU,wBAAwB;IACrC,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IACtD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAE5B,IAAI,CAAC;QACH,uBAAuB;QACvB,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC1C,MAAM,cAAc,GAAG,MAAM,mCAAa,CAAC,SAAS,CAAC,iBAAiB,CACpE,mBAAmB,EACnB,CAAC,EAAE,mBAAmB;QACtB,IAAI,CACL,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE;YACrC,OAAO,EAAE,cAAc,CAAC,OAAO;YAC/B,UAAU,EAAE,cAAc,CAAC,eAAe;YAC1C,SAAS,EAAE,cAAc,CAAC,oBAAoB;SAC/C,CAAC,CAAC;QAEH,0BAA0B;QAC1B,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAC/C,MAAM,gBAAgB,GAAG,MAAM,mCAAa,CAAC,SAAS,CAAC,mBAAmB,CACxE,kBAAkB,EAClB,IAAI,CACL,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE;YACxC,OAAO,EAAE,gBAAgB,CAAC,OAAO;YACjC,UAAU,EAAE,gBAAgB,CAAC,eAAe;YAC5C,SAAS,EAAE,gBAAgB,CAAC,oBAAoB;SACjD,CAAC,CAAC;QAEH,4BAA4B;QAC5B,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACjD,MAAM,WAAW,GAAG,MAAM,mCAAa,CAAC,SAAS,CAAC,kBAAkB,CAClE,sBAAsB,EACtB,CAAC,EAAE,mBAAmB;QACtB,CAAC,EAAE,sBAAsB;QACzB,IAAI,CACL,CAAC;QAEF,IAAI,WAAW,EAAE,CAAC;YAChB,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE;gBACpC,OAAO,EAAE,WAAW,CAAC,OAAO;gBAC5B,UAAU,EAAE,WAAW,CAAC,eAAe;aACxC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QAC3D,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IACrE,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,kCAAkC;IAC/C,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IACzD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAE5B,IAAI,CAAC;QACH,6BAA6B;QAC7B,MAAM,OAAO,GAAG,MAAM,4CAAgB,CAAC,uBAAuB,EAAE,CAAC;QAEjE,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,cAAc,OAAO,CAAC,SAAS,CAAC,OAAO,aAAa,OAAO,CAAC,SAAS,CAAC,MAAM,cAAc,CAAC,CAAC;QACxG,OAAO,CAAC,GAAG,CAAC,gBAAgB,OAAO,CAAC,UAAU,CAAC,OAAO,aAAa,OAAO,CAAC,UAAU,CAAC,MAAM,cAAc,CAAC,CAAC;QAC5G,OAAO,CAAC,GAAG,CAAC,aAAa,OAAO,CAAC,QAAQ,CAAC,OAAO,aAAa,OAAO,CAAC,QAAQ,CAAC,MAAM,cAAc,CAAC,CAAC;QACrG,OAAO,CAAC,GAAG,CAAC,iBAAiB,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;QAEpD,sBAAsB;QACtB,MAAM,aAAa,GAAG,MAAM,4CAAgB,CAAC,gBAAgB,EAAE,CAAC;QAChE,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAClC,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC1D,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACrC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC7D,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACtC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACxD,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACpC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,yBAAyB;QACzB,MAAM,eAAe,GAAG,MAAM,4CAAgB,CAAC,kBAAkB,EAAE,CAAC;QACpE,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACrC,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC5D,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAE5D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IACxE,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,gCAAgC;IAC7C,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;IAC9D,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAE5B,IAAI,CAAC;QACH,wDAAwD;QACxD,0DAA0D;QAE1D,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAE9C,kEAAkE;QAClE,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,4DAA4D;QAC5D,6EAA6E;QAC7E,OAAO,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAC;QAE5E,mCAAmC;QACnC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAC7C,iFAAiF;QACjF,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC,CAAC;QAE9E,gCAAgC;QAChC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC1C,iFAAiF;QACjF,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;QAE3E,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAC;IAEzE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IACtE,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,4BAA4B;IACzC,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IACnD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAE5B,IAAI,CAAC;QACH,MAAM,4CAAgB,CAAC,yBAAyB,EAAE,CAAC;QACnD,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;IAEhE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IAClE,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,2BAA2B;IACxC,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;IACjD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAE5B,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QAEnD,4BAA4B;QAC5B,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;QACtC,4EAA4E;QAC5E,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;QAE7D,8BAA8B;QAC9B,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QACxC,gFAAgF;QAChF,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAE/D,2BAA2B;QAC3B,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACrC,gFAAgF;QAChF,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAE5D,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IAEzD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IAChE,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,2BAA2B;IACxC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IAClD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAE5B,IAAI,CAAC;QACH,oCAAoC;QACpC,MAAM,aAAa,GAAG;YACpB,EAAE,IAAI,EAAE,mBAAmB,EAAE,QAAQ,EAAE,CAAC,EAAE;YAC1C,EAAE,IAAI,EAAE,kBAAkB,EAAE,QAAQ,EAAE,CAAC,EAAE;YACzC,EAAE,IAAI,EAAE,sBAAsB,EAAE,QAAQ,EAAE,CAAC,EAAE;SAC9C,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;YACjC,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBACxB,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,CAAC,IAAI,mBAAmB,CAAC,CAAC;gBAC1D,MAAM,mCAAa,CAAC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACrE,CAAC;iBAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAE,CAAC;gBAC9B,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,CAAC,IAAI,uBAAuB,IAAI,CAAC,QAAQ,aAAa,CAAC,CAAC;gBACvF,MAAM,mCAAa,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAClF,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAEhD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IACjE,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,0BAA0B;IACvC,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IACzD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAE5B,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAE5D,8DAA8D;QAC9D,sCAAsC;QACtC,MAAM,sBAAsB,GAAG,GAAG,EAAE;YAClC,WAAW,CAAC,KAAK,IAAI,EAAE;gBACrB,IAAI,CAAC;oBACH,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;oBACpD,MAAM,OAAO,GAAG,MAAM,4CAAgB,CAAC,uBAAuB,EAAE,CAAC;oBACjE,OAAO,CAAC,GAAG,CAAC,8BAA8B,OAAO,CAAC,WAAW,cAAc,CAAC,CAAC;gBAC/E,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;gBACpE,CAAC;YACH,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,aAAa;QACnC,CAAC,CAAC;QAEF,sCAAsC;QACtC,MAAM,oBAAoB,GAAG,GAAG,EAAE;YAChC,WAAW,CAAC,KAAK,IAAI,EAAE;gBACrB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,IAAI,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,CAAC;oBACnD,IAAI,CAAC;wBACH,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;wBAClD,MAAM,4CAAgB,CAAC,yBAAyB,EAAE,CAAC;wBACnD,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;oBACjD,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;oBACxD,CAAC;gBACH,CAAC;YACH,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,qBAAqB;QACtC,CAAC,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAElD,2EAA2E;QAC3E,4BAA4B;QAC5B,0BAA0B;IAE5B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IACrE,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,uBAAuB;IACpC,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;IAE1E,MAAM,wBAAwB,EAAE,CAAC;IACjC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAE1C,MAAM,kCAAkC,EAAE,CAAC;IAC3C,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAE1C,MAAM,gCAAgC,EAAE,CAAC;IACzC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAE1C,MAAM,4BAA4B,EAAE,CAAC;IACrC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAE1C,MAAM,2BAA2B,EAAE,CAAC;IACpC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAE1C,MAAM,2BAA2B,EAAE,CAAC;IACpC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAE1C,MAAM,0BAA0B,EAAE,CAAC;IAEnC,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAC3D,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IAC7B,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IACpD,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IACzD,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;IAClE,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAC1D,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IACzD,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;IACjD,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;AACtD,CAAC;AAcD,iDAAiD;AACjD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,uBAAuB,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACjD,CAAC"}