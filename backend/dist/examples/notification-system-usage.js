"use strict";
/**
 * Comprehensive Usage Examples for the Unified Notification System
 * Demonstrates how to use the new multilingual messaging service with SMS, Email, and Push notifications
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.exampleOtpVerification = exampleOtpVerification;
exports.exampleOrderLifecycle = exampleOrderLifecycle;
exports.examplePaymentNotifications = examplePaymentNotifications;
exports.exampleInventoryAlerts = exampleInventoryAlerts;
exports.exampleWelcomeMessages = exampleWelcomeMessages;
exports.exampleDirectDispatcherUsage = exampleDirectDispatcherUsage;
exports.exampleSystemNotifications = exampleSystemNotifications;
exports.exampleHealthAndMetrics = exampleHealthAndMetrics;
exports.runAllExamples = runAllExamples;
const notification_dispatcher_service_1 = require("../services/notification-dispatcher.service");
const notification_helper_1 = require("../utils/notification_helper");
const enums_1 = require("../enums/enums");
/**
 * Example 1: OTP Verification (High Priority)
 * Used during user registration and login
 */
async function exampleOtpVerification() {
    console.log('🔐 Example: OTP Verification');
    try {
        const result = await notification_helper_1.Notifications.User.sendOtpNotification('64f1234567890abcdef12345', // userId
        '+************', // phone
        '123456', // otp
        5, // expiresIn minutes
        'so' // language (Somali)
        );
        console.log('OTP notification sent:', result);
    }
    catch (error) {
        console.error('Failed to send OTP:', error.message);
    }
}
/**
 * Example 2: Order Lifecycle Notifications
 * Complete order journey from confirmation to delivery
 */
async function exampleOrderLifecycle() {
    console.log('📦 Example: Order Lifecycle Notifications');
    const customerId = '64f1234567890abcdef12345';
    const customerPhone = '+************';
    const orderId = 'ORD-2024-001';
    const agentId = '64f1234567890abcdef67890';
    const agentPhone = '+252613656022';
    try {
        // 1. Order Confirmed
        await notification_helper_1.Notifications.Order.sendOrderConfirmationNotification(customerId, customerPhone, orderId, 'so');
        console.log('✅ Order confirmation sent');
        // 2. Delivery Assignment (to agent)
        await notification_helper_1.Notifications.Order.sendDeliveryAssignmentNotification(agentId, agentPhone, orderId, 'Ahmed Mohamed', 'Hodan District, Mogadishu', 'so');
        console.log('📦 Delivery assignment sent to agent');
        // 3. Delivery Started (to customer)
        await notification_helper_1.Notifications.Order.sendDeliveryStartedNotification(customerId, customerPhone, orderId, 'Abdi Hassan', agentPhone, 'so');
        console.log('🚛 Delivery started notification sent to customer');
        // 4. Order Delivered
        await notification_helper_1.Notifications.Order.sendOrderDeliveredNotification(customerId, customerPhone, orderId, 'so');
        console.log('🎉 Order delivered notification sent');
    }
    catch (error) {
        console.error('Order lifecycle notification failed:', error.message);
    }
}
/**
 * Example 3: Payment Notifications
 * Handle payment success and failure scenarios
 */
async function examplePaymentNotifications() {
    console.log('💳 Example: Payment Notifications');
    const customerId = '64f1234567890abcdef12345';
    const customerPhone = '+************';
    const orderId = 'ORD-2024-002';
    try {
        // Payment Success
        await notification_helper_1.Notifications.Order.sendPaymentConfirmationNotification(customerId, customerPhone, orderId, 25.5, // amount
        'so');
        console.log('✅ Payment confirmation sent');
        // Payment Failure (in case of issues)
        await notification_helper_1.Notifications.Order.sendPaymentFailedNotification(customerId, customerPhone, orderId, 'Insufficient balance in your account', 'so');
        console.log('❌ Payment failure notification sent');
    }
    catch (error) {
        console.error('Payment notification failed:', error.message);
    }
}
/**
 * Example 4: Inventory Management Alerts
 * Notify admins about stock levels
 */
async function exampleInventoryAlerts() {
    console.log('📊 Example: Inventory Management Alerts');
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    const adminPhones = ['+************', '+************'];
    try {
        // Low Stock Alert (SMS + Email)
        await notification_helper_1.Notifications.Inventory.sendLowStockAlert('13kg Gas Cylinder', 5, // quantity remaining
        'so');
        console.log('⚠️ Low stock alert sent');
        // Out of Stock Alert (Urgent)
        await notification_helper_1.Notifications.Inventory.sendOutOfStockAlert('6kg Gas Cylinder', 'so');
        console.log('🚨 Out of stock alert sent');
    }
    catch (error) {
        console.error('Inventory alert failed:', error.message);
    }
}
/**
 * Example 5: User Welcome Messages
 * Role-based welcome messages for new users
 */
async function exampleWelcomeMessages() {
    console.log('🎉 Example: Welcome Messages');
    const users = [
        {
            id: '64f1234567890abcdef12345',
            phone: '+************',
            name: 'Ahmed Ali',
            role: enums_1.UserRole.CUSTOMER,
        },
        {
            id: '64f1234567890abcdef67890',
            phone: '+252613656022',
            name: 'Fatima Hassan',
            role: enums_1.UserRole.AGENT,
        },
        {
            id: '64f1234567890abcdef11111',
            phone: '+************',
            name: 'Mohamed Omar',
            role: enums_1.UserRole.ADMIN,
        },
        {
            id: '64f1234567890abcdef22222',
            phone: '+************',
            name: 'Khadija Ahmed',
            role: enums_1.UserRole.SUPERVISOR,
        },
    ];
    try {
        for (const user of users) {
            await notification_helper_1.Notifications.User.sendWelcomeMessage(user.id, user.phone, user.name, user.role, 'so');
            console.log(`👋 Welcome message sent to ${user.name} (${user.role})`);
        }
    }
    catch (error) {
        console.error('Welcome message failed:', error.message);
    }
}
/**
 * Example 6: Direct Notification Dispatcher Usage
 * For custom notification scenarios
 */
async function exampleDirectDispatcherUsage() {
    console.log('🔧 Example: Direct Dispatcher Usage');
    try {
        const result = await notification_dispatcher_service_1.notificationDispatcher.sendNotification({
            type: 'generic_notification',
            channels: ['sms', 'push', 'email'],
            recipients: [
                {
                    userId: '64f1234567890abcdef12345',
                    phone: '+************',
                    email: '<EMAIL>',
                    role: enums_1.UserRole.CUSTOMER,
                    language: 'so',
                },
            ],
            data: {
                title: 'Cusbooneysiin Muhiim Ah',
                message: 'Waxaan ku ogeysiineynaa in adeeggeenna cusub ee degdegga ah hadda la heli karo!',
            },
            options: {
                priority: 'high',
                includePhoneInFooter: true,
            },
        });
        console.log('Custom notification sent:', {
            success: result.success,
            totalRecipients: result.totalRecipients,
            successful: result.successfulDeliveries,
            failed: result.failedDeliveries,
        });
    }
    catch (error) {
        console.error('Direct dispatcher usage failed:', error.message);
    }
}
/**
 * Example 7: System Maintenance and Emergency Alerts
 * Broadcast notifications to all users
 */
async function exampleSystemNotifications() {
    console.log('🚨 Example: System Notifications');
    try {
        // Emergency Alert
        await notification_helper_1.Notifications.System.sendEmergencyAlert('Xaalad degdeg ah - dhammaan dalabka waa la joojiyay ilaa shalay subaxnimo', [enums_1.UserRole.CUSTOMER, enums_1.UserRole.AGENT], 'so');
        console.log('🚨 Emergency alert sent');
        // Maintenance Notification
        await notification_helper_1.Notifications.System.sendMaintenanceNotification('2:00 AM - 4:00 AM', '2 saacadood', 'so');
        console.log('🔧 Maintenance notification sent');
    }
    catch (error) {
        console.error('System notification failed:', error.message);
    }
}
/**
 * Example 8: Health Check and Metrics
 * Monitor notification system health
 */
async function exampleHealthAndMetrics() {
    console.log('📊 Example: Health Check and Metrics');
    try {
        // Check system health
        const health = await notification_dispatcher_service_1.notificationDispatcher.checkHealth();
        console.log('System Health:', health);
        // Get metrics
        const metrics = notification_dispatcher_service_1.notificationDispatcher.getMetrics();
        console.log('System Metrics:', {
            totalSent: metrics.totalSent,
            totalFailed: metrics.totalFailed,
            successRate: `${(metrics.successRate * 100).toFixed(2)}%`,
            channelSuccessRates: {
                sms: `${(metrics.channelSuccessRates.sms * 100).toFixed(2)}%`,
                email: `${(metrics.channelSuccessRates.email * 100).toFixed(2)}%`,
                push: `${(metrics.channelSuccessRates.push * 100).toFixed(2)}%`,
            },
        });
    }
    catch (error) {
        console.error('Health check failed:', error.message);
    }
}
/**
 * Run all examples
 */
async function runAllExamples() {
    console.log('🔥 Gas Delivery System - Unified Notification System Examples\n');
    await exampleOtpVerification();
    console.log('\n' + '─'.repeat(60) + '\n');
    await exampleOrderLifecycle();
    console.log('\n' + '─'.repeat(60) + '\n');
    await examplePaymentNotifications();
    console.log('\n' + '─'.repeat(60) + '\n');
    await exampleInventoryAlerts();
    console.log('\n' + '─'.repeat(60) + '\n');
    await exampleWelcomeMessages();
    console.log('\n' + '─'.repeat(60) + '\n');
    await exampleDirectDispatcherUsage();
    console.log('\n' + '─'.repeat(60) + '\n');
    await exampleSystemNotifications();
    console.log('\n' + '─'.repeat(60) + '\n');
    await exampleHealthAndMetrics();
    console.log('\n✅ All notification examples completed!');
}
// Run examples if this file is executed directly
if (require.main === module) {
    runAllExamples().catch(console.error);
}
//# sourceMappingURL=notification-system-usage.js.map