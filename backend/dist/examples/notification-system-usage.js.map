{"version": 3, "file": "notification-system-usage.js", "sourceRoot": "", "sources": ["../../src/examples/notification-system-usage.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAqUD,wDAAsB;AACtB,sDAAqB;AACrB,kEAA2B;AAC3B,wDAAsB;AACtB,wDAAsB;AACtB,oEAA4B;AAC5B,gEAA0B;AAC1B,0DAAuB;AACvB,wCAAc;AA3UhB,iGAAqF;AACrF,sEAA6D;AAC7D,0CAA0C;AAE1C;;;GAGG;AACH,KAAK,UAAU,sBAAsB;IACnC,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;IAE5C,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,mCAAa,CAAC,IAAI,CAAC,mBAAmB,CACzD,0BAA0B,EAAE,SAAS;QACrC,eAAe,EAAE,QAAQ;QACzB,QAAQ,EAAE,MAAM;QAChB,CAAC,EAAE,oBAAoB;QACvB,IAAI,CAAC,oBAAoB;SAC1B,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAC;IAChD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IACtD,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,qBAAqB;IAClC,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IAEzD,MAAM,UAAU,GAAG,0BAA0B,CAAC;IAC9C,MAAM,aAAa,GAAG,eAAe,CAAC;IACtC,MAAM,OAAO,GAAG,cAAc,CAAC;IAC/B,MAAM,OAAO,GAAG,0BAA0B,CAAC;IAC3C,MAAM,UAAU,GAAG,eAAe,CAAC;IAEnC,IAAI,CAAC;QACH,qBAAqB;QACrB,MAAM,mCAAa,CAAC,KAAK,CAAC,iCAAiC,CACzD,UAAU,EACV,aAAa,EACb,OAAO,EACP,IAAI,CACL,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QAEzC,oCAAoC;QACpC,MAAM,mCAAa,CAAC,KAAK,CAAC,kCAAkC,CAC1D,OAAO,EACP,UAAU,EACV,OAAO,EACP,eAAe,EACf,2BAA2B,EAC3B,IAAI,CACL,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QAEpD,oCAAoC;QACpC,MAAM,mCAAa,CAAC,KAAK,CAAC,+BAA+B,CACvD,UAAU,EACV,aAAa,EACb,OAAO,EACP,aAAa,EACb,UAAU,EACV,IAAI,CACL,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;QAEjE,qBAAqB;QACrB,MAAM,mCAAa,CAAC,KAAK,CAAC,8BAA8B,CACtD,UAAU,EACV,aAAa,EACb,OAAO,EACP,IAAI,CACL,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IACtD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IACvE,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,2BAA2B;IACxC,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;IAEjD,MAAM,UAAU,GAAG,0BAA0B,CAAC;IAC9C,MAAM,aAAa,GAAG,eAAe,CAAC;IACtC,MAAM,OAAO,GAAG,cAAc,CAAC;IAE/B,IAAI,CAAC;QACH,kBAAkB;QAClB,MAAM,mCAAa,CAAC,KAAK,CAAC,mCAAmC,CAC3D,UAAU,EACV,aAAa,EACb,OAAO,EACP,IAAI,EAAE,SAAS;QACf,IAAI,CACL,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAE3C,sCAAsC;QACtC,MAAM,mCAAa,CAAC,KAAK,CAAC,6BAA6B,CACrD,UAAU,EACV,aAAa,EACb,OAAO,EACP,sCAAsC,EACtC,IAAI,CACL,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IACrD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IAC/D,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,sBAAsB;IACnC,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IAEvD,MAAM,WAAW,GAAG,CAAC,6BAA6B,EAAE,+BAA+B,CAAC,CAAC;IACrF,MAAM,WAAW,GAAG,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC;IAEvD,IAAI,CAAC;QACH,gCAAgC;QAChC,MAAM,mCAAa,CAAC,SAAS,CAAC,iBAAiB,CAC7C,mBAAmB,EACnB,CAAC,EAAE,qBAAqB;QACxB,IAAI,CACL,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QAEvC,8BAA8B;QAC9B,MAAM,mCAAa,CAAC,SAAS,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;QAC5E,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC5C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IAC1D,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,sBAAsB;IACnC,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;IAE5C,MAAM,KAAK,GAAG;QACZ;YACE,EAAE,EAAE,0BAA0B;YAC9B,KAAK,EAAE,eAAe;YACtB,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,gBAAQ,CAAC,QAAQ;SACxB;QACD;YACE,EAAE,EAAE,0BAA0B;YAC9B,KAAK,EAAE,eAAe;YACtB,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE,gBAAQ,CAAC,KAAK;SACrB;QACD;YACE,EAAE,EAAE,0BAA0B;YAC9B,KAAK,EAAE,eAAe;YACtB,IAAI,EAAE,cAAc;YACpB,IAAI,EAAE,gBAAQ,CAAC,KAAK;SACrB;QACD;YACE,EAAE,EAAE,0BAA0B;YAC9B,KAAK,EAAE,eAAe;YACtB,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE,gBAAQ,CAAC,UAAU;SAC1B;KACF,CAAC;IAEF,IAAI,CAAC;QACH,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,mCAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC7F,OAAO,CAAC,GAAG,CAAC,8BAA8B,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IAC1D,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,4BAA4B;IACzC,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IAEnD,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,wDAAsB,CAAC,gBAAgB,CAAC;YAC3D,IAAI,EAAE,sBAAsB;YAC5B,QAAQ,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC;YAClC,UAAU,EAAE;gBACV;oBACE,MAAM,EAAE,0BAA0B;oBAClC,KAAK,EAAE,eAAe;oBACtB,KAAK,EAAE,sBAAsB;oBAC7B,IAAI,EAAE,gBAAQ,CAAC,QAAQ;oBACvB,QAAQ,EAAE,IAAI;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,KAAK,EAAE,yBAAyB;gBAChC,OAAO,EAAE,iFAAiF;aAC3F;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE,MAAM;gBAChB,oBAAoB,EAAE,IAAI;aAC3B;SACF,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE;YACvC,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,eAAe,EAAE,MAAM,CAAC,eAAe;YACvC,UAAU,EAAE,MAAM,CAAC,oBAAoB;YACvC,MAAM,EAAE,MAAM,CAAC,gBAAgB;SAChC,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IAClE,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,0BAA0B;IACvC,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAEhD,IAAI,CAAC;QACH,kBAAkB;QAClB,MAAM,mCAAa,CAAC,MAAM,CAAC,kBAAkB,CAC3C,2EAA2E,EAC3E,CAAC,gBAAQ,CAAC,QAAQ,EAAE,gBAAQ,CAAC,KAAK,CAAC,EACnC,IAAI,CACL,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QAEvC,2BAA2B;QAC3B,MAAM,mCAAa,CAAC,MAAM,CAAC,2BAA2B,CACpD,mBAAmB,EACnB,aAAa,EACb,IAAI,CACL,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAClD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IAC9D,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,uBAAuB;IACpC,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IAEpD,IAAI,CAAC;QACH,sBAAsB;QACtB,MAAM,MAAM,GAAG,MAAM,wDAAsB,CAAC,WAAW,EAAE,CAAC;QAC1D,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;QAEtC,cAAc;QACd,MAAM,OAAO,GAAG,wDAAsB,CAAC,UAAU,EAAE,CAAC;QACpD,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE;YAC7B,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,WAAW,EAAE,GAAG,CAAC,OAAO,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;YACzD,mBAAmB,EAAE;gBACnB,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;gBAC7D,KAAK,EAAE,GAAG,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;gBACjE,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;aAChE;SACF,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IACvD,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,cAAc;IAC3B,OAAO,CAAC,GAAG,CAAC,iEAAiE,CAAC,CAAC;IAE/E,MAAM,sBAAsB,EAAE,CAAC;IAC/B,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAE1C,MAAM,qBAAqB,EAAE,CAAC;IAC9B,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAE1C,MAAM,2BAA2B,EAAE,CAAC;IACpC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAE1C,MAAM,sBAAsB,EAAE,CAAC;IAC/B,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAE1C,MAAM,sBAAsB,EAAE,CAAC;IAC/B,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAE1C,MAAM,4BAA4B,EAAE,CAAC;IACrC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAE1C,MAAM,0BAA0B,EAAE,CAAC;IACnC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAE1C,MAAM,uBAAuB,EAAE,CAAC;IAEhC,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;AAC1D,CAAC;AAeD,iDAAiD;AACjD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,cAAc,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACxC,CAAC"}