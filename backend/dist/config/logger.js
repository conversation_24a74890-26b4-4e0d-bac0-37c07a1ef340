"use strict";
// import winston from 'winston';
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.log = void 0;
// const { combine, timestamp, printf, colorize, errors } = winston.format;
// const logFormat = printf(({ level, message, timestamp, stack }) => {
//   return `${timestamp} [${level}]: ${stack || message}`;
// });
// const logger = winston.createLogger({
//   level: 'info',
//   format: combine(
//     colorize(),
//     timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
//     errors({ stack: true }),
//     logFormat
//   ),
//   transports: [
//     new winston.transports.Console(),
//     // new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
//     //   new winston.transports.File({ filename: 'logs/combined.log' }),
//   ],
//   // exceptionHandlers: [new winston.transports.File({ filename: 'logs/exceptions.log' })],
// });
// export default logger;
const winston_1 = __importDefault(require("winston"));
const { combine, timestamp, printf, colorize, errors } = winston_1.default.format;
const logFormat = printf(({ level, message, timestamp, stack, ...metadata }) => {
    let logMessage = `${timestamp} [${level}]: ${stack || message}`;
    if (Object.keys(metadata).length > 0) {
        logMessage += `\n${JSON.stringify(metadata, null, 2)}`;
    }
    return logMessage;
});
const logger = winston_1.default.createLogger({
    level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
    format: combine(colorize(), timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), errors({ stack: true }), logFormat),
    transports: [new winston_1.default.transports.Console()],
    exitOnError: false, // Continue on logging errors
});
// Export for use in other files
exports.default = logger;
// Optional: Export individual log levels as functions
exports.log = {
    info: (message, meta) => logger.info(message, meta),
    error: (message, meta) => logger.error(message, meta),
    warn: (message, meta) => logger.warn(message, meta),
    debug: (message, meta) => logger.debug(message, meta),
    verbose: (message, meta) => logger.verbose(message, meta),
};
//# sourceMappingURL=logger.js.map