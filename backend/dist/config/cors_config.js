"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.logCorsConfig = exports.getCorsOptions = exports.devCorsOptions = exports.corsOptions = void 0;
const env_config_1 = require("./env_config");
/**
 * CORS Configuration for Gas System Project
 *
 * This configuration handles Cross-Origin Resource Sharing (CORS) for the API.
 * It allows requests from authorized frontend applications while maintaining security.
 */
/**
 * Get allowed origins based on environment
 */
const getAllowedOrigins = () => {
    const baseOrigins = [
        ...env_config_1.config.server.corsOrigins, // From environment config
        env_config_1.config.server.frontendUrl, // Frontend URL from env
        env_config_1.config.server.url, // Server URL from env
    ];
    // Frontend URLs based on api_end_points.dart
    const frontendOrigins = [
        'http://*************:3000', // Flutter web (port 3000)
        'http://*************:8080', // Flutter web (port 8080)
        'http://*************:3000', // Alternative local network
        'http://*************:8080', // Alternative local network
        'http://localhost:3000', // Local development
        'http://127.0.0.1:3000', // Local development alternative
        'http://localhost:8080', // Local development alternative port
        'http://127.0.0.1:8080', // Local development alternative port
    ];
    // Production URLs
    const productionOrigins = [
        'https://gas-booking-system-production.up.railway.app', // Railway production
        'https://your-frontend-domain.com', // Custom production domain
        'https://admin.your-domain.com', // Admin panel domain
    ];
    return [...baseOrigins, ...frontendOrigins, ...productionOrigins];
};
/**
 * Check if origin is allowed in development mode
 */
const isDevOriginAllowed = (origin) => {
    // Allow any localhost, 127.0.0.1, or local network IP (192.168.x.x, 10.x.x.x, 172.16-31.x.x)
    const localPatterns = [
        /^https?:\/\/localhost(:\d+)?$/, // localhost with optional port
        /^https?:\/\/127\.0\.0\.1(:\d+)?$/, // 127.0.0.1 with optional port
        /^https?:\/\/192\.168\.\d+\.\d+(:\d+)?$/, // 192.168.x.x with optional port
        /^https?:\/\/10\.\d+\.\d+\.\d+(:\d+)?$/, // 10.x.x.x with optional port
        /^https?:\/\/172\.(1[6-9]|2\d|3[01])\.\d+\.\d+(:\d+)?$/, // 172.16-31.x.x with optional port
    ];
    return localPatterns.some(pattern => pattern.test(origin));
};
/**
 * CORS Options Configuration
 */
exports.corsOptions = {
    origin: function (origin, callback) {
        // Allow requests with no origin (mobile apps, Postman, curl, etc.)
        if (!origin) {
            return callback(null, true);
        }
        const allowedOrigins = getAllowedOrigins();
        // Check if origin is in allowed list
        if (allowedOrigins.includes(origin)) {
            return callback(null, true);
        }
        // In development, be more permissive with local origins
        if (env_config_1.config.server.env === 'development' && isDevOriginAllowed(origin)) {
            return callback(null, true);
        }
        // Log rejected origins for debugging
        console.warn(`🚫 CORS: Origin ${origin} not allowed`);
        console.log(`📋 Allowed origins:`, allowedOrigins);
        callback(new Error(`Origin ${origin} not allowed by CORS policy`));
    },
    // Allow credentials (cookies, authorization headers, etc.)
    credentials: true,
    // Allowed HTTP methods
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'],
    // Allowed request headers
    allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'Cache-Control',
        'Pragma',
        'X-API-Key',
        'X-Client-Version',
        'X-Device-ID',
        'X-Platform',
    ],
    // Headers exposed to the client
    exposedHeaders: [
        'Content-Length',
        'X-Total-Count',
        'X-Page-Count',
        'X-Current-Page',
        'Authorization',
        'X-RateLimit-Limit',
        'X-RateLimit-Remaining',
    ],
    // Cache preflight response for 24 hours
    maxAge: 86400,
    // Handle preflight requests automatically
    preflightContinue: false,
    optionsSuccessStatus: 204, // Some legacy browsers (IE11) choke on 204
};
/**
 * Development-specific CORS options (more permissive)
 */
exports.devCorsOptions = {
    ...exports.corsOptions,
    origin: true, // Allow all origins in development
};
/**
 * Get CORS options based on environment
 */
const getCorsOptions = () => {
    return env_config_1.config.server.env === 'development' ? exports.corsOptions : exports.corsOptions;
};
exports.getCorsOptions = getCorsOptions;
/**
 * Log CORS configuration on startup
 */
const logCorsConfig = () => {
    console.log('🌐 CORS Configuration:');
    console.log(`   Environment: ${env_config_1.config.server.env}`);
    console.log(`   Allowed Origins: ${getAllowedOrigins().length} origins`);
    console.log(`   Credentials: ${exports.corsOptions.credentials ? 'Enabled' : 'Disabled'}`);
    console.log(`   Methods: ${exports.corsOptions.methods}`);
    if (env_config_1.config.server.env === 'development') {
        console.log('   🔓 Development mode: Local network origins allowed');
    }
    else {
        console.log('   🔒 Production mode: Strict origin validation');
    }
};
exports.logCorsConfig = logCorsConfig;
//# sourceMappingURL=cors_config.js.map