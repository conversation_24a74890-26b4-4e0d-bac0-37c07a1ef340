{"version": 3, "file": "cors_config.js", "sourceRoot": "", "sources": ["../../src/config/cors_config.ts"], "names": [], "mappings": ";;;AACA,6CAAsC;AAEtC;;;;;GAKG;AAEH;;GAEG;AACH,MAAM,iBAAiB,GAAG,GAAa,EAAE;IACvC,MAAM,WAAW,GAAG;QAClB,GAAG,mBAAM,CAAC,MAAM,CAAC,WAAW,EAAE,0BAA0B;QACxD,mBAAM,CAAC,MAAM,CAAC,WAAW,EAAE,wBAAwB;QACnD,mBAAM,CAAC,MAAM,CAAC,GAAG,EAAE,sBAAsB;KAC1C,CAAC;IAEF,6CAA6C;IAC7C,MAAM,eAAe,GAAG;QACtB,2BAA2B,EAAE,0BAA0B;QACvD,2BAA2B,EAAE,0BAA0B;QACvD,2BAA2B,EAAE,4BAA4B;QACzD,2BAA2B,EAAE,4BAA4B;QACzD,uBAAuB,EAAE,oBAAoB;QAC7C,uBAAuB,EAAE,gCAAgC;QACzD,uBAAuB,EAAE,qCAAqC;QAC9D,uBAAuB,EAAE,qCAAqC;KAC/D,CAAC;IAEF,kBAAkB;IAClB,MAAM,iBAAiB,GAAG;QACxB,sDAAsD,EAAE,qBAAqB;QAC7E,kCAAkC,EAAE,2BAA2B;QAC/D,+BAA+B,EAAE,qBAAqB;KACvD,CAAC;IAEF,OAAO,CAAC,GAAG,WAAW,EAAE,GAAG,eAAe,EAAE,GAAG,iBAAiB,CAAC,CAAC;AACpE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,kBAAkB,GAAG,CAAC,MAAc,EAAW,EAAE;IACrD,6FAA6F;IAC7F,MAAM,aAAa,GAAG;QACpB,+BAA+B,EAAE,+BAA+B;QAChE,kCAAkC,EAAE,+BAA+B;QACnE,wCAAwC,EAAE,iCAAiC;QAC3E,uCAAuC,EAAE,8BAA8B;QACvE,uDAAuD,EAAE,mCAAmC;KAC7F,CAAC;IAEF,OAAO,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AAC7D,CAAC,CAAC;AAEF;;GAEG;AACU,QAAA,WAAW,GAAgB;IACtC,MAAM,EAAE,UACN,MAA0B,EAC1B,QAAsD;QAEtD,mEAAmE;QACnE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC9B,CAAC;QAED,MAAM,cAAc,GAAG,iBAAiB,EAAE,CAAC;QAE3C,qCAAqC;QACrC,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC9B,CAAC;QAED,wDAAwD;QACxD,IAAI,mBAAM,CAAC,MAAM,CAAC,GAAG,KAAK,aAAa,IAAI,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC;YACtE,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC9B,CAAC;QAED,qCAAqC;QACrC,OAAO,CAAC,IAAI,CAAC,mBAAmB,MAAM,cAAc,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,cAAc,CAAC,CAAC;QAEnD,QAAQ,CAAC,IAAI,KAAK,CAAC,UAAU,MAAM,6BAA6B,CAAC,CAAC,CAAC;IACrE,CAAC;IAED,2DAA2D;IAC3D,WAAW,EAAE,IAAI;IAEjB,uBAAuB;IACvB,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC;IAErE,0BAA0B;IAC1B,cAAc,EAAE;QACd,QAAQ;QACR,kBAAkB;QAClB,cAAc;QACd,QAAQ;QACR,eAAe;QACf,eAAe;QACf,QAAQ;QACR,WAAW;QACX,kBAAkB;QAClB,aAAa;QACb,YAAY;KACb;IAED,gCAAgC;IAChC,cAAc,EAAE;QACd,gBAAgB;QAChB,eAAe;QACf,cAAc;QACd,gBAAgB;QAChB,eAAe;QACf,mBAAmB;QACnB,uBAAuB;KACxB;IAED,wCAAwC;IACxC,MAAM,EAAE,KAAK;IAEb,0CAA0C;IAC1C,iBAAiB,EAAE,KAAK;IACxB,oBAAoB,EAAE,GAAG,EAAE,2CAA2C;CACvE,CAAC;AAEF;;GAEG;AACU,QAAA,cAAc,GAAgB;IACzC,GAAG,mBAAW;IACd,MAAM,EAAE,IAAI,EAAE,mCAAmC;CAClD,CAAC;AAEF;;GAEG;AACI,MAAM,cAAc,GAAG,GAAgB,EAAE;IAC9C,OAAO,mBAAM,CAAC,MAAM,CAAC,GAAG,KAAK,aAAa,CAAC,CAAC,CAAC,mBAAW,CAAC,CAAC,CAAC,mBAAW,CAAC;AACzE,CAAC,CAAC;AAFW,QAAA,cAAc,kBAEzB;AAEF;;GAEG;AACI,MAAM,aAAa,GAAG,GAAS,EAAE;IACtC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;IACtC,OAAO,CAAC,GAAG,CAAC,mBAAmB,mBAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;IACpD,OAAO,CAAC,GAAG,CAAC,uBAAuB,iBAAiB,EAAE,CAAC,MAAM,UAAU,CAAC,CAAC;IACzE,OAAO,CAAC,GAAG,CAAC,mBAAmB,mBAAW,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;IACnF,OAAO,CAAC,GAAG,CAAC,eAAe,mBAAW,CAAC,OAAO,EAAE,CAAC,CAAC;IAElD,IAAI,mBAAM,CAAC,MAAM,CAAC,GAAG,KAAK,aAAa,EAAE,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;IACvE,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;IACjE,CAAC;AACH,CAAC,CAAC;AAZW,QAAA,aAAa,iBAYxB"}