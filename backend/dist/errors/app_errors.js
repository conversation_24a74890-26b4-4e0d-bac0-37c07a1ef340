"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentAlreadyCompleted = exports.InsufficientFundsError = exports.WaaFiServiceUnavailable = exports.WaaFiPreauthExpired = exports.WaaFiPaymentFailed = exports.PaymentError = exports.HormuudServiceError = exports.HormuudSmsError = exports.InternalServerError = exports.DuplicateResourceError = exports.ForbiddenError = exports.UnauthorizedError = exports.ValidationError = exports.BadRequestError = exports.NotFoundError = exports.AppError = void 0;
// app_errors.ts
class AppError extends Error {
    statusCode;
    status;
    isOperational;
    details;
    code;
    parentCode;
    constructor(message, statusCode, parentCode, options) {
        super(message);
        this.statusCode = statusCode;
        this.status = String(statusCode).startsWith('4') ? 'fail' : 'error';
        this.isOperational = true;
        this.parentCode = parentCode;
        if (options?.details)
            this.details = options.details;
        if (options?.code)
            this.code = options.code;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
// 🔍 404 Not Found
class NotFoundError extends AppError {
    constructor(message = 'Resource not found', options) {
        super(message, 404, 'NOT_FOUND', options);
    }
}
exports.NotFoundError = NotFoundError;
// 🛑 400 Bad Request
class BadRequestError extends AppError {
    constructor(message = 'Bad request', options) {
        super(message, 400, 'BAD_REQUEST', options);
    }
}
exports.BadRequestError = BadRequestError;
// 🧪 400 Validation Error
class ValidationError extends AppError {
    constructor(message = 'Validation failed', options) {
        super(message, 400, 'VALIDATION_ERROR', options);
    }
}
exports.ValidationError = ValidationError;
// 🔒 401 Unauthorized
class UnauthorizedError extends AppError {
    constructor(message = 'Unauthorized', options) {
        super(message, 401, 'UNAUTHORIZED', options);
    }
}
exports.UnauthorizedError = UnauthorizedError;
// ⛔ 403 Forbidden
class ForbiddenError extends AppError {
    constructor(message = 'Forbidden', options) {
        super(message, 403, 'FORBIDDEN', options);
    }
}
exports.ForbiddenError = ForbiddenError;
// ♻️ 409 Conflict
class DuplicateResourceError extends AppError {
    constructor(message = 'Resource already exists', options) {
        super(message, 409, 'DUPLICATE_RESOURCE', options);
    }
}
exports.DuplicateResourceError = DuplicateResourceError;
// 💥 500 Internal Server Error
class InternalServerError extends AppError {
    constructor(message = 'Internal server error', options) {
        super(message, 500, 'INTERNAL_SERVER_ERROR', options);
    }
}
exports.InternalServerError = InternalServerError;
// ======================
// 📱 Hormuud SMS/OTP Errors
// ======================
class HormuudSmsError extends AppError {
    constructor(hormuudResponseCode, responseData, options) {
        const errorConfig = {
            '201': { message: 'Hormuud authentication failed', statusCode: 401 },
            '203': { message: 'Invalid sender ID', statusCode: 400 },
            '204': { message: 'Zero SMS balance', statusCode: 402 },
            '205': { message: 'Insufficient SMS balance', statusCode: 402 },
            '206': { message: options?.isOtp ? 'OTP too long' : 'Message too long', statusCode: 400 },
            '207': { message: 'Invalid mobile number', statusCode: 400 },
            '500': { message: 'Hormuud internal error', statusCode: 500 },
        }[hormuudResponseCode] || { message: 'Unknown Hormuud error', statusCode: 500 };
        super(errorConfig.message, errorConfig.statusCode, `HORMUUD_${hormuudResponseCode}`, {
            details: {
                ...responseData,
                ...(options?.recipient && { recipient: options.recipient }),
                ...(options?.messageContent && { messageContent: options.messageContent }),
                isOtp: options?.isOtp || false,
            },
            code: hormuudResponseCode,
        });
    }
}
exports.HormuudSmsError = HormuudSmsError;
class HormuudServiceError extends AppError {
    constructor(message = 'Hormuud service unavailable', options) {
        super(message, 503, 'HORMUUD_UNAVAILABLE', {
            ...options,
            details: {
                service: 'sms',
                ...(options?.isOtp && { service: 'otp' }),
                ...(options?.retryAfter && { retryAfter: options.retryAfter }),
            },
        });
    }
}
exports.HormuudServiceError = HormuudServiceError;
// ======================
// 💳 WaaFi Payment Errors
// ======================
class PaymentError extends AppError {
    constructor(message, statusCode, parentCode, options) {
        super(message, statusCode, parentCode, {
            ...options,
            details: {
                ...(options?.paymentId && { paymentId: options.paymentId }),
                ...(options?.orderId && { orderId: options.orderId }),
                ...(options?.amount && { amount: options.amount }),
                ...(typeof options?.details === 'object' && options?.details !== null
                    ? options.details
                    : {}),
            },
        });
    }
}
exports.PaymentError = PaymentError;
// Core WaaFi Errors
class WaaFiPaymentFailed extends PaymentError {
    constructor(message = 'WaaFi payment failed', options) {
        super(message, 400, 'WAAFI_PAYMENT_FAILED', options);
    }
}
exports.WaaFiPaymentFailed = WaaFiPaymentFailed;
class WaaFiPreauthExpired extends PaymentError {
    constructor(message = 'WaaFi preauthorization expired', options) {
        super(message, 400, 'WAAFI_PREAUTH_EXPIRED', {
            ...options,
            details: {
                ...(options?.expiryDate && { expiryDate: options.expiryDate }),
            },
        });
    }
}
exports.WaaFiPreauthExpired = WaaFiPreauthExpired;
class WaaFiServiceUnavailable extends PaymentError {
    constructor(message = 'WaaFi payment service unavailable', options) {
        super(message, 503, 'WAAFI_UNAVAILABLE', {
            details: {
                ...(options?.retryAfter && { retryAfter: options.retryAfter }),
            },
        });
    }
}
exports.WaaFiServiceUnavailable = WaaFiServiceUnavailable;
// Utility Errors
class InsufficientFundsError extends PaymentError {
    constructor(amount, options) {
        super(`Insufficient funds for payment of ${amount}`, 400, 'INSUFFICIENT_FUNDS', {
            ...options,
            amount,
            details: {
                ...(options?.balance && { availableBalance: options.balance }),
            },
        });
    }
}
exports.InsufficientFundsError = InsufficientFundsError;
class PaymentAlreadyCompleted extends PaymentError {
    constructor(paymentId, options) {
        super(`Payment ${paymentId} already completed`, 409, 'PAYMENT_COMPLETED', {
            paymentId,
            ...options,
            details: {
                ...(options?.completedAt && { completedAt: options.completedAt }),
            },
        });
    }
}
exports.PaymentAlreadyCompleted = PaymentAlreadyCompleted;
//# sourceMappingURL=app_errors.js.map