{"version": 3, "file": "app_errors.js", "sourceRoot": "", "sources": ["../../src/errors/app_errors.ts"], "names": [], "mappings": ";;;AAAA,gBAAgB;AAChB,MAAa,QAAS,SAAQ,KAAK;IACjC,UAAU,CAAS;IACnB,MAAM,CAAmB;IACzB,aAAa,CAAU;IACvB,OAAO,CAAW;IAClB,IAAI,CAAU;IACd,UAAU,CAAS;IAEnB,YACE,OAAe,EACf,UAAkB,EAClB,UAAkB,EAClB,OAGC;QAED,KAAK,CAAC,OAAO,CAAC,CAAC;QAEf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;QACpE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,IAAI,OAAO,EAAE,OAAO;YAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QACrD,IAAI,OAAO,EAAE,IAAI;YAAE,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAE5C,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AA7BD,4BA6BC;AAED,mBAAmB;AACnB,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,OAAO,GAAG,oBAAoB,EAAE,OAA8C;QACxF,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;IAC5C,CAAC;CACF;AAJD,sCAIC;AAED,qBAAqB;AACrB,MAAa,eAAgB,SAAQ,QAAQ;IAC3C,YAAY,OAAO,GAAG,aAAa,EAAE,OAA8C;QACjF,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;CACF;AAJD,0CAIC;AAED,0BAA0B;AAC1B,MAAa,eAAgB,SAAQ,QAAQ;IAC3C,YAAY,OAAO,GAAG,mBAAmB,EAAE,OAA8C;QACvF,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;CACF;AAJD,0CAIC;AAED,sBAAsB;AACtB,MAAa,iBAAkB,SAAQ,QAAQ;IAC7C,YAAY,OAAO,GAAG,cAAc,EAAE,OAA8C;QAClF,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;IAC/C,CAAC;CACF;AAJD,8CAIC;AAED,kBAAkB;AAClB,MAAa,cAAe,SAAQ,QAAQ;IAC1C,YAAY,OAAO,GAAG,WAAW,EAAE,OAA8C;QAC/E,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;IAC5C,CAAC;CACF;AAJD,wCAIC;AAED,kBAAkB;AAClB,MAAa,sBAAuB,SAAQ,QAAQ;IAClD,YAAY,OAAO,GAAG,yBAAyB,EAAE,OAA8C;QAC7F,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,oBAAoB,EAAE,OAAO,CAAC,CAAC;IACrD,CAAC;CACF;AAJD,wDAIC;AAED,+BAA+B;AAC/B,MAAa,mBAAoB,SAAQ,QAAQ;IAC/C,YAAY,OAAO,GAAG,uBAAuB,EAAE,OAA8C;QAC3F,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,uBAAuB,EAAE,OAAO,CAAC,CAAC;IACxD,CAAC;CACF;AAJD,kDAIC;AAED,yBAAyB;AACzB,4BAA4B;AAC5B,yBAAyB;AAEzB,MAAa,eAAgB,SAAQ,QAAQ;IAC3C,YACE,mBAA2B,EAC3B,YAAiB,EACjB,OAKC;QAED,MAAM,WAAW,GAAG;YAClB,KAAK,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE,UAAU,EAAE,GAAG,EAAE;YACpE,KAAK,EAAE,EAAE,OAAO,EAAE,mBAAmB,EAAE,UAAU,EAAE,GAAG,EAAE;YACxD,KAAK,EAAE,EAAE,OAAO,EAAE,kBAAkB,EAAE,UAAU,EAAE,GAAG,EAAE;YACvD,KAAK,EAAE,EAAE,OAAO,EAAE,0BAA0B,EAAE,UAAU,EAAE,GAAG,EAAE;YAC/D,KAAK,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,kBAAkB,EAAE,UAAU,EAAE,GAAG,EAAE;YACzF,KAAK,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,UAAU,EAAE,GAAG,EAAE;YAC5D,KAAK,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,UAAU,EAAE,GAAG,EAAE;SAC9D,CAAC,mBAAmB,CAAC,IAAI,EAAE,OAAO,EAAE,uBAAuB,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;QAEhF,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,UAAU,EAAE,WAAW,mBAAmB,EAAE,EAAE;YACnF,OAAO,EAAE;gBACP,GAAG,YAAY;gBACf,GAAG,CAAC,OAAO,EAAE,SAAS,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC;gBAC3D,GAAG,CAAC,OAAO,EAAE,cAAc,IAAI,EAAE,cAAc,EAAE,OAAO,CAAC,cAAc,EAAE,CAAC;gBAC1E,KAAK,EAAE,OAAO,EAAE,KAAK,IAAI,KAAK;aAC/B;YACD,IAAI,EAAE,mBAAmB;SAC1B,CAAC,CAAC;IACL,CAAC;CACF;AA/BD,0CA+BC;AAED,MAAa,mBAAoB,SAAQ,QAAQ;IAC/C,YACE,OAAO,GAAG,6BAA6B,EACvC,OAIC;QAED,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,qBAAqB,EAAE;YACzC,GAAG,OAAO;YACV,OAAO,EAAE;gBACP,OAAO,EAAE,KAAK;gBACd,GAAG,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBACzC,GAAG,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,UAAU,EAAE,CAAC;aAC/D;SACF,CAAC,CAAC;IACL,CAAC;CACF;AAlBD,kDAkBC;AAED,yBAAyB;AACzB,0BAA0B;AAC1B,yBAAyB;AAEzB,MAAa,YAAa,SAAQ,QAAQ;IACxC,YACE,OAAe,EACf,UAAkB,EAClB,UAAkB,EAClB,OAKC;QAED,KAAK,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE;YACrC,GAAG,OAAO;YACV,OAAO,EAAE;gBACP,GAAG,CAAC,OAAO,EAAE,SAAS,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC;gBAC3D,GAAG,CAAC,OAAO,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC;gBACrD,GAAG,CAAC,OAAO,EAAE,MAAM,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC;gBAClD,GAAG,CAAC,OAAO,OAAO,EAAE,OAAO,KAAK,QAAQ,IAAI,OAAO,EAAE,OAAO,KAAK,IAAI;oBACnE,CAAC,CAAC,OAAO,CAAC,OAAO;oBACjB,CAAC,CAAC,EAAE,CAAC;aACR;SACF,CAAC,CAAC;IACL,CAAC;CACF;AAxBD,oCAwBC;AAED,oBAAoB;AACpB,MAAa,kBAAmB,SAAQ,YAAY;IAClD,YACE,OAAO,GAAG,sBAAsB,EAChC,OAKC;QAED,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,sBAAsB,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;CACF;AAZD,gDAYC;AAED,MAAa,mBAAoB,SAAQ,YAAY;IACnD,YACE,OAAO,GAAG,gCAAgC,EAC1C,OAIC;QAED,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,uBAAuB,EAAE;YAC3C,GAAG,OAAO;YACV,OAAO,EAAE;gBACP,GAAG,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,UAAU,EAAE,CAAC;aAC/D;SACF,CAAC,CAAC;IACL,CAAC;CACF;AAhBD,kDAgBC;AAED,MAAa,uBAAwB,SAAQ,YAAY;IACvD,YACE,OAAO,GAAG,mCAAmC,EAC7C,OAEC;QAED,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,mBAAmB,EAAE;YACvC,OAAO,EAAE;gBACP,GAAG,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,UAAU,EAAE,CAAC;aAC/D;SACF,CAAC,CAAC;IACL,CAAC;CACF;AAbD,0DAaC;AAED,iBAAiB;AACjB,MAAa,sBAAuB,SAAQ,YAAY;IACtD,YACE,MAAc,EACd,OAGC;QAED,KAAK,CAAC,qCAAqC,MAAM,EAAE,EAAE,GAAG,EAAE,oBAAoB,EAAE;YAC9E,GAAG,OAAO;YACV,MAAM;YACN,OAAO,EAAE;gBACP,GAAG,CAAC,OAAO,EAAE,OAAO,IAAI,EAAE,gBAAgB,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC;aAC/D;SACF,CAAC,CAAC;IACL,CAAC;CACF;AAhBD,wDAgBC;AAED,MAAa,uBAAwB,SAAQ,YAAY;IACvD,YACE,SAAiB,EACjB,OAGC;QAED,KAAK,CAAC,WAAW,SAAS,oBAAoB,EAAE,GAAG,EAAE,mBAAmB,EAAE;YACxE,SAAS;YACT,GAAG,OAAO;YACV,OAAO,EAAE;gBACP,GAAG,CAAC,OAAO,EAAE,WAAW,IAAI,EAAE,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC;aAClE;SACF,CAAC,CAAC;IACL,CAAC;CACF;AAhBD,0DAgBC"}