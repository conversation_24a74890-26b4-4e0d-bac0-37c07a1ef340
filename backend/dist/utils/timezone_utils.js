"use strict";
/**
 * Centralized Timezone Management System
 * Handles all date/time operations with proper timezone conversion
 * for Ciribey Gas Delivery System (Mogadishu, Somalia - EAT UTC+3)
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatDateForUser = exports.buildDateQuery = exports.convertDateRangeToUTC = exports.getDayBoundaries = exports.getUserTimezone = exports.timezoneManager = exports.TimezoneManager = exports.APP_TIMEZONE_OFFSET = exports.APP_TIMEZONE = void 0;
const logger_1 = __importDefault(require("../config/logger"));
// Application timezone configuration
exports.APP_TIMEZONE = 'Africa/Mogadishu'; // EAT (UTC+3)
exports.APP_TIMEZONE_OFFSET = '+03:00';
/**
 * Timezone utility class for centralized date/time management
 */
class TimezoneManager {
    static instance;
    constructor() { }
    static getInstance() {
        if (!TimezoneManager.instance) {
            TimezoneManager.instance = new TimezoneManager();
        }
        return TimezoneManager.instance;
    }
    /**
     * Get the appropriate timezone for a user based on their role and context
     * @param context - User context with role and timezone info
     * @returns timezone string (IANA timezone identifier)
     */
    getUserTimezone(context = {}) {
        // Priority order:
        // 1. Explicitly provided client timezone
        // 2. User's stored timezone preference (if implemented)
        // 3. Application default timezone (Mogadishu)
        if (context.clientTimezone) {
            return this.validateTimezone(context.clientTimezone) ? context.clientTimezone : exports.APP_TIMEZONE;
        }
        if (context.timezone) {
            return this.validateTimezone(context.timezone) ? context.timezone : exports.APP_TIMEZONE;
        }
        // Default to Mogadishu timezone for all users
        return exports.APP_TIMEZONE;
    }
    /**
     * Validate if a timezone string is valid
     * @param timezone - IANA timezone identifier
     * @returns true if valid, false otherwise
     */
    validateTimezone(timezone) {
        try {
            Intl.DateTimeFormat(undefined, { timeZone: timezone });
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * Get start and end of day in user's timezone
     * @param date - Date to get day boundaries for (defaults to today)
     * @param context - User timezone context
     * @returns DateRange with start and end of day in UTC for MongoDB queries
     */
    getDayBoundaries(date = new Date(), context = {}) {
        const userTimezone = this.getUserTimezone(context);
        try {
            // Create date in user's timezone
            const userDate = new Date(date.toLocaleString('en-US', { timeZone: userTimezone }));
            // Get start of day in user's timezone
            const startOfDay = new Date(userDate);
            startOfDay.setHours(0, 0, 0, 0);
            // Get end of day in user's timezone
            const endOfDay = new Date(userDate);
            endOfDay.setHours(23, 59, 59, 999);
            // Convert back to UTC for MongoDB storage/queries
            const utcStart = this.convertToUTC(startOfDay, userTimezone);
            const utcEnd = this.convertToUTC(endOfDay, userTimezone);
            logger_1.default.debug('Generated day boundaries', {
                userTimezone,
                inputDate: date.toISOString(),
                localStart: startOfDay.toISOString(),
                localEnd: endOfDay.toISOString(),
                utcStart: utcStart.toISOString(),
                utcEnd: utcEnd.toISOString(),
            });
            return {
                start: utcStart,
                end: utcEnd,
                timezone: userTimezone,
            };
        }
        catch (error) {
            logger_1.default.error('Failed to generate day boundaries', {
                error: error.message,
                userTimezone,
                inputDate: date.toISOString(),
            });
            // Fallback to UTC boundaries
            const startOfDay = new Date(date);
            startOfDay.setUTCHours(0, 0, 0, 0);
            const endOfDay = new Date(date);
            endOfDay.setUTCHours(23, 59, 59, 999);
            return {
                start: startOfDay,
                end: endOfDay,
                timezone: 'UTC',
            };
        }
    }
    /**
     * Convert date range from user's timezone to UTC for MongoDB queries
     * @param startDate - Start date in user's timezone
     * @param endDate - End date in user's timezone
     * @param context - User timezone context
     * @returns DateRange with UTC dates for MongoDB queries
     */
    convertDateRangeToUTC(startDate, endDate, context = {}) {
        if (!startDate && !endDate) {
            return null;
        }
        const userTimezone = this.getUserTimezone(context);
        try {
            let utcStart;
            let utcEnd;
            if (startDate) {
                const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
                utcStart = this.convertToUTC(start, userTimezone);
            }
            else {
                // If no start date, use beginning of time
                utcStart = new Date('1970-01-01T00:00:00.000Z');
            }
            if (endDate) {
                const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
                // If end date doesn't include time, set to end of day
                if (this.isDateOnly(end)) {
                    end.setHours(23, 59, 59, 999);
                }
                utcEnd = this.convertToUTC(end, userTimezone);
            }
            else {
                // If no end date, use current time
                utcEnd = new Date();
            }
            logger_1.default.debug('Converted date range to UTC', {
                userTimezone,
                originalStart: startDate?.toString(),
                originalEnd: endDate?.toString(),
                utcStart: utcStart.toISOString(),
                utcEnd: utcEnd.toISOString(),
            });
            return {
                start: utcStart,
                end: utcEnd,
                timezone: userTimezone,
            };
        }
        catch (error) {
            logger_1.default.error('Failed to convert date range to UTC', {
                error: error.message,
                startDate: startDate?.toString(),
                endDate: endDate?.toString(),
                userTimezone,
            });
            return null;
        }
    }
    /**
     * Convert a date from user's timezone to UTC
     * @param date - Date in user's timezone
     * @param timezone - User's timezone
     * @returns Date in UTC
     */
    convertToUTC(date, timezone) {
        // Get the timezone offset for the given date and timezone
        const utcTime = date.getTime();
        const localTime = new Date(date.toLocaleString('en-US', { timeZone: timezone })).getTime();
        const timezoneOffset = utcTime - localTime;
        return new Date(utcTime + timezoneOffset);
    }
    /**
     * Check if a date object represents date-only (no specific time)
     * @param date - Date to check
     * @returns true if date appears to be date-only
     */
    isDateOnly(date) {
        return date.getHours() === 0 && date.getMinutes() === 0 &&
            date.getSeconds() === 0 && date.getMilliseconds() === 0;
    }
    /**
     * Convert UTC date to user's timezone for display
     * @param utcDate - Date in UTC (from MongoDB)
     * @param context - User timezone context
     * @returns Date object in user's timezone
     */
    convertFromUTC(utcDate, context = {}) {
        const userTimezone = this.getUserTimezone(context);
        try {
            // Convert UTC date to user's timezone
            const userDate = new Date(utcDate.toLocaleString('en-US', { timeZone: userTimezone }));
            logger_1.default.debug('Converted UTC to user timezone', {
                userTimezone,
                utcDate: utcDate.toISOString(),
                userDate: userDate.toISOString(),
            });
            return userDate;
        }
        catch (error) {
            logger_1.default.error('Failed to convert UTC to user timezone', {
                error: error.message,
                utcDate: utcDate.toISOString(),
                userTimezone,
            });
            // Fallback to original date
            return utcDate;
        }
    }
    /**
     * Get today's date boundaries for role-specific filtering
     * @param role - User role
     * @param context - User timezone context
     * @returns DateRange for today in user's timezone (converted to UTC)
     */
    getTodayBoundariesForRole(role, context = {}) {
        const today = new Date();
        const boundaries = this.getDayBoundaries(today, { ...context, role });
        logger_1.default.info('Generated today boundaries for role', {
            role,
            timezone: boundaries.timezone,
            start: boundaries.start.toISOString(),
            end: boundaries.end.toISOString(),
        });
        return boundaries;
    }
    /**
     * Build MongoDB date query with proper timezone handling
     * @param startDate - Start date filter
     * @param endDate - End date filter
     * @param context - User timezone context
     * @returns MongoDB date query object or null
     */
    buildDateQuery(startDate, endDate, context = {}) {
        const dateRange = this.convertDateRangeToUTC(startDate, endDate, context);
        if (!dateRange) {
            return null;
        }
        const query = {};
        if (dateRange.start && dateRange.end) {
            query.createdAt = {
                $gte: dateRange.start,
                $lte: dateRange.end,
            };
        }
        else if (dateRange.start) {
            query.createdAt = { $gte: dateRange.start };
        }
        else if (dateRange.end) {
            query.createdAt = { $lte: dateRange.end };
        }
        logger_1.default.debug('Built MongoDB date query', {
            query,
            timezone: dateRange.timezone,
        });
        return Object.keys(query).length > 0 ? query : null;
    }
    /**
     * Format date for display in user's timezone
     * @param utcDate - Date in UTC
     * @param context - User timezone context
     * @param options - Intl.DateTimeFormat options
     * @returns Formatted date string
     */
    formatDateForUser(utcDate, context = {}, options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZoneName: 'short',
    }) {
        const userTimezone = this.getUserTimezone(context);
        try {
            return utcDate.toLocaleString('en-US', {
                ...options,
                timeZone: userTimezone,
            });
        }
        catch (error) {
            logger_1.default.error('Failed to format date for user', {
                error: error.message,
                utcDate: utcDate.toISOString(),
                userTimezone,
            });
            // Fallback to ISO string
            return utcDate.toISOString();
        }
    }
}
exports.TimezoneManager = TimezoneManager;
// Export singleton instance
exports.timezoneManager = TimezoneManager.getInstance();
// Convenience functions for common operations
const getUserTimezone = (context) => exports.timezoneManager.getUserTimezone(context);
exports.getUserTimezone = getUserTimezone;
const getDayBoundaries = (date, context) => exports.timezoneManager.getDayBoundaries(date, context);
exports.getDayBoundaries = getDayBoundaries;
const convertDateRangeToUTC = (startDate, endDate, context) => exports.timezoneManager.convertDateRangeToUTC(startDate, endDate, context);
exports.convertDateRangeToUTC = convertDateRangeToUTC;
const buildDateQuery = (startDate, endDate, context) => exports.timezoneManager.buildDateQuery(startDate, endDate, context);
exports.buildDateQuery = buildDateQuery;
const formatDateForUser = (utcDate, context, options) => exports.timezoneManager.formatDateForUser(utcDate, context, options);
exports.formatDateForUser = formatDateForUser;
//# sourceMappingURL=timezone_utils.js.map