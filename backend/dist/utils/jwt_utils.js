"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.qrSecretKey = exports.verifyToken = exports.generateToken = void 0;
exports.generateQRPayload = generateQRPayload;
exports.validateQRPayload = validateQRPayload;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const env_config_1 = require("../config/env_config");
const generateToken = (userId, role) => {
    return jsonwebtoken_1.default.sign({ userId, role }, env_config_1.config.server.jwtSecret, { expiresIn: '360d' });
};
exports.generateToken = generateToken;
const verifyToken = (token) => {
    const decoded = jsonwebtoken_1.default.verify(token, env_config_1.config.server.jwtSecret);
    if (typeof decoded === 'string' || !('userId' in decoded) || !('role' in decoded)) {
        throw new Error('Invalid token payload');
    }
    return decoded;
};
exports.verifyToken = verifyToken;
///---------------------- QR  SECTION ----------------
exports.qrSecretKey = env_config_1.config.server.qrSecretKey;
// Generate a signed QR payload using JWT
function generateQRPayload(payload) {
    const options = {
        expiresIn: (payload.expiresIn ?? '24h'),
    };
    return jsonwebtoken_1.default.sign(payload, exports.qrSecretKey, options);
}
// Validate QR payload using JWT
function validateQRPayload(token) {
    try {
        // const decoded = jwt.verify(token, qrSecretKey) as jwt.JwtPayload;
        const decoded = jsonwebtoken_1.default.verify(token, exports.qrSecretKey);
        const orderId = decoded.orderId;
        const expiresAt = decoded.exp ? new Date(decoded.exp * 1000) : null;
        if (!orderId) {
            return {
                success: false,
                orderId: null,
                expiresAt,
                message: 'Missing orderId in token',
            };
        }
        return {
            success: true,
            orderId,
            expiresAt,
            message: 'QR code is valid',
        };
    }
    catch (err) {
        return {
            success: false,
            orderId: null,
            expiresAt: null,
            message: err.message,
        };
    }
}
//# sourceMappingURL=jwt_utils.js.map