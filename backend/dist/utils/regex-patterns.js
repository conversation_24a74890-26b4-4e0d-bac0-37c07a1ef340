"use strict";
// src/utils/regex-patterns.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.RegexPatterns = void 0;
/**
 * Centralized reusable RegExp patterns.
 * Keep patterns immutable and shared to avoid re-allocation and ensure consistency.
 */
exports.RegexPatterns = Object.freeze({
    /**
     * Unicode character normalization patterns
     */
    unicode: {
        /**
         * Matches Unicode dash characters (en-dash, em-dash) to normalize to '-'
         * Examples: "–" (en-dash), "—" (em-dash) → "-"
         */
        dash: /[–—]/g,
        /**
         * Matches a single Unicode ellipsis character to normalize to '...'
         * Example: "…" → "..."
         */
        ellipsis: /…/g,
        /**
         * Quote character normalization
         */
        quotes: {
            /**
             * Matches curly/directional double quotes to normalize to '"'
             * Examples: """, """, «», „", ‟" → '"'
             */
            double: /[""«»„‟]/g,
            /**
             * Matches curly/single angled quotes to normalize to '\''
             * Examples: '', '', ‚', ‛', ‹, › → "'"
             */
            single: /[''‚‛‹›]/g,
        },
    },
    /**
     * Emoji and symbol removal patterns
     */
    emoji: {
        /**
         * Emoticons range (U+1F600-U+1F64F)
         * Examples: 😀, 😍, 😭, 🤔
         */
        emoticons: /[\uD83D][\uDE00-\uDE4F]/g,
        /**
         * Miscellaneous Symbols and Pictographs (U+1F300-U+1F5FF)
         * Examples: 🌈, 🎮, 🏠, 🚗
         */
        miscSymbolsAndPictographs: /[\uD83C][\uDF00-\uDFFF]|[\uD83D][\uDC00-\uDDFF]|[\uD83D][\uDE80-\uDEFF]/g,
        /**
         * Transport and Map Symbols (U+1F680-U+1F6FF)
         * Examples: 🚀, 🚁, 🚂, 🚃
         */
        transportAndMapSymbols: /[\uD83D][\uDE80-\uDEFF]/g,
        /**
         * Additional Emoticons (U+1F910-U+1F96B)
         * Examples: 🤐, 🥶, 🥵, 🥴
         */
        additionalEmoticons: /[\uD83E][\uDD10-\uDD6B]/g,
        /**
         * Miscellaneous Symbols (U+2600-U+26FF)
         * Examples: ☀️, ⭐, ♠️, ♥️
         */
        symbols: /[\u2600-\u26FF]/g,
        /**
         * Dingbats (U+2700-U+27BF)
         * Examples: ✂️, ✏️, ✒️, ✳️
         */
        dingbats: /[\u2700-\u27BF]/g,
        /**
         * Enclosed Alphanumeric Supplement (U+1F100-U+1F1FF)
         * Examples: 🅰️, 🅱️, 🆎, 🆑
         */
        enclosedAlphanumericSupplement: /[\uD83C][\uDD00-\uDDFF]/g,
        /**
         * Variation selectors and combining characters
         * Examples: ️ (variation selector-15), ️ (variation selector-16)
         */
        variationSelectors: /[\uFE0E\uFE0F]/g,
        /**
         * High surrogate pairs (catch-all for remaining emoji)
         * Matches any remaining high surrogate pairs that might be emoji
         */
        highSurrogatePairs: /[\uD800-\uDBFF][\uDC00-\uDFFF]/g,
    },
    /**
     * Whitespace and newline normalization patterns
     */
    whitespace: {
        /**
         * Non-breaking space to regular space conversion
         * Example: "Hello\u00A0World" → "Hello World"
         */
        nbsp: /\u00A0/g,
        /**
         * Collapse multiple consecutive newlines to double newlines
         * Example: "Line1\n\n\nLine2" → "Line1\n\nLine2"
         */
        tripleNewlines: /\n{3,}/g,
        /**
         * Remove leading/trailing whitespace from each line
         * Example: "  Line1  \n  Line2  " → "Line1\nLine2"
         */
        lineEdgeWhitespace: /^[ \t]+|[ \t]+$/gm,
        /**
         * Remove empty lines that are not part of intentional paragraph breaks
         * Example: "Line1\n\n\nLine2" → "Line1\n\nLine2"
         */
        tripleBlankLines: /\n\s*\n\s*\n/g,
        /**
         * Collapse multiple consecutive spaces/tabs to single space
         * Example: "Hello    World" → "Hello World"
         */
        multiSpaces: /[ \t]+/g,
    },
    /**
     * Punctuation handling patterns
     */
    punctuation: {
        /**
         * Remove redundant punctuation (but preserve legitimate ellipses)
         * Examples: "Hello!!!" → "Hello!", "What???" → "What?"
         */
        redundantPunctuation: /([,!?])\1+/g,
        /**
         * Literal ellipsis pattern for protection during processing
         * Example: "..." (literal three dots)
         */
        ellipsisLiteral: /\.\.\./g,
        /**
         * Multiple consecutive periods (2 or more)
         * Example: "Hello...." → "Hello."
         */
        multiPeriods: /\.{2,}/g,
        /**
         * Normalize colon spacing (no space before, one space after)
         * Example: "Order ID : 12345" → "Order ID: 12345"
         */
        colonSpacing: /\s*:\s*/g,
        /**
         * Add spacing after punctuation when missing
         * Example: "Hello,World" → "Hello, World"
         */
        punctuationNoSpaceAfter: /([,.!?])([^\s\n])/g,
        /**
         * Normalize extra spaces after punctuation
         * Example: "Hello,   World" → "Hello, World"
         */
        punctuationWithExtraSpaces: /([,.!?])[ \t]+/g,
    },
    /**
     * Content structure detection patterns
     */
    content: {
        /**
         * Matches structured data lines like:
         * - "Order ID: 12345"
         * - "Status: Processing"
         * - "Reference: ABC123"
         * Case-insensitive and allows flexible spacing
         */
        structuredDataLineStart: /^\s*(Order\s+ID|Status|Reference|Code|Number)\s*:\s*/im,
        /**
         * Detect double newlines (paragraph breaks)
         * Example: "Line1\n\nLine2" → true
         */
        doubleNewlineDetect: /\n\n/,
        /**
         * Global double newlines replacement
         * Example: "Line1\n\nLine2\n\nLine3" → "Line1\nLine2\nLine3"
         */
        doubleNewlinesGlobal: /\n\n/g,
        /**
         * Common narrative words for content type detection
         * Examples: arrive, gas, thank, please, your, will, can, processing, order, new, contact, us
         */
        narrativeWords: /\b(arrive|gas|thank|please|your|will|can|processing|order|new|contact|us)\b/i,
        /**
         * Detect single newlines
         * Example: "Line1\nLine2" → true
         */
        singleNewlineDetect: /\n/,
        /**
         * Global single newlines replacement
         * Example: "Line1\nLine2\nLine3" → "Line1 Line2 Line3"
         */
        singleNewlineGlobal: /\n/g,
        /**
         * Join lines without punctuation with periods
         * Example: "Hello\nWorld" → "Hello. World"
         */
        lineJoinWithoutPunctuation: /([^.!?\n])\n(?=\S)/g,
        /**
         * Three or more consecutive periods
         * Example: "Hello...." → "Hello..."
         */
        threeOrMorePeriods: /\.{3,}/g,
        /**
         * Remove ellipses before new sentences
         * Example: "... Hello World." → ". Hello World."
         */
        ellipsisBeforeNewSentence: /\.\.\.\s+([A-Z][^.!?]*[.!?])/g,
        /**
         * Two or more consecutive newlines
         * Example: "Line1\n\n\nLine2" → "Line1\nLine2"
         */
        doubleOrMoreNewlines: /\n{2,}/g,
    },
});
//# sourceMappingURL=regex-patterns.js.map