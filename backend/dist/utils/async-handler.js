"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.asyncHandler = void 0;
/**
 * Async handler wrapper for Express route handlers
 * Automatically catches async errors and passes them to Express error handler
 *
 * @param fn - Async route handler function
 * @returns Express middleware function
 */
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
exports.default = exports.asyncHandler;
//# sourceMappingURL=async-handler.js.map