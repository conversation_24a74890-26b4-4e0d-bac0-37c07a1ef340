"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendResponse = void 0;
const sendResponse = (res, statusCode, status, message, options = {}) => {
    const response = {
        status,
        message,
    };
    if (options.data !== undefined)
        response.data = options.data;
    if (options.meta !== undefined)
        response.meta = options.meta;
    if (options.errors !== undefined)
        response.errors = options.errors;
    if (options.stack !== undefined)
        response.stack = options.stack;
    return res.status(statusCode).json(response);
};
exports.sendResponse = sendResponse;
//# sourceMappingURL=response.js.map