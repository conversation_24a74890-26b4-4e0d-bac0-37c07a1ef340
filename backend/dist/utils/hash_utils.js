"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.compareHashedPasswords = exports.hashPassword = void 0;
const bcrypt_1 = __importDefault(require("bcrypt"));
const env_config_1 = require("../config/env_config");
// Hash password
const hashPassword = async (password) => {
    return await bcrypt_1.default.hash(password, env_config_1.config.server.saltRounds);
};
exports.hashPassword = hashPassword;
// Compare hashed password
const compareHashedPasswords = async (plainPassword, hashedPassword) => {
    return await bcrypt_1.default.compare(plainPassword, hashedPassword);
};
exports.compareHashedPasswords = compareHashedPasswords;
//# sourceMappingURL=hash_utils.js.map