"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileCleanupService = void 0;
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const logger_1 = __importDefault(require("../config/logger"));
/**
 * File cleanup utility for managing uploaded files
 * Handles deletion of image files when entities are permanently deleted
 */
class FileCleanupService {
    /**
     * Delete an image file from the uploads directory
     * @param imagePath - The relative path to the image file (e.g., "uploads/images/cylinders/2025/07/filename.jpg")
     * @returns Promise<boolean> - true if file was deleted, false if file didn't exist
     */
    static async deleteImageFile(imagePath) {
        try {
            if (!imagePath || imagePath.trim() === '') {
                logger_1.default.warn('Empty image path provided for deletion');
                return false;
            }
            // Ensure the path is within the uploads directory for security
            const normalizedPath = path_1.default.normalize(imagePath);
            if (!normalizedPath.startsWith('uploads/')) {
                logger_1.default.warn('Attempted to delete file outside uploads directory', {
                    imagePath,
                    normalizedPath,
                });
                return false;
            }
            // Get the absolute path
            const absolutePath = path_1.default.resolve(normalizedPath);
            // Check if file exists
            try {
                await promises_1.default.access(absolutePath);
            }
            catch (error) {
                logger_1.default.info('Image file does not exist, skipping deletion', {
                    imagePath,
                    absolutePath,
                });
                return false;
            }
            // Delete the file
            await promises_1.default.unlink(absolutePath);
            logger_1.default.info('Image file deleted successfully', {
                imagePath,
                absolutePath,
            });
            return true;
        }
        catch (error) {
            logger_1.default.error('Failed to delete image file', {
                imagePath,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
            });
            // Don't throw error - file cleanup shouldn't break the main operation
            return false;
        }
    }
    /**
     * Delete multiple image files
     * @param imagePaths - Array of image paths to delete
     * @returns Promise<{ deleted: number; failed: number }> - Summary of deletion results
     */
    static async deleteMultipleImageFiles(imagePaths) {
        let deleted = 0;
        let failed = 0;
        for (const imagePath of imagePaths) {
            const success = await this.deleteImageFile(imagePath);
            if (success) {
                deleted++;
            }
            else {
                failed++;
            }
        }
        logger_1.default.info('Bulk image file deletion completed', {
            total: imagePaths.length,
            deleted,
            failed,
        });
        return { deleted, failed };
    }
    /**
     * Clean up empty directories after file deletion
     * @param imagePath - The path of the deleted file
     */
    static async cleanupEmptyDirectories(imagePath) {
        try {
            if (!imagePath || !imagePath.startsWith('uploads/')) {
                return;
            }
            const dirPath = path_1.default.dirname(path_1.default.resolve(imagePath));
            // Check if directory is empty
            const files = await promises_1.default.readdir(dirPath);
            if (files.length === 0) {
                await promises_1.default.rmdir(dirPath);
                logger_1.default.info('Removed empty directory', { dirPath });
                // Recursively check parent directories
                const parentPath = path_1.default.dirname(dirPath);
                if (parentPath !== path_1.default.resolve('uploads') && parentPath.includes('uploads')) {
                    await this.cleanupEmptyDirectories(path_1.default.relative(process.cwd(), parentPath));
                }
            }
        }
        catch (error) {
            // Ignore errors - directory cleanup is optional
            logger_1.default.debug('Directory cleanup failed (non-critical)', {
                imagePath,
                error: error instanceof Error ? error.message : 'Unknown error',
            });
        }
    }
    /**
     * Validate that a file path is safe to delete
     * @param filePath - The file path to validate
     * @returns boolean - true if safe to delete
     */
    static isValidImagePath(filePath) {
        if (!filePath || typeof filePath !== 'string') {
            return false;
        }
        const normalizedPath = path_1.default.normalize(filePath);
        // Must be within uploads directory
        if (!normalizedPath.startsWith('uploads/')) {
            return false;
        }
        // Must be an image file
        const ext = path_1.default.extname(normalizedPath).toLowerCase();
        const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];
        return allowedExtensions.includes(ext);
    }
    /**
     * Get file size before deletion (for logging purposes)
     * @param imagePath - The image path
     * @returns Promise<number> - File size in bytes, or 0 if file doesn't exist
     */
    static async getFileSize(imagePath) {
        try {
            const absolutePath = path_1.default.resolve(imagePath);
            const stats = await promises_1.default.stat(absolutePath);
            return stats.size;
        }
        catch (error) {
            return 0;
        }
    }
}
exports.FileCleanupService = FileCleanupService;
//# sourceMappingURL=file_cleanup.js.map