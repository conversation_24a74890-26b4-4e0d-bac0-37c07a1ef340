{"version": 3, "file": "file_cleanup.js", "sourceRoot": "", "sources": ["../../src/utils/file_cleanup.ts"], "names": [], "mappings": ";;;;;;AAAA,2DAA6B;AAC7B,gDAAwB;AACxB,8DAAsC;AAEtC;;;GAGG;AACH,MAAa,kBAAkB;IAC7B;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,SAAiB;QAC5C,IAAI,CAAC;YACH,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;gBAC1C,gBAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;gBACtD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,+DAA+D;YAC/D,MAAM,cAAc,GAAG,cAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YACjD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC3C,gBAAM,CAAC,IAAI,CAAC,oDAAoD,EAAE;oBAChE,SAAS;oBACT,cAAc;iBACf,CAAC,CAAC;gBACH,OAAO,KAAK,CAAC;YACf,CAAC;YAED,wBAAwB;YACxB,MAAM,YAAY,GAAG,cAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAElD,uBAAuB;YACvB,IAAI,CAAC;gBACH,MAAM,kBAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAChC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,gBAAM,CAAC,IAAI,CAAC,8CAA8C,EAAE;oBAC1D,SAAS;oBACT,YAAY;iBACb,CAAC,CAAC;gBACH,OAAO,KAAK,CAAC;YACf,CAAC;YAED,kBAAkB;YAClB,MAAM,kBAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAE9B,gBAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE;gBAC7C,SAAS;gBACT,YAAY;aACb,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC1C,SAAS;gBACT,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;gBAC/D,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;aACxD,CAAC,CAAC;YAEH,sEAAsE;YACtE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,wBAAwB,CACnC,UAAoB;QAEpB,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YACtD,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,EAAE,CAAC;YACZ,CAAC;iBAAM,CAAC;gBACN,MAAM,EAAE,CAAC;YACX,CAAC;QACH,CAAC;QAED,gBAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE;YAChD,KAAK,EAAE,UAAU,CAAC,MAAM;YACxB,OAAO;YACP,MAAM;SACP,CAAC,CAAC;QAEH,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,SAAiB;QACpD,IAAI,CAAC;YACH,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBACpD,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,cAAI,CAAC,OAAO,CAAC,cAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;YAEtD,8BAA8B;YAC9B,MAAM,KAAK,GAAG,MAAM,kBAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACxC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,MAAM,kBAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBACxB,gBAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;gBAEpD,uCAAuC;gBACvC,MAAM,UAAU,GAAG,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBACzC,IAAI,UAAU,KAAK,cAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC7E,MAAM,IAAI,CAAC,uBAAuB,CAAC,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,CAAC,CAAC,CAAC;gBAC/E,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gDAAgD;YAChD,gBAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE;gBACtD,SAAS;gBACT,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAChE,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,gBAAgB,CAAC,QAAgB;QACtC,IAAI,CAAC,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC9C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,cAAc,GAAG,cAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAEhD,mCAAmC;QACnC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,wBAAwB;QACxB,MAAM,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;QACvD,MAAM,iBAAiB,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAErE,OAAO,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,SAAiB;QACxC,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,cAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC7C,MAAM,KAAK,GAAG,MAAM,kBAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC1C,OAAO,KAAK,CAAC,IAAI,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;CACF;AA9JD,gDA8JC"}