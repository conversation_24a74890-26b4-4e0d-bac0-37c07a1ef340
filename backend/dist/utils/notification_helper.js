"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Notifications = void 0;
const notification_dispatcher_service_1 = require("../services/notification-dispatcher.service");
const enums_1 = require("../enums/enums");
const index_1 = require("../models/index");
const logger_1 = __importDefault(require("../config/logger"));
/**
 * Notification Helper Utilities
 * Provides convenient methods for sending common notifications
 */
/**
 * Get admin and supervisor contact information for inventory alerts
 */
async function getAdminContacts() {
    try {
        const admins = await index_1.User.find({
            role: { $in: [enums_1.UserRole.ADMIN, enums_1.UserRole.SUPERVISOR] },
            isActive: true,
        })
            .select('email phone _id')
            .lean();
        const emails = admins.filter((admin) => admin.email).map((admin) => admin.email);
        const phones = admins.filter((admin) => admin.phone).map((admin) => admin.phone);
        const userIds = admins.map((admin) => admin._id.toString());
        return { emails, phones, userIds };
    }
    catch (error) {
        logger_1.default.error('Failed to get admin contacts', { error: error.message });
        return { emails: [], phones: [], userIds: [] };
    }
}
/**
 * Send OTP verification message
 */
async function sendOtpNotification(userId, phone, otp, expiresIn = 5, language = 'so') {
    try {
        return await notification_dispatcher_service_1.notificationDispatcher.sendNotification({
            type: 'otp_verification',
            channels: ['sms'],
            recipients: [{ userId, phone, language, }],
            data: { otp, expiresIn },
            options: { priority: 'high', includePhoneInFooter: false },
        });
    }
    catch (error) {
        logger_1.default.error('Failed to send OTP notification', { userId, error: error.message });
        throw error;
    }
}
/**
 * Send order confirmation notification
 */
async function sendOrderConfirmationNotification(userId, phone, orderId, language = 'so') {
    try {
        return await notification_dispatcher_service_1.notificationDispatcher.sendNotification({
            type: 'order_confirmed',
            channels: ['sms', 'push'],
            recipients: [{ userId, phone, language }],
            data: { orderId },
            options: { priority: 'high' },
        });
    }
    catch (error) {
        logger_1.default.error('Failed to send order confirmation notification', {
            userId,
            orderId,
            error: error.message,
        });
        throw error;
    }
}
/**
 * Send order delivered notification
 */
async function sendOrderDeliveredNotification(userId, phone, orderId, language = 'so') {
    try {
        return await notification_dispatcher_service_1.notificationDispatcher.sendNotification({
            type: 'order_delivered',
            channels: ['sms', 'push'],
            recipients: [{ userId, phone, language }],
            data: { orderId },
            options: { priority: 'normal' },
        });
    }
    catch (error) {
        logger_1.default.error('Failed to send order delivered notification', {
            userId,
            orderId,
            error: error.message,
        });
        throw error;
    }
}
/**
 * Send order cancelled notification
 */
async function sendOrderCancelledNotification(userId, phone, orderId, reason, language = 'so') {
    try {
        return await notification_dispatcher_service_1.notificationDispatcher.sendNotification({
            type: 'order_cancelled',
            channels: ['sms', 'push'],
            recipients: [{ userId, phone, language }],
            data: { orderId, reason },
            options: { priority: 'high' },
        });
    }
    catch (error) {
        logger_1.default.error('Failed to send order cancelled notification', {
            userId,
            orderId,
            error: error.message,
        });
        throw error;
    }
}
/**
 * Send delivery assignment notification to agent
 */
async function sendDeliveryAssignmentNotification(agentId, agentPhone, orderId, customerName, address, language = 'so') {
    try {
        return await notification_dispatcher_service_1.notificationDispatcher.sendNotification({
            type: 'delivery_assigned',
            channels: ['sms', 'push'],
            recipients: [{ userId: agentId, phone: agentPhone, role: enums_1.UserRole.AGENT, language }],
            data: { orderId, customerName, address },
            options: { priority: 'high', includePhoneInFooter: false },
        });
    }
    catch (error) {
        logger_1.default.error('Failed to send delivery assignment notification', {
            agentId,
            orderId,
            error: error.message,
        });
        throw error;
    }
}
/**
 * Send delivery started notification to customer
 */
async function sendDeliveryStartedNotification(customerId, customerPhone, orderId, agentName, agentPhone, language = 'so') {
    try {
        return await notification_dispatcher_service_1.notificationDispatcher.sendNotification({
            type: 'delivery_started',
            channels: ['sms', 'push'],
            recipients: [{ userId: customerId, phone: customerPhone, language }],
            data: { orderId, agentName, agentPhone },
            options: { priority: 'high' },
        });
    }
    catch (error) {
        logger_1.default.error('Failed to send delivery started notification', {
            customerId,
            orderId,
            error: error.message,
        });
        throw error;
    }
}
/**
 * Send payment confirmation notification
 */
async function sendPaymentConfirmationNotification(userId, phone, orderId, amount, language = 'so') {
    try {
        return await notification_dispatcher_service_1.notificationDispatcher.sendNotification({
            type: 'payment_confirmed',
            channels: ['sms', 'push'],
            recipients: [{ userId, phone, language }],
            data: { orderId, amount },
            options: { priority: 'high' },
        });
    }
    catch (error) {
        logger_1.default.error('Failed to send payment confirmation notification', {
            userId,
            orderId,
            error: error.message,
        });
        throw error;
    }
}
/**
 * Send payment failed notification
 */
async function sendPaymentFailedNotification(userId, phone, orderId, reason, language = 'so') {
    try {
        return await notification_dispatcher_service_1.notificationDispatcher.sendNotification({
            type: 'payment_failed',
            channels: ['sms', 'push'],
            recipients: [{ userId, phone, language }],
            data: { orderId, reason },
            options: { priority: 'high' },
        });
    }
    catch (error) {
        logger_1.default.error('Failed to send payment failed notification', {
            userId,
            orderId,
            error: error.message,
        });
        throw error;
    }
}
/**
 * Send low stock alert to admins and supervisors
 */
async function sendLowStockAlert(item, quantity, language = 'so') {
    try {
        const { emails, phones, userIds } = await getAdminContacts();
        if (emails.length === 0 && phones.length === 0) {
            logger_1.default.warn('No admin contacts found for low stock alert', { item, quantity });
            return {
                success: false,
                message: 'No admin contacts available',
                totalRecipients: 0,
                successfulDeliveries: 0,
                failedDeliveries: 0,
                errors: ['No admin contacts found'],
                channels: {},
            };
        }
        const recipients = [
            ...emails.map(email => ({ email, role: enums_1.UserRole.ADMIN, language })),
            ...phones.map(phone => ({ phone, role: enums_1.UserRole.ADMIN, language })),
        ];
        return await notification_dispatcher_service_1.notificationDispatcher.sendNotification({
            type: 'low_stock_alert',
            channels: ['sms', 'email'],
            recipients,
            data: { item, quantity },
            options: { priority: 'high', includePhoneInFooter: false },
        });
    }
    catch (error) {
        logger_1.default.error('Failed to send low stock alert', { item, quantity, error: error.message });
        throw error;
    }
}
/**
 * Send out of stock alert to admins
 */
async function sendOutOfStockAlert(item, language = 'so') {
    try {
        const { emails, phones } = await getAdminContacts();
        if (emails.length === 0 && phones.length === 0) {
            logger_1.default.warn('No admin contacts found for out of stock alert', { item });
            return {
                success: false,
                message: 'No admin contacts available',
                totalRecipients: 0,
                successfulDeliveries: 0,
                failedDeliveries: 0,
                errors: ['No admin contacts found'],
                channels: {},
            };
        }
        const recipients = [
            ...emails.map(email => ({ email, role: enums_1.UserRole.ADMIN, language })),
            ...phones.map(phone => ({ phone, role: enums_1.UserRole.ADMIN, language })),
        ];
        return await notification_dispatcher_service_1.notificationDispatcher.sendNotification({
            type: 'out_of_stock_alert',
            channels: ['sms', 'email'],
            recipients,
            data: { item },
            options: { priority: 'high', includePhoneInFooter: false },
        });
    }
    catch (error) {
        logger_1.default.error('Failed to send out of stock alert', { item, error: error.message });
        throw error;
    }
}
/**
 * Check stock levels and send appropriate alerts
 */
async function checkStockAndAlert(itemName, currentQuantity, minimumStockLevel, language = 'so') {
    try {
        if (currentQuantity === 0) {
            // Out of stock - urgent alert
            logger_1.default.warn('Item out of stock, sending alert', { itemName, currentQuantity });
            return await sendOutOfStockAlert(itemName, language);
        }
        else if (currentQuantity <= minimumStockLevel) {
            // Low stock - warning alert
            logger_1.default.warn('Item low stock, sending alert', {
                itemName,
                currentQuantity,
                minimumStockLevel,
            });
            return await sendLowStockAlert(itemName, currentQuantity, language);
        }
        // Stock levels are fine
        return null;
    }
    catch (error) {
        logger_1.default.error('Failed to check stock and send alert', {
            itemName,
            currentQuantity,
            minimumStockLevel,
            error: error.message,
        });
        throw error;
    }
}
/**
 * Send welcome message to new user
 */
async function sendWelcomeMessage(userId, phone, userName, userRole, language = 'so') {
    try {
        return await notification_dispatcher_service_1.notificationDispatcher.sendNotification({
            type: 'welcome_message',
            // channels: ['sms', 'push'],
            channels: ['sms'],
            recipients: [{ userId, phone, role: userRole, language }],
            data: { userName, userRole },
            options: { priority: 'normal' },
        });
    }
    catch (error) {
        logger_1.default.error('Failed to send welcome message', { userId, userName, error: error.message });
        throw error;
    }
}
/**
 * Send emergency alert to all users
 */
async function sendEmergencyAlert(message, targetRoles = [
    enums_1.UserRole.CUSTOMER,
    enums_1.UserRole.AGENT,
    enums_1.UserRole.ADMIN,
    enums_1.UserRole.SUPERVISOR,
], language = 'so') {
    try {
        // This would typically fetch users from database based on roles
        // For now, we'll use topic-based notification
        return await notification_dispatcher_service_1.notificationDispatcher.sendNotification({
            type: 'emergency_alert',
            channels: ['sms', 'push'],
            recipients: [{ phone: 'broadcast', role: enums_1.UserRole.CUSTOMER, language }], // Placeholder
            data: { message },
            options: { priority: 'high', includePhoneInFooter: true },
        });
    }
    catch (error) {
        logger_1.default.error('Failed to send emergency alert', { message, error: error.message });
        throw error;
    }
}
/**
 * Send system maintenance notification
 */
async function sendMaintenanceNotification(startTime, duration, language = 'so') {
    try {
        return await notification_dispatcher_service_1.notificationDispatcher.sendNotification({
            type: 'maintenance_notification',
            channels: ['sms', 'push'],
            recipients: [{ phone: 'broadcast', language }], // Placeholder for broadcast
            data: { startTime, duration },
            options: { priority: 'normal' },
        });
    }
    catch (error) {
        logger_1.default.error('Failed to send maintenance notification', {
            startTime,
            duration,
            error: error.message,
        });
        throw error;
    }
}
/*

Namespace export by group related notifications eg(order..)

*/
const Order = {
    sendOrderConfirmationNotification,
    sendOrderDeliveredNotification,
    sendOrderCancelledNotification,
    sendDeliveryAssignmentNotification,
    sendDeliveryStartedNotification,
    sendPaymentConfirmationNotification,
    sendPaymentFailedNotification,
};
const Inventory = {
    sendLowStockAlert,
    sendOutOfStockAlert,
    checkStockAndAlert,
};
const User = {
    sendWelcomeMessage,
    sendOtpNotification,
};
const System = {
    sendEmergencyAlert,
    sendMaintenanceNotification,
};
exports.Notifications = {
    Order,
    Inventory,
    User,
    System,
};
//# sourceMappingURL=notification_helper.js.map