"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDefaultImageUrl = exports.getPackageImageUrl = exports.getSparePartImageUrl = exports.getCylinderImageUrl = exports.ImageUrlGenerator = void 0;
const enums_1 = require("../enums/enums");
const image_utils_1 = require("./image_utils");
class ImageUrlGenerator {
    static BASE_IMAGE_PATH = '/api/v1/images';
    /**
     * Generate image URL for cylinders based on type
     */
    static getCylinderImageUrl(type, material) {
        // Extract numeric value from cylinder type (e.g., "6KG" -> "6")
        const weight = type.replace(/[^\d]/g, '');
        const imageFileName = `${weight}kg.png`.toLowerCase();
        return `${ImageUrlGenerator.BASE_IMAGE_PATH}/cylinders/${imageFileName}`;
    }
    /**
     * Generate image URL for spare parts based on category
     */
    static getSparePartImageUrl(category) {
        const imageMap = {
            [enums_1.SparePartCategory.BRASS_CONTROL_VALVE_KIT]: 'brass_controll_valve_kit.png',
            [enums_1.SparePartCategory.REGULATOR_HIGH_PRESSURE]: 'regulator_high_pressure.png',
            [enums_1.SparePartCategory.REGULATOR_LOW_PRESSURE]: 'regulator_low_pressure.png',
            [enums_1.SparePartCategory.SINGLE_BURNER_GAS_STOVE]: 'single_burner_gas_stove.png',
            [enums_1.SparePartCategory.THREE_BURNER_LPG_STOVE]: 'three_burner_lpg_stove.png',
            [enums_1.SparePartCategory.TUBO_2_METER]: 'tubo_2_meter.png',
            [enums_1.SparePartCategory.VALUE_BURNER]: 'value_burner.png',
        };
        const fileName = imageMap[category] || 'default.png';
        return `${ImageUrlGenerator.BASE_IMAGE_PATH}/spare-parts/${fileName}`;
    }
    /**
     * Generate image URL for packages
     */
    static getPackageImageUrl(packageType) {
        const packageName = packageType.toLowerCase().replace(/\s+/g, '-');
        const imageFileName = `${packageName}.png`;
        return `${ImageUrlGenerator.BASE_IMAGE_PATH}/packages/${imageFileName}`;
    }
    /**
     * Get default fallback image URL
     */
    static getDefaultImageUrl(type) {
        return `${ImageUrlGenerator.BASE_IMAGE_PATH}/${type}s/default.png`;
    }
    /**
     * Get image URL from uploaded file path (new system)
     * This method bridges the old and new image systems
     */
    static getUploadedImageUrl(imagePath) {
        return image_utils_1.ImagePathUtils.getImageUrl(imagePath);
    }
    /**
     * Get the best available image URL for an entity
     * Prioritizes uploaded images over legacy convention-based images
     */
    static getBestImageUrl(uploadedImagePath, legacyImageUrl, fallbackType) {
        // Priority 1: Uploaded image path
        if (uploadedImagePath) {
            return image_utils_1.ImagePathUtils.getImageUrl(uploadedImagePath);
        }
        // Priority 2: Legacy image URL
        if (legacyImageUrl) {
            return legacyImageUrl;
        }
        // Priority 3: Default fallback
        if (fallbackType) {
            return ImageUrlGenerator.getDefaultImageUrl(fallbackType);
        }
        // Final fallback
        return `${ImageUrlGenerator.BASE_IMAGE_PATH}/default.png`;
    }
}
exports.ImageUrlGenerator = ImageUrlGenerator;
// Convenience functions for direct use
exports.getCylinderImageUrl = ImageUrlGenerator.getCylinderImageUrl;
exports.getSparePartImageUrl = ImageUrlGenerator.getSparePartImageUrl;
exports.getPackageImageUrl = ImageUrlGenerator.getPackageImageUrl;
exports.getDefaultImageUrl = ImageUrlGenerator.getDefaultImageUrl;
//# sourceMappingURL=image-url-generator.js.map