"use strict";
/**
 * Simple Image Path Management
 * Each model stores its own image paths directly for high performance
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImagePathUtils = exports.ImageCategoryHelper = exports.ALL_IMAGE_CATEGORIES = exports.AdditionalImageCategory = exports.IMAGE_CATEGORY_MAP = void 0;
const enums_1 = require("../enums/enums");
/**
 * Image category mapping from EntityType to directory names
 */
exports.IMAGE_CATEGORY_MAP = {
    [enums_1.EntityType.Cylinder]: 'cylinders',
    [enums_1.EntityType.SparePart]: 'spare-parts',
    [enums_1.EntityType.Package]: 'packages',
};
/**
 * Additional image categories for non-product entities
 */
var AdditionalImageCategory;
(function (AdditionalImageCategory) {
    AdditionalImageCategory["USER_PROFILE"] = "profiles";
})(AdditionalImageCategory || (exports.AdditionalImageCategory = AdditionalImageCategory = {}));
/**
 * All valid image category values
 */
exports.ALL_IMAGE_CATEGORIES = [
    ...Object.values(exports.IMAGE_CATEGORY_MAP),
    ...Object.values(AdditionalImageCategory),
];
/**
 * Helper functions for EntityType to category conversion
 */
exports.ImageCategoryHelper = {
    fromEntityType: (entityType) => exports.IMAGE_CATEGORY_MAP[entityType],
    isValidCategory: (category) => exports.ALL_IMAGE_CATEGORIES.includes(category),
    getAllCategories: () => exports.ALL_IMAGE_CATEGORIES,
};
/**
 * Image path utilities for consistent file organization
 */
class ImagePathUtils {
    static UPLOADS_BASE = 'uploads/images';
    /**
     * Generate organized file path for uploaded images
     * Format: uploads/images/{category}/{year}/{month}/{filename}
     */
    static generateImagePath(category, originalName) {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const timestamp = Date.now();
        const extension = originalName.split('.').pop()?.toLowerCase() || 'jpg';
        const filename = `${timestamp}-${Math.random().toString(36).substring(2)}.${extension}`;
        return `${this.UPLOADS_BASE}/${category}/${year}/${month}/${filename}`;
    }
    /**
     * Generate URL for serving images
     */
    static getImageUrl(imagePath) {
        if (!imagePath)
            return '';
        // Remove 'uploads/' prefix for URL serving
        const urlPath = imagePath.replace(/^uploads\//, '');
        return `/api/v1/images/${urlPath}`;
    }
    /**
     * Get full file system path
     */
    static getFullPath(imagePath) {
        return imagePath; // Already includes uploads/images/...
    }
    /**
     * Extract category from image path
     */
    static getCategoryFromPath(imagePath) {
        const match = imagePath.match(/uploads\/images\/([^\/]+)/);
        if (!match)
            return null;
        const categoryPath = match[1];
        return exports.ImageCategoryHelper.isValidCategory(categoryPath) ? categoryPath : null;
    }
}
exports.ImagePathUtils = ImagePathUtils;
//# sourceMappingURL=image_utils.js.map