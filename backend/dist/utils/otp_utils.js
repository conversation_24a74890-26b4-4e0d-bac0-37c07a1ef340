"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.shouldSkipOtp = shouldSkipOtp;
exports.generateOtp = generateOtp;
exports.validateOtp = validateOtp;
exports.isOtpValid = isOtpValid;
exports.getOtpRemainingTime = getOtpRemainingTime;
const crypto_1 = __importDefault(require("crypto"));
const env_config_1 = require("../config/env_config");
const logger_1 = __importDefault(require("../config/logger"));
/**
 * Check if OTP verification should be skipped
 * @returns true if OTP verification is disabled in configuration
 */
function shouldSkipOtp() {
    return env_config_1.config.server.disableOtpVerification;
}
/**
 * Generate OTP code and expiration time
 * @returns IOtp object with code and expiration time
 */
function generateOtp() {
    if (shouldSkipOtp()) {
        logger_1.default.info('OTP verification disabled, generating dummy OTP');
        return {
            code: '000000',
            expirationTime: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
        };
    }
    const otp = crypto_1.default.randomInt(100000, 999999).toString();
    const expirationTime = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes
    logger_1.default.info('Generated secure OTP', {
        expirationTime: expirationTime.toISOString(),
        expiresInMinutes: 5,
    });
    return { code: otp, expirationTime };
}
/**
 * Validate OTP code and expiration
 * @param storedOtp - The stored OTP object
 * @param providedCode - The code provided by user
 * @returns validation result with success status and error message
 */
function validateOtp(storedOtp, providedCode) {
    if (!storedOtp) {
        return { isValid: false, error: 'No OTP found. Please request a new one.' };
    }
    if (!storedOtp.code) {
        return { isValid: false, error: 'Invalid OTP data. Please request a new one.' };
    }
    if (storedOtp.code !== providedCode) {
        return { isValid: false, error: 'Incorrect OTP. Please try again.' };
    }
    if (storedOtp.expirationTime <= new Date()) {
        return { isValid: false, error: 'OTP has expired. Please request a new one.' };
    }
    return { isValid: true };
}
/**
 * Check if OTP is still valid (not expired)
 * @param storedOtp - The stored OTP object
 * @returns true if OTP exists and is not expired
 */
function isOtpValid(storedOtp) {
    if (!storedOtp || !storedOtp.expirationTime) {
        return false;
    }
    return storedOtp.expirationTime > new Date();
}
/**
 * Get remaining time for OTP in seconds
 * @param storedOtp - The stored OTP object
 * @returns remaining seconds or 0 if expired/invalid
 */
function getOtpRemainingTime(storedOtp) {
    if (!storedOtp || !storedOtp.expirationTime) {
        return 0;
    }
    const now = new Date();
    const remaining = Math.ceil((storedOtp.expirationTime.getTime() - now.getTime()) / 1000);
    return Math.max(0, remaining);
}
//# sourceMappingURL=otp_utils.js.map