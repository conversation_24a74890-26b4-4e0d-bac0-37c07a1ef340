"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Payment = void 0;
const mongoose_1 = require("mongoose");
const enums_1 = require("../enums/enums");
const PaymentSchema = new mongoose_1.Schema({
    orderId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Order',
        required: false,
    },
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
    },
    method: {
        type: String,
        enum: Object.values(enums_1.PaymentMethod),
        default: enums_1.PaymentMethod.CASH,
    },
    amount: { type: Number, required: true, min: 0 },
    currency: {
        type: String,
        required: false,
        enum: ['SOS', 'USD'],
        default: 'USD',
    },
    transactionId: String,
    preauthCode: String,
    status: {
        type: String,
        enum: Object.values(enums_1.PaymentStatus),
        default: enums_1.PaymentStatus.PENDING,
    },
    paidAt: Date,
    failedAt: Date,
    cancelledAt: Date,
    gatewayResponses: {
        type: [
            {
                provider: {
                    type: String,
                    enum: ['WAAFI', 'ZAAD', 'EDAHAB', 'OTHER'],
                    required: true,
                },
                operation: {
                    type: String,
                    enum: ['PREAUTHORIZE', 'CAPTURE', 'CANCEL', 'REFUND', 'WEBHOOK', 'ERROR'],
                    required: true,
                },
                timestamp: {
                    type: Date,
                    default: Date.now,
                    required: true,
                },
                requestId: String,
                responseCode: String,
                responseMessage: String,
                rawRequest: {
                    type: mongoose_1.Schema.Types.Mixed,
                    default: {},
                },
                rawResponse: {
                    type: mongoose_1.Schema.Types.Mixed,
                    default: {},
                },
                httpStatus: Number,
                processingTime: Number,
                errorDetails: {
                    errorCode: String,
                    errorMessage: String,
                    stackTrace: String,
                },
                metadata: {
                    type: mongoose_1.Schema.Types.Mixed,
                    default: {},
                },
            },
        ],
        default: [],
    },
    metadata: {
        type: mongoose_1.Schema.Types.Mixed,
        default: {},
    },
}, { timestamps: true });
// Indexes for performance
PaymentSchema.index({ orderId: 1 });
PaymentSchema.index({ userId: 1 });
PaymentSchema.index({ transactionId: 1 });
PaymentSchema.index({ status: 1 });
PaymentSchema.index({ method: 1 });
PaymentSchema.index({ createdAt: -1 });
PaymentSchema.index({ 'gatewayResponses.provider': 1 });
PaymentSchema.index({ 'gatewayResponses.operation': 1 });
PaymentSchema.index({ 'gatewayResponses.timestamp': -1 });
// Compound indexes for common queries
PaymentSchema.index({ userId: 1, status: 1 });
PaymentSchema.index({ orderId: 1, status: 1 });
PaymentSchema.index({ transactionId: 1, status: 1 });
exports.Payment = (0, mongoose_1.model)('Payment', PaymentSchema);
//# sourceMappingURL=payment.model.js.map