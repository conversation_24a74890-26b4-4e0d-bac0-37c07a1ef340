"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TempOtp = void 0;
const mongoose_1 = require("mongoose");
const TempOtpSchema = new mongoose_1.Schema({
    phone: { type: String, required: true, index: true },
    code: { type: String, required: true },
    expirationTime: { type: Date, required: true },
    createdAt: { type: Date, default: Date.now, expires: '5m' }, // Auto-delete after 5 mins
});
exports.TempOtp = (0, mongoose_1.model)('TempOtp', TempOtpSchema);
//# sourceMappingURL=TempOtp.model.js.map