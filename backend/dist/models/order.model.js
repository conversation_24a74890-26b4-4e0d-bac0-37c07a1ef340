"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Order = void 0;
const mongoose_1 = require("mongoose");
const enums_1 = require("../enums/enums");
const enums_2 = require("../enums/enums");
const OrderSchema = new mongoose_1.Schema({
    customer: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
    },
    payment: { type: mongoose_1.Schema.Types.ObjectId, ref: 'Payment' },
    deliveryAgent: { type: mongoose_1.Schema.Types.ObjectId, ref: 'User', required: false },
    items: [
        {
            itemType: {
                type: String,
                enum: Object.values(enums_2.EntityType),
                required: true,
            },
            itemId: { type: mongoose_1.Schema.Types.ObjectId, required: true },
            quantity: { type: Number, default: 1 },
        },
    ],
    totalAmount: { type: Number, required: true },
    status: {
        type: String,
        enum: Object.values(enums_1.OrderStatus),
        default: enums_1.OrderStatus.PENDING,
    },
    deliveryAddress: { type: String, required: true },
    paymentMethod: {
        type: String,
        enum: Object.values(enums_1.PaymentMethod),
        // required: true,
        default: enums_1.PaymentMethod.WAAFI_PREAUTH,
    },
    confirmedAt: Date,
    assignedAt: Date,
    outForDeliveryAt: Date,
    deliveredAt: Date,
    cancelledAt: Date,
    failedAt: Date,
    verifiedAt: Date,
    verificationAttempts: { type: Number, default: 0 },
    failureReason: String,
    qrCode: { type: String, unique: true },
    qrCodeExpiresAt: Date,
}, { timestamps: true });
// Index for order tracking
OrderSchema.index({ customer: 1, status: 1 });
OrderSchema.index({ createdAt: -1 }); // Recent orders first
exports.Order = (0, mongoose_1.model)('Order', OrderSchema);
//# sourceMappingURL=order.model.js.map