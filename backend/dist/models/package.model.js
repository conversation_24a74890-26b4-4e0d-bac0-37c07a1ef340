"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Package = void 0;
const mongoose_1 = require("mongoose");
const app_errors_1 = require("../errors/app_errors");
const PackageSchema = new mongoose_1.Schema({
    name: {
        type: String,
        required: true,
        trim: true,
    },
    description: { type: String },
    cylinder: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Cylinder',
        required: true,
    },
    includedSpareParts: [
        {
            part: {
                type: mongoose_1.Schema.Types.ObjectId,
                ref: 'SparePart',
                required: true,
            },
            quantity: {
                type: Number,
                default: 1,
                min: 1,
            },
        },
    ],
    totalPrice: {
        type: Number,
        required: true,
        min: 0,
    },
    costPrice: {
        type: Number,
        min: 0,
    },
    discount: {
        type: Number,
        min: 0,
        max: 100,
        default: 0,
    },
    imageUrl: { type: String }, // Legacy field
    imagePath: { type: String }, // New field for uploaded images
    isActive: {
        type: Boolean,
        default: true,
    },
    quantity: { type: Number, default: 0, min: 0 },
    reserved: { type: Number, default: 0, min: 0 },
    sold: { type: Number, default: 0, min: 0 },
    lastRestockedAt: Date,
    minimumStockLevel: { type: Number, default: 10, min: 0 },
    // Soft delete fields
    isDeleted: { type: Boolean, default: false },
    deletedAt: { type: Date },
    deletedBy: { type: String },
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
});
// Compound indexes for performance
PackageSchema.index({ name: 'text', description: 'text' });
PackageSchema.index({ isActive: 1 });
PackageSchema.index({ totalPrice: 1 });
PackageSchema.index({ quantity: 1, minimumStockLevel: 1 });
PackageSchema.index({ quantity: 1 }); // Low stock alerts
PackageSchema.index({ quantity: 1, reserved: 1 }); // Helps with availableQuantity calculations
// Soft delete indexes
PackageSchema.index({ isDeleted: 1 }); // Filter deleted items
PackageSchema.index({ isDeleted: 1, isActive: 1 }); // Combined queries
PackageSchema.index({ deletedAt: 1 }); // Sort by deletion date
// Virtuals
PackageSchema.virtual('availableQuantity').get(function () {
    return Math.max(0, this.quantity - this.reserved);
});
// Virtual for getting the correct image URL (prioritize uploaded images)
PackageSchema.virtual('currentImageUrl').get(function () {
    if (this.imagePath) {
        // Remove 'uploads/' prefix for URL serving
        const urlPath = this.imagePath.replace(/^uploads\//, '');
        return `/api/v1/images/${urlPath}`;
    }
    return this.imageUrl; // Fallback to legacy imageUrl
});
// Hook for ensuring data consistency
PackageSchema.pre('save', function (next) {
    if (this.reserved > this.quantity) {
        throw new app_errors_1.BadRequestError('Reserved quantity cannot exceed total quantity');
    }
    next();
});
exports.Package = (0, mongoose_1.model)('Package', PackageSchema);
//# sourceMappingURL=package.model.js.map