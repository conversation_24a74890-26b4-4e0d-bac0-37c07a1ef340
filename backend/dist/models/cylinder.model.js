"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cylinder = void 0;
const enums_1 = require("../enums/enums");
const app_errors_1 = require("../errors/app_errors");
const mongoose_1 = require("mongoose");
const CylinderSchema = new mongoose_1.Schema({
    type: {
        type: String,
        enum: Object.values(enums_1.CylinderType),
        required: true,
    },
    material: {
        type: String,
        enum: Object.values(enums_1.CylinderMaterial),
        required: true,
    },
    status: {
        type: String,
        enum: Object.values(enums_1.CylinderStatus),
        default: enums_1.CylinderStatus.Active,
    },
    price: { type: Number, required: true, min: 0 },
    cost: { type: Number, required: true, min: 0 },
    imageUrl: { type: String }, // Legacy field
    imagePath: { type: String }, // New field for uploaded images
    description: { type: String },
    quantity: { type: Number, default: 0, min: 0 },
    reserved: { type: Number, default: 0, min: 0 },
    sold: { type: Number, default: 0, min: 0 },
    lastRestockedAt: Date,
    minimumStockLevel: { type: Number, default: 10, min: 0 },
    // Soft delete fields
    isDeleted: { type: Boolean, default: false },
    deletedAt: { type: Date },
    deletedBy: { type: String },
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
});
// Compound index
CylinderSchema.index({ type: 1, material: 1, status: 1 });
CylinderSchema.index({ quantity: 1, minimumStockLevel: 1 });
CylinderSchema.index({ type: 1, status: 1 }); // Fast type/status queries
CylinderSchema.index({ quantity: 1 }); // Low stock alerts
CylinderSchema.index({ quantity: 1, reserved: 1 }); // Helps with availableQuantity calculations
// Soft delete indexes
CylinderSchema.index({ isDeleted: 1 }); // Filter deleted items
CylinderSchema.index({ isDeleted: 1, type: 1, status: 1 }); // Combined queries
CylinderSchema.index({ deletedAt: 1 }); // Sort by deletion date
// Virtuals
CylinderSchema.virtual('availableQuantity').get(function () {
    return Math.max(0, this.quantity - this.reserved);
});
// Virtual for getting the correct image URL (prioritize uploaded images)
CylinderSchema.virtual('currentImageUrl').get(function () {
    if (this.imagePath) {
        // Remove 'uploads/' prefix for URL serving
        const urlPath = this.imagePath.replace(/^uploads\//, '');
        return `/api/v1/images/${urlPath}`;
    }
    return this.imageUrl; // Fallback to legacy imageUrl
});
// Hook for ensuring data consisitency
CylinderSchema.pre('save', function (next) {
    if (this.reserved > this.quantity) {
        // this.reserved = this.quantity;
        throw new app_errors_1.BadRequestError('Reserved quantity cannot exceed total quantity');
    }
    next();
});
exports.Cylinder = (0, mongoose_1.model)('Cylinder', CylinderSchema);
//# sourceMappingURL=cylinder.model.js.map