"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Notification = void 0;
const mongoose_1 = require("mongoose");
const notification_utils_1 = require("../utils/notification_utils");
const enums_1 = require("../enums/enums");
const NotificationSchema = new mongoose_1.Schema({
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
    },
    title: {
        type: String,
        required: true,
    },
    body: {
        type: String,
        required: true,
    },
    data: {
        type: Map,
        of: String,
    },
    imageUrl: String,
    topic: {
        type: String,
        enum: Object.values(notification_utils_1.NotificationTopic),
    },
    status: {
        type: String,
        enum: Object.values(enums_1.NotificationStatus),
        default: enums_1.NotificationStatus.PENDING,
    },
    deliveredAt: Date,
    readAt: Date,
    error: String,
    result: mongoose_1.Schema.Types.Mixed,
}, {
    timestamps: true,
    toJSON: {
        virtuals: true,
        transform: (doc, ret) => {
            delete ret.__v;
            return ret;
        },
    },
});
// Indexes for faster queries
NotificationSchema.index({ userId: 1 });
NotificationSchema.index({ status: 1 });
NotificationSchema.index({ createdAt: -1 });
NotificationSchema.index({ topic: 1 });
exports.Notification = (0, mongoose_1.model)('Notification', NotificationSchema);
//# sourceMappingURL=notification.model.js.map