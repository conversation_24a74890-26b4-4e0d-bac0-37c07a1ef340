"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
const mongoose_1 = require("mongoose");
const enums_1 = require("../enums/enums");
const notification_utils_1 = require("../utils/notification_utils");
const UserSchema = new mongoose_1.Schema({
    phone: { type: String, required: true, unique: true },
    email: {
        type: String,
        unique: true,
        sparse: true,
        lowercase: true,
        trim: true,
        required: false,
    },
    role: { type: String, enum: Object.values(enums_1.UserRole), required: true },
    username: { type: String, required: false },
    addresses: [
        {
            tag: String,
            location: {
                type: { type: String, enum: ['Point'], default: 'Point' },
                coordinates: { type: [Number], required: true },
            },
            details: { type: String, required: true },
            contactPhone: String,
        },
    ],
    isActive: { type: Boolean, default: true },
    // Only for agents - make it optional
    agentMetadata: {
        type: {
            vehicle: {
                type: {
                    type: String,
                    enum: Object.values(enums_1.VehicleType),
                    default: enums_1.VehicleType.MOTORCYCLE,
                },
                number: String,
            },
            isOnDuty: { type: Boolean, default: true },
            lastKnownLocation: {
                type: {
                    type: String,
                    enum: ['Point'],
                    default: 'Point',
                },
                coordinates: { type: [Number], default: [0, 0] },
            },
            rating: { type: Number, min: 0, max: 5 },
        },
        required: false, //  optional
    },
    otp: {
        type: {
            code: { type: String },
            expirationTime: { type: Date },
        },
        required: false,
    },
    notification: {
        type: {
            fcmToken: { type: String, required: false },
            topics: {
                type: [
                    {
                        type: String,
                        enum: Object.values(notification_utils_1.NotificationTopic),
                    },
                ],
                default: [],
                required: false,
            },
            isEnabled: { type: Boolean, default: true },
        },
        required: false,
    },
}, { timestamps: true });
// GeoJSON Index
UserSchema.index({ 'addresses.location': '2dsphere' });
UserSchema.index({ 'agentMetadata.lastKnownLocation': '2dsphere' });
// Performance indexes
UserSchema.index({ role: 1, 'agentMetadata.isOnDuty': 1 });
UserSchema.index({ role: 1, isActive: 1 });
UserSchema.index({ createdAt: -1 });
// 🔍 Text search indexes for powerful search functionality
UserSchema.index({
    phone: 'text',
    email: 'text',
    username: 'text',
}, {
    weights: {
        phone: 10, // Highest priority for phone searches
        email: 5, // Medium priority for email searches
        username: 3, // Lower priority for username searches
    },
    name: 'user_search_index',
});
// Compound indexes for efficient filtering + search
UserSchema.index({ role: 1, phone: 1 });
UserSchema.index({ role: 1, email: 1 });
UserSchema.index({ role: 1, isActive: 1, createdAt: -1 });
exports.User = (0, mongoose_1.model)('User', UserSchema);
//# sourceMappingURL=user.model.js.map