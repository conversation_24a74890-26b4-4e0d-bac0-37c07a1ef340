"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.notificationController = void 0;
const notification_services_1 = require("../services/notification.services");
const models_1 = require("../models");
const app_errors_1 = require("../errors/app_errors");
const notification_utils_1 = require("../utils/notification_utils");
const logger_1 = __importDefault(require("../config/logger"));
const enums_1 = require("../enums/enums");
const response_1 = require("../utils/response");
class NotificationController {
    /**
     * Send notification to a specific user
     */
    async sendToUser(req, res, next) {
        try {
            const { userId, title, body, data, imageUrl } = req.body;
            // Validate required fields
            if (!userId || !title || !body) {
                throw new app_errors_1.BadRequestError('User ID, title, and body are required');
            }
            // Check if user has permission to send notifications
            if (req.user?.role !== enums_1.UserRole.ADMIN && req.user?.role !== enums_1.UserRole.AGENT) {
                throw new app_errors_1.BadRequestError('Insufficient permissions to send notifications');
            }
            const result = await notification_services_1.notificationService.sendToUser(userId, {
                title,
                body,
                data,
                imageUrl,
            });
            logger_1.default.info('Notification sent to user successfully', {
                senderId: req.user?.id,
                recipientId: userId,
                title,
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Notification sent successfully', {
                data: {
                    success: result.success,
                    sentAt: new Date().toISOString(),
                    recipient: userId,
                },
            });
            return;
        }
        catch (error) {
            logger_1.default.error('Failed to send notification to user', {
                senderId: req.user?.id,
                error: error.message,
                recipientId: req.body.userId,
            });
            next(error);
        }
    }
    /**
     * Send notification to all users subscribed to a topic
     */
    async sendToTopic(req, res, next) {
        try {
            const { topic, title, body, data, imageUrl, onlyActiveUsers } = req.body;
            // Validate required fields
            if (!topic || !title || !body) {
                throw new app_errors_1.BadRequestError('Topic, title, and body are required');
            }
            // Check if user has permission
            if (req.user?.role !== enums_1.UserRole.ADMIN) {
                throw new app_errors_1.BadRequestError('Only admins can send topic notifications');
            }
            // Validate topic
            if (!Object.values(notification_utils_1.NotificationTopic).includes(topic)) {
                throw new app_errors_1.BadRequestError('Invalid notification topic');
            }
            const result = await notification_services_1.notificationService.sendToTopic(topic, { title, body, data, imageUrl }, { onlyActiveUsers });
            logger_1.default.info('Topic notification sent successfully', {
                senderId: req.user?.id,
                topic,
                title,
                recipientCount: result.count,
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Topic notification sent successfully', {
                data: {
                    success: result.success,
                    topic,
                    recipientCount: result.count || 0,
                    sentAt: new Date().toISOString(),
                },
            });
            return;
        }
        catch (error) {
            logger_1.default.error('Failed to send topic notification', {
                senderId: req.user?.id,
                error: error.message,
                topic: req.body.topic,
            });
            next(error);
        }
    }
    /**
     * Update user's FCM token
     */
    async updateFcmToken(req, res, next) {
        try {
            const { token } = req.body;
            const userId = req.user?.id;
            if (!userId) {
                throw new app_errors_1.BadRequestError('User authentication required');
            }
            if (!token) {
                throw new app_errors_1.BadRequestError('FCM token is required');
            }
            const result = await notification_services_1.notificationService.updateFcmToken(userId, token);
            logger_1.default.info('FCM token updated successfully', {
                userId,
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'FCM token updated successfully', {
                data: {
                    success: result.success,
                    updatedAt: new Date().toISOString(),
                },
            });
            return;
        }
        catch (error) {
            logger_1.default.error('Failed to update FCM token', {
                userId: req.user?.id,
                error: error.message,
            });
            next(error);
        }
    }
    /**
     * Subscribe user to notification topic
     */
    async subscribeToTopic(req, res, next) {
        try {
            const { topic } = req.body;
            const userId = req.user?.id;
            if (!userId) {
                throw new app_errors_1.BadRequestError('User authentication required');
            }
            if (!topic) {
                throw new app_errors_1.BadRequestError('Topic is required');
            }
            // Validate topic
            if (!Object.values(notification_utils_1.NotificationTopic).includes(topic)) {
                throw new app_errors_1.BadRequestError('Invalid notification topic');
            }
            const result = await notification_services_1.notificationService.subscribeToTopic(userId, topic);
            logger_1.default.info('User subscribed to topic successfully', {
                userId,
                topic,
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Successfully subscribed to topic', {
                data: {
                    success: result.success,
                    topic,
                    subscribedAt: new Date().toISOString(),
                },
            });
            return;
        }
        catch (error) {
            logger_1.default.error('Failed to subscribe to topic', {
                userId: req.user?.id,
                error: error.message,
                topic: req.body.topic,
            });
            next(error);
        }
    }
    /**
     * Toggle user's notification settings
     */
    async toggleNotifications(req, res, next) {
        try {
            const { enabled } = req.body;
            const userId = req.user?.id;
            if (!userId) {
                throw new app_errors_1.BadRequestError('User authentication required');
            }
            if (typeof enabled !== 'boolean') {
                throw new app_errors_1.BadRequestError('Enabled field must be a boolean');
            }
            const result = await notification_services_1.notificationService.toggleNotifications(userId, enabled);
            logger_1.default.info('Notification settings updated', {
                userId,
                enabled,
            });
            (0, response_1.sendResponse)(res, 200, 'success', `Notifications ${enabled ? 'enabled' : 'disabled'} successfully`, {
                data: {
                    success: result.success,
                    enabled,
                    updatedAt: new Date().toISOString(),
                },
            });
            return;
        }
        catch (error) {
            logger_1.default.error('Failed to toggle notifications', {
                userId: req.user?.id,
                error: error.message,
            });
            next(error);
        }
    }
    /**
     * Get user's notification history
     */
    async getNotificationHistory(req, res, next) {
        try {
            const userId = req.user?.id;
            const { page = 1, limit = 20, status } = req.query;
            if (!userId) {
                throw new app_errors_1.BadRequestError('User authentication required');
            }
            const query = { userId };
            if (status) {
                query.status = status;
            }
            const pageNum = parseInt(page);
            const limitNum = parseInt(limit);
            const skip = (pageNum - 1) * limitNum;
            const [notifications, total] = await Promise.all([
                models_1.Notification.find(query).sort({ createdAt: -1 }).skip(skip).limit(limitNum).lean(),
                models_1.Notification.countDocuments(query),
            ]);
            (0, response_1.sendResponse)(res, 200, 'success', 'Notification history retrieved successfully', {
                data: {
                    notifications,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total,
                        pages: Math.ceil(total / limitNum),
                    },
                },
            });
            return;
        }
        catch (error) {
            logger_1.default.error('Failed to get notification history', {
                userId: req.user?.id,
                error: error.message,
            });
            next(error);
        }
    }
    /**
     * Get notification statistics (Admin only)
     */
    async getNotificationStats(req, res, next) {
        try {
            // Check if user has permission
            if (req.user?.role !== enums_1.UserRole.ADMIN) {
                throw new app_errors_1.BadRequestError('Only admins can view notification statistics');
            }
            const { startDate, endDate } = req.query;
            const dateFilter = {};
            if (startDate) {
                dateFilter.$gte = new Date(startDate);
            }
            if (endDate) {
                dateFilter.$lte = new Date(endDate);
            }
            const matchStage = Object.keys(dateFilter).length > 0 ? { createdAt: dateFilter } : {};
            const stats = await models_1.Notification.aggregate([
                { $match: matchStage },
                {
                    $group: {
                        _id: null,
                        total: { $sum: 1 },
                        delivered: { $sum: { $cond: [{ $eq: ['$status', 'delivered'] }, 1, 0] } },
                        failed: { $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] } },
                        pending: { $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] } },
                    },
                },
            ]);
            const topicStats = await models_1.Notification.aggregate([
                { $match: { topic: { $exists: true }, ...matchStage } },
                {
                    $group: {
                        _id: '$topic',
                        count: { $sum: 1 },
                        delivered: { $sum: { $cond: [{ $eq: ['$status', 'delivered'] }, 1, 0] } },
                    },
                },
                { $sort: { count: -1 } },
            ]);
            const result = stats[0] || { total: 0, delivered: 0, failed: 0, pending: 0 };
            const successRate = result.total > 0 ? (result.delivered / result.total) * 100 : 0;
            // res.status(200).json({
            //   status: 'success',
            //   message: 'Notification statistics retrieved successfully',
            //   data: {
            //     overview: {
            //       ...result,
            //       successRate: Math.round(successRate),
            //     },
            //     topicBreakdown: topicStats,
            //     period: {
            //       startDate: startDate || null,
            //       endDate: endDate || null,
            //     },
            //     generatedAt: new Date().toISOString(),
            //   },
            // });
            (0, response_1.sendResponse)(res, 200, 'success', 'Notification statistics retrieved successfully', {
                data: {
                    overview: {
                        ...result,
                        successRate: Math.round(successRate),
                    },
                    topicBreakdown: topicStats,
                    period: {
                        startDate: startDate || null,
                        endDate: endDate || null,
                    },
                    generatedAt: new Date().toISOString(),
                },
            });
        }
        catch (error) {
            logger_1.default.error('Failed to get notification statistics', {
                userId: req.user?.id,
                error: error.message,
            });
            next(error);
        }
    }
    /**
     * Get available notification topics
     */
    async getTopics(req, res, next) {
        try {
            const topics = Object.values(notification_utils_1.NotificationTopic).map(topic => ({
                value: topic,
                label: topic.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
            }));
            (0, response_1.sendResponse)(res, 200, 'success', 'Notification topics retrieved successfully', {
                data: {
                    topics,
                },
            });
            return;
        }
        catch (error) {
            logger_1.default.error('Failed to get notification topics', {
                userId: req.user?.id,
                error: error.message,
            });
            next(error);
        }
    }
}
exports.notificationController = new NotificationController();
//# sourceMappingURL=notification.controller.js.map