"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.dashboardController = exports.DashboardController = void 0;
const dashboard_services_1 = require("../services/dashboard.services");
const enums_1 = require("../enums/enums");
const app_errors_1 = require("../errors/app_errors");
const logger_1 = __importDefault(require("../config/logger"));
const response_1 = require("../utils/response");
class DashboardController {
    /**
     * Get dashboard data based on user role
     */
    async getDashboard(req, res, next) {
        try {
            const userId = req.user.userId.toString();
            const userRole = req.user.role;
            // Get dashboard data based on user role
            let dashboardData;
            switch (userRole) {
                case enums_1.UserRole.ADMIN:
                    dashboardData = await dashboard_services_1.dashboardService.getAdminDashboard();
                    break;
                case enums_1.UserRole.SUPERVISOR:
                    dashboardData = await dashboard_services_1.dashboardService.getSupervisorDashboard();
                    break;
                case enums_1.UserRole.AGENT:
                    dashboardData = await dashboard_services_1.dashboardService.getAgentDashboard(userId);
                    break;
                case enums_1.UserRole.CUSTOMER:
                    dashboardData = await dashboard_services_1.dashboardService.getUserDashboard(userId);
                    break;
                default:
                    throw new app_errors_1.BadRequestError('Invalid user role');
            }
            logger_1.default.info('Dashboard summary retrieved successfully', {
                userId,
                userRole,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Dashboard data retrieved successfully', {
                data: dashboardData,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * Get admin dashboard data
     */
    async getAdminDashboard(req, res, next) {
        try {
            const dashboardData = await dashboard_services_1.dashboardService.getAdminDashboard();
            logger_1.default.info('Admin dashboard retrieved successfully', {
                userId: req.user.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Admin dashboard data retrieved successfully', {
                data: dashboardData,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * Get supervisor dashboard data
     */
    async getSupervisorDashboard(req, res, next) {
        try {
            const dashboardData = await dashboard_services_1.dashboardService.getSupervisorDashboard();
            logger_1.default.info('Supervisor dashboard retrieved successfully', {
                userId: req.user.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Supervisor dashboard data retrieved successfully', {
                data: dashboardData,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * Get agent dashboard data
     */
    async getAgentDashboard(req, res, next) {
        try {
            const userId = req.user.userId.toString();
            const dashboardData = await dashboard_services_1.dashboardService.getAgentDashboard(userId);
            logger_1.default.info('Agent dashboard retrieved successfully', {
                userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Agent dashboard data retrieved successfully', {
                data: dashboardData,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * Get customer dashboard data
     */
    async getCustomerDashboard(req, res, next) {
        try {
            const userId = req.user.userId.toString();
            const dashboardData = await dashboard_services_1.dashboardService.getUserDashboard(userId);
            logger_1.default.info('Customer dashboard retrieved successfully', {
                userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Customer dashboard data retrieved successfully', {
                data: dashboardData,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * Generate reports based on date range
     */
    async getReports(req, res, next) {
        try {
            const { startDate, endDate } = req.query;
            const reportType = req.params.type;
            // Validate dates
            if (!startDate || !endDate) {
                throw new app_errors_1.BadRequestError('Start date and end date are required');
            }
            const start = new Date(startDate);
            const end = new Date(endDate);
            if (isNaN(start.getTime()) || isNaN(end.getTime())) {
                throw new app_errors_1.BadRequestError('Invalid date format');
            }
            if (start > end) {
                throw new app_errors_1.BadRequestError('Start date must be before end date');
            }
            const dateRange = { startDate: start, endDate: end };
            // Generate comprehensive report data
            const comprehensiveData = await dashboard_services_1.dashboardService.getReports(start, end, req.user.role);
            // Extract specific report data based on type
            let reportData;
            switch (reportType) {
                case 'orders':
                    reportData = {
                        salesOverview: comprehensiveData.salesOverview,
                    };
                    break;
                case 'revenue':
                    reportData = {
                        financialOverview: comprehensiveData.financialOverview,
                    };
                    break;
                case 'inventory':
                    reportData = {
                        inventoryStatus: comprehensiveData.inventoryStatus,
                    };
                    break;
                case 'agents':
                    reportData = {
                        agentPerformance: comprehensiveData.agentPerformance.agentPerformance,
                        teamStats: comprehensiveData.agentPerformance.teamStats,
                        topPerformers: comprehensiveData.agentPerformance.topPerformers,
                    };
                    break;
                default:
                    throw new app_errors_1.BadRequestError('Invalid report type. Available types: orders, revenue, inventory, agents');
            }
            logger_1.default.info('Dashboard report generated successfully', {
                adminId: req.user.userId,
                reportType,
                dateRange,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', `${reportType} report generated successfully`, {
                data: {
                    reportType,
                    dateRange,
                    report: reportData,
                },
            });
        }
        catch (error) {
            next(error);
        }
    }
}
exports.DashboardController = DashboardController;
exports.dashboardController = new DashboardController();
//# sourceMappingURL=dashboard.controller.js.map