"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.paymentController = void 0;
const payment_services_1 = require("../services/payment.services");
const response_1 = require("../utils/response");
const app_errors_1 = require("../errors/app_errors");
const mongoose_1 = require("mongoose");
const logger_1 = __importDefault(require("../config/logger"));
/**
 * PaymentController class for handling payment-related HTTP requests
 * Includes enhanced functionality for gateway response analytics and debugging
 */
class PaymentController {
    /**
     * @route   POST /api/v1/payments/:id/preauthorize
     * @desc    Initiate payment preauthorization
     * @access  Private (Customer, Admin)
     */
    async initiatePreauthorization(req, res, next) {
        try {
            const { id: paymentId } = req.params;
            const { mobile, amount, deliveryDetails } = req.body;
            if (!mongoose_1.Types.ObjectId.isValid(paymentId)) {
                throw new app_errors_1.BadRequestError('Invalid payment ID format');
            }
            if (!mobile) {
                throw new app_errors_1.ValidationError('Mobile number is required');
            }
            if (!amount || typeof amount !== 'number' || amount <= 0) {
                throw new app_errors_1.ValidationError('Valid amount is required');
            }
            const result = await payment_services_1.paymentService.initiatePreauthorization(paymentId, mobile, amount, deliveryDetails);
            logger_1.default.info('Payment preauthorization initiated successfully', {
                paymentId,
                mobile: mobile.replace(/\d(?=\d{4})/g, '*'), // Mask mobile number
                amount,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Payment preauthorization initiated successfully', {
                data: {
                    payment: result.payment,
                    cashierUrl: result.cashierUrl,
                    preauthCode: result.preauthCode,
                },
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   POST /api/v1/payments/:id/cancel
     * @desc    Cancel payment preauthorization
     * @access  Private (Customer, Admin, Agent)
     */
    async cancelPreauthorization(req, res, next) {
        try {
            const { id: paymentId } = req.params;
            if (!mongoose_1.Types.ObjectId.isValid(paymentId)) {
                throw new app_errors_1.BadRequestError('Invalid payment ID format');
            }
            const cancelledPayment = await payment_services_1.paymentService.cancelPreauthorization(paymentId);
            logger_1.default.info('Payment preauthorization cancelled successfully', {
                paymentId,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Payment preauthorization cancelled successfully', {
                data: cancelledPayment,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   POST /api/v1/payments/:id/capture
     * @desc    Capture preauthorized payment
     * @access  Private (Admin, Agent)
     */
    async capturePreauthorizedPayment(req, res, next) {
        try {
            const { id: paymentId } = req.params;
            if (!mongoose_1.Types.ObjectId.isValid(paymentId)) {
                throw new app_errors_1.BadRequestError('Invalid payment ID format');
            }
            const capturedPayment = await payment_services_1.paymentService.capturePreauthorizedPayment(paymentId);
            logger_1.default.info('Payment captured successfully', {
                paymentId,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Payment captured successfully', {
                data: capturedPayment,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/payments/:id/gateway-responses
     * @desc    Get gateway responses for a payment (for debugging and audit)
     * @access  Private (Admin only)
     */
    async getGatewayResponses(req, res, next) {
        try {
            const { id: paymentId } = req.params;
            const { operation, provider } = req.query;
            if (!mongoose_1.Types.ObjectId.isValid(paymentId)) {
                throw new app_errors_1.BadRequestError('Invalid payment ID format');
            }
            const gatewayResponses = await payment_services_1.paymentService.getGatewayResponses(paymentId, operation, provider);
            logger_1.default.info('Gateway responses retrieved successfully', {
                paymentId,
                responseCount: gatewayResponses.length,
                operation,
                provider,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Gateway responses retrieved successfully', {
                data: gatewayResponses,
                meta: {
                    paymentId,
                    totalResponses: gatewayResponses.length,
                    filters: { operation, provider },
                },
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/payments/:id/analytics
     * @desc    Get payment analytics including gateway response statistics
     * @access  Private (Admin only)
     */
    async getPaymentAnalytics(req, res, next) {
        try {
            const { id: paymentId } = req.params;
            if (!mongoose_1.Types.ObjectId.isValid(paymentId)) {
                throw new app_errors_1.BadRequestError('Invalid payment ID format');
            }
            const analytics = await payment_services_1.paymentService.getPaymentAnalytics(paymentId);
            logger_1.default.info('Payment analytics retrieved successfully', {
                paymentId,
                totalInteractions: analytics.analytics.totalGatewayInteractions,
                successRate: analytics.analytics.successRate,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Payment analytics retrieved successfully', {
                data: analytics,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   POST /api/v1/payments/:id/webhook
     * @desc    Handle webhook/callback from payment gateway
     * @access  Public (webhook endpoint)
     */
    async handleWebhook(req, res, next) {
        try {
            const { id: paymentId } = req.params;
            const { provider = 'WAAFI' } = req.query;
            const webhookData = req.body;
            if (!mongoose_1.Types.ObjectId.isValid(paymentId)) {
                throw new app_errors_1.BadRequestError('Invalid payment ID format');
            }
            const payment = await payment_services_1.paymentService.handleWebhookResponse(paymentId, webhookData, provider);
            logger_1.default.info('Webhook processed successfully', {
                paymentId,
                provider,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Webhook processed successfully', {
                data: payment,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/payments/system/analytics
     * @desc    Get system-wide payment analytics
     * @access  Private (Admin only)
     */
    async getSystemAnalytics(req, res, next) {
        try {
            const { startDate, endDate, provider, operation } = req.query;
            // This would be implemented to provide system-wide analytics
            // For now, return a placeholder response
            const analytics = {
                totalPayments: 0,
                totalGatewayInteractions: 0,
                averageResponseTime: 0,
                successRate: 0,
                errorRate: 0,
                providerBreakdown: {},
                operationBreakdown: {},
                timeRange: {
                    startDate: startDate || 'Not specified',
                    endDate: endDate || 'Not specified',
                },
            };
            logger_1.default.info('System payment analytics retrieved', {
                filters: { startDate, endDate, provider, operation },
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'System payment analytics retrieved successfully', {
                data: analytics,
                meta: {
                    note: 'System-wide analytics implementation pending',
                },
            });
        }
        catch (error) {
            next(error);
        }
    }
}
exports.paymentController = new PaymentController();
//# sourceMappingURL=payment.controller.js.map