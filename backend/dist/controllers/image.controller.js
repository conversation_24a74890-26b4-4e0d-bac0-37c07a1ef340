"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImageController = void 0;
const image_upload_service_1 = require("../services/image-upload.service");
const image_utils_1 = require("../utils/image_utils");
const models_1 = require("../models");
const app_errors_1 = require("../errors/app_errors");
/**
 * Image management controller for handling uploads and serving images
 */
class ImageController {
    /**
     * Upload image for a specific product
     * POST /api/v1/images/upload
     */
    static async uploadImage(req, res) {
        try {
            const { category, entityId, entityType } = req.body;
            const file = req.file;
            if (!file) {
                throw new app_errors_1.BadRequestError('No image file provided');
            }
            if (!category || !image_utils_1.ImageCategoryHelper.isValidCategory(category)) {
                throw new app_errors_1.BadRequestError('Valid category is required');
            }
            // Validate and process the uploaded file
            image_upload_service_1.ImageUploadService.validateImageFile(file);
            const imagePath = await image_upload_service_1.ImageUploadService.processUploadedFile(file, category);
            // If entityId and entityType provided, update the corresponding model
            if (entityId && entityType) {
                await ImageController.updateEntityImage(entityType, entityId, imagePath);
            }
            const imageUrl = image_upload_service_1.ImageUploadService.getImageUrl(imagePath);
            res.status(201).json({
                message: {
                    status: 'success',
                    msg: 'Image uploaded successfully',
                    data: {
                        imagePath,
                        imageUrl,
                        originalName: file.originalname,
                        size: file.size,
                        mimeType: file.mimetype,
                    },
                },
            });
        }
        catch (error) {
            // Clean up uploaded file if there was an error
            if (req.file?.path) {
                await image_upload_service_1.ImageUploadService.deleteImageFile(req.file.path);
            }
            throw error;
        }
    }
    /**
     * Update entity with new image path
     */
    static async updateEntityImage(entityType, entityId, imagePath) {
        switch (entityType.toLowerCase()) {
            case 'cylinder':
                const cylinder = await models_1.Cylinder.findById(entityId);
                if (!cylinder)
                    throw new app_errors_1.NotFoundError('Cylinder not found');
                // Delete old image if exists
                if (cylinder.imagePath) {
                    await image_upload_service_1.ImageUploadService.deleteImageFile(cylinder.imagePath);
                }
                cylinder.imagePath = imagePath;
                await cylinder.save();
                break;
            case 'sparepart':
                const sparePart = await models_1.SparePart.findById(entityId);
                if (!sparePart)
                    throw new app_errors_1.NotFoundError('Spare part not found');
                if (sparePart.imagePath) {
                    await image_upload_service_1.ImageUploadService.deleteImageFile(sparePart.imagePath);
                }
                sparePart.imagePath = imagePath;
                await sparePart.save();
                break;
            case 'package':
                const packageItem = await models_1.Package.findById(entityId);
                if (!packageItem)
                    throw new app_errors_1.NotFoundError('Package not found');
                if (packageItem.imagePath) {
                    await image_upload_service_1.ImageUploadService.deleteImageFile(packageItem.imagePath);
                }
                packageItem.imagePath = imagePath;
                await packageItem.save();
                break;
            default:
                throw new app_errors_1.BadRequestError('Invalid entity type');
        }
    }
    /**
     * Get image info
     * GET /api/v1/images/info/:entityType/:entityId
     */
    static async getImageInfo(req, res) {
        const { entityType, entityId } = req.params;
        let entity;
        switch (entityType.toLowerCase()) {
            case 'cylinder':
                entity = await models_1.Cylinder.findById(entityId);
                break;
            case 'sparepart':
                entity = await models_1.SparePart.findById(entityId);
                break;
            case 'package':
                entity = await models_1.Package.findById(entityId);
                break;
            default:
                throw new app_errors_1.BadRequestError('Invalid entity type');
        }
        if (!entity) {
            throw new app_errors_1.NotFoundError(`${entityType} not found`);
        }
        const imagePath = entity.imagePath;
        const imageUrl = entity.currentImageUrl || entity.imageUrl;
        let fileInfo = null;
        if (imagePath) {
            fileInfo = await image_upload_service_1.ImageUploadService.getFileInfo(imagePath);
        }
        res.json({
            message: {
                status: 'success',
                msg: 'Image info retrieved successfully',
                data: {
                    imagePath,
                    imageUrl,
                    legacyImageUrl: entity.imageUrl,
                    fileInfo,
                },
            },
        });
    }
    /**
     * Delete image
     * DELETE /api/v1/images/:entityType/:entityId
     */
    static async deleteImage(req, res) {
        const { entityType, entityId } = req.params;
        let entity;
        switch (entityType.toLowerCase()) {
            case 'cylinder':
                entity = await models_1.Cylinder.findById(entityId);
                break;
            case 'sparepart':
                entity = await models_1.SparePart.findById(entityId);
                break;
            case 'package':
                entity = await models_1.Package.findById(entityId);
                break;
            default:
                throw new app_errors_1.BadRequestError('Invalid entity type');
        }
        if (!entity) {
            throw new app_errors_1.NotFoundError(`${entityType} not found`);
        }
        // Delete the file from filesystem
        if (entity.imagePath) {
            await image_upload_service_1.ImageUploadService.deleteImageFile(entity.imagePath);
            entity.imagePath = undefined;
            await entity.save();
        }
        res.json({
            message: {
                status: 'success',
                msg: 'Image deleted successfully',
                data: null,
            },
        });
    }
    /**
     * Replace image
     * PUT /api/v1/images/:entityType/:entityId
     */
    static async replaceImage(req, res) {
        const { entityType, entityId } = req.params;
        const { category } = req.body;
        const file = req.file;
        if (!file) {
            throw new app_errors_1.BadRequestError('No image file provided');
        }
        if (!category || !image_utils_1.ImageCategoryHelper.isValidCategory(category)) {
            throw new app_errors_1.BadRequestError('Valid category is required');
        }
        try {
            // Validate and process the uploaded file
            image_upload_service_1.ImageUploadService.validateImageFile(file);
            const newImagePath = await image_upload_service_1.ImageUploadService.processUploadedFile(file, category);
            // Update the entity and delete old image
            await ImageController.updateEntityImage(entityType, entityId, newImagePath);
            const imageUrl = image_upload_service_1.ImageUploadService.getImageUrl(newImagePath);
            res.json({
                message: {
                    status: 'success',
                    msg: 'Image replaced successfully',
                    data: {
                        imagePath: newImagePath,
                        imageUrl,
                        originalName: file.originalname,
                        size: file.size,
                        mimeType: file.mimetype,
                    },
                },
            });
        }
        catch (error) {
            // Clean up uploaded file if there was an error
            if (req.file?.path) {
                await image_upload_service_1.ImageUploadService.deleteImageFile(req.file.path);
            }
            throw error;
        }
    }
}
exports.ImageController = ImageController;
//# sourceMappingURL=image.controller.js.map