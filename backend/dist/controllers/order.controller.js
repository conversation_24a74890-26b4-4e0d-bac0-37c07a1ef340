"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.orderController = void 0;
const order_services_1 = require("../services/order.services");
const response_1 = require("../utils/response");
const logger_1 = __importDefault(require("../config/logger"));
const app_errors_1 = require("../errors/app_errors");
class OrderController {
    /**
     * Create a new order
     */
    async createOrder(req, res, next) {
        try {
            const { items, deliveryAddress, paymentMethod, customerId: bodyCustomerId } = req.body;
            // Priority: body.customerId > req.user.userId
            const customerId = bodyCustomerId || req.user?.userId;
            if (!customerId) {
                throw new app_errors_1.BadRequestError('Customer ID is required. Either provide it in the request body or ensure you are authenticated.');
            }
            const result = await order_services_1.orderService.createOrder(customerId.toString(), // Ensure string conversion
            items, deliveryAddress, paymentMethod);
            logger_1.default.info('Order created successfully', {
                orderId: result._id,
                customerId,
                items,
                deliveryAddress,
                paymentMethod,
            });
            (0, response_1.sendResponse)(res, 201, 'success', 'Order created successfully', {
                data: result,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * Get all orders
     */
    async getOrders(req, res, next) {
        try {
            const filters = {
                customer: req.query.customer?.toString(),
                status: req.query.status,
                paymentMethod: req.query.paymentMethod,
                startDate: req.query.startDate ? new Date(req.query.startDate) : undefined,
                endDate: req.query.endDate ? new Date(req.query.endDate) : undefined,
            };
            const requestingUser = {
                userId: req.user?.userId.toString(),
                role: req.user?.role,
            };
            const orders = await order_services_1.orderService.getOrders(filters, requestingUser);
            (0, response_1.sendResponse)(res, 200, 'success', 'Orders retrieved successfully', {
                data: orders,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * Assign agent to order
     */
    async assignAgentToOrder(req, res, next) {
        try {
            const orderId = req.params.id;
            const agentId = req.body.agentId;
            if (!agentId) {
                throw new app_errors_1.BadRequestError('Agent ID is required');
            }
            if (!orderId) {
                throw new app_errors_1.BadRequestError('Order ID is required');
            }
            const result = await order_services_1.orderService.assignAgentToOrder(orderId, agentId);
            (0, response_1.sendResponse)(res, 200, 'success', 'Agent assigned to order successfully', {
                data: result,
                meta: {
                    orderId,
                    agentId,
                },
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * Cancel order
     */
    async cancelOrder(req, res, next) {
        try {
            const orderId = req.params.id;
            if (!orderId) {
                throw new app_errors_1.BadRequestError('Order ID is required');
            }
            const result = await order_services_1.orderService.cancelOrder(orderId);
            (0, response_1.sendResponse)(res, 200, 'success', 'Order cancelled successfully', {
                data: result,
                meta: {
                    orderId,
                },
            });
        }
        catch (error) {
            next(error);
            logger_1.default.error('Order cancellation failed', {
                orderId: req.params.id,
                error: error.message,
                timestamp: new Date().toISOString(),
            });
        }
    }
    /**
     * Complete order
     */
    async completeOrder(req, res, next) {
        try {
            const orderId = req.params.id;
            if (!orderId) {
                throw new app_errors_1.BadRequestError('Order ID is required');
            }
            const result = await order_services_1.orderService.completeOrder(orderId);
            (0, response_1.sendResponse)(res, 200, 'success', 'Order completed successfully', {
                data: result,
                meta: {
                    orderId,
                },
            });
        }
        catch (error) {
            next(error);
            logger_1.default.error('Order completion failed', {
                orderId: req.params.id,
                error: error.message,
                timestamp: new Date().toISOString(),
            });
        }
    }
    async regenerateQRCode(req, res, next) {
        try {
            const { id } = req.params;
            if (!id) {
                throw new app_errors_1.BadRequestError('Order ID is required');
            }
            const result = await order_services_1.orderService.regenerateQRCode(id);
            (0, response_1.sendResponse)(res, 200, 'success', 'QR code regenerated successfully', {
                data: result,
            });
        }
        catch (error) {
            next(error);
        }
    }
    async validateOrderInQRCode(req, res, next) {
        try {
            const { qrCode } = req.body;
            if (!qrCode) {
                throw new app_errors_1.BadRequestError('QR code is required');
            }
            const result = await order_services_1.orderService.validateOrderInQRCode(qrCode);
            (0, response_1.sendResponse)(res, 200, 'success', 'Order validated successfully', {
                data: result,
            });
        }
        catch (error) {
            next(error);
        }
    }
    async updateOrder(req, res, next) {
        try {
            const orderId = req.params.id;
            if (!orderId) {
                throw new app_errors_1.BadRequestError('Order ID is required');
            }
            const { customerId, items, deliveryAddress, paymentMethod } = req.body;
            const result = await order_services_1.orderService.updateOrder(orderId, customerId, items, deliveryAddress, paymentMethod);
            (0, response_1.sendResponse)(res, 200, 'success', 'Order updated successfully', {
                data: result,
                meta: {
                    orderId,
                },
            });
        }
        catch (error) {
            next(error);
        }
    }
    async deleteOrder(req, res, next) {
        try {
            const orderId = req.params.id;
            if (!orderId) {
                throw new app_errors_1.BadRequestError('Order ID is required');
            }
            const result = await order_services_1.orderService.deleteOrder(orderId);
            (0, response_1.sendResponse)(res, 200, 'success', 'Order deleted successfully', {
                data: result,
                meta: {
                    orderId,
                },
            });
        }
        catch (error) {
            next(error);
        }
    }
}
exports.orderController = new OrderController();
//# sourceMappingURL=order.controller.js.map