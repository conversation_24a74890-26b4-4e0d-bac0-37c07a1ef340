"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.changeUserRole = exports.toggleAgentOnDutyStatus = exports.toggleUserActiveStatus = exports.searchUsers = exports.getCurrentUser = exports.subscribeToNotifications = exports.deleteUser = exports.updateUser = exports.getAllUsers = exports.getSingleUser = exports.verifyOtp = exports.resendOtp = exports.registerCustomer = exports.registerAdminOrAgent = exports.login = void 0;
const user_service_1 = require("../services/user.service");
const response_1 = require("../utils/response");
const enums_1 = require("../enums/enums");
const app_errors_1 = require("../errors/app_errors");
const login = async (req, res, next) => {
    try {
        const { phone } = req.body;
        if (!phone || phone.trim() === '') {
            throw new app_errors_1.ValidationError('Phone number is required');
        }
        const result = await user_service_1.userService.login(phone);
        (0, response_1.sendResponse)(res, 200, 'success', 'Login successful', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.login = login;
const registerAdminOrAgent = async (req, res, next) => {
    try {
        const userData = req.body;
        const result = await user_service_1.userService.registerAdminOrAgent(userData, req.user);
        (0, response_1.sendResponse)(res, 201, 'success', 'Admin/Agent registered successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.registerAdminOrAgent = registerAdminOrAgent;
const registerCustomer = async (req, res, next) => {
    try {
        const userData = req.body;
        const result = await user_service_1.userService.registerCustomer(userData, req.user);
        (0, response_1.sendResponse)(res, 201, 'success', 'Customer registered successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.registerCustomer = registerCustomer;
const resendOtp = async (req, res, next) => {
    try {
        const { phone } = req.body;
        if (!phone || phone.trim() === '') {
            throw new app_errors_1.ValidationError('Phone number is required');
        }
        const result = await user_service_1.userService.resendOtp(phone);
        (0, response_1.sendResponse)(res, 200, 'success', 'OTP resent successfully'
        //   {
        //   data: result,
        // }
        );
    }
    catch (error) {
        next(error);
    }
};
exports.resendOtp = resendOtp;
const verifyOtp = async (req, res, next) => {
    try {
        const { phone, otp } = req.body;
        if (!phone || phone.trim() === '') {
            throw new app_errors_1.ValidationError('Phone number is required');
        }
        if (!otp || otp.trim() === '') {
            throw new app_errors_1.ValidationError('OTP is required');
        }
        const result = await user_service_1.userService.verifyOtp(phone, otp);
        (0, response_1.sendResponse)(res, 200, 'success', 'OTP verified successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.verifyOtp = verifyOtp;
const getSingleUser = async (req, res, next) => {
    try {
        const userId = req.params.id;
        const result = await user_service_1.userService.getSingleUser(userId, req.user);
        (0, response_1.sendResponse)(res, 200, 'success', 'User retrieved successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getSingleUser = getSingleUser;
const getAllUsers = async (req, res, next) => {
    try {
        // 🔍 Enhanced options with search support
        const options = {};
        // Basic filters
        if (req.query.role) {
            options.role = req.query.role;
        }
        if (req.query.isActive !== undefined) {
            options.isActive = req.query.isActive === 'true';
        }
        // Pagination with validation
        if (req.query.page) {
            const page = parseInt(req.query.page);
            options.page = Math.max(1, page || 1);
        }
        if (req.query.limit) {
            const limit = parseInt(req.query.limit);
            options.limit = Math.min(100, Math.max(1, limit || 20));
        }
        // Sorting
        if (req.query.sortBy) {
            options.sortBy = req.query.sortBy;
        }
        if (req.query.sortOrder) {
            options.sortOrder = req.query.sortOrder;
        }
        // 🚀 Search functionality
        if (req.query.search) {
            options.search = req.query.search.trim();
        }
        if (req.query.searchFields) {
            options.searchFields = req.query.searchFields.split(',');
        }
        if (req.query.limit) {
            options.limit = parseInt(req.query.limit);
        }
        if (req.query.sortBy) {
            options.sortBy = req.query.sortBy;
        }
        if (req.query.sortOrder) {
            options.sortOrder = req.query.sortOrder;
        }
        const result = await user_service_1.userService.getAllUsers(req.user, options);
        // 📊 Enhanced response with search metadata
        const responseData = {
            data: result.users,
            meta: {
                pagination: result.pagination,
                ...(result.searchMetadata && { search: result.searchMetadata }),
            },
        };
        const message = options.search
            ? `Found ${result.users.length} users matching "${options.search}"`
            : 'Users retrieved successfully';
        (0, response_1.sendResponse)(res, 200, 'success', message, responseData);
    }
    catch (error) {
        next(error);
    }
};
exports.getAllUsers = getAllUsers;
const updateUser = async (req, res, next) => {
    try {
        const userId = req.params.id;
        const updateData = req.body;
        const result = await user_service_1.userService.updateUser(userId, updateData, req.user);
        (0, response_1.sendResponse)(res, 200, 'success', 'User updated successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.updateUser = updateUser;
const deleteUser = async (req, res, next) => {
    try {
        const userId = req.params.id;
        const result = await user_service_1.userService.deleteUser(userId, req.user);
        (0, response_1.sendResponse)(res, 200, 'success', 'User deleted successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.deleteUser = deleteUser;
const subscribeToNotifications = async (req, res, next) => {
    try {
        const { userId, topic, deviceToken } = req.body;
        const result = await user_service_1.userService.subscribeToTopic(userId, topic, deviceToken, req.user);
        (0, response_1.sendResponse)(res, 200, 'success', 'Subscribed to notifications successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.subscribeToNotifications = subscribeToNotifications;
const getCurrentUser = async (req, res, next) => {
    try {
        const result = await user_service_1.userService.getSingleUser(req.user.userId.toString(), req.user);
        (0, response_1.sendResponse)(res, 200, 'success', 'Current user retrieved successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getCurrentUser = getCurrentUser;
/**
 * 🔍 Advanced user search endpoint
 * @route   GET /api/v1/users/search
 * @desc    Search users with advanced filtering and autocomplete
 * @access  Private (Admin, Supervisor)
 */
const searchUsers = async (req, res, next) => {
    try {
        const { q: query, limit, role, isActive, fuzzy, autocomplete } = req.query;
        // Validation
        if (!query || typeof query !== 'string' || query.trim().length < 1) {
            throw new app_errors_1.ValidationError('Search query (q) is required and must be at least 1 character');
        }
        const options = {};
        // Parse options
        if (limit) {
            const limitNum = parseInt(limit);
            options.limit = Math.min(50, Math.max(1, limitNum || 10));
        }
        if (role) {
            options.role = role;
        }
        if (isActive !== undefined) {
            options.isActive = isActive === 'true';
        }
        if (fuzzy === 'true') {
            options.fuzzySearch = true;
        }
        if (autocomplete === 'true') {
            options.autocomplete = true;
        }
        const result = await user_service_1.userService.searchUsers(req.user, query, options);
        const message = options.autocomplete
            ? `Found ${result.results.length} users and ${result.autocomplete?.length || 0} suggestions for "${query}"`
            : `Found ${result.results.length} users matching "${query}"`;
        (0, response_1.sendResponse)(res, 200, 'success', message, {
            data: result.results,
            meta: {
                search: result.metadata,
                ...(result.autocomplete && { autocomplete: result.autocomplete }),
            },
        });
    }
    catch (error) {
        next(error);
    }
};
exports.searchUsers = searchUsers;
const toggleUserActiveStatus = async (req, res, next) => {
    try {
        const userId = req.params.id;
        const isActive = req.body.isActive;
        const result = await user_service_1.userService.toggleUserActiveStatus(userId, isActive, req.user);
        (0, response_1.sendResponse)(res, 200, 'success', 'User active status toggled successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.toggleUserActiveStatus = toggleUserActiveStatus;
const toggleAgentOnDutyStatus = async (req, res, next) => {
    try {
        const userId = req.params.id;
        const isOnDuty = req.body.isOnDuty;
        const result = await user_service_1.userService.toggleAgentOnDutyStatus(userId, isOnDuty, req.user);
        (0, response_1.sendResponse)(res, 200, 'success', 'Agent on duty status toggled successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.toggleAgentOnDutyStatus = toggleAgentOnDutyStatus;
const changeUserRole = async (req, res, next) => {
    try {
        const userId = req.params.id;
        const { newRole } = req.body;
        if (!newRole || !Object.values(enums_1.UserRole).includes(newRole)) {
            throw new app_errors_1.ValidationError('Valid role is required');
        }
        const result = await user_service_1.userService.changeUserRole(userId, newRole, req.user);
        (0, response_1.sendResponse)(res, 200, 'success', 'User role changed successfully', {
            data: result,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.changeUserRole = changeUserRole;
//# sourceMappingURL=user.controller.js.map