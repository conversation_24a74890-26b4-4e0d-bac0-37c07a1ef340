"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.inventoryMonitor = void 0;
const cylinder_model_1 = require("../models/cylinder.model");
const spareParts_model_1 = require("../models/spareParts.model");
const package_model_1 = require("../models/package.model");
const notification_helper_1 = require("../utils/notification_helper");
const enums_1 = require("../enums/enums");
const logger_1 = __importDefault(require("../config/logger"));
/**
 * Inventory Monitoring Service
 * Provides comprehensive inventory monitoring and alerting capabilities
 */
class InventoryMonitorService {
    static instance;
    constructor() { }
    static getInstance() {
        if (!InventoryMonitorService.instance) {
            InventoryMonitorService.instance = new InventoryMonitorService();
        }
        return InventoryMonitorService.instance;
    }
    /**
     * Check all inventory items and send alerts for low/out of stock items
     */
    async checkAllInventoryLevels() {
        logger_1.default.info('Starting comprehensive inventory level check');
        const results = {
            cylinders: { checked: 0, alerts: 0 },
            spareParts: { checked: 0, alerts: 0 },
            packages: { checked: 0, alerts: 0 },
            totalAlerts: 0,
        };
        try {
            // Check cylinders
            const cylinderResults = await this.checkCylinderLevels();
            results.cylinders = cylinderResults;
            // Check spare parts
            const sparePartResults = await this.checkSparePartLevels();
            results.spareParts = sparePartResults;
            // Check packages
            const packageResults = await this.checkPackageLevels();
            results.packages = packageResults;
            results.totalAlerts =
                cylinderResults.alerts + sparePartResults.alerts + packageResults.alerts;
            logger_1.default.info('Inventory level check completed', results);
            return results;
        }
        catch (error) {
            logger_1.default.error('Failed to check inventory levels', { error: error.message });
            throw error;
        }
    }
    /**
     * Check cylinder inventory levels
     */
    async checkCylinderLevels() {
        try {
            const cylinders = await cylinder_model_1.Cylinder.find({
                status: { $in: [enums_1.CylinderStatus.Active, enums_1.CylinderStatus.OutOfStock] },
            }).select('type availableQuantity minimumStockLevel');
            let alertsSent = 0;
            for (const cylinder of cylinders) {
                try {
                    const itemName = `${cylinder.type} Gas Cylinder`;
                    const alertResult = await notification_helper_1.Notifications.Inventory.checkStockAndAlert(itemName, cylinder.availableQuantity, cylinder.minimumStockLevel || 5, 'so');
                    if (alertResult) {
                        alertsSent++;
                    }
                }
                catch (alertError) {
                    logger_1.default.error('Failed to send cylinder stock alert', {
                        cylinderId: cylinder._id,
                        type: cylinder.type,
                        error: alertError.message,
                    });
                }
            }
            return { checked: cylinders.length, alerts: alertsSent };
        }
        catch (error) {
            logger_1.default.error('Failed to check cylinder levels', { error: error.message });
            throw error;
        }
    }
    /**
     * Check spare part inventory levels
     */
    async checkSparePartLevels() {
        try {
            const spareParts = await spareParts_model_1.SparePart.find({
                status: { $in: [enums_1.SparePartStatus.AVAILABLE, enums_1.SparePartStatus.OUT_OF_STOCK] },
            }).select('category availableQuantity minimumStockLevel');
            let alertsSent = 0;
            for (const sparePart of spareParts) {
                try {
                    const itemName = `${sparePart.category} Spare Part`;
                    const alertResult = await notification_helper_1.Notifications.Inventory.checkStockAndAlert(itemName, sparePart.availableQuantity, sparePart.minimumStockLevel || 5, 'so');
                    if (alertResult) {
                        alertsSent++;
                    }
                }
                catch (alertError) {
                    logger_1.default.error('Failed to send spare part stock alert', {
                        sparePartId: sparePart._id,
                        category: sparePart.category,
                        error: alertError.message,
                    });
                }
            }
            return { checked: spareParts.length, alerts: alertsSent };
        }
        catch (error) {
            logger_1.default.error('Failed to check spare part levels', { error: error.message });
            throw error;
        }
    }
    /**
     * Check package inventory levels
     */
    async checkPackageLevels() {
        try {
            const packages = await package_model_1.Package.find({
                isActive: true,
            }).select('name availableQuantity minimumStockLevel');
            let alertsSent = 0;
            for (const packageItem of packages) {
                try {
                    const itemName = `${packageItem.name} Package`;
                    const alertResult = await notification_helper_1.Notifications.Inventory.checkStockAndAlert(itemName, packageItem.availableQuantity, packageItem.minimumStockLevel || 5, 'so');
                    if (alertResult) {
                        alertsSent++;
                    }
                }
                catch (alertError) {
                    logger_1.default.error('Failed to send package stock alert', {
                        packageId: packageItem._id,
                        name: packageItem.name,
                        error: alertError.message,
                    });
                }
            }
            return { checked: packages.length, alerts: alertsSent };
        }
        catch (error) {
            logger_1.default.error('Failed to check package levels', { error: error.message });
            throw error;
        }
    }
    /**
     * Get low stock items across all inventory types
     */
    async getLowStockItems() {
        try {
            // Get low stock cylinders
            const lowStockCylinders = await cylinder_model_1.Cylinder.find({
                status: enums_1.CylinderStatus.Active,
                $expr: { $lte: ['$availableQuantity', '$minimumStockLevel'] },
            }).select('type availableQuantity minimumStockLevel');
            // Get low stock spare parts
            const lowStockSpareParts = await spareParts_model_1.SparePart.find({
                status: enums_1.SparePartStatus.AVAILABLE,
                $expr: { $lte: ['$availableQuantity', '$minimumStockLevel'] },
            }).select('category availableQuantity minimumStockLevel');
            // Get low stock packages
            const lowStockPackages = await package_model_1.Package.find({
                isActive: true,
                $expr: { $lte: ['$availableQuantity', '$minimumStockLevel'] },
            }).select('name availableQuantity minimumStockLevel');
            return {
                cylinders: lowStockCylinders.map(c => ({
                    id: c._id.toString(),
                    name: `${c.type} Gas Cylinder`,
                    available: c.availableQuantity,
                    minimum: c.minimumStockLevel || 5,
                })),
                spareParts: lowStockSpareParts.map(sp => ({
                    id: sp._id.toString(),
                    name: `${sp.category} Spare Part`,
                    available: sp.availableQuantity,
                    minimum: sp.minimumStockLevel || 5,
                })),
                packages: lowStockPackages.map(p => ({
                    id: p._id.toString(),
                    name: `${p.name} Package`,
                    available: p.availableQuantity,
                    minimum: p.minimumStockLevel || 5,
                })),
            };
        }
        catch (error) {
            logger_1.default.error('Failed to get low stock items', { error: error.message });
            throw error;
        }
    }
    /**
     * Get out of stock items across all inventory types
     */
    async getOutOfStockItems() {
        try {
            // Get out of stock cylinders
            const outOfStockCylinders = await cylinder_model_1.Cylinder.find({
                availableQuantity: 0,
            }).select('type');
            // Get out of stock spare parts
            const outOfStockSpareParts = await spareParts_model_1.SparePart.find({
                availableQuantity: 0,
            }).select('category');
            // Get out of stock packages
            const outOfStockPackages = await package_model_1.Package.find({
                availableQuantity: 0,
            }).select('name');
            return {
                cylinders: outOfStockCylinders.map(c => ({
                    id: c._id.toString(),
                    name: `${c.type} Gas Cylinder`,
                })),
                spareParts: outOfStockSpareParts.map(sp => ({
                    id: sp._id.toString(),
                    name: `${sp.category} Spare Part`,
                })),
                packages: outOfStockPackages.map(p => ({
                    id: p._id.toString(),
                    name: `${p.name} Package`,
                })),
            };
        }
        catch (error) {
            logger_1.default.error('Failed to get out of stock items', { error: error.message });
            throw error;
        }
    }
    /**
     * Send daily inventory summary to admins
     */
    async sendDailyInventorySummary() {
        try {
            const lowStockItems = await this.getLowStockItems();
            const outOfStockItems = await this.getOutOfStockItems();
            const totalLowStock = lowStockItems.cylinders.length +
                lowStockItems.spareParts.length +
                lowStockItems.packages.length;
            const totalOutOfStock = outOfStockItems.cylinders.length +
                outOfStockItems.spareParts.length +
                outOfStockItems.packages.length;
            if (totalLowStock > 0 || totalOutOfStock > 0) {
                // Send summary notification
                const summaryMessage = `Inventory Summary: ${totalOutOfStock} items out of stock, ${totalLowStock} items low stock. Please check admin dashboard for details.`;
                await notification_helper_1.Notifications.Inventory.sendLowStockAlert('Daily Inventory Summary', totalLowStock + totalOutOfStock, 'so');
                logger_1.default.info('Daily inventory summary sent', {
                    totalLowStock,
                    totalOutOfStock,
                });
            }
            else {
                logger_1.default.info('Daily inventory summary: All items adequately stocked');
            }
        }
        catch (error) {
            logger_1.default.error('Failed to send daily inventory summary', { error: error.message });
            throw error;
        }
    }
}
exports.inventoryMonitor = InventoryMonitorService.getInstance();
//# sourceMappingURL=inventory-monitor.service.js.map