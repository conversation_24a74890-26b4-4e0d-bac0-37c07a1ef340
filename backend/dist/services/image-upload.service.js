"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uploadMultipleImages = exports.uploadSingleImage = exports.ImageUploadService = void 0;
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const promises_1 = __importDefault(require("fs/promises"));
const image_utils_1 = require("../utils/image_utils");
const app_errors_1 = require("../errors/app_errors");
/**
 * File upload configuration and validation
 */
class ImageUploadService {
    static MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    static ALLOWED_MIME_TYPES = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/webp',
        'image/gif',
    ];
    static ALLOWED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];
    /**
     * Configure multer storage for organized file uploads
     */
    static createStorage() {
        return multer_1.default.diskStorage({
            destination: async (req, file, cb) => {
                try {
                    // Determine category based on the route/entity type
                    let category;
                    // Check if this is a spare part request (has category field)
                    if (req.body.category) {
                        // For spare parts, we use the 'spare-parts' directory regardless of the specific category
                        category = 'spare-parts'; // Use directory name for spare parts
                    }
                    // Check if this is a cylinder request (has type field)
                    else if (req.body.type) {
                        category = 'cylinders'; // Use directory name for cylinders
                    }
                    // Check if this is a package request (could have name or other identifier)
                    else if (req.originalUrl.includes('/packages')) {
                        category = 'packages'; // Use directory name for packages
                    }
                    else {
                        return cb(new app_errors_1.BadRequestError('Unable to determine entity type for file upload'), '');
                    }
                    // Generate directory path
                    const now = new Date();
                    const year = now.getFullYear();
                    const month = String(now.getMonth() + 1).padStart(2, '0');
                    const dirPath = path_1.default.join('uploads', 'images', category, String(year), month);
                    // Ensure directory exists
                    await promises_1.default.mkdir(dirPath, { recursive: true });
                    cb(null, dirPath);
                }
                catch (error) {
                    cb(new app_errors_1.InternalServerError('Failed to create upload directory'), '');
                }
            },
            filename: (req, file, cb) => {
                try {
                    // Generate unique filename with timestamp and random string
                    const timestamp = Date.now();
                    const randomString = Math.random().toString(36).substring(2);
                    const extension = path_1.default.extname(file.originalname).toLowerCase();
                    const filename = `${timestamp}-${randomString}${extension}`;
                    cb(null, filename);
                }
                catch (error) {
                    cb(new app_errors_1.InternalServerError('Failed to generate filename'), '');
                }
            },
        });
    }
    /**
     * File filter for validation
     */
    static fileFilter(req, file, cb) {
        // Check MIME type
        if (!ImageUploadService.ALLOWED_MIME_TYPES.includes(file.mimetype)) {
            return cb(new app_errors_1.BadRequestError(`Invalid file type. Allowed types: ${ImageUploadService.ALLOWED_MIME_TYPES.join(', ')}`));
        }
        // Check file extension
        const extension = path_1.default.extname(file.originalname).toLowerCase();
        if (!ImageUploadService.ALLOWED_EXTENSIONS.includes(extension)) {
            return cb(new app_errors_1.BadRequestError(`Invalid file extension. Allowed extensions: ${ImageUploadService.ALLOWED_EXTENSIONS.join(', ')}`));
        }
        cb(null, true);
    }
    /**
     * Create multer upload middleware
     */
    static createUploadMiddleware() {
        return (0, multer_1.default)({
            storage: ImageUploadService.createStorage(),
            fileFilter: ImageUploadService.fileFilter,
            limits: {
                fileSize: ImageUploadService.MAX_FILE_SIZE,
                files: 1, // Single file upload
            },
        });
    }
    /**
     * Process uploaded file and return image path
     */
    static async processUploadedFile(file, category) {
        if (!file) {
            throw new app_errors_1.BadRequestError('No file uploaded');
        }
        // Validate category
        if (!image_utils_1.ImageCategoryHelper.isValidCategory(category)) {
            throw new app_errors_1.BadRequestError('Invalid image category');
        }
        // Return the full path (multer already saved the file)
        return file.path;
    }
    /**
     * Delete image file from filesystem
     */
    static async deleteImageFile(imagePath) {
        try {
            if (!imagePath)
                return;
            // Check if file exists before attempting to delete
            await promises_1.default.access(imagePath);
            await promises_1.default.unlink(imagePath);
        }
        catch (error) {
            // Log error but don't throw - file might already be deleted
            console.warn(`Failed to delete image file: ${imagePath}`, error);
        }
    }
    /**
     * Validate image file before processing
     */
    static validateImageFile(file) {
        if (!file) {
            throw new app_errors_1.BadRequestError('No file provided');
        }
        if (file.size > ImageUploadService.MAX_FILE_SIZE) {
            throw new app_errors_1.BadRequestError(`File size exceeds limit of ${ImageUploadService.MAX_FILE_SIZE / (1024 * 1024)}MB`);
        }
        if (!ImageUploadService.ALLOWED_MIME_TYPES.includes(file.mimetype)) {
            throw new app_errors_1.BadRequestError(`Invalid file type: ${file.mimetype}`);
        }
    }
    /**
     * Get image URL from file path
     */
    static getImageUrl(imagePath) {
        return image_utils_1.ImagePathUtils.getImageUrl(imagePath);
    }
    /**
     * Check if file exists
     */
    static async fileExists(imagePath) {
        try {
            await promises_1.default.access(imagePath);
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * Get file info
     */
    static async getFileInfo(imagePath) {
        try {
            const stats = await promises_1.default.stat(imagePath);
            const extension = path_1.default.extname(imagePath).toLowerCase();
            // Map extension to MIME type
            const mimeTypeMap = {
                '.jpg': 'image/jpeg',
                '.jpeg': 'image/jpeg',
                '.png': 'image/png',
                '.webp': 'image/webp',
                '.gif': 'image/gif',
            };
            return {
                size: stats.size,
                mimeType: mimeTypeMap[extension] || 'application/octet-stream',
                exists: true,
            };
        }
        catch {
            return {
                size: 0,
                mimeType: '',
                exists: false,
            };
        }
    }
}
exports.ImageUploadService = ImageUploadService;
/**
 * Express middleware for handling single image upload
 */
exports.uploadSingleImage = ImageUploadService.createUploadMiddleware().single('image');
/**
 * Express middleware for handling multiple image uploads (max 5)
 */
exports.uploadMultipleImages = ImageUploadService.createUploadMiddleware().array('images', 5);
//# sourceMappingURL=image-upload.service.js.map