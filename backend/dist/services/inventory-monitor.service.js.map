{"version": 3, "file": "inventory-monitor.service.js", "sourceRoot": "", "sources": ["../../src/services/inventory-monitor.service.ts"], "names": [], "mappings": ";;;;;;AAAA,6DAAoD;AACpD,iEAAuD;AACvD,2DAAkD;AAClD,sEAA6D;AAC7D,0CAAiE;AACjE,8DAAsC;AAEtC;;;GAGG;AACH,MAAM,uBAAuB;IACnB,MAAM,CAAC,QAAQ,CAA0B;IAEjD,gBAAuB,CAAC;IAEjB,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,CAAC;YACtC,uBAAuB,CAAC,QAAQ,GAAG,IAAI,uBAAuB,EAAE,CAAC;QACnE,CAAC;QACD,OAAO,uBAAuB,CAAC,QAAQ,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB;QAM3B,gBAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QAE5D,MAAM,OAAO,GAAG;YACd,SAAS,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;YACpC,UAAU,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;YACrC,QAAQ,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;YACnC,WAAW,EAAE,CAAC;SACf,CAAC;QAEF,IAAI,CAAC;YACH,kBAAkB;YAClB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACzD,OAAO,CAAC,SAAS,GAAG,eAAe,CAAC;YAEpC,oBAAoB;YACpB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC3D,OAAO,CAAC,UAAU,GAAG,gBAAgB,CAAC;YAEtC,iBAAiB;YACjB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACvD,OAAO,CAAC,QAAQ,GAAG,cAAc,CAAC;YAElC,OAAO,CAAC,WAAW;gBACjB,eAAe,CAAC,MAAM,GAAG,gBAAgB,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC;YAE3E,gBAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE,OAAO,CAAC,CAAC;YACxD,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC3E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,yBAAQ,CAAC,IAAI,CAAC;gBACpC,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC,sBAAc,CAAC,MAAM,EAAE,sBAAc,CAAC,UAAU,CAAC,EAAE;aACpE,CAAC,CAAC,MAAM,CAAC,0CAA0C,CAAC,CAAC;YAEtD,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,GAAG,QAAQ,CAAC,IAAI,eAAe,CAAC;oBACjD,MAAM,WAAW,GAAG,MAAM,mCAAa,CAAC,SAAS,CAAC,kBAAkB,CAClE,QAAQ,EACR,QAAQ,CAAC,iBAAiB,EAC1B,QAAQ,CAAC,iBAAiB,IAAI,CAAC,EAC/B,IAAI,CACL,CAAC;oBAEF,IAAI,WAAW,EAAE,CAAC;wBAChB,UAAU,EAAE,CAAC;oBACf,CAAC;gBACH,CAAC;gBAAC,OAAO,UAAU,EAAE,CAAC;oBACpB,gBAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;wBAClD,UAAU,EAAE,QAAQ,CAAC,GAAG;wBACxB,IAAI,EAAE,QAAQ,CAAC,IAAI;wBACnB,KAAK,EAAE,UAAU,CAAC,OAAO;qBAC1B,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC1E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB;QAChC,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,4BAAS,CAAC,IAAI,CAAC;gBACtC,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC,uBAAe,CAAC,SAAS,EAAE,uBAAe,CAAC,YAAY,CAAC,EAAE;aAC3E,CAAC,CAAC,MAAM,CAAC,8CAA8C,CAAC,CAAC;YAE1D,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;gBACnC,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,GAAG,SAAS,CAAC,QAAQ,aAAa,CAAC;oBACpD,MAAM,WAAW,GAAG,MAAM,mCAAa,CAAC,SAAS,CAAC,kBAAkB,CAClE,QAAQ,EACR,SAAS,CAAC,iBAAiB,EAC3B,SAAS,CAAC,iBAAiB,IAAI,CAAC,EAChC,IAAI,CACL,CAAC;oBAEF,IAAI,WAAW,EAAE,CAAC;wBAChB,UAAU,EAAE,CAAC;oBACf,CAAC;gBACH,CAAC;gBAAC,OAAO,UAAU,EAAE,CAAC;oBACpB,gBAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE;wBACpD,WAAW,EAAE,SAAS,CAAC,GAAG;wBAC1B,QAAQ,EAAE,SAAS,CAAC,QAAQ;wBAC5B,KAAK,EAAE,UAAU,CAAC,OAAO;qBAC1B,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC5E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB;QAC9B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,uBAAO,CAAC,IAAI,CAAC;gBAClC,QAAQ,EAAE,IAAI;aACf,CAAC,CAAC,MAAM,CAAC,0CAA0C,CAAC,CAAC;YAEtD,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,KAAK,MAAM,WAAW,IAAI,QAAQ,EAAE,CAAC;gBACnC,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,GAAG,WAAW,CAAC,IAAI,UAAU,CAAC;oBAC/C,MAAM,WAAW,GAAG,MAAM,mCAAa,CAAC,SAAS,CAAC,kBAAkB,CAClE,QAAQ,EACR,WAAW,CAAC,iBAAiB,EAC7B,WAAW,CAAC,iBAAiB,IAAI,CAAC,EAClC,IAAI,CACL,CAAC;oBAEF,IAAI,WAAW,EAAE,CAAC;wBAChB,UAAU,EAAE,CAAC;oBACf,CAAC;gBACH,CAAC;gBAAC,OAAO,UAAU,EAAE,CAAC;oBACpB,gBAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE;wBACjD,SAAS,EAAE,WAAW,CAAC,GAAG;wBAC1B,IAAI,EAAE,WAAW,CAAC,IAAI;wBACtB,KAAK,EAAE,UAAU,CAAC,OAAO;qBAC1B,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACzE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB;QAKpB,IAAI,CAAC;YACH,0BAA0B;YAC1B,MAAM,iBAAiB,GAAG,MAAM,yBAAQ,CAAC,IAAI,CAAC;gBAC5C,MAAM,EAAE,sBAAc,CAAC,MAAM;gBAC7B,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,CAAC,EAAE;aAC9D,CAAC,CAAC,MAAM,CAAC,0CAA0C,CAAC,CAAC;YAEtD,4BAA4B;YAC5B,MAAM,kBAAkB,GAAG,MAAM,4BAAS,CAAC,IAAI,CAAC;gBAC9C,MAAM,EAAE,uBAAe,CAAC,SAAS;gBACjC,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,CAAC,EAAE;aAC9D,CAAC,CAAC,MAAM,CAAC,8CAA8C,CAAC,CAAC;YAE1D,yBAAyB;YACzB,MAAM,gBAAgB,GAAG,MAAM,uBAAO,CAAC,IAAI,CAAC;gBAC1C,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,CAAC,EAAE;aAC9D,CAAC,CAAC,MAAM,CAAC,0CAA0C,CAAC,CAAC;YAEtD,OAAO;gBACL,SAAS,EAAE,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBACrC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE;oBACpB,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,eAAe;oBAC9B,SAAS,EAAE,CAAC,CAAC,iBAAiB;oBAC9B,OAAO,EAAE,CAAC,CAAC,iBAAiB,IAAI,CAAC;iBAClC,CAAC,CAAC;gBACH,UAAU,EAAE,kBAAkB,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBACxC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE;oBACrB,IAAI,EAAE,GAAG,EAAE,CAAC,QAAQ,aAAa;oBACjC,SAAS,EAAE,EAAE,CAAC,iBAAiB;oBAC/B,OAAO,EAAE,EAAE,CAAC,iBAAiB,IAAI,CAAC;iBACnC,CAAC,CAAC;gBACH,QAAQ,EAAE,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBACnC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE;oBACpB,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,UAAU;oBACzB,SAAS,EAAE,CAAC,CAAC,iBAAiB;oBAC9B,OAAO,EAAE,CAAC,CAAC,iBAAiB,IAAI,CAAC;iBAClC,CAAC,CAAC;aACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACxE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QAKtB,IAAI,CAAC;YACH,6BAA6B;YAC7B,MAAM,mBAAmB,GAAG,MAAM,yBAAQ,CAAC,IAAI,CAAC;gBAC9C,iBAAiB,EAAE,CAAC;aACrB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAElB,+BAA+B;YAC/B,MAAM,oBAAoB,GAAG,MAAM,4BAAS,CAAC,IAAI,CAAC;gBAChD,iBAAiB,EAAE,CAAC;aACrB,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAEtB,4BAA4B;YAC5B,MAAM,kBAAkB,GAAG,MAAM,uBAAO,CAAC,IAAI,CAAC;gBAC5C,iBAAiB,EAAE,CAAC;aACrB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAElB,OAAO;gBACL,SAAS,EAAE,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBACvC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE;oBACpB,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,eAAe;iBAC/B,CAAC,CAAC;gBACH,UAAU,EAAE,oBAAoB,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBAC1C,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE;oBACrB,IAAI,EAAE,GAAG,EAAE,CAAC,QAAQ,aAAa;iBAClC,CAAC,CAAC;gBACH,QAAQ,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBACrC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE;oBACpB,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,UAAU;iBAC1B,CAAC,CAAC;aACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC3E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB;QAC7B,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAExD,MAAM,aAAa,GACjB,aAAa,CAAC,SAAS,CAAC,MAAM;gBAC9B,aAAa,CAAC,UAAU,CAAC,MAAM;gBAC/B,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC;YAEhC,MAAM,eAAe,GACnB,eAAe,CAAC,SAAS,CAAC,MAAM;gBAChC,eAAe,CAAC,UAAU,CAAC,MAAM;gBACjC,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC;YAElC,IAAI,aAAa,GAAG,CAAC,IAAI,eAAe,GAAG,CAAC,EAAE,CAAC;gBAC7C,4BAA4B;gBAC5B,MAAM,cAAc,GAAG,sBAAsB,eAAe,wBAAwB,aAAa,6DAA6D,CAAC;gBAE/J,MAAM,mCAAa,CAAC,SAAS,CAAC,iBAAiB,CAC7C,yBAAyB,EACzB,aAAa,GAAG,eAAe,EAC/B,IAAI,CACL,CAAC;gBAEF,gBAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;oBAC1C,aAAa;oBACb,eAAe;iBAChB,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,gBAAM,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;YACvE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACjF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AAEY,QAAA,gBAAgB,GAAG,uBAAuB,CAAC,WAAW,EAAE,CAAC"}