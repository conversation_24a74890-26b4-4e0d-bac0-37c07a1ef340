"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sparePartService = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const index_1 = require("../models/index");
const enums_1 = require("../enums/enums");
const app_errors_1 = require("../errors/app_errors");
const image_url_generator_1 = require("../utils/image-url-generator");
const notification_helper_1 = require("../utils/notification_helper");
const logger_1 = __importDefault(require("../config/logger"));
const file_cleanup_1 = require("../utils/file_cleanup");
class SparePartService {
    /**
     * Create a new spare part
     * @throws {DuplicateResourceError} If spare part with same category/barcode exists
     */
    async createSparePart(data, session) {
        const sessionToUse = session || (await mongoose_1.default.startSession());
        const shouldCommit = !session; // Only commit if we started the session
        try {
            if (!session)
                sessionToUse.startTransaction();
            // Check for duplicates - category must be unique
            const existing = await index_1.SparePart.findOne({
                category: data.category,
            }).session(sessionToUse);
            if (existing) {
                throw new app_errors_1.DuplicateResourceError('Spare part with this category already exists', {
                    code: 'DUPLICATE_SPARE_PART',
                });
            }
            const initialStock = data.initialStock ?? 0;
            const initialReserved = data.initialReserved ?? 0;
            const initialSold = data.initialSold ?? 0;
            const minimumStockLevel = data.minimumStockLevel ?? 5;
            const dynamicImageUrl = (0, image_url_generator_1.getSparePartImageUrl)(data.category);
            const sparePart = new index_1.SparePart({
                ...data,
                stock: initialStock,
                reserved: initialReserved,
                sold: initialSold,
                minimumStockLevel,
                status: this.determineStatus(initialStock, initialReserved, minimumStockLevel),
                imageUrl: dynamicImageUrl,
            });
            await sparePart.save({ session: sessionToUse });
            if (shouldCommit) {
                await sessionToUse.commitTransaction();
            }
            return sparePart;
        }
        catch (error) {
            if (shouldCommit) {
                await sessionToUse.abortTransaction();
            }
            throw error;
        }
        finally {
            if (shouldCommit) {
                sessionToUse.endSession();
            }
        }
    }
    /**
     * Update spare part details
     * @throws {NotFoundError} If spare part not found
     */
    async updateSparePart(id, updateData, session) {
        // Prevent updating inventory fields through this method
        const { stock, reserved, sold, availableQuantity, ...safeUpdateData } = updateData;
        const sparePart = await index_1.SparePart.findByIdAndUpdate(id, safeUpdateData, {
            new: true,
            runValidators: true,
            session,
        });
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        const restrictedFields = ['stock', 'reserved', 'sold', 'availableQuantity'];
        const attemptedRestrictedUpdates = Object.keys(updateData).filter(key => restrictedFields.includes(key));
        if (attemptedRestrictedUpdates.length > 0) {
            logger_1.default.warn('Restricted fields ignored in updateSparePart', {
                id,
                fields: attemptedRestrictedUpdates,
                userAttempt: updateData,
            });
        }
        // Recalculate status if minimumStockLevel changed
        if ('minimumStockLevel' in safeUpdateData) {
            sparePart.status = this.determineStatus(sparePart.stock, sparePart.reserved, sparePart.minimumStockLevel);
        }
        await sparePart.save({ session });
        return sparePart;
    }
    /**
     * Get spare part by ID (excludes soft-deleted items)
     * @throws {NotFoundError} If spare part not found
     */
    async getSparePartById(id, includeDeleted = false) {
        const query = { _id: id };
        if (!includeDeleted) {
            query.isDeleted = false;
        }
        const sparePart = await index_1.SparePart.findOne(query);
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        return sparePart;
    }
    /**
     * List spare parts with filtering and pagination
     */
    async listSpareParts(filters = {}, pagination = { page: 1, limit: 50 }, requestedUser) {
        const query = {};
        // Exclude soft-deleted items by default
        if (!filters.includeDeleted) {
            query.isDeleted = false;
        }
        // Text search
        if (filters.search) {
            query.$text = { $search: filters.search };
        }
        // Category filter
        if (filters.category) {
            query.category = filters.category;
        }
        // Status filter
        if (filters.status) {
            console.log('Status filter: ', {
                requestedUser,
                filters,
            });
            // query.status = filters.status;
            if (requestedUser.role === enums_1.UserRole.CUSTOMER) {
                query.status = enums_1.SparePartStatus.AVAILABLE;
            }
            else {
                query.status = filters.status;
            }
        }
        else {
            if (requestedUser.role === enums_1.UserRole.CUSTOMER) {
                query.status = enums_1.SparePartStatus.AVAILABLE;
            }
        }
        // Compatibility filter
        if (filters.compatibleWith) {
            query.compatibleCylinderTypes = filters.compatibleWith;
        }
        // Low stock filter
        if (filters.lowStock) {
            query.$expr = { $lte: ['$stock', '$minimumStockLevel'] };
        }
        const [data, total] = await Promise.all([
            index_1.SparePart.find(query)
                .sort({ name: 1 })
                .skip((pagination.page - 1) * pagination.limit)
                .limit(pagination.limit),
            index_1.SparePart.countDocuments(query),
        ]);
        return { data, total };
    }
    /**
     * Restock spare parts
     * @throws {NotFoundError} If spare part not found
     * @throws {BadRequestError} If quantity is invalid
     */
    async restock(id, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const sparePart = await index_1.SparePart.findById(id).session(session || null);
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        sparePart.stock += quantity;
        sparePart.lastRestockedAt = new Date();
        sparePart.status = this.determineStatus(sparePart.stock, sparePart.reserved, sparePart.minimumStockLevel);
        await sparePart.save({ session });
        // Log successful restock
        logger_1.default.info('Spare part restocked successfully', {
            sparePartId: id,
            category: sparePart.category,
            restockQuantity: quantity,
            newTotalStock: sparePart.stock,
            availableQuantity: sparePart.availableQuantity,
        });
        return sparePart;
    }
    /**
     * Reserve spare parts for an order
     * @throws {NotFoundError} If spare part not found
     * @throws {BadRequestError} If insufficient stock
     */
    async reserve(id, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const sparePart = await index_1.SparePart.findById(id).session(session || null);
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        if (sparePart.availableQuantity < quantity) {
            throw new app_errors_1.BadRequestError('Insufficient stock', {
                code: 'INSUFFICIENT_STOCK',
                details: {
                    available: sparePart.availableQuantity,
                    requested: quantity,
                },
            });
        }
        sparePart.reserved += quantity;
        sparePart.status = this.determineStatus(sparePart.stock, sparePart.reserved, sparePart.minimumStockLevel);
        await sparePart.save({ session });
        return sparePart;
    }
    /**
     * Release reserved spare parts
     * @throws {NotFoundError} If spare part not found
     * @throws {BadRequestError} If trying to release more than reserved
     */
    async release(id, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const sparePart = await index_1.SparePart.findById(id).session(session || null);
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        if (sparePart.reserved < quantity) {
            throw new app_errors_1.BadRequestError('Cannot release more than reserved quantity', {
                code: 'INVALID_RELEASE',
                details: {
                    reserved: sparePart.reserved,
                    toRelease: quantity,
                },
            });
        }
        sparePart.reserved -= quantity;
        sparePart.status = this.determineStatus(sparePart.stock, sparePart.reserved, sparePart.minimumStockLevel);
        await sparePart.save({ session });
        return sparePart;
    }
    /**
     * Mark spare parts as sold
     * @throws {NotFoundError} If spare part not found
     * @throws {BadRequestError} If insufficient stock
     */
    async markAsSold(id, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const sparePart = await index_1.SparePart.findById(id).session(session || null);
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        if (sparePart.availableQuantity < quantity) {
            throw new app_errors_1.BadRequestError('Insufficient stock', {
                code: 'INSUFFICIENT_STOCK',
                details: {
                    available: sparePart.availableQuantity,
                    requested: quantity,
                },
            });
        }
        if (sparePart.reserved < quantity) {
            throw new app_errors_1.BadRequestError('Cannot sell more than reserved quantity', {
                code: 'INVALID_SALE_QUANTITY',
            });
        }
        sparePart.stock -= quantity;
        sparePart.reserved -= quantity;
        sparePart.sold += quantity;
        sparePart.status = this.determineStatus(sparePart.stock, sparePart.reserved, sparePart.minimumStockLevel);
        await sparePart.save({ session });
        // Check stock levels and send alerts if needed
        try {
            const itemName = `${sparePart.category} Spare Part`;
            await notification_helper_1.Notifications.Inventory.checkStockAndAlert(itemName, sparePart.availableQuantity, sparePart.minimumStockLevel || 5, // Default minimum stock level
            'so');
        }
        catch (alertError) {
            logger_1.default.error('Failed to send stock alert for spare part', {
                sparePartId: id,
                category: sparePart.category,
                error: alertError.message,
                availableQuantity: sparePart.availableQuantity,
                minimumStockLevel: sparePart.minimumStockLevel,
            });
            // Don't throw error - stock alerts shouldn't block the main operation
        }
        return sparePart;
    }
    /**
     * Get low stock alerts
     */
    async getLowStockAlerts() {
        return index_1.SparePart.find({
            $expr: { $lte: ['$stock', '$minimumStockLevel'] },
            status: { $nin: [enums_1.SparePartStatus.DISCONTINUED, enums_1.SparePartStatus.OUT_OF_STOCK] },
        }).sort({ stock: 1 });
    }
    /**
     * Soft delete a spare part
     * @throws {NotFoundError} If spare part not found
     * @throws {BadRequestError} If spare part has active reservations or is linked to packages
     */
    async deleteSparePart(id, deletedBy, session) {
        // Check if spare part exists and has no reservations
        const existing = await index_1.SparePart.findOne({
            _id: id,
            isDeleted: false,
        }).session(session || null);
        if (!existing) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        if (existing.reserved > 0) {
            throw new app_errors_1.BadRequestError('Cannot delete spare part with active reservations', {
                code: 'ACTIVE_RESERVATIONS',
                details: {
                    reserved: existing.reserved,
                },
            });
        }
        // Check if spare part is linked to any packages
        const linkedPackages = await index_1.Package.find({
            'includedSpareParts.part': id,
            isDeleted: false,
        })
            .select('name')
            .session(session || null);
        if (linkedPackages.length > 0) {
            const packageNames = linkedPackages.map(pkg => pkg.name).join(', ');
            throw new app_errors_1.BadRequestError(`Cannot delete spare part. It is linked to the following package${linkedPackages.length > 1 ? 's' : ''}: ${packageNames}`, {
                code: 'LINKED_TO_PACKAGES',
                details: {
                    linkedPackages: linkedPackages.map(pkg => ({
                        id: pkg._id,
                        name: pkg.name,
                    })),
                },
            });
        }
        // Perform soft delete
        const sparePart = await index_1.SparePart.findByIdAndUpdate(id, {
            isDeleted: true,
            deletedAt: new Date(),
            deletedBy: deletedBy,
            status: enums_1.SparePartStatus.DISCONTINUED, // Mark as discontinued when deleted
        }, { new: true, session });
        return sparePart;
    }
    /**
     * Restore a soft-deleted spare part
     * @throws {NotFoundError} If spare part not found or not deleted
     */
    async restoreSparePart(id, session) {
        const sparePart = await index_1.SparePart.findOneAndUpdate({
            _id: id,
            isDeleted: true,
        }, {
            isDeleted: false,
            deletedAt: undefined,
            deletedBy: undefined,
            status: enums_1.SparePartStatus.AVAILABLE, // Restore to available status
        }, { new: true, session });
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Deleted spare part not found', {
                code: 'DELETED_SPARE_PART_NOT_FOUND',
            });
        }
        return sparePart;
    }
    /**
     * Permanently delete a spare part (hard delete)
     * Also deletes associated image file from uploads directory
     * @throws {NotFoundError} If spare part not found
     * @throws {BadRequestError} If spare part is linked to packages
     */
    async permanentlyDeleteSparePart(id, session) {
        // Check if spare part is linked to any packages (even if soft-deleted)
        const linkedPackages = await index_1.Package.find({
            'includedSpareParts.part': id,
        })
            .select('name isDeleted')
            .session(session || null);
        if (linkedPackages.length > 0) {
            const activePackages = linkedPackages.filter(pkg => !pkg.isDeleted);
            const deletedPackages = linkedPackages.filter(pkg => pkg.isDeleted);
            let errorMessage = 'Cannot permanently delete spare part. It is linked to packages.';
            const details = { linkedPackages: [] };
            if (activePackages.length > 0) {
                const activeNames = activePackages.map(pkg => pkg.name).join(', ');
                errorMessage += ` Active packages: ${activeNames}.`;
                details.activePackages = activePackages.map(pkg => ({ id: pkg._id, name: pkg.name }));
            }
            if (deletedPackages.length > 0) {
                const deletedNames = deletedPackages.map(pkg => pkg.name).join(', ');
                errorMessage += ` Deleted packages: ${deletedNames}.`;
                details.deletedPackages = deletedPackages.map(pkg => ({ id: pkg._id, name: pkg.name }));
            }
            errorMessage += ' Remove spare part from packages first.';
            throw new app_errors_1.BadRequestError(errorMessage, {
                code: 'LINKED_TO_PACKAGES',
                details,
            });
        }
        const sparePart = await index_1.SparePart.findOneAndDelete({
            _id: id,
            isDeleted: true, // Only allow permanent deletion of soft-deleted items
        }).session(session || null);
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Deleted spare part not found', {
                code: 'DELETED_SPARE_PART_NOT_FOUND',
            });
        }
        // After successful database deletion, clean up the image file
        if (sparePart.imagePath) {
            logger_1.default.info('Cleaning up spare part image file', {
                sparePartId: sparePart._id,
                imagePath: sparePart.imagePath,
            });
            const deleted = await file_cleanup_1.FileCleanupService.deleteImageFile(sparePart.imagePath);
            if (deleted) {
                // Optionally clean up empty directories
                await file_cleanup_1.FileCleanupService.cleanupEmptyDirectories(sparePart.imagePath);
            }
        }
        return sparePart;
    }
    /**
     * Discontinue a spare part (legacy method - now calls soft delete)
     * @throws {NotFoundError} If spare part not found
     * @throws {BadRequestError} If spare part has active reservations or is linked to packages
     */
    async discontinue(id, session) {
        return this.deleteSparePart(id, undefined, session);
    }
    /**
     * Get packages that use a specific spare part
     * @param sparePartId - The spare part ID to check
     * @param includeDeleted - Whether to include deleted packages
     * @returns Array of packages that use this spare part
     */
    async getPackagesUsingPart(sparePartId, includeDeleted = false) {
        const query = { 'includedSpareParts.part': sparePartId };
        if (!includeDeleted) {
            query.isDeleted = false;
        }
        const packages = await index_1.Package.find(query).select('name isDeleted');
        return packages.map(pkg => ({
            id: pkg._id.toString(),
            name: pkg.name,
            isDeleted: pkg.isDeleted || false,
        }));
    }
    /**
     * List soft-deleted spare parts with pagination
     */
    async listDeletedSpareParts(pagination = { page: 1, limit: 10 }) {
        const query = { isDeleted: true };
        const [data, total] = await Promise.all([
            index_1.SparePart.find(query)
                .sort({ deletedAt: -1 }) // Most recently deleted first
                .skip((pagination.page - 1) * pagination.limit)
                .limit(pagination.limit),
            index_1.SparePart.countDocuments(query),
        ]);
        return { data, total };
    }
    /**
     * Get sales statistics
     */
    async getSalesStatistics() {
        const result = {
            totalSold: 0,
            byCategory: {},
            byStatus: {},
        };
        // Initialize all possible values with 0
        Object.values(enums_1.SparePartCategory).forEach(cat => {
            result.byCategory[cat] = 0;
        });
        Object.values(enums_1.SparePartStatus).forEach(status => {
            result.byStatus[status] = 0;
        });
        const spareParts = await index_1.SparePart.find();
        spareParts.forEach(sp => {
            result.totalSold += sp.sold;
            result.byCategory[sp.category] += sp.sold;
            result.byStatus[sp.status]++;
        });
        return result;
    }
    async searchSpareParts(query, limit = 10) {
        try {
            return index_1.SparePart.find({ $text: { $search: query } }, { score: { $meta: 'textScore' } })
                .sort({ score: { $meta: 'textScore' } })
                .limit(limit);
        }
        catch (error) {
            logger_1.default.error('Failed to search spare parts', {
                query,
                error: error.message,
                timestamp: new Date().toISOString(),
            });
            throw error;
        }
    }
    determineStatus(stock, reserved, minimumStockLevel) {
        const available = Math.max(0, stock - reserved);
        if (available <= 0)
            return enums_1.SparePartStatus.OUT_OF_STOCK;
        if (available <= minimumStockLevel)
            return enums_1.SparePartStatus.LOW_STOCK;
        return enums_1.SparePartStatus.AVAILABLE;
    }
}
exports.sparePartService = new SparePartService();
//# sourceMappingURL=spare_part.services.js.map