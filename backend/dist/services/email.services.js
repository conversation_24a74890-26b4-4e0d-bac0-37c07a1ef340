"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.emailService = void 0;
const nodemailer_1 = __importStar(require("nodemailer"));
const env_config_1 = require("../config/env_config");
const app_errors_1 = require("../errors/app_errors");
const logger_1 = __importDefault(require("../config/logger"));
/**
 * Professional Email Service for Gas Delivery System
 * Supports HTML templates, attachments, and delivery tracking
 */
class EmailService {
    static instance;
    transporter = null;
    metrics = {
        sentCount: 0,
        failedCount: 0,
        lastError: null,
    };
    constructor() {
        this.initializeTransporter();
    }
    static getInstance() {
        if (!EmailService.instance) {
            EmailService.instance = new EmailService();
        }
        return EmailService.instance;
    }
    /**
     * Initialize email transporter based on configuration
     */
    initializeTransporter() {
        try {
            // Use Gmail SMTP configuration for both development and production
            const emailConfig = {
                host: env_config_1.config.email?.host || 'smtp.gmail.com',
                port: env_config_1.config.email?.port || 587,
                secure: env_config_1.config.email?.secure || false,
                auth: {
                    user: env_config_1.config.email?.user || process.env.EMAIL_USER || '',
                    pass: env_config_1.config.email?.pass || process.env.EMAIL_PASS || '',
                },
                from: env_config_1.config.email?.from || process.env.EMAIL_FROM || '<EMAIL>',
            };
            logger_1.default.info('Initializing email service with configuration', {
                host: emailConfig.host,
                port: emailConfig.port,
                secure: emailConfig.secure,
                user: emailConfig.auth.user,
                from: emailConfig.from,
                environment: env_config_1.config.server.env,
            });
            this.transporter = (0, nodemailer_1.createTransport)(emailConfig);
            // Verify connection
            this.verifyConnection();
        }
        catch (error) {
            logger_1.default.error('Failed to initialize email transporter', { error: error.message });
            throw new app_errors_1.InternalServerError('Email service initialization failed');
        }
    }
    /**
     * Verify email service connection
     */
    async verifyConnection() {
        try {
            if (this.transporter) {
                await this.transporter.verify();
                logger_1.default.info('Email service connection verified successfully');
            }
        }
        catch (error) {
            logger_1.default.error('Email service connection verification failed', { error: error.message });
            // Don't throw error here - allow service to continue with degraded functionality
        }
    }
    /**
     * Send a single email
     */
    async sendEmail(to, template, options) {
        try {
            if (!this.transporter) {
                throw new app_errors_1.InternalServerError('Email transporter not initialized');
            }
            // Validate inputs
            if (!to || (Array.isArray(to) && to.length === 0)) {
                throw new app_errors_1.BadRequestError('Email recipient is required');
            }
            if (!template.subject || !template.body) {
                throw new app_errors_1.BadRequestError('Email subject and body are required');
            }
            const mailOptions = {
                from: env_config_1.config.email?.from || 'Ciribey Gas Delivery <<EMAIL>>',
                to: Array.isArray(to) ? to.join(', ') : to,
                subject: template.subject,
                html: template.body,
                priority: options?.priority || 'normal',
                replyTo: options?.replyTo,
                cc: options?.cc,
                bcc: options?.bcc,
                attachments: options?.attachments,
            };
            const result = await this.transporter.sendMail(mailOptions);
            this.metrics.sentCount++;
            logger_1.default.info('Email sent successfully', {
                to: Array.isArray(to) ? to.length + ' recipients' : to,
                subject: template.subject,
                messageId: result.messageId,
            });
            return {
                success: true,
                messageId: result.messageId,
                details: result,
            };
        }
        catch (error) {
            this.metrics.failedCount++;
            this.metrics.lastError = error instanceof Error ? error : new Error(String(error));
            logger_1.default.error('Failed to send email', {
                to: Array.isArray(to) ? to.length + ' recipients' : to,
                subject: template.subject,
                error: error.message,
            });
            return {
                success: false,
                error: error.message,
                details: error,
            };
        }
    }
    /**
     * Send emails to multiple recipients (bulk email)
     */
    async sendBulkEmail(recipients, template, options) {
        const batchSize = options?.batchSize || 50;
        const delay = options?.delayBetweenBatches || 1000;
        const results = {
            successful: [],
            failed: [],
        };
        // Process in batches to avoid overwhelming the SMTP server
        for (let i = 0; i < recipients.length; i += batchSize) {
            const batch = recipients.slice(i, i + batchSize);
            await Promise.all(batch.map(async (email) => {
                try {
                    const result = await this.sendEmail(email, template);
                    if (result.success) {
                        results.successful.push(email);
                    }
                    else {
                        results.failed.push({ email, error: result.error || 'Unknown error' });
                    }
                }
                catch (error) {
                    results.failed.push({
                        email,
                        error: error instanceof Error ? error.message : String(error),
                    });
                }
                // Update progress
                options?.onProgress?.(results.successful.length, results.failed.length, recipients.length);
            }));
            // Delay between batches (except for the last batch)
            if (i + batchSize < recipients.length) {
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        // Determine status
        let status = 'success';
        let message = 'All emails sent successfully';
        if (results.failed.length > 0) {
            status = results.successful.length > 0 ? 'partial_success' : 'failed';
            message =
                results.successful.length > 0
                    ? 'Some emails sent successfully'
                    : 'All emails failed to send';
        }
        return {
            status,
            message,
            data: {
                successful: results.successful,
                failed: results.failed,
                counts: {
                    total: recipients.length,
                    successful: results.successful.length,
                    failed: results.failed.length,
                },
                sentAt: new Date().toISOString(),
            },
        };
    }
    /**
     * Get email service metrics
     */
    getMetrics() {
        return {
            ...this.metrics,
            successRate: this.metrics.sentCount > 0
                ? (this.metrics.sentCount - this.metrics.failedCount) / this.metrics.sentCount
                : 1,
        };
    }
    /**
     * Check email service health
     */
    async checkHealth() {
        try {
            if (!this.transporter) {
                return false;
            }
            await this.transporter.verify();
            return true;
        }
        catch (error) {
            logger_1.default.error('Email service health check failed', { error: error.message });
            return false;
        }
    }
    /**
     * Create a test email account (for development)
     */
    static async createTestAccount() {
        try {
            const testAccount = await nodemailer_1.default.createTestAccount();
            logger_1.default.info('Test email account created', {
                user: testAccount.user,
                pass: testAccount.pass,
                smtp: testAccount.smtp,
                imap: testAccount.imap,
                pop3: testAccount.pop3,
                web: testAccount.web,
            });
            return testAccount;
        }
        catch (error) {
            logger_1.default.error('Failed to create test email account', { error: error.message });
            throw error;
        }
    }
}
exports.emailService = EmailService.getInstance();
//# sourceMappingURL=email.services.js.map