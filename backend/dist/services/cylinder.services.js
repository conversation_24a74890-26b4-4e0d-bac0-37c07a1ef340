"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cylinderService = void 0;
const index_1 = require("../models/index");
const app_errors_1 = require("../errors/app_errors");
const mongoose_1 = __importDefault(require("mongoose"));
const enums_1 = require("../enums/enums");
const image_url_generator_1 = require("../utils/image-url-generator");
const file_cleanup_1 = require("../utils/file_cleanup");
const notification_helper_1 = require("../utils/notification_helper");
const logger_1 = __importDefault(require("../config/logger"));
class CylinderService {
    /**
     * Reserve cylinders for an order
     * @throws {BadRequestError} If quantity is invalid or insufficient stock
     * @throws {NotFoundError} If cylinder not found
     */
    async reserveCylinder(cylinderId, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const cylinder = await index_1.Cylinder.findById(cylinderId).session(session || null);
        if (!cylinder) {
            throw new app_errors_1.NotFoundError('Cylinder not found', {
                code: 'CYLINDER_NOT_FOUND',
            });
        }
        if (cylinder.status !== enums_1.CylinderStatus.Active) {
            throw new app_errors_1.BadRequestError(`Cannot reserve cylinder with status ${cylinder.status}`, {
                code: 'INVALID_CYLINDER_STATUS',
            });
        }
        if (cylinder.availableQuantity < quantity) {
            throw new app_errors_1.BadRequestError('Insufficient cylinder stock', {
                code: 'INSUFFICIENT_CYLINDER_STOCK',
                details: {
                    available: cylinder.availableQuantity,
                    requested: quantity,
                },
            });
        }
        const update = {
            $inc: { reserved: quantity },
        };
        // Update status if this reservation brings us to out of stock
        if (cylinder.quantity - (cylinder.reserved + quantity) <= 0) {
            update.$set = { status: enums_1.CylinderStatus.OutOfStock };
        }
        const result = await index_1.Cylinder.updateOne({
            _id: cylinderId,
            $expr: { $gte: [{ $subtract: ['$quantity', '$reserved'] }, quantity] },
        }, update, { session });
        if (result.modifiedCount === 0) {
            throw new app_errors_1.DuplicateResourceError('Cylinder stock changed during reservation', {
                code: 'CONCURRENT_MODIFICATION',
            });
        }
        return {
            modifiedCount: result.modifiedCount,
            newStatus: update.$set?.status,
        };
    }
    /**
     * Release reserved cylinders (when order is cancelled)
     * @throws {BadRequestError} If quantity is invalid or exceeds reserved amount
     * @throws {NotFoundError} If cylinder not found
     */
    async releaseReservation(cylinderId, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const cylinder = await index_1.Cylinder.findById(cylinderId).session(session || null);
        if (!cylinder) {
            throw new app_errors_1.NotFoundError('Cylinder not found', {
                code: 'CYLINDER_NOT_FOUND',
            });
        }
        if (cylinder.reserved < quantity) {
            throw new app_errors_1.BadRequestError('Cannot release more than reserved quantity', {
                code: 'INVALID_RESERVATION_RELEASE',
                details: {
                    reserved: cylinder.reserved,
                    toRelease: quantity,
                },
            });
        }
        const update = {
            $inc: { reserved: -quantity },
        };
        // Update status if we're coming back from out of stock
        if (cylinder.status === enums_1.CylinderStatus.OutOfStock &&
            cylinder.quantity - (cylinder.reserved - quantity) > 0) {
            update.$set = { status: enums_1.CylinderStatus.Active };
        }
        const result = await index_1.Cylinder.updateOne({ _id: cylinderId, reserved: { $gte: quantity } }, update, { session });
        if (result.modifiedCount === 0) {
            throw new app_errors_1.DuplicateResourceError('Cylinder stock changed during release', {
                code: 'CONCURRENT_MODIFICATION',
            });
        }
        return {
            modifiedCount: result.modifiedCount,
            newStatus: update.$set?.status,
        };
    }
    /**
     * Mark reserved cylinders as sold (when order is completed)
     * @throws {BadRequestError} If quantity is invalid
     * @throws {NotFoundError} If cylinder not found
     */
    async markAsSold(cylinderId, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const cylinder = await index_1.Cylinder.findByIdAndUpdate(cylinderId, {
            $inc: {
                reserved: -quantity,
                sold: quantity,
                quantity: -quantity, // Physical count decreases when sold
            },
        }, { new: true, session });
        if (!cylinder) {
            throw new app_errors_1.NotFoundError('Cylinder not found', {
                code: 'CYLINDER_NOT_FOUND',
            });
        }
        // Check stock levels and send alerts if needed
        try {
            const itemName = `${cylinder.type} Gas Cylinder`;
            await notification_helper_1.Notifications.Inventory.checkStockAndAlert(itemName, cylinder.availableQuantity, cylinder.minimumStockLevel || 5, // Default minimum stock level
            'so');
        }
        catch (alertError) {
            logger_1.default.error('Failed to send stock alert for cylinder', {
                cylinderId,
                error: alertError.message,
                availableQuantity: cylinder.availableQuantity,
                minimumStockLevel: cylinder.minimumStockLevel,
            });
            // Don't throw error - stock alerts shouldn't block the main operation
        }
        return cylinder;
    }
    /**
     * Restock cylinders and update status if needed
     * @throws {BadRequestError} If quantity is invalid
     * @throws {NotFoundError} If cylinder not found
     */
    async restock(cylinderId, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const update = {
            $inc: { quantity },
            lastRestockedAt: new Date(),
        };
        // Update status if coming from out of stock
        update.$set = {
            status: enums_1.CylinderStatus.Active,
        };
        const cylinder = await index_1.Cylinder.findByIdAndUpdate(cylinderId, update, { new: true, session });
        if (!cylinder) {
            throw new app_errors_1.NotFoundError('Cylinder not found', {
                code: 'CYLINDER_NOT_FOUND',
            });
        }
        // Log successful restock
        logger_1.default.info('Cylinder restocked successfully', {
            cylinderId,
            restockQuantity: quantity,
            newTotalQuantity: cylinder.quantity,
            availableQuantity: cylinder.availableQuantity,
        });
        return cylinder;
    }
    /**
     * Create a new cylinder type
     * @throws {DuplicateResourceError} If cylinder type already exists
     */
    async createCylinder(cylinderData) {
        const session = await mongoose_1.default.startSession();
        try {
            session.startTransaction();
            // Check if cylinder type already exists
            const existing = await index_1.Cylinder.findOne({
                type: cylinderData.type,
                material: cylinderData.material,
            }).session(session);
            if (existing) {
                throw new app_errors_1.DuplicateResourceError('Cylinder type already exists', {
                    code: 'CYLINDER_EXISTS',
                });
            }
            // Handle image URL logic - prioritize uploaded images over generated ones
            let finalImageUrl = cylinderData.imageUrl;
            if (!cylinderData.imagePath && !cylinderData.imageUrl) {
                // Generate dynamic image URL only if no uploaded image or custom URL provided
                finalImageUrl = (0, image_url_generator_1.getCylinderImageUrl)(cylinderData.type, cylinderData.material);
            }
            const cylinder = new index_1.Cylinder({
                quantity: 0,
                reserved: 0,
                sold: 0,
                minimumStockLevel: 10,
                status: enums_1.CylinderStatus.Active,
                ...cylinderData,
                imageUrl: finalImageUrl,
                // imagePath is already included in cylinderData if provided
            });
            await cylinder.save({ session });
            await session.commitTransaction();
            return cylinder;
        }
        catch (error) {
            await session.abortTransaction();
            throw error;
        }
        finally {
            session.endSession();
        }
    }
    /**
     * Update cylinder details (excluding inventory-related fields)
     * @throws {NotFoundError} If cylinder not found
     */
    async updateCylinder(cylinderId, updateData) {
        const session = await mongoose_1.default.startSession();
        try {
            session.startTransaction();
            // Prevent updating inventory-related fields through this method
            const { quantity, reserved, sold, availableQuantity, ...safeUpdateData } = updateData;
            const cylinder = await index_1.Cylinder.findByIdAndUpdate(cylinderId, safeUpdateData, {
                new: true,
                runValidators: true,
                session,
            });
            if (!cylinder) {
                throw new app_errors_1.NotFoundError('Cylinder not found', {
                    code: 'CYLINDER_NOT_FOUND',
                });
            }
            await session.commitTransaction();
            return cylinder;
        }
        catch (error) {
            await session.abortTransaction();
            throw error;
        }
        finally {
            session.endSession();
        }
    }
    /**
     * Soft delete a cylinder type (only if no active reservations)
     * @throws {NotFoundError} If cylinder not found
     * @throws {BadRequestError} If cylinder has active reservations or is linked to packages
     */
    async deleteCylinder(cylinderId, deletedBy) {
        const session = await mongoose_1.default.startSession();
        try {
            session.startTransaction();
            // Check if cylinder exists and has no reservations
            const existing = await index_1.Cylinder.findOne({
                _id: cylinderId,
                isDeleted: false,
            }).session(session);
            if (!existing) {
                throw new app_errors_1.NotFoundError('Cylinder not found', {
                    code: 'CYLINDER_NOT_FOUND',
                });
            }
            if (existing.reserved > 0) {
                throw new app_errors_1.BadRequestError('Cannot delete cylinder with active reservations', {
                    code: 'ACTIVE_RESERVATIONS',
                    details: {
                        reserved: existing.reserved,
                    },
                });
            }
            // Check if cylinder is linked to any packages
            const linkedPackages = await index_1.Package.find({
                cylinder: cylinderId,
                isDeleted: false,
            })
                .select('name')
                .session(session);
            if (linkedPackages.length > 0) {
                const packageNames = linkedPackages.map(pkg => pkg.name).join(', ');
                throw new app_errors_1.BadRequestError(`Cannot delete cylinder. It is linked to the following package${linkedPackages.length > 1 ? 's' : ''}: ${packageNames}`, {
                    code: 'LINKED_TO_PACKAGES',
                    details: {
                        linkedPackages: linkedPackages.map(pkg => ({
                            id: pkg._id,
                            name: pkg.name,
                        })),
                    },
                });
            }
            // Perform soft delete
            const cylinder = await index_1.Cylinder.findByIdAndUpdate(cylinderId, {
                isDeleted: true,
                deletedAt: new Date(),
                deletedBy: deletedBy,
                status: enums_1.CylinderStatus.Discontinued, // Mark as discontinued when deleted
            }, { new: true, session });
            await session.commitTransaction();
            return cylinder;
        }
        catch (error) {
            await session.abortTransaction();
            throw error;
        }
        finally {
            session.endSession();
        }
    }
    /**
     * Restore a soft-deleted cylinder
     * @throws {NotFoundError} If cylinder not found or not deleted
     */
    async restoreCylinder(cylinderId) {
        const session = await mongoose_1.default.startSession();
        try {
            session.startTransaction();
            const cylinder = await index_1.Cylinder.findOneAndUpdate({
                _id: cylinderId,
                isDeleted: true,
            }, {
                isDeleted: false,
                deletedAt: undefined,
                deletedBy: undefined,
                status: enums_1.CylinderStatus.Active, // Restore to active status
            }, { new: true, session });
            if (!cylinder) {
                throw new app_errors_1.NotFoundError('Deleted cylinder not found', {
                    code: 'DELETED_CYLINDER_NOT_FOUND',
                });
            }
            await session.commitTransaction();
            return cylinder;
        }
        catch (error) {
            await session.abortTransaction();
            throw error;
        }
        finally {
            session.endSession();
        }
    }
    /**
     * Permanently delete a cylinder (hard delete)
     * Also deletes associated image file from uploads directory
     * @throws {NotFoundError} If cylinder not found
     * @throws {BadRequestError} If cylinder is linked to packages
     */
    async permanentlyDeleteCylinder(cylinderId) {
        const session = await mongoose_1.default.startSession();
        try {
            session.startTransaction();
            // Check if cylinder is linked to any packages (even if soft-deleted)
            const linkedPackages = await index_1.Package.find({
                cylinder: cylinderId,
            })
                .select('name isDeleted')
                .session(session);
            if (linkedPackages.length > 0) {
                const activePackages = linkedPackages.filter(pkg => !pkg.isDeleted);
                const deletedPackages = linkedPackages.filter(pkg => pkg.isDeleted);
                let errorMessage = 'Cannot permanently delete cylinder. It is linked to packages.';
                const details = { linkedPackages: [] };
                if (activePackages.length > 0) {
                    const activeNames = activePackages.map(pkg => pkg.name).join(', ');
                    errorMessage += ` Active packages: ${activeNames}.`;
                    details.activePackages = activePackages.map(pkg => ({ id: pkg._id, name: pkg.name }));
                }
                if (deletedPackages.length > 0) {
                    const deletedNames = deletedPackages.map(pkg => pkg.name).join(', ');
                    errorMessage += ` Deleted packages: ${deletedNames}.`;
                    details.deletedPackages = deletedPackages.map(pkg => ({ id: pkg._id, name: pkg.name }));
                }
                errorMessage += ' Remove cylinder from packages first.';
                throw new app_errors_1.BadRequestError(errorMessage, {
                    code: 'LINKED_TO_PACKAGES',
                    details,
                });
            }
            const cylinder = await index_1.Cylinder.findOneAndDelete({
                _id: cylinderId,
                isDeleted: true, // Only allow permanent deletion of soft-deleted items
            }).session(session);
            if (!cylinder) {
                throw new app_errors_1.NotFoundError('Deleted cylinder not found', {
                    code: 'DELETED_CYLINDER_NOT_FOUND',
                });
            }
            await session.commitTransaction();
            // After successful database deletion, clean up the image file
            if (cylinder.imagePath) {
                logger_1.default.info('Cleaning up cylinder image file', {
                    cylinderId: cylinder._id,
                    imagePath: cylinder.imagePath,
                });
                const deleted = await file_cleanup_1.FileCleanupService.deleteImageFile(cylinder.imagePath);
                if (deleted) {
                    // Optionally clean up empty directories
                    await file_cleanup_1.FileCleanupService.cleanupEmptyDirectories(cylinder.imagePath);
                }
            }
            return cylinder;
        }
        catch (error) {
            await session.abortTransaction();
            throw error;
        }
        finally {
            session.endSession();
        }
    }
    /**
     * Get packages that use a specific cylinder
     * @param cylinderId - The cylinder ID to check
     * @param includeDeleted - Whether to include deleted packages
     * @returns Array of packages that use this cylinder
     */
    async getPackagesUsingCylinder(cylinderId, includeDeleted = false) {
        const query = { cylinder: cylinderId };
        if (!includeDeleted) {
            query.isDeleted = false;
        }
        const packages = await index_1.Package.find(query).select('name isDeleted');
        return packages.map(pkg => ({
            id: pkg._id.toString(),
            name: pkg.name,
            isDeleted: pkg.isDeleted || false,
        }));
    }
    /**
     * Get cylinder by ID (excludes soft-deleted items)
     * @throws {NotFoundError} If cylinder not found
     */
    async getCylinderById(cylinderId, includeDeleted = false) {
        const query = { _id: cylinderId };
        if (!includeDeleted) {
            query.isDeleted = false;
        }
        const cylinder = await index_1.Cylinder.findOne(query);
        if (!cylinder) {
            throw new app_errors_1.NotFoundError('Cylinder not found', {
                code: 'CYLINDER_NOT_FOUND',
            });
        }
        return cylinder;
    }
    /**
     * List all cylinders with optional filtering and pagination (excludes soft-deleted items)
     */
    async listCylinders(filter = {}, pagination = { page: 1, limit: 10 }, requestedUser) {
        const query = {};
        // Exclude soft-deleted items by default
        if (!filter.includeDeleted) {
            query.isDeleted = false;
        }
        if (filter.type)
            query.type = filter.type;
        if (filter.material)
            query.material = filter.material;
        // if (filter.status) query.status = filter.status;
        if (filter.status) {
            if (requestedUser.role === enums_1.UserRole.CUSTOMER) {
                query.status = enums_1.CylinderStatus.Active;
            }
            else {
                query.status = filter.status;
            }
        }
        if (filter.lowStockOnly) {
            query.$expr = { $lte: ['$quantity', '$minimumStockLevel'] };
        }
        const [data, total] = await Promise.all([
            index_1.Cylinder.find(query)
                .sort({ type: 1, material: 1 })
                .skip((pagination.page - 1) * pagination.limit)
                .limit(pagination.limit),
            index_1.Cylinder.countDocuments(query),
        ]);
        return { data, total };
    }
    /**
     * List soft-deleted cylinders with pagination
     */
    async listDeletedCylinders(pagination = { page: 1, limit: 10 }) {
        const query = { isDeleted: true };
        const [data, total] = await Promise.all([
            index_1.Cylinder.find(query)
                .sort({ deletedAt: -1 }) // Most recently deleted first
                .skip((pagination.page - 1) * pagination.limit)
                .limit(pagination.limit),
            index_1.Cylinder.countDocuments(query),
        ]);
        return { data, total };
    }
    /**
     * Check cylinder availability
     * @throws {NotFoundError} If cylinder not found
     */
    async checkAvailability(cylinderId, quantity) {
        const cylinder = await index_1.Cylinder.findById(cylinderId);
        if (!cylinder) {
            throw new app_errors_1.NotFoundError('Cylinder not found', {
                code: 'CYLINDER_NOT_FOUND',
            });
        }
        return {
            available: cylinder.status === enums_1.CylinderStatus.Active && cylinder.availableQuantity >= quantity,
            availableQuantity: cylinder.availableQuantity,
            status: cylinder.status,
        };
    }
    /**
     * Get low stock alerts (quantity <= minimumStockLevel)
     */
    async getLowStockAlerts() {
        return index_1.Cylinder.find({
            $expr: { $lte: ['$quantity', '$minimumStockLevel'] },
            status: enums_1.CylinderStatus.Active, // Only active cylinders
        }).sort({ quantity: 1 }); // Sort by most critical first
    }
    /**
     * Get sales statistics aggregated by type and material
     */
    async getSalesStatistics() {
        const result = {
            totalSold: 0,
            byType: {},
            byMaterial: {},
            byStatus: {},
        };
        // Initialize all possible values with 0
        Object.values(enums_1.CylinderType).forEach(type => (result.byType[type] = 0));
        Object.values(enums_1.CylinderMaterial).forEach(material => (result.byMaterial[material] = 0));
        Object.values(enums_1.CylinderStatus).forEach(status => (result.byStatus[status] = 0));
        const cylinders = await index_1.Cylinder.find();
        cylinders.forEach(cylinder => {
            result.totalSold += cylinder.sold;
            result.byType[cylinder.type] += cylinder.sold;
            result.byMaterial[cylinder.material] += cylinder.sold;
            result.byStatus[cylinder.status]++;
        });
        return result;
    }
    /**
     * Bulk update cylinder statuses
     * @throws {BadRequestError} If invalid status transition
     */
    async bulkUpdateStatus(cylinderIds, newStatus, session) {
        // Validate status transitions
        if (newStatus === enums_1.CylinderStatus.Active) {
            // Can only activate cylinders that are out of stock
            const result = await index_1.Cylinder.updateMany({
                _id: { $in: cylinderIds },
                status: { $in: [enums_1.CylinderStatus.OutOfStock] },
            }, { $set: { status: newStatus } }, { session });
            return result.modifiedCount;
        }
        else {
            // For other statuses (Discontinued)
            const result = await index_1.Cylinder.updateMany({ _id: { $in: cylinderIds } }, { $set: { status: newStatus } }, { session });
            return result.modifiedCount;
        }
    }
    /**
     * Get cylinders with enhanced image URLs (prioritizes uploaded images)
     * This method adds the best available image URL to each cylinder
     */
    async getCylindersWithImages(filter = {}, options = {}) {
        const { page = 1, limit = 10, sort = { createdAt: -1 } } = options;
        const skip = (page - 1) * limit;
        // Base query excludes soft-deleted items
        const query = { isDeleted: false, ...filter };
        const [cylinders, total] = await Promise.all([
            index_1.Cylinder.find(query).sort(sort).skip(skip).limit(limit).lean(),
            index_1.Cylinder.countDocuments(query),
        ]);
        // Enhance each cylinder with the best available image URL
        const enhancedCylinders = cylinders.map((cylinder) => ({
            ...cylinder,
            bestImageUrl: image_url_generator_1.ImageUrlGenerator.getBestImageUrl(cylinder.imagePath, cylinder.imageUrl, 'cylinder'),
        }));
        return {
            cylinders: enhancedCylinders,
            total,
            page,
            totalPages: Math.ceil(total / limit),
        };
    }
}
exports.cylinderService = new CylinderService();
//# sourceMappingURL=cylinder.services.js.map