"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.notificationDispatcher = void 0;
const app_message_service_1 = require("../constants/app_message.service");
const sms_services_1 = require("./sms.services");
const email_services_1 = require("./email.services");
const notification_services_1 = require("./notification.services");
const models_1 = require("../models");
const app_errors_1 = require("../errors/app_errors");
const logger_1 = __importDefault(require("../config/logger"));
/**
 * Unified Notification Dispatcher Service
 * Handles SMS, Email, and Push notifications with template-based messaging
 */
class NotificationDispatcherService {
    static instance;
    metrics = {
        totalSent: 0,
        totalFailed: 0,
        channelMetrics: {
            sms: { sent: 0, failed: 0 },
            email: { sent: 0, failed: 0 },
            push: { sent: 0, failed: 0 },
        },
    };
    constructor() { }
    static getInstance() {
        if (!NotificationDispatcherService.instance) {
            NotificationDispatcherService.instance = new NotificationDispatcherService();
        }
        return NotificationDispatcherService.instance;
    }
    /**
     * Send notification using the unified dispatcher
     */
    async sendNotification(payload) {
        try {
            this.validatePayload(payload);
            const results = {
                success: true,
                channels: {},
                totalRecipients: payload.recipients.length,
                successfulDeliveries: 0,
                failedDeliveries: 0,
                errors: [],
            };
            // Process each recipient
            for (const recipient of payload.recipients) {
                const recipientResults = await this.processRecipient(recipient, payload);
                // Merge results
                this.mergeResults(results, recipientResults);
            }
            // Update metrics
            this.updateMetrics(results);
            // Determine overall success
            results.success = results.failedDeliveries === 0;
            logger_1.default.info('Notification dispatch completed', {
                type: payload.type,
                channels: payload.channels,
                totalRecipients: results.totalRecipients,
                successful: results.successfulDeliveries,
                failed: results.failedDeliveries,
            });
            return results;
        }
        catch (error) {
            logger_1.default.error('Notification dispatch failed', {
                type: payload.type,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Process notification for a single recipient
     */
    async processRecipient(recipient, payload) {
        const results = {
            channels: {},
            successfulDeliveries: 0,
            failedDeliveries: 0,
            errors: [],
        };
        // Get user data if userId is provided
        let userData = null;
        if (recipient.userId) {
            userData = await models_1.User.findById(recipient.userId).lean();
            if (userData) {
                recipient.phone = recipient.phone || userData.phone;
                recipient.email = recipient.email || userData.email;
                recipient.role = recipient.role || userData.role;
                recipient.fcmToken = recipient.fcmToken || userData.notification?.fcmToken;
            }
        }
        // Create message service instance
        const messageService = new app_message_service_1.AppMessageService({
            lang: recipient.language || 'so',
            recipientRole: recipient.role || 'customer',
            includePhoneInFooter: payload.options?.includePhoneInFooter ?? true,
        });
        // Generate messages for each channel
        const messages = this.generateMessages(messageService, payload.type, payload.data);
        // Send to each requested channel
        for (const channel of payload.channels) {
            if (channel === 'all') {
                // Send to all available channels
                await this.sendToAllChannels(recipient, messages, results);
            }
            else {
                await this.sendToChannel(channel, recipient, messages, results);
            }
        }
        return results;
    }
    /**
     * Generate messages for different channels based on notification type
     */
    generateMessages(messageService, type, data) {
        let smsMessage = '';
        let emailTemplate = null;
        let pushMessage = { title: '', body: '' };
        switch (type) {
            case 'otp_verification':
                smsMessage = messageService.otpMessage(data.otp, data.expiresIn);
                pushMessage = {
                    title: `🔐 Verification Code`,
                    body: `Your code: ${data.otp}`,
                };
                break;
            case 'order_confirmed':
                smsMessage = messageService.orderConfirmed(data.orderId);
                pushMessage = {
                    title: '✅ Order Confirmed',
                    body: `Order ${data.orderId} has been confirmed`,
                };
                break;
            case 'order_delivered':
                smsMessage = messageService.orderDelivered(data.orderId);
                pushMessage = {
                    title: '🚚 Order Delivered',
                    body: `Order ${data.orderId} has been delivered`,
                };
                break;
            case 'order_cancelled':
                smsMessage = messageService.orderCancelled(data.orderId, data.reason);
                pushMessage = {
                    title: '❌ Order Cancelled',
                    body: `Order ${data.orderId} has been cancelled`,
                };
                break;
            case 'delivery_assigned':
                smsMessage = messageService.deliveryAssigned(data.orderId, data.customerName, data.address);
                pushMessage = {
                    title: '📦 New Delivery Assignment',
                    body: `Order ${data.orderId} assigned to you`,
                };
                break;
            case 'delivery_started':
                smsMessage = messageService.deliveryStarted(data.orderId, data.agentName, data.agentPhone);
                pushMessage = {
                    title: '🚛 Delivery Started',
                    body: `Your order ${data.orderId} is on the way`,
                };
                break;
            case 'payment_confirmed':
                smsMessage = messageService.paymentConfirmed(data.orderId, data.amount);
                pushMessage = {
                    title: '💳 Payment Confirmed',
                    body: `Payment for order ${data.orderId} confirmed`,
                };
                break;
            case 'payment_failed':
                smsMessage = messageService.paymentFailed(data.orderId, data.reason);
                pushMessage = {
                    title: '❌ Payment Failed',
                    body: `Payment for order ${data.orderId} failed`,
                };
                break;
            case 'low_stock_alert':
                smsMessage = messageService.lowStockAlert(data.item, data.quantity);
                emailTemplate = messageService.lowStockEmail(data.item, data.quantity);
                pushMessage = {
                    title: '⚠️ Low Stock Alert',
                    body: `${data.item}: Only ${data.quantity} remaining`,
                };
                break;
            case 'out_of_stock_alert':
                smsMessage = messageService.outOfStockAlert(data.item);
                pushMessage = {
                    title: '🚨 Out of Stock',
                    body: `${data.item} is out of stock`,
                };
                break;
            case 'welcome_message':
                smsMessage = messageService.welcomeMessage(data.userName, data.userRole);
                pushMessage = {
                    title: '🎉 Welcome!',
                    body: `Welcome to Ciribey Gas Delivery, ${data.userName}`,
                };
                break;
            case 'generic_notification':
                smsMessage = messageService.genericNotification(data.title, data.message);
                pushMessage = {
                    title: data.title,
                    body: data.message,
                };
                break;
            case 'emergency_alert':
                smsMessage = messageService.emergencyAlert(data.message);
                pushMessage = {
                    title: '🚨 URGENT',
                    body: data.message,
                };
                break;
            case 'maintenance_notification':
                smsMessage = messageService.maintenanceNotification(data.startTime, data.duration);
                pushMessage = {
                    title: '🔧 System Maintenance',
                    body: `Maintenance scheduled: ${data.startTime}`,
                };
                break;
            default:
                throw new app_errors_1.BadRequestError(`Unsupported notification type: ${type}`);
        }
        return {
            sms: smsMessage,
            email: emailTemplate,
            push: pushMessage,
        };
    }
    /**
     * Send notification to all available channels for a recipient
     */
    async sendToAllChannels(recipient, messages, results) {
        const channels = [];
        if (recipient.phone)
            channels.push('sms');
        if (recipient.email)
            channels.push('email');
        if (recipient.fcmToken)
            channels.push('push');
        for (const channel of channels) {
            await this.sendToChannel(channel, recipient, messages, results);
        }
    }
    /**
     * Send notification to a specific channel
     */
    async sendToChannel(channel, recipient, messages, results) {
        try {
            switch (channel) {
                case 'sms':
                    if (recipient.phone && messages.sms) {
                        const result = await sms_services_1.smsService.sendSms(recipient.phone, messages.sms);
                        results.channels.sms = {
                            success: true,
                            messageId: result.messageId,
                        };
                        results.successfulDeliveries++;
                    }
                    else {
                        results.channels.sms = {
                            success: false,
                            error: 'Phone number not available',
                        };
                        results.failedDeliveries++;
                    }
                    break;
                case 'email':
                    if (recipient.email && messages.email) {
                        const result = await email_services_1.emailService.sendEmail(recipient.email, messages.email);
                        results.channels.email = {
                            success: result.success,
                            messageId: result.messageId,
                            error: result.error,
                        };
                        if (result.success) {
                            results.successfulDeliveries++;
                        }
                        else {
                            results.failedDeliveries++;
                            results.errors.push(result.error || 'Email delivery failed');
                        }
                    }
                    else {
                        results.channels.email = {
                            success: false,
                            error: 'Email address not available or no email template',
                        };
                        results.failedDeliveries++;
                    }
                    break;
                case 'push':
                    if (recipient.userId && recipient.fcmToken && messages.push) {
                        const result = await notification_services_1.notificationService.sendToUser(recipient.userId, {
                            title: messages.push.title,
                            body: messages.push.body,
                        });
                        results.channels.push = {
                            success: result.success,
                            messageId: result.details?.messageId,
                        };
                        if (result.success) {
                            results.successfulDeliveries++;
                        }
                        else {
                            results.failedDeliveries++;
                            results.errors.push('Push notification delivery failed');
                        }
                    }
                    else {
                        results.channels.push = {
                            success: false,
                            error: 'FCM token or user ID not available',
                        };
                        results.failedDeliveries++;
                    }
                    break;
                default:
                    throw new app_errors_1.BadRequestError(`Unsupported channel: ${channel}`);
            }
        }
        catch (error) {
            logger_1.default.error(`Failed to send notification via ${channel}`, {
                channel,
                recipient: recipient.userId || recipient.phone || recipient.email,
                error: error.message,
            });
            if (channel === 'sms' || channel === 'email' || channel === 'push') {
                results.channels[channel] = {
                    success: false,
                    error: error.message,
                };
            }
            results.failedDeliveries++;
            results.errors.push(`${channel}: ${error.message}`);
        }
    }
    /**
     * Merge results from individual recipient processing
     */
    mergeResults(mainResults, recipientResults) {
        mainResults.successfulDeliveries += recipientResults.successfulDeliveries || 0;
        mainResults.failedDeliveries += recipientResults.failedDeliveries || 0;
        mainResults.errors.push(...(recipientResults.errors || []));
        // Merge channel results (this is simplified - in production you might want more detailed aggregation)
        Object.keys(recipientResults.channels || {}).forEach(channel => {
            if (!mainResults.channels[channel]) {
                mainResults.channels[channel] =
                    recipientResults.channels[channel];
            }
        });
    }
    /**
     * Update service metrics
     */
    updateMetrics(results) {
        this.metrics.totalSent += results.successfulDeliveries;
        this.metrics.totalFailed += results.failedDeliveries;
        // Update channel-specific metrics
        Object.keys(results.channels).forEach(channel => {
            const channelResult = results.channels[channel];
            if (channelResult) {
                if (channelResult.success) {
                    this.metrics.channelMetrics[channel].sent++;
                }
                else {
                    this.metrics.channelMetrics[channel].failed++;
                }
            }
        });
    }
    /**
     * Validate notification payload
     */
    validatePayload(payload) {
        if (!payload.type) {
            throw new app_errors_1.BadRequestError('Notification type is required');
        }
        if (!payload.channels || payload.channels.length === 0) {
            throw new app_errors_1.BadRequestError('At least one notification channel is required');
        }
        if (!payload.recipients || payload.recipients.length === 0) {
            throw new app_errors_1.BadRequestError('At least one recipient is required');
        }
        if (!payload.data) {
            throw new app_errors_1.BadRequestError('Notification data is required');
        }
        // Validate recipients have required contact information
        for (const recipient of payload.recipients) {
            if (!recipient.userId && !recipient.phone && !recipient.email && !recipient.fcmToken) {
                throw new app_errors_1.BadRequestError('Each recipient must have at least one contact method');
            }
        }
    }
    /**
     * Get service metrics
     */
    getMetrics() {
        return {
            ...this.metrics,
            successRate: this.metrics.totalSent > 0
                ? (this.metrics.totalSent - this.metrics.totalFailed) / this.metrics.totalSent
                : 1,
            channelSuccessRates: {
                sms: this.metrics.channelMetrics.sms.sent > 0
                    ? (this.metrics.channelMetrics.sms.sent - this.metrics.channelMetrics.sms.failed) /
                        this.metrics.channelMetrics.sms.sent
                    : 1,
                email: this.metrics.channelMetrics.email.sent > 0
                    ? (this.metrics.channelMetrics.email.sent - this.metrics.channelMetrics.email.failed) /
                        this.metrics.channelMetrics.email.sent
                    : 1,
                push: this.metrics.channelMetrics.push.sent > 0
                    ? (this.metrics.channelMetrics.push.sent - this.metrics.channelMetrics.push.failed) /
                        this.metrics.channelMetrics.push.sent
                    : 1,
            },
        };
    }
    /**
     * Check health of all notification channels
     */
    async checkHealth() {
        const health = {
            overall: true,
            channels: {
                sms: false,
                email: false,
                push: false,
            },
        };
        try {
            health.channels.sms = await sms_services_1.smsService.checkHealth();
            health.channels.email = await email_services_1.emailService.checkHealth();
            health.channels.push = true; // Push service doesn't have a health check method
            health.overall = health.channels.sms && health.channels.email && health.channels.push;
        }
        catch (error) {
            logger_1.default.error('Health check failed', { error: error.message });
            health.overall = false;
        }
        return health;
    }
}
exports.notificationDispatcher = NotificationDispatcherService.getInstance();
//# sourceMappingURL=notification-dispatcher.service.js.map