"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.smsService = void 0;
const axios_1 = __importDefault(require("axios"));
const querystring_1 = __importDefault(require("querystring"));
const env_config_1 = require("../config/env_config");
const app_errors_1 = require("../errors/app_errors");
const logger_1 = __importDefault(require("../config/logger"));
const app_constants_1 = require("../constants/app_constants");
class SmsService {
    static instance;
    token = null;
    tokenExpiry = null;
    lastRequestTime = 0;
    axiosInstance;
    minRequestDelay = 100; // 100ms between requests
    metrics = {
        sentCount: 0,
        failedCount: 0,
        lastError: null,
    };
    activeRequests = 0;
    maxConcurrent = 5; // Adjust based on provider limits
    retryConfig = {
        maxRetries: 3,
        retryDelay: 1000, // 1 second
    };
    async concurrencyLimit(fn) {
        while (this.activeRequests >= this.maxConcurrent) {
            await new Promise(resolve => setTimeout(resolve, 50));
        }
        this.activeRequests++;
        try {
            return await fn();
        }
        finally {
            this.activeRequests--;
        }
    }
    constructor() {
        this.validateConfig();
        this.axiosInstance = axios_1.default.create({
            // timeout: 10000,
            timeout: env_config_1.config.sms.timeout,
            headers: { 'Content-Type': 'application/json' },
        });
    }
    static getInstance() {
        if (!SmsService.instance) {
            SmsService.instance = new SmsService();
        }
        return SmsService.instance;
    }
    getMetrics() {
        return {
            ...this.metrics,
            successRate: this.metrics.sentCount > 0
                ? (this.metrics.sentCount - this.metrics.failedCount) / this.metrics.sentCount
                : 1,
        };
    }
    async checkHealth() {
        try {
            await this.getToken(true); // Force new token check
            return true;
        }
        catch (error) {
            this.log('Health check failed', 'error', error);
            return false;
        }
    }
    async sendSms(phoneNumber, message, options) {
        try {
            await this.rateLimit();
            // Validate inputs
            this.validatePhoneNumber(phoneNumber);
            this.validateMessage(message, options?.isOtp);
            const token = await this.getToken();
            const url = `${env_config_1.config.sms.providerUrl}/api/SendSMS`;
            const senderid = options?.senderId || env_config_1.config.sms.senderId;
            // const senderid = config.sms.senderId;
            const response = await this.axiosInstance.post(url, {
                mobile: phoneNumber,
                message: message,
                senderid,
                refid: options?.refId || Date.now().toString(),
                validity: 0,
            }, {
                headers: { Authorization: `Bearer ${token}` },
            });
            if (response.data.ResponseCode !== '200') {
                throw new app_errors_1.HormuudSmsError(response.data.ResponseCode, response.data, {
                    recipient: phoneNumber,
                    messageContent: message,
                    isOtp: options?.isOtp,
                });
            }
            this.metrics.sentCount++;
            this.log('SMS sent successfully', 'info', {
                recipient: phoneNumber,
                sender: senderid,
                messageId: response.data.Data?.MessageID,
            });
            return { messageId: response.data.Data?.MessageID };
        }
        catch (error) {
            this.metrics.failedCount++;
            this.metrics.lastError = error instanceof Error ? error : new Error(String(error));
            if (axios_1.default.isAxiosError(error)) {
                if (error.code === 'ECONNABORTED') {
                    throw new app_errors_1.HormuudServiceError('SMS request timed out', {
                        isOtp: options?.isOtp,
                        retryAfter: 60, // 1 minute
                    });
                }
                if (error.response?.status === 401) {
                    this.token = null;
                    this.tokenExpiry = null;
                }
            }
            if (error instanceof app_errors_1.HormuudSmsError || error instanceof app_errors_1.HormuudServiceError) {
                throw error;
            }
            throw new app_errors_1.HormuudServiceError('Unexpected SMS service error', {
                isOtp: options?.isOtp,
                details: this.safeErrorDetails(error),
            });
        }
    }
    // send sms to list of people
    async sendSmsToMany(phoneNumbers, message, options) {
        // Clean and normalize numbers
        const cleanedNumbers = phoneNumbers.map(num => num.trim().replace(/\s+/g, ''));
        // Remove duplicates while preserving original order
        const uniqueNumbers = [];
        const duplicatesRemoved = new Set();
        cleanedNumbers.forEach(num => {
            if (!uniqueNumbers.includes(num)) {
                uniqueNumbers.push(num);
            }
            else {
                duplicatesRemoved.add(num);
            }
        });
        // Validate message (common for all)
        this.validateMessage(message, options?.isOtp);
        const results = {
            successes: [],
            failures: [],
        };
        const updateProgress = () => {
            options?.onProgress?.(results.successes.length, results.failures.length, uniqueNumbers.length);
        };
        const BATCH_SIZE = 100;
        for (let i = 0; i < uniqueNumbers.length; i += BATCH_SIZE) {
            const batch = uniqueNumbers.slice(i, i + BATCH_SIZE);
            await Promise.all(batch.map(phoneNumber => this.concurrencyLimit(async () => {
                try {
                    this.validatePhoneNumber(phoneNumber);
                    await this.rateLimit();
                    const { messageId } = await this.sendSms(phoneNumber, message, options);
                    results.successes.push({ phoneNumber, messageId });
                }
                catch (error) {
                    results.failures.push({
                        phoneNumber,
                        error: error instanceof Error ? error : new Error(String(error)),
                    });
                }
                updateProgress();
            })));
        }
        // Determine result status
        let status = 'success';
        let responseMessage = 'Bulk SMS sent successfully';
        if (results.failures.length > 0) {
            status = results.successes.length > 0 ? 'partial_success' : 'failed';
            responseMessage =
                results.successes.length > 0
                    ? 'Bulk SMS sent with some failures'
                    : 'Bulk SMS failed for all recipients';
        }
        return {
            status,
            message: responseMessage,
            data: {
                messageIds: {
                    successes: results.successes,
                    failures: results.failures,
                },
                counts: {
                    requested: phoneNumbers.length,
                    duplicatesRemoved: duplicatesRemoved.size,
                    attempted: uniqueNumbers.length,
                    successful: results.successes.length,
                    failed: results.failures.length,
                },
                sentAt: new Date().toISOString(),
            },
        };
    }
    async getToken(forceRefresh = false) {
        // Return cached token if valid (with 5 minute buffer)
        if (!forceRefresh && this.token && this.tokenExpiry && Date.now() < this.tokenExpiry - 300000) {
            return this.token;
        }
        try {
            const url = `${env_config_1.config.sms.providerUrl}/token`;
            const payload = querystring_1.default.stringify({
                username: env_config_1.config.sms.username,
                password: env_config_1.config.sms.password,
                grant_type: 'password',
            });
            const response = await axios_1.default.post(url, payload, {
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                timeout: 5000,
            });
            if (!response.data.access_token) {
                throw new Error('No access token received');
            }
            // Cache token
            this.token = response.data.access_token;
            this.tokenExpiry = Date.now() + (response.data.expires_in || 3600) * 1000;
            return this.token;
        }
        catch (error) {
            if (axios_1.default.isAxiosError(error) && error.response?.status === 401) {
                throw new app_errors_1.HormuudSmsError('201', error.response.data);
            }
            throw new app_errors_1.HormuudServiceError('Failed to authenticate with SMS service', {
                details: this.safeErrorDetails(error),
            });
        }
    }
    validateConfig() {
        const required = ['providerUrl', 'username', 'password', 'senderId'];
        const missing = required.filter(key => !env_config_1.config.sms[key]);
        if (missing.length > 0) {
            throw new Error(`Missing SMS configuration: ${missing.join(', ')}`);
        }
    }
    validatePhoneNumber(phoneNumber) {
        const cleaned = phoneNumber.replace(/\D/g, '');
        const regex = /^(252)?(61|62|65|66|67|68|69|71|77|79|88|89|90)\d{7}$/;
        if (!regex.test(cleaned)) {
            throw new app_errors_1.HormuudSmsError('207', {}, {
                recipient: phoneNumber,
                details: 'Invalid Somalia phone number format',
            });
        }
    }
    validateMessage(message, isOtp = false) {
        // const maxLength = isOtp ? 200 : 459;
        const maxLength = isOtp ? app_constants_1.AppConstants.otpCharacterLimit : app_constants_1.AppConstants.smsCharacterLimit;
        if (message.length > maxLength) {
            throw new app_errors_1.HormuudSmsError('206', {}, {
                isOtp,
                details: `Message too long (max ${maxLength} characters)`,
            });
        }
    }
    async rateLimit() {
        const now = Date.now();
        const delay = this.lastRequestTime + this.minRequestDelay - now;
        if (delay > 0) {
            await new Promise(resolve => setTimeout(resolve, delay));
        }
        this.lastRequestTime = Date.now();
    }
    log(message, level = 'info', data) {
        // console.log(
        //   JSON.stringify({
        //     timestamp: new Date().toISOString(),
        //     service: 'sms',
        //     level,
        //     message,
        //     ...data,
        //   })
        // );
        logger_1.default.info(JSON.stringify({
            timestamp: new Date().toISOString(),
            service: 'sms',
            level,
            message,
            ...data,
        }));
    }
    safeErrorDetails(error) {
        if (error instanceof Error) {
            return {
                message: error.message,
                stack: error.stack,
                // ...(error.cause && { cause: this.safeErrorDetails(error.cause) }),
            };
        }
        return String(error);
    }
}
exports.smsService = SmsService.getInstance();
//# sourceMappingURL=sms.services.js.map