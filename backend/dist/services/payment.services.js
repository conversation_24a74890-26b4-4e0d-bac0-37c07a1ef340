"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.paymentService = void 0;
const env_config_1 = require("../config/env_config");
const mongoose_1 = __importStar(require("mongoose"));
const axios_1 = __importDefault(require("axios"));
const index_1 = require("../models/index");
const enums_1 = require("../enums/enums");
const logger_1 = __importDefault(require("../config/logger"));
const app_errors_1 = require("../errors/app_errors");
const timezone_utils_1 = require("../utils/timezone_utils");
/**
 * WaaFi API service names
 */
var PaymentServiceName;
(function (PaymentServiceName) {
    PaymentServiceName["PREAUTHORIZE"] = "API_PREAUTHORIZE";
    PaymentServiceName["PREAUTHORIZE_CANCEL"] = "API_PREAUTHORIZE_CANCEL";
    PaymentServiceName["PREAUTHORIZE_COMMIT"] = "API_PREAUTHORIZE_COMMIT";
})(PaymentServiceName || (PaymentServiceName = {}));
/**
 * Payment configuration constants
 */
const PAYMENT_CONSTANTS = {
    MAX_AMOUNT: 10000000, // 10,000,000 SOS (~$1,500)
    MIN_AMOUNT: 0.001,
    PREAUTH_EXPIRY_HOURS: 24,
    API_TIMEOUT: 30000,
    SUCCESS_RESPONSE_CODE: '2001',
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000,
};
/**
 * PaymentService class for handling WaaFi payment operations
 * Implements singleton pattern for consistent configuration
 */
class PaymentService {
    static instance;
    axiosInstance;
    paymentApiUrl = env_config_1.config.payment.rasiin.apiUrl;
    paymentApiKey = env_config_1.config.payment.rasiin.apiKey;
    paymentApiUserId = env_config_1.config.payment.rasiin.apiUserId;
    paymentMerchantUid = env_config_1.config.payment.rasiin.merchantUid;
    constructor() {
        this.validateConfig();
        this.axiosInstance = this.createAxiosInstance();
    }
    /**
     * Creates and configures axios instance with proper error handling
     */
    createAxiosInstance() {
        const instance = axios_1.default.create({
            baseURL: this.paymentApiUrl,
            timeout: PAYMENT_CONSTANTS.API_TIMEOUT,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'GasSystem/1.0',
            },
        });
        // Add request interceptor for logging
        instance.interceptors.request.use(config => {
            logger_1.default.info('WaaFi API Request', {
                url: config.url,
                method: config.method,
                timestamp: new Date().toISOString(),
            });
            return config;
        }, error => {
            logger_1.default.error('WaaFi API Request Error', { error: error.message });
            return Promise.reject(error);
        });
        // Add response interceptor for logging and error handling
        instance.interceptors.response.use(response => {
            logger_1.default.info('WaaFi API Response', {
                status: response.status,
                responseCode: response.data?.responseCode,
                timestamp: new Date().toISOString(),
            });
            return response;
        }, error => {
            logger_1.default.error('WaaFi API Response Error', {
                status: error.response?.status,
                message: error.message,
                timestamp: new Date().toISOString(),
            });
            return Promise.reject(this.handleAxiosError(error));
        });
        return instance;
    }
    /**
     * Validates payment service configuration
     * @throws {InternalServerError} When configuration is missing or invalid
     */
    validateConfig() {
        const missingConfigs = [];
        if (!this.paymentApiUrl)
            missingConfigs.push('apiUrl');
        if (!this.paymentApiKey)
            missingConfigs.push('apiKey');
        if (!this.paymentApiUserId)
            missingConfigs.push('apiUserId');
        if (!this.paymentMerchantUid)
            missingConfigs.push('merchantUid');
        if (missingConfigs.length > 0) {
            const errorMessage = `Missing WaaFi payment configuration: ${missingConfigs.join(', ')}`;
            logger_1.default.error('Payment service configuration error', {
                missingConfigs,
                timestamp: new Date().toISOString(),
            });
            throw new app_errors_1.InternalServerError(errorMessage, {
                code: 'PAYMENT_CONFIG_MISSING',
                details: { missingConfigs },
            });
        }
    }
    /**
     * Validates payment amount
     * @param amount - Payment amount to validate
     * @throws {BadRequestError} When amount is invalid
     */
    validateAmount(amount) {
        if (typeof amount !== 'number' || isNaN(amount)) {
            throw new app_errors_1.BadRequestError('Amount must be a valid number', {
                code: 'INVALID_AMOUNT_TYPE',
                details: { providedAmount: amount, type: typeof amount },
            });
        }
        if (amount < PAYMENT_CONSTANTS.MIN_AMOUNT) {
            throw new app_errors_1.BadRequestError(`Amount must be at least ${PAYMENT_CONSTANTS.MIN_AMOUNT}`, {
                code: 'AMOUNT_TOO_LOW',
                details: { amount, minimum: PAYMENT_CONSTANTS.MIN_AMOUNT },
            });
        }
        if (amount > PAYMENT_CONSTANTS.MAX_AMOUNT) {
            throw new app_errors_1.BadRequestError(`Amount exceeds maximum limit of ${PAYMENT_CONSTANTS.MAX_AMOUNT}`, {
                code: 'AMOUNT_TOO_HIGH',
                details: { amount, maximum: PAYMENT_CONSTANTS.MAX_AMOUNT },
            });
        }
    }
    /**
     * Validates and formats Somalia mobile number
     * @param mobile - Mobile number to validate
     * @returns Formatted mobile number with country code
     * @throws {ValidationError} When mobile number format is invalid
     */
    validateMobileNumber(mobile) {
        if (!mobile || typeof mobile !== 'string') {
            throw new app_errors_1.ValidationError('Mobile number is required and must be a string', {
                code: 'MOBILE_REQUIRED',
                details: { providedMobile: mobile, type: typeof mobile },
            });
        }
        const cleaned = mobile.replace(/\D/g, '');
        const regex = /^(252)?(61|62|65|66|67|68|69|71|77|79|88|89|90)\d{7}$/;
        if (!regex.test(cleaned)) {
            throw new app_errors_1.ValidationError('Invalid Somalia phone number format', {
                code: 'INVALID_MOBILE_FORMAT',
                details: {
                    providedMobile: mobile,
                    cleanedMobile: cleaned,
                    expectedFormat: '252XXXXXXXXX or XXXXXXXXX',
                },
            });
        }
        const formattedNumber = cleaned.startsWith('252') ? cleaned : `252${cleaned}`;
        logger_1.default.debug('Mobile number validated and formatted', {
            original: mobile,
            formatted: formattedNumber,
        });
        return formattedNumber;
    }
    /**
     * Handles axios errors and converts them to appropriate custom errors
     * @param error - Axios error to handle
     * @returns Appropriate custom error
     */
    handleAxiosError(error) {
        if (error.code === 'ECONNABORTED') {
            return new app_errors_1.WaaFiServiceUnavailable('Payment service request timeout');
        }
        if (error.response?.status === 503) {
            return new app_errors_1.WaaFiServiceUnavailable('Payment service temporarily unavailable');
        }
        if (error.response?.status >= 500) {
            return new app_errors_1.InternalServerError('Payment service internal error', {
                code: 'PAYMENT_SERVICE_ERROR',
                details: {
                    status: error.response.status,
                    message: error.message,
                },
            });
        }
        return new app_errors_1.PaymentError('Payment service communication error', 400, 'PAYMENT_COMMUNICATION_ERROR', {
            details: {
                message: error.message,
                status: error.response?.status,
            },
        });
    }
    /**
     * Validates ObjectId format
     * @param id - ID to validate
     * @param fieldName - Name of the field for error messages
     * @throws {BadRequestError} When ID format is invalid
     */
    validateObjectId(id, fieldName) {
        if (!id) {
            throw new app_errors_1.BadRequestError(`${fieldName} is required`, {
                code: 'MISSING_REQUIRED_FIELD',
                details: { fieldName },
            });
        }
        const idString = typeof id === 'string' ? id : id.toString();
        if (!mongoose_1.Types.ObjectId.isValid(idString)) {
            throw new app_errors_1.BadRequestError(`Invalid ${fieldName} format`, {
                code: 'INVALID_OBJECT_ID',
                details: { fieldName, providedId: idString },
            });
        }
    }
    /**
     * Validates WaaFi API response
     * @param response - API response to validate
     * @param context - Context information for error reporting
     * @throws {WaaFiPaymentFailed} When API response indicates failure
     */
    validateWaaFiResponse(response, context) {
        if (response.responseCode !== PAYMENT_CONSTANTS.SUCCESS_RESPONSE_CODE) {
            logger_1.default.error('WaaFi API returned error response', {
                responseCode: response.responseCode,
                responseMsg: response.responseMsg,
                context,
                timestamp: new Date().toISOString(),
            });
            throw new app_errors_1.WaaFiPaymentFailed(response.responseMsg, {
                paymentId: context.paymentId,
                orderId: context.orderId,
                amount: context.amount,
                details: {
                    responseCode: response.responseCode,
                    responseMsg: response.responseMsg,
                    timestamp: new Date().toISOString(),
                },
            });
        }
    }
    /**
     * Sanitizes request payload by removing sensitive information
     * @param payload - Original request payload
     * @returns Sanitized payload safe for storage
     */
    sanitizeRequestPayload(payload) {
        const sanitized = JSON.parse(JSON.stringify(payload));
        // Remove sensitive fields
        if (sanitized.serviceParams) {
            // Mask API key (keep first 4 and last 4 characters)
            if (sanitized.serviceParams.apiKey) {
                const apiKey = sanitized.serviceParams.apiKey;
                if (apiKey.length > 8) {
                    sanitized.serviceParams.apiKey = `${apiKey.substring(0, 4)}****${apiKey.substring(apiKey.length - 4)}`;
                }
                else {
                    sanitized.serviceParams.apiKey = '****';
                }
            }
            // Mask merchant UID (keep first 4 characters)
            if (sanitized.serviceParams.merchantUid) {
                const merchantUid = sanitized.serviceParams.merchantUid;
                sanitized.serviceParams.merchantUid = `${merchantUid.substring(0, 4)}****`;
            }
            // Mask account number (keep first 3 and last 3 digits)
            if (sanitized.serviceParams.payerInfo?.accountNo) {
                const accountNo = sanitized.serviceParams.payerInfo.accountNo;
                if (accountNo.length > 6) {
                    sanitized.serviceParams.payerInfo.accountNo = `${accountNo.substring(0, 3)}****${accountNo.substring(accountNo.length - 3)}`;
                }
                else {
                    sanitized.serviceParams.payerInfo.accountNo = '****';
                }
            }
        }
        return sanitized;
    }
    /**
     * Sanitizes response payload by removing sensitive information
     * @param response - Original response payload
     * @returns Sanitized response safe for storage
     */
    sanitizeResponsePayload(response) {
        const sanitized = JSON.parse(JSON.stringify(response));
        // Remove or mask sensitive fields if any
        // For WaaFi responses, most fields are safe to store
        // But we can add additional sanitization if needed
        return sanitized;
    }
    /**
     * Stores raw gateway response for audit and debugging
     * @param paymentId - Payment ID
     * @param operation - Type of operation performed
     * @param requestPayload - Original request payload
     * @param response - Gateway response
     * @param httpStatus - HTTP status code
     * @param processingTime - Response time in milliseconds
     * @param error - Error details if operation failed
     */
    async storeGatewayResponse(paymentId, operation, requestPayload, response, httpStatus, processingTime, error, options) {
        let session = options?.session ?? null;
        try {
            const gatewayResponse = {
                provider: 'WAAFI',
                operation,
                timestamp: new Date(),
                requestId: requestPayload?.requestId,
                responseCode: response?.responseCode,
                responseMessage: response?.responseMsg,
                rawRequest: this.sanitizeRequestPayload(requestPayload),
                rawResponse: response ? this.sanitizeResponsePayload(response) : undefined,
                httpStatus,
                processingTime,
                errorDetails: error,
                metadata: {
                    apiVersion: requestPayload?.schemaVersion,
                    channelName: requestPayload?.channelName,
                    serviceName: requestPayload?.serviceName,
                },
            };
            await index_1.Payment.findByIdAndUpdate(paymentId, {
                $push: {
                    gatewayResponses: gatewayResponse,
                },
            }, { new: true, session });
            logger_1.default.info('Gateway response stored successfully', {
                paymentId,
                operation,
                provider: 'WAAFI',
                responseCode: response?.responseCode,
                httpStatus,
                processingTime,
                timestamp: new Date().toISOString(),
            });
        }
        catch (error) {
            logger_1.default.error('Failed to store gateway response', {
                paymentId,
                operation,
                error: error.message,
                timestamp: new Date().toISOString(),
            });
            // Don't throw error here to avoid breaking the main payment flow
        }
    }
    /**
     * Initiates a preauthorization payment with WaaFi
     * @param mobile - Customer's mobile number
     * @param amount - Payment amount
     * @param userId - Customer's user ID
     * @param orderId - Order ID for the payment
     * @param deliveryDetails - Optional delivery information
     * @returns Payment initiation result with cashier URL and preauth code
     * @throws {ValidationError} When input validation fails
     * @throws {WaaFiPaymentFailed} When WaaFi API returns error
     * @throws {InternalServerError} When payment record operations fail
     */
    async initiatePreauthorization(paymentId, mobile, amount, deliveryDetails) {
        const session = await mongoose_1.default.startSession();
        try {
            session.startTransaction();
            // Validate payment exists
            const payment = await index_1.Payment.findById(paymentId);
            if (!payment) {
                throw new app_errors_1.NotFoundError('Payment not found', {
                    code: 'PAYMENT_NOT_FOUND',
                    details: { paymentId },
                });
            }
            // Validate payment status
            if (payment.status !== enums_1.PaymentStatus.PENDING) {
                throw new app_errors_1.BadRequestError('Payment must be in PENDING state', {
                    code: 'INVALID_PAYMENT_STATUS',
                    details: {
                        currentStatus: payment.status,
                        requiredStatus: enums_1.PaymentStatus.PENDING,
                    },
                });
            }
            // Input validation
            const cleanedMobile = this.validateMobileNumber(mobile);
            this.validateAmount(amount);
            logger_1.default.info('Initiating payment preauthorization', {
                paymentId: payment._id.toString(),
                amount,
                mobile: cleanedMobile,
                timestamp: new Date().toISOString(),
            });
            // Prepare WaaFi API payload
            const paymentPayload = {
                schemaVersion: '1.0',
                requestId: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
                timestamp: new Date().toISOString(),
                channelName: 'WEB',
                serviceName: PaymentServiceName.PREAUTHORIZE,
                serviceParams: {
                    merchantUid: this.paymentMerchantUid,
                    apiUserId: this.paymentApiUserId,
                    apiKey: this.paymentApiKey,
                    paymentMethod: 'MWALLET_ACCOUNT',
                    payerInfo: {
                        accountNo: cleanedMobile,
                    },
                    transactionInfo: {
                        referenceId: payment._id.toString(),
                        invoiceId: payment.orderId.toString(),
                        amount: amount.toString(),
                        currency: 'USD',
                        description: `Gas delivery order: ${payment.orderId}`,
                    },
                },
            };
            // Call WaaFi API and measure response time
            const startTime = Date.now();
            let response;
            let httpStatus;
            let gatewayError = null;
            // if the transaction not started or not running start
            try {
                response = await this.axiosInstance.post(this.paymentApiUrl, paymentPayload);
                httpStatus = response.status;
            }
            catch (error) {
                const processingTime = Date.now() - startTime;
                httpStatus = error.response?.status || 0;
                gatewayError = {
                    errorCode: error.code || 'UNKNOWN_ERROR',
                    errorMessage: error.message,
                    stackTrace: error.stack,
                };
                // Store failed gateway response
                await this.storeGatewayResponse(payment._id.toString(), 'PREAUTHORIZE', paymentPayload, error.response?.data, httpStatus, processingTime, gatewayError, { session });
                throw error;
            }
            const processingTime = Date.now() - startTime;
            // Store successful gateway response
            await this.storeGatewayResponse(payment._id.toString(), 'PREAUTHORIZE', paymentPayload, response.data, httpStatus, processingTime, undefined, { session });
            // Validate API response
            this.validateWaaFiResponse(response.data, {
                paymentId: payment._id.toString(),
                orderId: payment.orderId.toString(),
                amount,
            });
            // Update payment with transaction details
            const updateData = {
                transactionId: response.data.params.transactionId,
                preauthCode: response.data.params.preauthCode,
                status: enums_1.PaymentStatus.PREAUTHORIZED,
                currency: response.data.params.currency,
                metadata: {
                    ...payment.metadata,
                    deliveryDetails,
                    preauthExpiry: new Date(Date.now() + PAYMENT_CONSTANTS.PREAUTH_EXPIRY_HOURS * 60 * 60 * 1000),
                },
            };
            const updatedPayment = await index_1.Payment.findByIdAndUpdate(payment._id, updateData, {
                new: true,
                runValidators: true,
                session,
            });
            if (!updatedPayment) {
                throw new app_errors_1.InternalServerError('Failed to update payment record');
            }
            await session.commitTransaction();
            return {
                payment: updatedPayment,
                cashierUrl: response.data.params.cashierURL,
                preauthCode: response.data.params.preauthCode,
            };
        }
        catch (error) {
            logger_1.default.error('Payment preauthorization failed', {
                paymentId,
                error: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString(),
            });
            try {
                await session.abortTransaction();
                logger_1.default.info('Payment transaction aborted successfully', {
                    paymentId,
                    timestamp: new Date().toISOString(),
                });
            }
            catch (abortError) {
                logger_1.default.error('Failed to abort payment transaction', {
                    error: abortError.message,
                    originalError: error.message,
                    timestamp: new Date().toISOString(),
                });
            }
            // CRITICAL FIX: Always re-throw the original error
            throw error;
        }
        finally {
            session.endSession();
        }
    }
    // cancel preautorization
    async cancelPreauthorization(paymentId) {
        try {
            const payment = await index_1.Payment.findById(paymentId);
            if (!payment) {
                throw new app_errors_1.NotFoundError('Payment not found', {
                    code: 'PAYMENT_NOT_FOUND',
                    details: {
                        paymentId,
                    },
                });
            }
            if (payment.status !== enums_1.PaymentStatus.PREAUTHORIZED) {
                throw new app_errors_1.PaymentError(`Payment is not preauthorized, status is ${payment.status}`, 400, 'PAYMENT_NOT_PREAUTHORIZED', {
                    paymentId: payment._id.toString(),
                    orderId: payment.orderId.toString(),
                    details: {
                        currentStatus: payment.status,
                    },
                });
            }
            if (payment.metadata?.preauthExpiry && payment.metadata.preauthExpiry < new Date()) {
                throw new app_errors_1.WaaFiPreauthExpired('Preauthorization expired', {
                    paymentId: payment._id.toString(),
                    orderId: payment.orderId.toString(),
                    expiryDate: payment.metadata.preauthExpiry,
                });
            }
            let user = await index_1.User.findById(payment.userId);
            if (!user) {
                // throw new Error('User not found');
                throw new app_errors_1.NotFoundError('User not found', {
                    code: 'USER_NOT_FOUND',
                    details: {
                        userId: payment.userId.toString(),
                    },
                });
            }
            const paymentPayload = {
                schemaVersion: '1.0',
                requestId: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
                timestamp: new Date().toISOString(),
                channelName: 'WEB',
                serviceName: PaymentServiceName.PREAUTHORIZE_CANCEL,
                serviceParams: {
                    merchantUid: this.paymentMerchantUid,
                    apiUserId: this.paymentApiUserId,
                    apiKey: this.paymentApiKey,
                    paymentMethod: 'MWALLET_ACCOUNT',
                    transactionId: payment.transactionId,
                    referenceId: payment.orderId.toString(),
                    description: ` Gas delivery order : ${payment.orderId} and mobile number ${payment.userId} `,
                    payerInfo: {
                        accountNo: user.phone,
                    },
                },
            };
            // Call WaaFi API and measure response time
            const startTime = Date.now();
            let response;
            let httpStatus;
            let gatewayError = null;
            try {
                response = await this.axiosInstance.post(this.paymentApiUrl, paymentPayload);
                httpStatus = response.status;
            }
            catch (error) {
                const processingTime = Date.now() - startTime;
                httpStatus = error.response?.status || 0;
                gatewayError = {
                    errorCode: error.code || 'UNKNOWN_ERROR',
                    errorMessage: error.message,
                    stackTrace: error.stack,
                };
                // Store failed gateway response
                await this.storeGatewayResponse(payment._id.toString(), 'CANCEL', paymentPayload, error.response?.data, httpStatus, processingTime, gatewayError);
                throw error;
            }
            const processingTime = Date.now() - startTime;
            // Store successful gateway response
            await this.storeGatewayResponse(payment._id.toString(), 'CANCEL', paymentPayload, response.data, httpStatus, processingTime);
            if (response.data.responseCode !== '2001') {
                throw new app_errors_1.WaaFiPaymentFailed(response.data.responseMsg, {
                    paymentId: payment._id.toString(),
                    orderId: payment.orderId.toString(),
                    amount: payment.amount,
                    details: response.data,
                });
            }
            const updateData = {
                status: enums_1.PaymentStatus.CANCELLED,
                cancelledAt: new Date(),
                metadata: {
                    ...payment.metadata,
                    cancellationReason: 'Preauthorization cancelled',
                },
            };
            const updatedPayment = await index_1.Payment.findByIdAndUpdate(payment._id, updateData, {
                new: true,
            });
            if (!updatedPayment) {
                throw new Error('Failed to update payment record');
            }
            return updatedPayment;
        }
        catch (error) {
            console.error('Cancel preauthorization error:', error);
            throw error;
        }
    }
    async capturePreauthorizedPayment(paymentId) {
        try {
            const payment = await index_1.Payment.findById(paymentId);
            if (!payment) {
                throw new app_errors_1.NotFoundError('Payment not found', {
                    code: 'PAYMENT_NOT_FOUND',
                    details: {
                        paymentId,
                    },
                });
            }
            if (payment.status !== enums_1.PaymentStatus.PREAUTHORIZED) {
                // throw new Error('Payment is not preauthorized');
                throw new app_errors_1.PaymentError(`Payment is not preauthorized, status is ${payment.status}`, 400, 'PAYMENT_NOT_PREAUTHORIZED', {
                    paymentId: payment._id.toString(),
                    orderId: payment.orderId.toString(),
                    details: {
                        currentStatus: payment.status,
                    },
                });
            }
            if (payment.metadata?.preauthExpiry && payment.metadata.preauthExpiry < new Date()) {
                throw new app_errors_1.WaaFiPreauthExpired('Preauthorization expired', {
                    paymentId: payment._id.toString(),
                    orderId: payment.orderId.toString(),
                    expiryDate: payment.metadata.preauthExpiry,
                });
            }
            let user = await index_1.User.findById(payment.userId);
            if (!user) {
                throw new app_errors_1.NotFoundError('User not found', {
                    code: 'USER_NOT_FOUND',
                    details: {
                        userId: payment.userId.toString(),
                    },
                });
            }
            const paymentPayload = {
                schemaVersion: '1.0',
                requestId: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
                timestamp: new Date().toISOString(),
                channelName: 'WEB',
                serviceName: PaymentServiceName.PREAUTHORIZE_COMMIT,
                serviceParams: {
                    merchantUid: this.paymentMerchantUid,
                    apiUserId: this.paymentApiUserId,
                    apiKey: this.paymentApiKey,
                    paymentMethod: 'MWALLET_ACCOUNT',
                    transactionId: payment.transactionId,
                    referenceId: payment.orderId.toString(),
                    description: `Gas delivery order : ${payment.orderId} and mobile number ${payment.userId} `,
                    payerInfo: {
                        accountNo: user.phone,
                    },
                },
            };
            // Call WaaFi API and measure response time
            const startTime = Date.now();
            let response;
            let httpStatus;
            let gatewayError = null;
            try {
                response = await this.axiosInstance.post(this.paymentApiUrl, paymentPayload);
                httpStatus = response.status;
            }
            catch (error) {
                const processingTime = Date.now() - startTime;
                httpStatus = error.response?.status || 0;
                gatewayError = {
                    errorCode: error.code || 'UNKNOWN_ERROR',
                    errorMessage: error.message,
                    stackTrace: error.stack,
                };
                // Store failed gateway response
                await this.storeGatewayResponse(payment._id.toString(), 'CAPTURE', paymentPayload, error.response?.data, httpStatus, processingTime, gatewayError);
                throw error;
            }
            const processingTime = Date.now() - startTime;
            // Store successful gateway response
            await this.storeGatewayResponse(payment._id.toString(), 'CAPTURE', paymentPayload, response.data, httpStatus, processingTime);
            if (response.data.responseCode !== '2001') {
                throw new app_errors_1.WaaFiPaymentFailed(response.data.responseMsg, {
                    paymentId: payment._id.toString(),
                    orderId: payment.orderId.toString(),
                    amount: payment.amount,
                    details: response.data,
                });
            }
            const updateData = {
                status: enums_1.PaymentStatus.CAPTURED,
                paidAt: new Date(),
                metadata: {
                    ...payment.metadata,
                    finalCapture: true,
                },
            };
            const updatedPayment = await index_1.Payment.findByIdAndUpdate(payment._id, updateData, {
                new: true,
            });
            if (!updatedPayment) {
                throw new app_errors_1.InternalServerError('Failed to update payment record', {
                    code: 'PAYMENT_UPDATE_FAILED',
                    details: {
                        paymentId: payment._id.toString(),
                        orderId: payment.orderId.toString(),
                        amount: payment.amount,
                    },
                });
            }
            return updatedPayment;
        }
        catch (error) {
            console.error('Capture preauthorized payment error:', error);
            throw error;
        }
    }
    /**
     * Retrieves gateway responses for a specific payment
     * @param paymentId - Payment ID
     * @param operation - Optional filter by operation type
     * @param provider - Optional filter by provider
     * @returns Array of gateway responses
     */
    async getGatewayResponses(paymentId, operation, provider) {
        try {
            this.validateObjectId(paymentId, 'paymentId');
            const payment = await index_1.Payment.findById(paymentId).select('gatewayResponses');
            if (!payment) {
                throw new app_errors_1.NotFoundError('Payment not found', {
                    code: 'PAYMENT_NOT_FOUND',
                    details: { paymentId },
                });
            }
            let responses = payment.gatewayResponses || [];
            // Apply filters
            if (operation) {
                responses = responses.filter(response => response.operation === operation);
            }
            if (provider) {
                responses = responses.filter(response => response.provider === provider);
            }
            return responses;
        }
        catch (error) {
            logger_1.default.error('Failed to retrieve gateway responses', {
                paymentId,
                operation,
                provider,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Gets payment analytics including gateway response statistics
     * @param paymentId - Payment ID
     * @returns Payment analytics with gateway response data
     */
    async getPaymentAnalytics(paymentId) {
        try {
            this.validateObjectId(paymentId, 'paymentId');
            const payment = await index_1.Payment.findById(paymentId);
            if (!payment) {
                throw new app_errors_1.NotFoundError('Payment not found', {
                    code: 'PAYMENT_NOT_FOUND',
                    details: { paymentId },
                });
            }
            const responses = payment.gatewayResponses || [];
            const totalInteractions = responses.length;
            // Calculate operation counts
            const operationCounts = {};
            responses.forEach(response => {
                operationCounts[response.operation] = (operationCounts[response.operation] || 0) + 1;
            });
            // Calculate average response time
            const responseTimes = responses
                .filter(response => response.processingTime)
                .map(response => response.processingTime);
            const averageResponseTime = responseTimes.length > 0
                ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
                : 0;
            // Calculate success rate
            const successfulResponses = responses.filter(response => response.responseCode === PAYMENT_CONSTANTS.SUCCESS_RESPONSE_CODE);
            const successRate = totalInteractions > 0 ? (successfulResponses.length / totalInteractions) * 100 : 0;
            // Get last interaction
            const lastInteraction = responses.length > 0 ? responses[responses.length - 1].timestamp : undefined;
            // Count errors
            const errorCount = responses.filter(response => response.errorDetails).length;
            return {
                payment,
                analytics: {
                    totalGatewayInteractions: totalInteractions,
                    operationCounts,
                    averageResponseTime,
                    successRate,
                    lastInteraction,
                    errorCount,
                },
            };
        }
        catch (error) {
            logger_1.default.error('Failed to get payment analytics', {
                paymentId,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Handles webhook/callback responses from payment gateways
     * @param paymentId - Payment ID
     * @param webhookData - Webhook payload from gateway
     * @param provider - Payment provider
     * @returns Updated payment
     */
    async handleWebhookResponse(paymentId, webhookData, provider = 'WAAFI') {
        try {
            this.validateObjectId(paymentId, 'paymentId');
            const payment = await index_1.Payment.findById(paymentId);
            if (!payment) {
                throw new app_errors_1.NotFoundError('Payment not found', {
                    code: 'PAYMENT_NOT_FOUND',
                    details: { paymentId },
                });
            }
            // Store webhook response
            await this.storeGatewayResponse(paymentId, 'WEBHOOK', null, // No request payload for webhooks
            webhookData, 200, // Assume successful webhook receipt
            undefined, // No processing time for webhooks
            undefined);
            logger_1.default.info('Webhook response processed and stored', {
                paymentId,
                provider,
                webhookData: this.sanitizeResponsePayload(webhookData),
                timestamp: new Date().toISOString(),
            });
            return payment;
        }
        catch (error) {
            logger_1.default.error('Failed to handle webhook response', {
                paymentId,
                provider,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Format payment expiry date for user display
     * @param expiryDate - Payment expiry date in UTC
     * @param userId - User ID for timezone context
     * @returns Formatted expiry date string in user's timezone
     */
    formatPaymentExpiryForUser(expiryDate, userId) {
        const timezoneContext = {
            userId: userId,
            role: enums_1.UserRole.CUSTOMER, // Default to customer for payment context
        };
        return timezone_utils_1.timezoneManager.formatDateForUser(expiryDate, timezoneContext, {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            timeZoneName: 'short',
        });
    }
    /**
     * Format payment timestamp for user display
     * @param timestamp - Payment timestamp in UTC
     * @param userId - User ID for timezone context
     * @returns Formatted timestamp string in user's timezone
     */
    formatPaymentTimestampForUser(timestamp, userId) {
        const timezoneContext = {
            userId: userId,
            role: enums_1.UserRole.CUSTOMER, // Default to customer for payment context
        };
        return timezone_utils_1.timezoneManager.formatDateForUser(timestamp, timezoneContext, {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            timeZoneName: 'short',
        });
    }
    /**
     * Get payment with timezone-formatted dates
     * @param paymentId - Payment ID
     * @param userId - User ID for timezone context
     * @returns Payment with formatted dates
     */
    async getPaymentWithFormattedDates(paymentId, userId) {
        try {
            this.validateObjectId(paymentId, 'paymentId');
            const payment = await index_1.Payment.findById(paymentId).lean();
            if (!payment) {
                throw new app_errors_1.NotFoundError('Payment not found', {
                    code: 'PAYMENT_NOT_FOUND',
                    details: { paymentId },
                });
            }
            // Format dates for user timezone
            const formattedPayment = {
                ...payment,
                createdAt: this.formatPaymentTimestampForUser(payment.createdAt, userId),
                updatedAt: this.formatPaymentTimestampForUser(payment.updatedAt, userId),
                formattedExpiryDate: payment.metadata?.preauthExpiry
                    ? this.formatPaymentExpiryForUser(payment.metadata.preauthExpiry, userId)
                    : null,
            };
            logger_1.default.debug('Payment retrieved with formatted dates', {
                paymentId,
                userId,
                originalCreatedAt: payment.createdAt.toISOString(),
                formattedCreatedAt: formattedPayment.createdAt,
                originalExpiryDate: payment.metadata?.preauthExpiry?.toISOString(),
                formattedExpiryDate: formattedPayment.formattedExpiryDate,
            });
            return formattedPayment;
        }
        catch (error) {
            logger_1.default.error('Failed to get payment with formatted dates', {
                paymentId,
                userId,
                error: error.message,
            });
            throw error;
        }
    }
    static getInstance() {
        if (!PaymentService.instance) {
            PaymentService.instance = new PaymentService();
        }
        return PaymentService.instance;
    }
}
exports.paymentService = PaymentService.getInstance();
//# sourceMappingURL=payment.services.js.map