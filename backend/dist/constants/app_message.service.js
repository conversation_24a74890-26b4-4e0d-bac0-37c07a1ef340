"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppMessageService = void 0;
const app_constants_1 = require("./app_constants");
const regex_patterns_1 = require("../utils/regex-patterns");
/**
 * Professional Multilingual Messaging Service for Gas Delivery System
 * Primary Language: Somali (so), Secondary: English (en)
 *
 * Features:
 * - SMS character limit awareness (150 chars)
 * - Role-based messaging
 * - Professional branding
 * - HTML email templates
 * - Extensible for future languages
 * - Message sanitization for clean, professional output
 */
class AppMessageService {
    appName;
    appNameSomali;
    appLink;
    phoneNumber;
    lang;
    includePhoneInFooter;
    recipientRole;
    // Cached regex patterns for performance
    ELLIPSIS_PLACEHOLDER = '___ELLIPSIS___';
    constructor(config = {}) {
        this.appName = config.appName ?? app_constants_1.AppConstants.appName;
        this.appNameSomali = app_constants_1.AppConstants.appNameSomali;
        this.appLink = config.appLink ?? app_constants_1.AppConstants.appLink;
        this.phoneNumber = app_constants_1.AppConstants.phoneNumber;
        this.lang = this.validateLanguage(config.lang ?? app_constants_1.AppConstants.defaultLanguage);
        this.includePhoneInFooter = config.includePhoneInFooter ?? true;
        this.recipientRole = config.recipientRole ?? 'customer';
    }
    /**
     * Validates and returns supported language, defaults to Somali
     */
    validateLanguage(lang) {
        return app_constants_1.AppConstants.supportedLanguages.includes(lang) ? lang : 'so';
    }
    /**
     * Gets the appropriate app name based on language
     */
    get localizedAppName() {
        return this.lang === 'so' ? this.appNameSomali : this.appName;
    }
    /**
     * Generates professional footer with app download link and contact info
     */
    get appFooter() {
        const templates = {
            so: {
                download: `Lasoo dag ${this.localizedAppName}: ${this.appLink}`,
                contact: `Nagala soo xiriir: ${this.phoneNumber}`,
                hours: `Saacadaha shaqada: ${app_constants_1.AppConstants.businessHoursSomali}`,
            },
            en: {
                download: `Download ${this.appName}: ${this.appLink}`,
                contact: `Contact us: ${this.phoneNumber}`,
                hours: `Business hours: ${app_constants_1.AppConstants.businessHours}`,
            },
        };
        const t = templates[this.lang];
        const contact = this.includePhoneInFooter ? `\n${t.contact}` : '';
        return `\n\n${t.download}${contact}`;
    }
    /**
     * Generates company signature based on language
     */
    get companySignature() {
        const signatures = {
            so: `\n\nMahadsanid,\nCompany ${this.localizedAppName}`,
            en: `\n\nThank you,\n${this.appName} Team`,
        };
        return signatures[this.lang];
    }
    /**
     * Truncates message to SMS character limit while preserving important info
     */
    truncateForSMS(message) {
        if (message.length <= app_constants_1.AppConstants.smsCharacterLimit) {
            return message;
        }
        // Keep the most important part and add truncation indicator
        const truncated = message.substring(0, app_constants_1.AppConstants.smsCharacterLimit - 3) + '...';
        return truncated;
    }
    /**
     * Sanitizes message content by removing emojis, normalizing whitespace,
     * and standardizing punctuation for professional SMS delivery
     *
     * @param text - The raw message text to sanitize
     * @returns Clean, professional message text
     */
    sanitizeMessageContent(text) {
        if (!text || typeof text !== 'string') {
            return '';
        }
        let sanitized = text;
        // 1. Replace Unicode dashes (en-dash, em-dash) with standard hyphen
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.unicode.dash, '-');
        // 2. Replace ellipses with three periods
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.unicode.ellipsis, '...');
        // 3. Replace curly quotes with straight quotes
        sanitized = sanitized
            .replace(regex_patterns_1.RegexPatterns.unicode.quotes.double, '"')
            .replace(regex_patterns_1.RegexPatterns.unicode.quotes.single, "'");
        // 4. Remove emojis and symbols (comprehensive emoji removal)
        // Remove common emoji ranges using surrogate pairs and basic ranges
        // Emoticons (U+1F600-U+1F64F)
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.emoji.emoticons, '');
        // Miscellaneous Symbols and Pictographs (U+1F300-U+1F5FF)
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.emoji.miscSymbolsAndPictographs, '');
        // Transport and Map Symbols (U+1F680-U+1F6FF)
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.emoji.transportAndMapSymbols, '');
        // Additional Emoticons (U+1F910-U+1F96B)
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.emoji.additionalEmoticons, '');
        // Miscellaneous Symbols (U+2600-U+26FF)
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.emoji.symbols, '');
        // Dingbats (U+2700-U+27BF)
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.emoji.dingbats, '');
        // Enclosed Alphanumeric Supplement (U+1F100-U+1F1FF)
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.emoji.enclosedAlphanumericSupplement, '');
        // Remove variation selectors and combining characters
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.emoji.variationSelectors, '');
        // Remove any remaining high surrogate pairs that might be emoji
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.emoji.highSurrogatePairs, '');
        // 5. Convert non-breaking spaces to regular spaces (do this early)
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.whitespace.nbsp, ' ');
        // 6. Normalize whitespace and handle newlines properly
        // First, collapse multiple consecutive newlines to double newlines (paragraph breaks)
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.whitespace.tripleNewlines, '\n\n');
        // Remove leading/trailing whitespace from each line
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.whitespace.lineEdgeWhitespace, '');
        // Remove empty lines that are not part of intentional paragraph breaks
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.whitespace.tripleBlankLines, '\n\n');
        // 7. Trim consecutive spaces to single space
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.whitespace.multiSpaces, ' ');
        // 8. Remove redundant punctuation (but preserve legitimate ellipses)
        // Handle redundant commas, exclamation marks, and question marks
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.punctuation.redundantPunctuation, '$1');
        // Handle redundant periods more carefully
        // First, protect legitimate ellipses
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.punctuation.ellipsisLiteral, this.ELLIPSIS_PLACEHOLDER);
        // Remove redundant periods (2 or more consecutive periods that aren't ellipses)
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.punctuation.multiPeriods, '.');
        // Restore ellipses
        sanitized = sanitized.replace(new RegExp(this.ELLIPSIS_PLACEHOLDER, 'g'), '...');
        // 9. Normalize colon spacing (no space before, one space after)
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.punctuation.colonSpacing, ': ');
        // 10. Ensure proper spacing after punctuation (but protect ellipses)
        // First protect ellipses again
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.punctuation.ellipsisLiteral, this.ELLIPSIS_PLACEHOLDER);
        // Add spacing after punctuation
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.punctuation.punctuationNoSpaceAfter, '$1 $2');
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.punctuation.punctuationWithExtraSpaces, '$1 ');
        // Restore ellipses
        sanitized = sanitized.replace(new RegExp(this.ELLIPSIS_PLACEHOLDER, 'g'), '...');
        // 11. Handle newlines based on content structure
        // Improved structured data detection - look for specific key-value patterns like "Order ID: 12345"
        // Only match lines that start with specific structured data keywords
        const hasStructuredData = regex_patterns_1.RegexPatterns.content.structuredDataLineStart.test(sanitized);
        const hasMultipleParagraphs = regex_patterns_1.RegexPatterns.content.doubleNewlineDetect.test(sanitized);
        // Check if content has narrative words
        const hasNarrativeWords = regex_patterns_1.RegexPatterns.content.narrativeWords.test(sanitized);
        // Check if content looks like simple line list or structured data
        const isSimpleLineList = regex_patterns_1.RegexPatterns.content.singleNewlineDetect.test(sanitized) && !hasNarrativeWords;
        // Check if content is narrative (has common narrative words)
        const isNarrativeContent = hasMultipleParagraphs && hasNarrativeWords;
        const isSingleLineNarrative = !hasMultipleParagraphs && hasNarrativeWords && !regex_patterns_1.RegexPatterns.content.singleNewlineDetect.test(sanitized);
        if (hasStructuredData || isSimpleLineList) {
            // Preserve line structure for structured data or simple lists
            // Just collapse multiple newlines to single newlines
            sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.content.doubleOrMoreNewlines, '\n');
        }
        else if (isNarrativeContent) {
            // Has paragraph structure - flatten to single line for narrative content
            // Convert ellipses to periods in narrative content for SMS clarity
            sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.punctuation.ellipsisLiteral, '.');
            // Convert double newlines to single, then join lines with periods
            sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.content.doubleNewlinesGlobal, '\n');
            // Join lines with periods if they don't end with punctuation
            sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.content.lineJoinWithoutPunctuation, '$1. ');
            // Remove remaining newlines
            sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.content.singleNewlineGlobal, ' ');
        }
        else if (isSingleLineNarrative) {
            // Single line narrative content - only simplify ellipses if they appear redundant
            // (e.g., before other sentences with different punctuation)
            sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.content.threeOrMorePeriods, '...'); // First normalize multiple periods to ellipses
            sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.content.ellipsisBeforeNewSentence, '. $1'); // Remove ellipses before new sentences
        }
        else {
            // Simple line breaks without paragraph structure - preserve them
            // Just collapse multiple consecutive newlines
            sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.content.doubleOrMoreNewlines, '\n');
        }
        // 12. Final cleanup
        // Fix any double spaces that might have been created (but preserve newlines)
        sanitized = sanitized.replace(regex_patterns_1.RegexPatterns.whitespace.multiSpaces, ' ');
        // Trim the entire string
        sanitized = sanitized.trim();
        return sanitized;
    }
    // =============================================================================
    // OTP VERIFICATION MESSAGES
    // =============================================================================
    /**
     * Generates OTP verification message
     */
    otpMessage(otp, expiresIn = app_constants_1.AppConstants.otpExpiryMinutes) {
        const templates = {
            so: {
                subject: `${this.localizedAppName} - Koodka Xaqiijinta`,
                body: `Koodkaagu waa: ${otp}\nWuxuu dhacayaa ${expiresIn} daqiiqo.\n\nHa la wadaagin qof kale.`,
                warning: 'Ha la wadaagin qof kale.',
            },
            en: {
                subject: `${this.appName} - Verification Code`,
                body: `Your code is: ${otp}\nExpires in ${expiresIn} minute${expiresIn !== 1 ? 's' : ''}.\n\nDo not share this code.`,
                warning: 'Do not share this code.',
            },
        };
        const t = templates[this.lang];
        const message = `${t.subject}\n${t.body}${this.companySignature}`;
        const sanitizedMessage = this.sanitizeMessageContent(message);
        return this.truncateForSMS(sanitizedMessage);
    }
    // =============================================================================
    // ORDER MANAGEMENT MESSAGES
    // =============================================================================
    /**
     * Order confirmation message
     */
    orderConfirmed(orderId) {
        const templates = {
            so: {
                subject: `${this.localizedAppName} - Dalabka la Xaqiijiyay`,
                body: `Lambarka dalabka: ${orderId}\nWaan kula soo xiriiri doonaa marka uu diyaar yahay.\n\nWaqtiga Deliveryta: ${app_constants_1.AppConstants.standardDeliveryTimeSomali}`,
            },
            en: {
                subject: `${this.appName} - Order Confirmed`,
                body: `Order ID: ${orderId}\nWe will contact you when ready.\n\nDelivery time: ${app_constants_1.AppConstants.standardDeliveryTime}`,
            },
        };
        const t = templates[this.lang];
        const message = `${t.subject}\n${t.body}${this.appFooter}`;
        const sanitizedMessage = this.sanitizeMessageContent(message);
        return this.truncateForSMS(sanitizedMessage);
    }
    /**
     * Order delivered confirmation message
     */
    orderDelivered(orderId) {
        const templates = {
            so: {
                subject: `${this.localizedAppName} - Dalabka la Gaarsiiyay!`,
                body: `Lambarka dalabka: ${orderId}\n\nMahadsanid ganacsigeenna.`,
                help: `Haddii aad caawimaad u baahan tahay, naga soo wac ${this.phoneNumber}.`,
            },
            en: {
                subject: `${this.appName} - Order Delivered!`,
                body: `Order ID: ${orderId}\n\nThank you for your business.`,
                help: `If you need help, call us at ${this.phoneNumber}.`,
            },
        };
        const t = templates[this.lang];
        const helpInfo = this.includePhoneInFooter ? `\n${t.help}` : '';
        const message = `${t.subject}\n${t.body}${helpInfo}${this.companySignature}`;
        const sanitizedMessage = this.sanitizeMessageContent(message);
        return this.truncateForSMS(sanitizedMessage);
    }
    /**
     * Order cancelled message
     */
    orderCancelled(orderId, reason) {
        const templates = {
            so: {
                subject: `${this.localizedAppName} - Dalabka waala Joojiyay`,
                body: `Lambarka dalabka: ${orderId}\nSababta: ${reason || 'Sabab lama bixin'}\n\nWaan ka xunahay dhibaatada.`,
            },
            en: {
                subject: `${this.appName} - Order Cancelled`,
                body: `Order ID: ${orderId}\nReason: ${reason || 'Not specified'}\n\nWe apologize for the inconvenience.`,
            },
        };
        const t = templates[this.lang];
        const message = `${t.subject}\n${t.body}${this.companySignature}`;
        const sanitizedMessage = this.sanitizeMessageContent(message);
        return this.truncateForSMS(sanitizedMessage);
    }
    // =============================================================================
    // DELIVERY MANAGEMENT MESSAGES
    // =============================================================================
    /**
     * Delivery assignment message for agents
     */
    deliveryAssigned(orderId, customerName, address) {
        const templates = {
            so: {
                subject: `${this.localizedAppName} - Delivery Cusub`,
                body: `Dalabka: ${orderId}\nMacmiilka: ${customerName}\nCiwaanka: ${address}\n\nKu dhamaystir ${app_constants_1.AppConstants.maxDeliveryTimeHours} saacadood.`,
            },
            en: {
                subject: `${this.appName} - New Delivery`,
                body: `Order: ${orderId}\nCustomer: ${customerName}\nAddress: ${address}\n\nComplete within ${app_constants_1.AppConstants.maxDeliveryTimeHours} hours.`,
            },
        };
        const t = templates[this.lang];
        const message = `${t.subject}\n${t.body}${this.companySignature}`;
        const sanitizedMessage = this.sanitizeMessageContent(message);
        return this.truncateForSMS(sanitizedMessage);
    }
    /**
     * Delivery started notification for customers
     */
    deliveryStarted(orderId, agentName, agentPhone) {
        const templates = {
            so: {
                subject: `${this.localizedAppName} - Deliverytu Waa Bilaabatay`,
                body: `Dalabka: ${orderId}\nGaarsiiyaha: ${agentName}\nTeleefanka: ${agentPhone}\n\nWuu soo socda!`,
            },
            en: {
                subject: `${this.appName} - Delivery Started`,
                body: `Order: ${orderId}\nAgent: ${agentName}\nPhone: ${agentPhone}\n\nOn the way!`,
            },
        };
        const t = templates[this.lang];
        const message = `${t.subject}\n${t.body}`;
        const sanitizedMessage = this.sanitizeMessageContent(message);
        return this.truncateForSMS(sanitizedMessage);
    }
    // =============================================================================
    // INVENTORY MANAGEMENT MESSAGES
    // =============================================================================
    /**
     * Low stock alert SMS for admins/supervisors
     */
    lowStockAlert(item, quantity) {
        const templates = {
            so: {
                subject: `${this.localizedAppName} - Digniin Alaab Yar`,
                body: `${item}: Kaliya ${quantity} ayaa haray!\n\nFadlan dib u buuxi si aad u ilaaliso.`,
            },
            en: {
                subject: `${this.appName} - Low Stock Alert`,
                body: `${item}: Only ${quantity} remaining!\n\nPlease restock to avoid shortages.`,
            },
        };
        const t = templates[this.lang];
        const message = `${t.subject}\n${t.body}`;
        const sanitizedMessage = this.sanitizeMessageContent(message);
        return this.truncateForSMS(sanitizedMessage);
    }
    /**
     * Out of stock alert SMS for admins
     */
    outOfStockAlert(item) {
        const templates = {
            so: {
                subject: `${this.localizedAppName} - Alaabta Dhammaatay`,
                body: `${item} ayaa dhammaatay!\n\nDegdeg ah u buuxi.`,
            },
            en: {
                subject: `${this.appName} - Out of Stock`,
                body: `${item} is out of stock!\n\nRestock immediately.`,
            },
        };
        const t = templates[this.lang];
        const message = `${t.subject}\n${t.body}`;
        const sanitizedMessage = this.sanitizeMessageContent(message);
        return this.truncateForSMS(sanitizedMessage);
    }
    /**
     * Professional HTML email for low stock alerts
     */
    lowStockEmail(item, quantity) {
        const templates = {
            so: {
                subject: `[TALLAABO LAGAMA MAARMAAN AH] ${this.localizedAppName} - ${item} Waa Yar Yahay`,
                actionRequired: 'Tallaabo Lagama Maarmaan Ah:',
                restockItem: 'Alaabtan dib u buuxi',
                updateSystem: 'Nidaamka cusbooneysii marka la buuxiyo',
                currentQty: 'Tirada Hadda Jirta:',
                itemLabel: 'Alaabta:',
                signature: `Mahadsanid,<br>${this.localizedAppName} Nidaamka Kaydka`,
            },
            en: {
                subject: `[ACTION REQUIRED] ${this.appName} - ${item} Running Low`,
                actionRequired: 'Action Required:',
                restockItem: 'Restock this item',
                updateSystem: 'Update system when restocked',
                currentQty: 'Current Quantity:',
                itemLabel: 'Item:',
                signature: `Thank you,<br>${this.appName} Inventory System`,
            },
        };
        const t = templates[this.lang];
        return {
            subject: t.subject,
            body: `
        <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <!-- Header -->
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center;">
            <h1 style="margin: 0; font-size: 24px; font-weight: 600;">${this.localizedAppName}</h1>
            <p style="margin: 5px 0 0 0; opacity: 0.9; font-size: 14px;">${app_constants_1.AppConstants.companyNameSomali}</p>
          </div>

          <!-- Alert Content -->
          <div style="padding: 30px;">
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin-bottom: 25px;">
              <h2 style="color: #856404; margin: 0 0 10px 0; font-size: 18px; display: flex; align-items: center;">
                ⚠️ ${t.subject.includes('Digniin') ? 'Digniin Alaab Yar' : 'Low Stock Alert'}
              </h2>
            </div>

            <!-- Item Details -->
            <div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 25px; border-left: 4px solid #667eea;">
              <p style="margin: 0 0 10px 0; font-size: 16px;"><strong>${t.itemLabel}</strong> ${item}</p>
              <p style="margin: 0; font-size: 16px;"><strong>${t.currentQty}</strong> <span style="color: #dc3545; font-weight: bold; font-size: 18px;">${quantity}</span></p>
            </div>

            <!-- Action Items -->
            <div style="margin-bottom: 25px;">
              <p style="font-weight: 600; color: #495057; margin-bottom: 15px; font-size: 16px;">${t.actionRequired}</p>
              <ol style="color: #6c757d; line-height: 1.6; padding-left: 20px;">
                <li style="margin-bottom: 8px;">${t.restockItem}</li>
                <li>${t.updateSystem}</li>
              </ol>
            </div>

            <!-- Contact Info -->
            <div style="background: #e9ecef; padding: 15px; border-radius: 6px; text-align: center; margin-bottom: 20px;">
              <p style="margin: 0; color: #6c757d; font-size: 14px;">
                📞 ${this.phoneNumber} | 📧 ${app_constants_1.AppConstants.supportEmail}
              </p>
            </div>

            <!-- Signature -->
            <p style="margin: 0; color: #6c757d; font-size: 14px;">${t.signature}</p>
          </div>

          <!-- Footer -->
          <div style="background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; text-align: center; border-top: 1px solid #dee2e6;">
            <p style="margin: 0; color: #6c757d; font-size: 12px;">
              © ${new Date().getFullYear()} ${app_constants_1.AppConstants.companyName}. All rights reserved.
            </p>
          </div>
        </div>
      `,
        };
    }
    // =============================================================================
    // ROLE-SPECIFIC MESSAGES
    // =============================================================================
    /**
     * Welcome message for new users based on role
     */
    welcomeMessage(userName, userRole) {
        const templates = {
            so: {
                customer: `Ku soo dhawoow ${this.localizedAppName}!\nMagacaaga: ${userName}\n\nWaxaad hadda awooda  inaad dalabto gaaska adigoo guriga jooga.`,
                agent: `Ku soo dhawoow ${this.localizedAppName}!\nMagacaaga: ${userName}\n\nDiyaar u noqo dalabka cusub.`,
                admin: `Ku soo dhawoow ${this.localizedAppName}!\nMagacaaga: ${userName}\n\nWaxaad maamuli kartaa dhammaan nidaamka.`,
                supervisor: `Ku soo dhawoow ${this.localizedAppName}!\nMagacaaga: ${userName}\n\nWaxaad la socon kartaa hawlaha maanta.`,
            },
            en: {
                customer: `Welcome to ${this.appName}!\nName: ${userName}\n\nYou can now order gas for your home.`,
                agent: `Welcome to the delivery team!\nName: ${userName}\n\nGet ready for new orders.`,
                admin: `Welcome to admin panel!\nName: ${userName}\n\nYou can manage the entire system.`,
                supervisor: `Welcome to supervision!\nName: ${userName}\n\nYou can monitor today's operations.`,
            },
        };
        const t = templates[this.lang];
        const roleMessage = t[userRole] || t.customer;
        const message = `${roleMessage}${this.companySignature}`;
        const sanitizedMessage = this.sanitizeMessageContent(message);
        return this.truncateForSMS(sanitizedMessage);
    }
    /**
     * Payment confirmation message
     */
    paymentConfirmed(orderId, amount) {
        const templates = {
            so: {
                subject: `${this.localizedAppName} - Lacagta la Bixiyay`,
                body: `Dalabka: ${orderId}\nLacagta: $${amount}\n\nLacagtu si guul leh ayaa loo bixiyay.`,
            },
            en: {
                subject: `${this.appName} - Payment Confirmed`,
                body: `Order: ${orderId}\nAmount: $${amount}\n\nPayment processed successfully.`,
            },
        };
        const t = templates[this.lang];
        const message = `${t.subject}\n${t.body}`;
        const sanitizedMessage = this.sanitizeMessageContent(message);
        return this.truncateForSMS(sanitizedMessage);
    }
    /**
     * Payment failed message
     */
    paymentFailed(orderId, reason) {
        const templates = {
            so: {
                subject: `${this.localizedAppName} - Lacagta Lama Bixin`,
                body: `Dalabka: ${orderId}\nSababta: ${reason}\n\nFadlan mar kale isku day.`,
            },
            en: {
                subject: `${this.appName} - Payment Failed`,
                body: `Order: ${orderId}\nReason: ${reason}\n\nPlease try again.`,
            },
        };
        const t = templates[this.lang];
        const message = `${t.subject}\n${t.body}${this.companySignature}`;
        const sanitizedMessage = this.sanitizeMessageContent(message);
        return this.truncateForSMS(sanitizedMessage);
    }
    // =============================================================================
    // UTILITY METHODS
    // =============================================================================
    /**
     * Generic notification message
     */
    genericNotification(title, message) {
        const templates = {
            so: {
                prefix: `${this.localizedAppName}`,
            },
            en: {
                prefix: `${this.appName}`,
            },
        };
        const t = templates[this.lang];
        const fullMessage = `${t.prefix} - ${title}\n${message}`;
        const sanitizedMessage = this.sanitizeMessageContent(fullMessage);
        return this.truncateForSMS(sanitizedMessage);
    }
    /**
     * Emergency alert message
     */
    emergencyAlert(message) {
        const templates = {
            so: {
                prefix: `DEGDEG - ${this.localizedAppName}`,
            },
            en: {
                prefix: `URGENT - ${this.appName}`,
            },
        };
        const t = templates[this.lang];
        const fullMessage = `${t.prefix}\n${message}\n\n${this.phoneNumber}`;
        const sanitizedMessage = this.sanitizeMessageContent(fullMessage);
        return this.truncateForSMS(sanitizedMessage);
    }
    /**
     * System maintenance notification
     */
    maintenanceNotification(startTime, duration) {
        const templates = {
            so: {
                subject: `${this.localizedAppName} - Dayactir Nidaam`,
                body: `Waqtiga: ${startTime}\nMudada: ${duration}\n\nWaan ka xunahay dhibaatada.`,
            },
            en: {
                subject: `${this.appName} - System Maintenance`,
                body: `Time: ${startTime}\nDuration: ${duration}\n\nWe apologize for the inconvenience.`,
            },
        };
        const t = templates[this.lang];
        const message = `${t.subject}\n${t.body}${this.companySignature}`;
        const sanitizedMessage = this.sanitizeMessageContent(message);
        return this.truncateForSMS(sanitizedMessage);
    }
    /**
     * Get message character count for SMS planning
     */
    getMessageLength(message) {
        return message.length;
    }
    /**
     * Check if message exceeds SMS limit
     */
    exceedsSMSLimit(message) {
        return message.length > app_constants_1.AppConstants.smsCharacterLimit;
    }
    /**
     * Get current language setting
     */
    getCurrentLanguage() {
        return this.lang;
    }
    /**
     * Get supported languages
     */
    static getSupportedLanguages() {
        return app_constants_1.AppConstants.supportedLanguages;
    }
}
exports.AppMessageService = AppMessageService;
//# sourceMappingURL=app_message.service.js.map