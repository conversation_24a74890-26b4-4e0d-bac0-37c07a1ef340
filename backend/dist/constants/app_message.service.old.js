"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppMessageService = void 0;
const app_constants_1 = require("./app_constants");
class AppMessageService {
    appName;
    appLink;
    phoneNumber;
    lang;
    includePhoneInFooter;
    constructor(config = {}) {
        this.appName = config.appName ?? app_constants_1.AppConstants.appName;
        this.appLink = config.appLink ?? app_constants_1.AppConstants.appLink;
        this.phoneNumber = app_constants_1.AppConstants.phoneNumber;
        this.lang = config.lang ?? 'en'; // Force to English (default or override)
        this.includePhoneInFooter = config.includePhoneInFooter ?? true;
    }
    get appFooter() {
        const contact = this.includePhoneInFooter ? `\nContact us: ${this.phoneNumber}` : '';
        return `\n\nDownload the ${this.appName} app: ${this.appLink}${contact}`;
    }
    get companySignature() {
        return `\n\nThank you,\n${this.appName} Team`;
    }
    otpMessage(otp, expiresIn = 5) {
        return `🔐 ${this.appName} - Your Verification Code\nYour code is: ${otp}\nExpires in ${expiresIn} minute${expiresIn !== 1 ? 's' : ''}.\n\n⚠️ Do not share this code with anyone.${this.companySignature}`;
    }
    orderPlaced(orderId) {
        return `✅ ${this.appName} - Order Confirmed\nOrder ID: ${orderId}\nWe will contact you when it’s ready.\n\nDelivery time: 24-48 hours${this.appFooter}`;
    }
    orderDelivered(orderId) {
        const contactInfo = this.includePhoneInFooter
            ? `\nIf you need any help, please call us at ${this.phoneNumber}.`
            : '';
        return `🚚 ${this.appName} - Order Delivered!\nOrder ID: ${orderId}\n\nThank you for your business.${contactInfo}${this.companySignature}${this.appFooter}`;
    }
    lowStockAlert(item, quantity) {
        return `⚠️ ${this.appName} - Low Stock Alert\n${item}: Only ${quantity} remaining!\n\nPLEASE restock to avoid shortages.`;
    }
    lowStockEmail(item, quantity) {
        return {
            subject: `[ACTION REQUIRED] ${this.appName} - ${item} Running Low`,
            body: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #d9534f;">⚠️ Low Stock Alert</h2>
          <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
            <p><strong>Item:</strong> ${item}</p>
            <p><strong>Current Quantity:</strong> <span style="color: #d9534f; font-weight: bold;">${quantity}</span></p>
          </div>
          <p style="font-weight: bold;">Action Required:</p>
          <ol>
            <li>Restock this item</li>
            <li>Update system when restocked</li>
          </ol>
          <p>Thank you,<br>${this.appName} Inventory System</p>
        </div>
      `,
        };
    }
    deliveryAssignment(orderId, customerName, address) {
        return `📦 ${this.appName} - New Delivery\nOrder ID: ${orderId}\nCustomer: ${customerName}\nAddress: ${address}\n\nPlease complete the delivery within 2 hours.${this.companySignature}`;
    }
}
exports.AppMessageService = AppMessageService;
//# sourceMappingURL=app_message.service.old.js.map