"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppConstants = void 0;
/**
 * Application Constants for Gas Delivery System
 * Contains core configuration values used throughout the application
 */
exports.AppConstants = Object.freeze({
    // Application Information
    appVersion: '1.0.0',
    appName: 'Ciribey Gas Delivery',
    appNameSomali: 'Ciribey Gas Delivery',
    // Contact Information
    phoneNumber: '+252613656021',
    whatsappNumber: '+252613656021',
    email: '<EMAIL>',
    supportEmail: '<EMAIL>',
    // App Store Links
    appLink: 'https://play.google.com/store/apps/details?id=com.ciribey.gasdelivery',
    appStoreLink: 'https://apps.apple.com/app/ciribey-gas-delivery/id123456789',
    // Business Information
    companyName: 'Ciribey Gas Delivery Services',
    companyNameSomali: 'Ciribey Gas Delivery Services',
    businessHours: '6:00 AM - 8:00 PM',
    businessHoursSomali: '6:00 subaxnimo - 8:00 fiidnimo',
    // Messaging Configuration
    smsCharacterLimit: 499, // SMS character limit
    otpCharacterLimit: 200, // OTP message limit  
    defaultLanguage: 'so', // Somali as default
    supportedLanguages: ['so', 'en'], // Somali & English
    otpExpiryMinutes: 5, // OTP expires in 5 minutes
    maxOtpAttempts: 3, // Max OTP attempts allowed
    // Delivery Information
    standardDeliveryTime: '30-45 minutes',
    standardDeliveryTimeSomali: '30-45 daqiiqo',
    emergencyDeliveryTime: '10-20 minutes',
    emergencyDeliveryTimeSomali: '10-20 daqiiqo',
    maxDeliveryTimeHours: 1,
    // Website and Social Media
    website: 'https://www.cirbeygasdelivery.com',
    facebookPage: 'https://facebook.com/cirbeygasdelivery',
    instagramPage: 'https://instagram.com/cirbeygasdelivery',
});
//# sourceMappingURL=app_constants.js.map