Test: Basic Short Message
Message: HOSPITAL"
}
Message Length: 11 characters
Timestamp: Wed Aug  6 13:48:01 EAT 2025
---
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100   604  100   478  100   126   1769    466 --:--:-- --:--:-- --:--:--  2237
{"ResponseCode":"200","ResponseMessage":"SUCCESS!.","Data":{"MessageID":"51a15323-4d88-4bf8-b2f4-7f2e5229f81d","Description":"The message is successfully sent!!","DeliveryCallBack":"https://smsapi.hormuud.com/api/Delivery/GetDelivery?MessageId=51a15323-4d88-4bf8-b2f4-7f2e5229f81d","Details":{"TextLength":18,"TotalCharacters":18,"TotalSMS":1,"IsGMS7Bit":true,"ContainsUnicode":false,"IsMultipart":false,"ExtensionSet":[],"UnicodeSet":[],"MessageParts":["Basic test message"]}}}

Response Time: 0.270127s
HTTP Status: 200
Content Length: 478 bytes
========================================
Test: Minimal Required Fields
Message: HOSPITAL"
}
Message Length: 11 characters
Timestamp: Wed Aug  6 13:48:03 EAT 2025
---
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
  0     0    0     0    0     0      0      0 --:--:--  0:00:01 --:--:--     0
  0     0    0     0    0     0      0      0 --:--:--  0:00:01 --:--:--     0
100   554  100   472  100    82    268     46  0:00:01  0:00:01 --:--:--   315
{"ResponseCode":"200","ResponseMessage":"SUCCESS!.","Data":{"MessageID":"4adfb4a7-86bb-463f-938f-60b4ab5d716d","Description":"The message is successfully sent!!","DeliveryCallBack":"https://smsapi.hormuud.com/api/Delivery/GetDelivery?MessageId=4adfb4a7-86bb-463f-938f-60b4ab5d716d","Details":{"TextLength":12,"TotalCharacters":12,"TotalSMS":1,"IsGMS7Bit":true,"ContainsUnicode":false,"IsMultipart":false,"ExtensionSet":[],"UnicodeSet":[],"MessageParts":["Minimal test"]}}}

Response Time: 1.758054s
HTTP Status: 200
Content Length: 472 bytes
========================================
Test: 50 Characters
Message: HOSPITAL"
}
Message Length: 11 characters
Timestamp: Wed Aug  6 13:48:07 EAT 2025
---
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100   663  100   507  100   156   2683    825 --:--:-- --:--:-- --:--:--  3526
{"ResponseCode":"200","ResponseMessage":"SUCCESS!.","Data":{"MessageID":"a0e6a032-bec9-4631-b9da-0e50d2baa0ab","Description":"The message is successfully sent!!","DeliveryCallBack":"https://smsapi.hormuud.com/api/Delivery/GetDelivery?MessageId=a0e6a032-bec9-4631-b9da-0e50d2baa0ab","Details":{"TextLength":47,"TotalCharacters":47,"TotalSMS":1,"IsGMS7Bit":true,"ContainsUnicode":false,"IsMultipart":false,"ExtensionSet":[],"UnicodeSet":[],"MessageParts":["This is a 50 character test message for length."]}}}

Response Time: 0.188924s
HTTP Status: 200
Content Length: 507 bytes
========================================
Test: 100 Characters
Message: HOSPITAL"
}
Message Length: 11 characters
Timestamp: Wed Aug  6 13:48:09 EAT 2025
---
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100   760  100   555  100   205   2969   1096 --:--:-- --:--:-- --:--:--  4086
{"ResponseCode":"200","ResponseMessage":"SUCCESS!.","Data":{"MessageID":"06097f19-b2ad-4af8-b6a5-42d642f4c2f4","Description":"The message is successfully sent!!","DeliveryCallBack":"https://smsapi.hormuud.com/api/Delivery/GetDelivery?MessageId=06097f19-b2ad-4af8-b6a5-42d642f4c2f4","Details":{"TextLength":95,"TotalCharacters":95,"TotalSMS":1,"IsGMS7Bit":true,"ContainsUnicode":false,"IsMultipart":false,"ExtensionSet":[],"UnicodeSet":[],"MessageParts":["This is a 100 character test message to check length limits and see how the API responds to it."]}}}

Response Time: 0.186898s
HTTP Status: 200
Content Length: 555 bytes
========================================
Test: 160 Characters (Standard SMS)
Message: HOSPITAL"
}
Message Length: 11 characters
Timestamp: Wed Aug  6 13:48:11 EAT 2025
---
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100   256    0     0  100   256      0    240  0:00:01  0:00:01 --:--:--   240
100   864  100   608  100   256    326    137  0:00:01  0:00:01 --:--:--   464
100   864  100   608  100   256    326    137  0:00:01  0:00:01 --:--:--   464
{"ResponseCode":"200","ResponseMessage":"SUCCESS!.","Data":{"MessageID":"eed44b09-e134-4b94-999d-df4b1dabd60f","Description":"The message is successfully sent!!","DeliveryCallBack":"https://smsapi.hormuud.com/api/Delivery/GetDelivery?MessageId=eed44b09-e134-4b94-999d-df4b1dabd60f","Details":{"TextLength":146,"TotalCharacters":146,"TotalSMS":1,"IsGMS7Bit":true,"ContainsUnicode":false,"IsMultipart":false,"ExtensionSet":[],"UnicodeSet":[],"MessageParts":["This is a 160 character test message to check the standard SMS length limit and see how the Hormuud API responds to messages of this exact length."]}}}

Response Time: 1.860196s
HTTP Status: 200
Content Length: 608 bytes
========================================
Test: 320 Characters (2 SMS)
Message: HOSPITAL"
}
Message Length: 11 characters
Timestamp: Wed Aug  6 13:48:15 EAT 2025
---
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100  1182  100   768  100   414   1009    544 --:--:-- --:--:-- --:--:--  1553
100  1182  100   768  100   414   1009    544 --:--:-- --:--:-- --:--:--  1553
{"ResponseCode":"200","ResponseMessage":"SUCCESS!.","Data":{"MessageID":"a9cfa0e6-6ec3-42fc-b55c-d9b1ae7e6901","Description":"The message is successfully sent!!","DeliveryCallBack":"https://smsapi.hormuud.com/api/Delivery/GetDelivery?MessageId=a9cfa0e6-6ec3-42fc-b55c-d9b1ae7e6901","Details":{"TextLength":304,"TotalCharacters":304,"TotalSMS":2,"IsGMS7Bit":true,"ContainsUnicode":false,"IsMultipart":true,"ExtensionSet":[],"UnicodeSet":[],"MessageParts":["This is a 320 character test message to check how the API handles longer messages that would typically be split into multiple SMS parts. This message sho","uld be long enough to test the multipart SMS functionality and see if the API properly handles concatenated messages or if it has specific limitations."]}}}

Response Time: 0.760796s
HTTP Status: 200
Content Length: 768 bytes
========================================
Test: 459 Characters (Maximum)
Message: HOSPITAL"
}
Message Length: 11 characters
Timestamp: Wed Aug  6 13:48:18 EAT 2025
---
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100  1381  100   869  100   512   3444   2029 --:--:-- --:--:-- --:--:--  5480
{"ResponseCode":"200","ResponseMessage":"SUCCESS!.","Data":{"MessageID":"80da42c2-7ed4-4d58-9f43-c91e53385309","Description":"The message is successfully sent!!","DeliveryCallBack":"https://smsapi.hormuud.com/api/Delivery/GetDelivery?MessageId=80da42c2-7ed4-4d58-9f43-c91e53385309","Details":{"TextLength":402,"TotalCharacters":402,"TotalSMS":3,"IsGMS7Bit":true,"ContainsUnicode":false,"IsMultipart":true,"ExtensionSet":[],"UnicodeSet":[],"MessageParts":["This is a 459 character test message to check the maximum length that our system allows for non-OTP messages. This message is designed to test the upper ","limit of what the Hormuud SMS API can handle and whether it properly processes very long messages or if there are any restrictions or limitations that we"," need to be aware of when sending longer content to customers through their SMS gateway service."]}}}

Response Time: 0.252290s
HTTP Status: 200
Content Length: 869 bytes
========================================
Test: 500+ Characters (Over Limit)
Message: HOSPITAL"
}
Message Length: 11 characters
Timestamp: Wed Aug  6 13:48:20 EAT 2025
---
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100  1556  100   958  100   598   1363    851 --:--:-- --:--:-- --:--:--  2213
100  1556  100   958  100   598   1363    851 --:--:-- --:--:-- --:--:--  2213
{"ResponseCode":"200","ResponseMessage":"SUCCESS!.","Data":{"MessageID":"c9531243-e811-470a-b6b1-2c6a4d55d72f","Description":"The message is successfully sent!!","DeliveryCallBack":"https://smsapi.hormuud.com/api/Delivery/GetDelivery?MessageId=c9531243-e811-470a-b6b1-2c6a4d55d72f","Details":{"TextLength":488,"TotalCharacters":488,"TotalSMS":4,"IsGMS7Bit":true,"ContainsUnicode":false,"IsMultipart":true,"ExtensionSet":[],"UnicodeSet":[],"MessageParts":["This is a 500+ character test message that exceeds our normal limits to see how the Hormuud SMS API responds to very long messages. This test is designed"," to understand if the API has its own length restrictions and how it handles messages that are longer than typical SMS limits. We want to see if it trunc","ates, rejects, or processes the entire message and what kind of response we get back from the API when we send content that is significantly longer than ","standard SMS message lengths."]}}}

Response Time: 0.702562s
HTTP Status: 200
Content Length: 958 bytes
========================================
Test: ASCII Only
Message: HOSPITAL"
}
Message Length: 11 characters
Timestamp: Wed Aug  6 13:48:23 EAT 2025
---
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100   139    0     0  100   139      0    131  0:00:01  0:00:01 --:--:--   131
100   630  100   491  100   139    266     75  0:00:01  0:00:01 --:--:--   341
100   630  100   491  100   139    266     75  0:00:01  0:00:01 --:--:--   341
{"ResponseCode":"200","ResponseMessage":"SUCCESS!.","Data":{"MessageID":"ce0df7c9-c4e2-4e42-8a34-de6bc6e75ed7","Description":"The message is successfully sent!!","DeliveryCallBack":"https://smsapi.hormuud.com/api/Delivery/GetDelivery?MessageId=ce0df7c9-c4e2-4e42-8a34-de6bc6e75ed7","Details":{"TextLength":31,"TotalCharacters":31,"TotalSMS":1,"IsGMS7Bit":true,"ContainsUnicode":false,"IsMultipart":false,"ExtensionSet":[],"UnicodeSet":[],"MessageParts":["ASCII only test message 123 ABC"]}}}

Response Time: 1.842667s
HTTP Status: 200
Content Length: 491 bytes
========================================
Test: Special Characters
Message: HOSPITAL"
}
Message Length: 11 characters
Timestamp: Wed Aug  6 13:48:27 EAT 2025
---
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100   719  100   546  100   173   2847    902 --:--:-- --:--:-- --:--:--  3764
{"ResponseCode":"200","ResponseMessage":"SUCCESS!.","Data":{"MessageID":"b9a7b4c9-e134-4f80-8275-5379d2ae16cb","Description":"The message is successfully sent!!","DeliveryCallBack":"https://smsapi.hormuud.com/api/Delivery/GetDelivery?MessageId=b9a7b4c9-e134-4f80-8275-5379d2ae16cb","Details":{"TextLength":63,"TotalCharacters":69,"TotalSMS":1,"IsGMS7Bit":true,"ContainsUnicode":false,"IsMultipart":false,"ExtensionSet":["^","[","]","{","}","|"],"UnicodeSet":[],"MessageParts":["Test with numbers 123456 and symbols !@#$%^&*()_+-=[]{}|;:,.<>?"]}}}

Response Time: 0.191765s
HTTP Status: 200
Content Length: 546 bytes
========================================
Test: Unicode/Emojis (Problematic)
Message: HOSPITAL"
}
Message Length: 11 characters
Timestamp: Wed Aug  6 13:48:29 EAT 2025
---
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100   187  100    36  100   151    167    701 --:--:-- --:--:-- --:--:--   869
{"Message":"An error has occurred."}

Response Time: 0.215376s
HTTP Status: 500
Content Length: 36 bytes
========================================
Test: Smart Quotes and Dashes
Message: HOSPITAL"
}
Message Length: 11 characters
Timestamp: Wed Aug  6 13:48:32 EAT 2025
---
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100   670  100   516  100   154   1060    316 --:--:-- --:--:-- --:--:--  1375
100   670  100   516  100   154   1059    316 --:--:-- --:--:-- --:--:--  1375
{"ResponseCode":"200","ResponseMessage":"SUCCESS!.","Data":{"MessageID":"8e45055a-9f13-46b9-8b83-4222153b0dfa","Description":"The message is successfully sent!!","DeliveryCallBack":"https://smsapi.hormuud.com/api/Delivery/GetDelivery?MessageId=8e45055a-9f13-46b9-8b83-4222153b0dfa","Details":{"TextLength":39,"TotalCharacters":39,"TotalSMS":1,"IsGMS7Bit":false,"ContainsUnicode":true,"IsMultipart":false,"ExtensionSet":[],"UnicodeSet":["–","—"],"MessageParts":["Smart quotes \"test\" and dashes – — test"]}}}

Response Time: 0.486860s
HTTP Status: 200
Content Length: 516 bytes
========================================
Test: Invalid Phone Number
Message: HOSPITAL"
}
Message Length: 11 characters
Timestamp: Wed Aug  6 13:48:34 EAT 2025
---
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100   335  100   192  100   143   1821   1356 --:--:-- --:--:-- --:--:--  3190
{"ResponseCode":"207","ResponseMessage":"Failed.","Data":{"MessageID":"null","Description":"Invalid Mobile Number/ Not allowed for international Sms!!","DeliveryCallBack":null,"Details":null}}

Response Time: 0.105415s
HTTP Status: 200
Content Length: 192 bytes
========================================
Test: Missing Mobile Field
Message: HOSPITAL"
}
Message Length: 11 characters
Timestamp: Wed Aug  6 13:48:36 EAT 2025
---
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100   303  100   183  100   120   2503   1641 --:--:-- --:--:-- --:--:--  4150
{"ResponseCode":"205","ResponseMessage":"Failed.","Data":{"MessageID":"null","Description":"An error occurred, please contact support center.","DeliveryCallBack":null,"Details":null}}

Response Time: 0.073098s
HTTP Status: 200
Content Length: 183 bytes
========================================
Test: Empty Message
Message: HOSPITAL"
}
Message Length: 11 characters
Timestamp: Wed Aug  6 13:48:38 EAT 2025
---
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0
100   264  100   148  100   116   2360   1850 --:--:-- --:--:-- --:--:--  4258
{"ResponseCode":"500","ResponseMessage":"Failed.","Data":{"MessageID":"null","Description":"Unkown Error!!","DeliveryCallBack":null,"Details":null}}

Response Time: 0.062697s
HTTP Status: 200
Content Length: 148 bytes
========================================
