# 📱 SMS Service Testing Guide

This guide explains how to test the SMS service functionality in the Gas Delivery System backend.

## 🎯 Test Phone Number

All SMS tests are configured to use the phone number: **613656021**

## 📋 Available Test Scripts

### 1. Comprehensive SMS Test Suite
```bash
npm run test:sms
```
**File**: `src/test/sms_service_test.ts`

**What it tests**:
- ✅ Service health check
- ✅ Service metrics retrieval
- ✅ Single SMS sending
- ✅ OTP SMS sending
- ✅ Phone number validation
- ✅ Message validation
- ✅ Bulk SMS sending
- ✅ Error handling

### 2. Simple SMS Test
```bash
npm run test:sms:simple
```
**File**: `src/test/sms_test_simple.ts`

**What it tests**:
- ✅ Quick SMS service validation
- ✅ Single SMS to test number
- ✅ OTP SMS functionality
- ✅ Basic error handling

### 3. Bulk SMS Test
```bash
npm run test:sms:bulk
```
**File**: `src/test/sms_bulk_test.ts`

**What it tests**:
- ✅ Bulk SMS to multiple recipients
- ✅ Duplicate number handling
- ✅ Progress tracking
- ✅ Performance metrics
- ✅ Error handling for invalid numbers
- ✅ Large batch processing

### 4. Diagnostic Test
```bash
npm run test:sms:diagnostic
```
**File**: `src/test/sms_diagnostic_test.ts`

**What it tests**:
- ✅ Configuration validation
- ✅ Authentication testing
- ✅ Direct API calls
- ✅ Different message types
- ✅ Unicode character detection

### 5. Debug Test
```bash
npm run test:sms:debug
```
**File**: `src/test/sms_debug_test.ts`

**What it tests**:
- ✅ Quick issue identification
- ✅ Detailed error analysis
- ✅ Service health validation

### 6. Unicode Fix Test
```bash
npm run test:sms:unicode-fix
```
**File**: `src/test/sms_unicode_fix_test.ts`

**What it tests**:
- ✅ Unicode character detection
- ✅ Text sanitization
- ✅ SMS-safe templates
- ✅ Fixed message sending

## 🚀 Quick Start

1. **Ensure environment variables are set** in your `.env` file:
   ```env
   SMS_USERNAME=your_sms_username
   SMS_PASSWORD=your_sms_password
   SMS_PROVIDER_URL=your_provider_url
   SMS_SENDER_ID=your_sender_id
   SMS_TIMEOUT=30000
   ```

2. **Run a simple test first**:
   ```bash
   npm run test:sms:simple
   ```

3. **If simple test passes, run comprehensive tests**:
   ```bash
   npm run test:sms
   ```

## 📊 Test Results Interpretation

### ✅ Successful Test Output
```
✅ PASSED: Service Health Check (150ms)
✅ PASSED: Single SMS Sending (1200ms)
✅ SMS sent successfully to 613656021
📧 Message ID: MSG_123456789
```

### ❌ Failed Test Output
```
❌ FAILED: Single SMS Sending (5000ms)
   Error: SMS request timed out
```

### 📈 Service Metrics
```
📊 Service Metrics:
   Sent Count: 5
   Failed Count: 1
   Success Rate: 80.0%
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Authentication Errors
```
Error: Failed to authenticate with SMS service
```
**Solution**: Check your SMS credentials in environment variables.

#### 2. Invalid Phone Number
```
Error: Invalid Somalia phone number format
```
**Solution**: Ensure phone numbers follow Somalia format (61xxxxxxx, 62xxxxxxx, etc.).

#### 3. Message Too Long
```
Error: Message too long (max 459 characters)
```
**Solution**: Reduce message length or use OTP format (max 160 characters).

#### 4. Rate Limiting
```
Error: Too many requests
```
**Solution**: Tests include built-in rate limiting. Wait and retry.

#### 5. Network Timeout
```
Error: SMS request timed out
```
**Solution**: Check internet connection and SMS provider status.

#### 6. Unicode/Emoji Errors ⚠️ **MOST COMMON ISSUE**
```
Error: Unexpected SMS service error
```
**Root Cause**: Unicode characters (emojis, special symbols) cause SMS provider errors.

**Examples of Problematic Characters**:
- Emojis: 🔥 📱 ✅ ❌ 📊 🚀
- Special symbols: • → ← ✓ ×
- Smart quotes: " " ' '
- Special dashes: – —

**Solutions**:
1. **Use SMS-safe templates**: `SmsSafeTemplates.welcome('Ahmed')`
2. **Auto-sanitize messages**: `prepareSmsText('🔥 Message')`
3. **Validate before sending**: `validateSmsText('Message')`
4. **Run Unicode fix test**: `npm run test:sms:unicode-fix`

**Quick Fix Example**:
```typescript
import { prepareSmsText } from '../utils/sms_utils';

// Instead of:
const message = '🔥 Welcome to Ciribey Gas Delivery! ✅';

// Use:
const message = prepareSmsText('🔥 Welcome to Ciribey Gas Delivery! ✅');
// Result: "FIRE Welcome to Ciribey Gas Delivery! CHECK"
```

## 📱 Test Phone Number Details

- **Number**: 613656021
- **Format**: Somalia mobile number
- **Provider**: Compatible with Hormuud/Telesom networks
- **Usage**: All test messages will be sent to this number

## 🔍 Test Coverage

### Phone Number Validation Tests
- ✅ Valid Somalia numbers (61x, 62x, 65x, etc.)
- ❌ Invalid formats (too short, too long, wrong prefix)
- ❌ Non-numeric characters

### Message Validation Tests
- ✅ Normal messages (up to 459 characters)
- ✅ OTP messages (up to 160 characters)
- ❌ Messages exceeding limits

### Bulk SMS Tests
- ✅ Multiple recipients
- ✅ Duplicate removal
- ✅ Progress tracking
- ✅ Partial success handling
- ✅ Performance metrics

## 📊 Performance Benchmarks

### Expected Performance
- **Single SMS**: 500-2000ms
- **Bulk SMS (10 messages)**: 2-5 seconds
- **Success Rate**: >95% under normal conditions

### Rate Limits
- **Minimum delay**: 100ms between requests
- **Concurrent requests**: Max 5 simultaneous
- **Retry attempts**: 3 retries with 1-second delay

## 🛡️ Security Considerations

### Test Data
- All tests use the designated test number: 613656021
- No sensitive data is transmitted in test messages
- Test messages clearly identify as test communications

### Error Handling
- Sensitive information is not logged in error messages
- Authentication tokens are not exposed in logs
- Failed messages are tracked for debugging without exposing content

## 📝 Test Message Examples

### Standard Test Message
```
🔥 Ciribey Gas Delivery - SMS Test

Mahadsanid! (Thank you!)

This is a test message from the Ciribey Gas Delivery SMS service.

✅ Service Status: Working
📅 Test Time: [timestamp]

Kooxda Ciribey Gas Delivery Team
📞 +252613656021
```

### OTP Test Message
```
Your Ciribey Gas Delivery verification code is: 123456

This code will expire in 5 minutes.

Do not share this code with anyone.

Mahadsanid! (Thank you!)
Ciribey Gas Delivery Team
```

## 🔄 Continuous Testing

### Automated Testing
- Tests can be integrated into CI/CD pipelines
- Use environment variables to control test execution
- Monitor success rates and performance metrics

### Manual Testing
- Run tests before deployments
- Verify SMS functionality after configuration changes
- Test different message types and lengths

## 📞 Support

If you encounter issues with SMS testing:

1. **Check environment configuration**
2. **Verify network connectivity**
3. **Review SMS provider status**
4. **Check test phone number availability**
5. **Review error logs for specific issues**

For additional support, contact the development team or check the SMS provider documentation.

---

**Built with ❤️ for Ciribey Gas Delivery System**
