# Individual Curl Commands for Hormuud SMS API Testing
# Copy and paste these commands one by one in your terminal

# Set environment variables first:
export API_URL="https://smsapi.hormuud.com/api/SendSMS"
export TOKEN="Y100Z9tep744u7numGF7CgYA4l7YRqH4pc8K6AqZ6YZIWVTY6JdNt0kUQWCty2cJcL8QSkVL1GZJzhKtY50-DXa1LzesoVVOSTmaMUGO7OFhlN-9ueGIWQQdRPbIUBKtAmq4ctudbFFq2lHBQo7GfBV8iDNeUIGI-AkQgEUkKlO7AotE3JSiEoPzmmBpDz1nknxEz5WZSI9vQUjEYu_909slFGRJ68DVDVDr3DZeute7r84VfCuQXaRx0iR-vKGG-vHF0y2blrpCREC18ciGcRCzy5kPsO5Fik2rY72bR5Og9JYp_NjfLujiR3i03JjJgkIPRkmSdO5vbdmUFxbi5YV7Bw_EDS8w8dlp2SOiaLYCbei28avsnLO_ot_WJnmxFvyhzkXZ_7zdLPytGmhsP5UYY24Ng6GUqSrQpVjcm5CatcCvN0EnGvlW1ZPsW7sIRbQxHVIeUpYzwQT-NNVQTyBb_7kNPvXQqLVm6VjyerwynGJLNosp409zBWfRkuEamJSTiLWAL8BBVnByFSXnKA"
export PHONE="613656021"
export SENDER_ID="HODAN HOSPITAL"

# ========================================
# 1. BASIC FUNCTIONALITY TESTS
# ========================================

# Test 1.1: Basic Short Message
echo "=== Test 1.1: Basic Short Message ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\n\nResponse Time: %{time_total}s\nHTTP Status: %{http_code}\nContent Length: %{size_download} bytes\n" \
  -d '{
    "refid": "test-basic-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "Basic test message",
    "senderid": "'$SENDER_ID'"
  }'

echo -e "\n\n"

# Test 1.2: Minimal Required Fields
echo "=== Test 1.2: Minimal Required Fields ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\n\nResponse Time: %{time_total}s\nHTTP Status: %{http_code}\n" \
  -d '{
    "mobile": "'$PHONE'",
    "message": "Minimal test",
    "senderid": "'$SENDER_ID'"
  }'

echo -e "\n\n"

# ========================================
# 2. MESSAGE LENGTH TESTS
# ========================================

# Test 2.1: 50 Characters
echo "=== Test 2.1: 50 Characters ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\n\nResponse Time: %{time_total}s\nHTTP Status: %{http_code}\n" \
  -d '{
    "refid": "test-50char-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "This is a 50 character test message for length.",
    "senderid": "'$SENDER_ID'"
  }'

echo -e "\n\n"

# Test 2.2: 100 Characters
echo "=== Test 2.2: 100 Characters ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\n\nResponse Time: %{time_total}s\nHTTP Status: %{http_code}\n" \
  -d '{
    "refid": "test-100char-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "This is a 100 character test message to check length limits and see how the API responds to it.",
    "senderid": "'$SENDER_ID'"
  }'

echo -e "\n\n"

# Test 2.3: 160 Characters (Standard SMS)
echo "=== Test 2.3: 160 Characters (Standard SMS) ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\n\nResponse Time: %{time_total}s\nHTTP Status: %{http_code}\n" \
  -d '{
    "refid": "test-160char-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "This is a 160 character test message to check the standard SMS length limit and see how the Hormuud API responds to messages of this exact length.",
    "senderid": "'$SENDER_ID'"
  }'

echo -e "\n\n"

# Test 2.4: 320 Characters (2 SMS)
echo "=== Test 2.4: 320 Characters (2 SMS) ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\n\nResponse Time: %{time_total}s\nHTTP Status: %{http_code}\n" \
  -d '{
    "refid": "test-320char-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "This is a 320 character test message to check how the API handles longer messages that would typically be split into multiple SMS parts. This message should be long enough to test the multipart SMS functionality and see if the API properly handles concatenated messages or if it has specific limitations.",
    "senderid": "'$SENDER_ID'"
  }'

echo -e "\n\n"

# Test 2.5: 459 Characters (Maximum)
echo "=== Test 2.5: 459 Characters (Maximum) ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\n\nResponse Time: %{time_total}s\nHTTP Status: %{http_code}\n" \
  -d '{
    "refid": "test-459char-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "This is a 459 character test message to check the maximum length that our system allows for non-OTP messages. This message is designed to test the upper limit of what the Hormuud SMS API can handle and whether it properly processes very long messages or if there are any restrictions or limitations that we need to be aware of when sending longer content to customers through their SMS gateway service.",
    "senderid": "'$SENDER_ID'"
  }'

echo -e "\n\n"

# Test 2.6: 500+ Characters (Over Limit)
echo "=== Test 2.6: 500+ Characters (Over Limit) ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\n\nResponse Time: %{time_total}s\nHTTP Status: %{http_code}\n" \
  -d '{
    "refid": "test-500char-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "This is a 500+ character test message that exceeds our normal limits to see how the Hormuud SMS API responds to very long messages. This test is designed to understand if the API has its own length restrictions and how it handles messages that are longer than typical SMS limits. We want to see if it truncates, rejects, or processes the entire message and what kind of response we get back from the API when we send content that is significantly longer than standard SMS message lengths.",
    "senderid": "'$SENDER_ID'"
  }'

echo -e "\n\n"

# ========================================
# 3. CHARACTER ENCODING TESTS
# ========================================

# Test 3.1: ASCII Only
echo "=== Test 3.1: ASCII Only ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\n\nResponse Time: %{time_total}s\nHTTP Status: %{http_code}\n" \
  -d '{
    "refid": "test-ascii-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "ASCII only test message 123 ABC",
    "senderid": "'$SENDER_ID'"
  }'

echo -e "\n\n"

# Test 3.2: Special Characters
echo "=== Test 3.2: Special Characters ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\n\nResponse Time: %{time_total}s\nHTTP Status: %{http_code}\n" \
  -d '{
    "refid": "test-special-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "Test with numbers 123456 and symbols !@#$%^&*()_+-=[]{}|;:,.<>?",
    "senderid": "'$SENDER_ID'"
  }'

echo -e "\n\n"

# Test 3.3: Unicode/Emojis (Problematic)
echo "=== Test 3.3: Unicode/Emojis (Problematic) ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\n\nResponse Time: %{time_total}s\nHTTP Status: %{http_code}\n" \
  -d '{
    "refid": "test-unicode-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "Unicode test: 🔥 📱 ✅ ❌ 📊 🚀",
    "senderid": "'$SENDER_ID'"
  }'

echo -e "\n\n"

# Test 3.4: Smart Quotes and Dashes
echo "=== Test 3.4: Smart Quotes and Dashes ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\n\nResponse Time: %{time_total}s\nHTTP Status: %{http_code}\n" \
  -d '{
    "refid": "test-quotes-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "Smart quotes \"test\" and dashes – — test",
    "senderid": "'$SENDER_ID'"
  }'

echo -e "\n\n"

# ========================================
# 4. ERROR HANDLING TESTS
# ========================================

# Test 4.1: Invalid Phone Number
echo "=== Test 4.1: Invalid Phone Number ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\n\nResponse Time: %{time_total}s\nHTTP Status: %{http_code}\n" \
  -d '{
    "refid": "test-invalid-phone-'$(date +%s)'",
    "mobile": "123456",
    "message": "Test with invalid phone number",
    "senderid": "'$SENDER_ID'"
  }'

echo -e "\n\n"

# Test 4.2: Missing Mobile Field
echo "=== Test 4.2: Missing Mobile Field ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\n\nResponse Time: %{time_total}s\nHTTP Status: %{http_code}\n" \
  -d '{
    "refid": "test-missing-mobile-'$(date +%s)'",
    "message": "Test with missing mobile field",
    "senderid": "'$SENDER_ID'"
  }'

echo -e "\n\n"

# Test 4.3: Empty Message
echo "=== Test 4.3: Empty Message ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\n\nResponse Time: %{time_total}s\nHTTP Status: %{http_code}\n" \
  -d '{
    "refid": "test-empty-message-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "",
    "senderid": "'$SENDER_ID'"
  }'

echo -e "\n\n"

# Test 4.4: No Authorization Header
echo "=== Test 4.4: No Authorization Header ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -w "\n\nResponse Time: %{time_total}s\nHTTP Status: %{http_code}\n" \
  -d '{
    "refid": "test-no-auth-'$(date +%s)'",
    "mobile": "'$PHONE'",
    "message": "Test without authorization",
    "senderid": "'$SENDER_ID'"
  }'

echo -e "\n\n"
